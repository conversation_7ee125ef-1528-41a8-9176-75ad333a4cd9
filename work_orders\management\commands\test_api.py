from django.core.management.base import BaseCommand
from django.http import HttpRequest
from work_orders.api_spare_parts import api_get_spare_parts

class Command(BaseCommand):
    help = 'Test the API endpoint for spare parts'

    def handle(self, *args, **options):
        # Create a mock request
        request = HttpRequest()
        request.method = 'GET'
        request.GET = {
            'operation_id': '1',
            'vehicle_id': '1',
        }
        
        # Call the API function directly
        response = api_get_spare_parts(request)
        
        # Print the response
        self.stdout.write(f"Status code: {response.status_code}")
        self.stdout.write(f"Content: {response.content.decode()}") 