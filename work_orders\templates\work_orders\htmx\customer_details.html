{% load i18n %}

<div class="bg-blue-50 p-3 rounded-lg">
    <div class="flex justify-between items-start">
        <div>
            <h6 class="font-semibold text-blue-800">{{ customer.get_full_name }}</h6>
            <div class="mt-1">
                {% if customer.phone %}
                    <div class="text-sm text-gray-700">
                        <i class="fas fa-phone-alt text-blue-600 mr-2"></i> {{ customer.phone }}
                    </div>
                {% endif %}
                
                {% if customer.email %}
                    <div class="text-sm text-gray-700">
                        <i class="fas fa-envelope text-blue-600 mr-2"></i> {{ customer.email }}
                    </div>
                {% endif %}
                
                {% if customer.address %}
                    <div class="text-sm text-gray-700">
                        <i class="fas fa-map-marker-alt text-blue-600 mr-2"></i> {{ customer.address }}
                    </div>
                {% endif %}
            </div>
        </div>
        
        <div>
            <button type="button" class="text-red-600 hover:text-red-800 text-sm"
                    onclick="clearCustomerSelection()">
                <i class="fas fa-times"></i> {% trans "Clear" %}
            </button>
        </div>
    </div>
    
    <input type="hidden" name="customer" value="{{ customer.id }}">
</div> 