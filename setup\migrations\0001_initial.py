# Generated by Django 4.2.20 on 2025-05-07 10:32

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Company',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, verbose_name='Company Name')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='Company Code')),
                ('logo', models.ImageField(blank=True, null=True, upload_to='company_logos', verbose_name='Logo')),
                ('address', models.TextField(blank=True, verbose_name='Address')),
                ('city', models.CharField(blank=True, max_length=100, verbose_name='City')),
                ('state', models.CharField(blank=True, max_length=100, verbose_name='State/Province')),
                ('country', models.CharField(blank=True, max_length=100, verbose_name='Country')),
                ('postal_code', models.CharField(blank=True, max_length=20, verbose_name='Postal Code')),
                ('phone', models.CharField(blank=True, max_length=50, verbose_name='Phone')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='Email')),
                ('website', models.URLField(blank=True, verbose_name='Website')),
                ('tax_id', models.CharField(blank=True, max_length=100, verbose_name='Tax ID')),
                ('registration_number', models.CharField(blank=True, max_length=100, verbose_name='Registration Number')),
                ('founding_date', models.DateField(blank=True, null=True, verbose_name='Founding Date')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('attributes', models.JSONField(blank=True, default=dict, verbose_name='Custom Attributes')),
            ],
            options={
                'verbose_name': 'Company',
                'verbose_name_plural': 'Companies',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Franchise',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, verbose_name='Franchise Name')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='Franchise Code')),
                ('logo', models.ImageField(blank=True, null=True, upload_to='franchise_logos', verbose_name='Logo')),
                ('address', models.TextField(blank=True, verbose_name='Address')),
                ('city', models.CharField(blank=True, max_length=100, verbose_name='City')),
                ('state', models.CharField(blank=True, max_length=100, verbose_name='State/Province')),
                ('country', models.CharField(blank=True, max_length=100, verbose_name='Country')),
                ('postal_code', models.CharField(blank=True, max_length=20, verbose_name='Postal Code')),
                ('phone', models.CharField(blank=True, max_length=50, verbose_name='Phone')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='Email')),
                ('website', models.URLField(blank=True, verbose_name='Website')),
                ('tax_id', models.CharField(blank=True, max_length=100, verbose_name='Tax ID')),
                ('registration_number', models.CharField(blank=True, max_length=100, verbose_name='Registration Number')),
                ('founding_date', models.DateField(blank=True, null=True, verbose_name='Founding Date')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('attributes', models.JSONField(blank=True, default=dict, verbose_name='Custom Attributes')),
            ],
            options={
                'verbose_name': 'Franchise',
                'verbose_name_plural': 'Franchises',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='ServiceCenter',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('name', models.CharField(max_length=255, verbose_name='Service Center Name')),
                ('code', models.CharField(max_length=50, verbose_name='Service Center Code')),
                ('address', models.TextField(blank=True, verbose_name='Address')),
                ('city', models.CharField(blank=True, max_length=100, verbose_name='City')),
                ('state', models.CharField(blank=True, max_length=100, verbose_name='State/Province')),
                ('country', models.CharField(blank=True, max_length=100, verbose_name='Country')),
                ('postal_code', models.CharField(blank=True, max_length=20, verbose_name='Postal Code')),
                ('latitude', models.DecimalField(blank=True, decimal_places=7, max_digits=10, null=True, verbose_name='Latitude')),
                ('longitude', models.DecimalField(blank=True, decimal_places=7, max_digits=10, null=True, verbose_name='Longitude')),
                ('phone', models.CharField(blank=True, max_length=50, verbose_name='Phone')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='Email')),
                ('opening_hours', models.JSONField(blank=True, default=dict, verbose_name='Opening Hours')),
                ('capacity', models.PositiveIntegerField(default=10, help_text='Maximum work orders per day', verbose_name='Daily Capacity')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('attributes', models.JSONField(blank=True, default=dict, verbose_name='Custom Attributes')),
            ],
            options={
                'verbose_name': 'Service Center',
                'verbose_name_plural': 'Service Centers',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='ServiceCenterType',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Type Name')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('max_capacity', models.PositiveIntegerField(blank=True, help_text='Maximum number of work orders per day', null=True, verbose_name='Maximum Capacity')),
                ('color_code', models.CharField(blank=True, help_text='For UI display', max_length=20, verbose_name='Color Code')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('attributes', models.JSONField(blank=True, default=dict, verbose_name='Custom Attributes')),
            ],
            options={
                'verbose_name': 'Service Center Type',
                'verbose_name_plural': 'Service Center Types',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='ServiceLevel',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Service Level Name')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('priority', models.PositiveSmallIntegerField(default=0, verbose_name='Priority')),
                ('response_time_hours', models.PositiveIntegerField(default=24, verbose_name='Response Time (Hours)')),
                ('resolution_time_hours', models.PositiveIntegerField(default=72, verbose_name='Resolution Time (Hours)')),
                ('support_hours', models.CharField(default='24/7', max_length=100, verbose_name='Support Hours')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('attributes', models.JSONField(blank=True, default=dict, verbose_name='Custom Attributes')),
            ],
            options={
                'verbose_name': 'Service Level',
                'verbose_name_plural': 'Service Levels',
                'ordering': ['priority'],
            },
        ),
        migrations.CreateModel(
            name='Vehicle',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('make', models.CharField(max_length=100, verbose_name='Make')),
                ('model', models.CharField(max_length=100, verbose_name='Model')),
                ('year', models.PositiveIntegerField(blank=True, null=True, verbose_name='Year')),
                ('vin', models.CharField(blank=True, max_length=100, verbose_name='VIN')),
                ('license_plate', models.CharField(blank=True, max_length=50, verbose_name='License Plate')),
                ('color', models.CharField(blank=True, max_length=50, verbose_name='Color')),
                ('owner_name', models.CharField(max_length=255, verbose_name='Owner Name')),
                ('owner_phone', models.CharField(blank=True, max_length=50, verbose_name='Owner Phone')),
                ('owner_email', models.EmailField(blank=True, max_length=254, verbose_name='Owner Email')),
                ('purchase_date', models.DateField(blank=True, null=True, verbose_name='Purchase Date')),
                ('warranty_end_date', models.DateField(blank=True, null=True, verbose_name='Warranty End Date')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('attributes', models.JSONField(blank=True, default=dict, verbose_name='Custom Attributes')),
                ('service_center', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='vehicles', to='setup.servicecenter', verbose_name='Service Center')),
            ],
            options={
                'verbose_name': 'Vehicle',
                'verbose_name_plural': 'Vehicles',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ServiceHistory',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('service_date', models.DateField(verbose_name='Service Date')),
                ('odometer', models.PositiveIntegerField(blank=True, null=True, verbose_name='Odometer Reading')),
                ('description', models.TextField(verbose_name='Description')),
                ('work_order_number', models.CharField(blank=True, max_length=50, verbose_name='Work Order #')),
                ('services_performed', models.JSONField(blank=True, default=list, verbose_name='Services Performed')),
                ('parts_used', models.JSONField(blank=True, default=list, verbose_name='Parts Used')),
                ('technician', models.CharField(blank=True, max_length=255, verbose_name='Technician')),
                ('total_cost', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True, verbose_name='Total Cost')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('attributes', models.JSONField(blank=True, default=dict, verbose_name='Custom Attributes')),
                ('service_center', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='service_records', to='setup.servicecenter', verbose_name='Service Center')),
                ('vehicle', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='service_history', to='setup.vehicle', verbose_name='Vehicle')),
            ],
            options={
                'verbose_name': 'Service History',
                'verbose_name_plural': 'Service History',
                'ordering': ['-service_date'],
            },
        ),
        migrations.AddField(
            model_name='servicecenter',
            name='center_type',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='service_centers', to='setup.servicecentertype', verbose_name='Center Type'),
        ),
        migrations.AddField(
            model_name='servicecenter',
            name='company',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='service_centers', to='setup.company', verbose_name='Company'),
        ),
        migrations.AddField(
            model_name='servicecenter',
            name='manager',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='managed_centers', to=settings.AUTH_USER_MODEL, verbose_name='Manager'),
        ),
        migrations.AddField(
            model_name='servicecenter',
            name='service_level',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='service_centers', to='setup.servicelevel', verbose_name='Service Level'),
        ),
        migrations.AddField(
            model_name='company',
            name='franchise',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='companies', to='setup.franchise', verbose_name='Franchise'),
        ),
        migrations.AddField(
            model_name='company',
            name='service_level',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='companies', to='setup.servicelevel', verbose_name='Service Level'),
        ),
        migrations.AlterUniqueTogether(
            name='servicecenter',
            unique_together={('tenant_id', 'code')},
        ),
    ]
