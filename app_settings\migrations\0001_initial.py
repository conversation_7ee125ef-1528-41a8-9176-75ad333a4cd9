# Generated by Django 4.2.20 on 2025-05-07 10:01

from django.db import migrations, models
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='SystemSetting',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='Name')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('value', models.JSONField(default=dict, verbose_name='Value')),
            ],
            options={
                'verbose_name': 'System Setting',
                'verbose_name_plural': 'System Settings',
            },
        ),
        migrations.CreateModel(
            name='TenantProfile',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('name', models.CharField(max_length=255, verbose_name='Company Name')),
                ('logo', models.ImageField(blank=True, null=True, upload_to='tenant_logos', verbose_name='Logo')),
                ('contact_email', models.EmailField(blank=True, max_length=254, verbose_name='Contact Email')),
                ('contact_phone', models.CharField(blank=True, max_length=50, verbose_name='Contact Phone')),
                ('address', models.TextField(blank=True, verbose_name='Address')),
                ('website', models.URLField(blank=True, verbose_name='Website')),
                ('subscription_tier', models.CharField(choices=[('free', 'Free'), ('basic', 'Basic'), ('professional', 'Professional'), ('enterprise', 'Enterprise')], default='free', max_length=50, verbose_name='Subscription Tier')),
            ],
            options={
                'verbose_name': 'Tenant Profile',
                'verbose_name_plural': 'Tenant Profiles',
            },
        ),
        migrations.CreateModel(
            name='TenantSetting',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('name', models.CharField(max_length=100, verbose_name='Name')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('value', models.JSONField(default=dict, verbose_name='Value')),
            ],
            options={
                'verbose_name': 'Tenant Setting',
                'verbose_name_plural': 'Tenant Settings',
                'unique_together': {('tenant_id', 'name')},
            },
        ),
    ]
