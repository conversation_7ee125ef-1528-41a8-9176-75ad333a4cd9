from django.shortcuts import render, get_object_or_404, redirect
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.urls import reverse_lazy
from django.contrib import messages
from django.utils.translation import gettext_lazy as _

from .models import PurchaseOrder, PurchaseOrderItem, Supplier
from inventory.models import Item


class PurchaseDashboardView(LoginRequiredMixin, ListView):
    """Dashboard view for purchases module"""
    model = PurchaseOrder
    template_name = 'purchases/dashboard.html'
    context_object_name = 'purchase_orders'
    paginate_by = 10
    
    def get_queryset(self):
        """Filter by tenant ID"""
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            return PurchaseOrder.objects.filter(tenant_id=tenant_id).order_by('-created_at')
        return PurchaseOrder.objects.none()
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        tenant_id = getattr(self.request, 'tenant_id', None)
        
        # Calculate summary statistics
        if tenant_id:
            context['total_orders'] = PurchaseOrder.objects.filter(tenant_id=tenant_id).count()
            context['pending_orders'] = PurchaseOrder.objects.filter(
                tenant_id=tenant_id, 
                status='pending'
            ).count()
            context['received_orders'] = PurchaseOrder.objects.filter(
                tenant_id=tenant_id, 
                status='received'
            ).count()
            context['suppliers_count'] = Supplier.objects.filter(tenant_id=tenant_id).count()
            
            # Get recent orders
            context['recent_orders'] = PurchaseOrder.objects.filter(
                tenant_id=tenant_id
            ).order_by('-created_at')[:5]
        else:
            context['missing_tenant'] = True
            
        return context


class PurchaseOrderListView(LoginRequiredMixin, ListView):
    """View to list all purchase orders"""
    model = PurchaseOrder
    template_name = 'purchases/purchase_order_list.html'
    context_object_name = 'purchase_orders'
    paginate_by = 20
    
    def get_queryset(self):
        """Filter by tenant ID and apply any search/filter params"""
        tenant_id = getattr(self.request, 'tenant_id', None)
        if not tenant_id:
            return PurchaseOrder.objects.none()
            
        queryset = PurchaseOrder.objects.filter(tenant_id=tenant_id)
        
        # Apply filters if provided in GET parameters
        status = self.request.GET.get('status')
        if status:
            queryset = queryset.filter(status=status)
            
        supplier = self.request.GET.get('supplier')
        if supplier:
            queryset = queryset.filter(supplier_id=supplier)
            
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(po_number__icontains=search)
            
        # Default ordering
        return queryset.order_by('-created_at')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        tenant_id = getattr(self.request, 'tenant_id', None)
        
        if tenant_id:
            context['suppliers'] = Supplier.objects.filter(tenant_id=tenant_id)
            context['missing_tenant'] = False
        else:
            context['missing_tenant'] = True
            
        # Preserve filter params for pagination
        context['current_status'] = self.request.GET.get('status', '')
        context['current_supplier'] = self.request.GET.get('supplier', '')
        context['current_search'] = self.request.GET.get('search', '')
        
        return context


class PurchaseOrderDetailView(LoginRequiredMixin, DetailView):
    """View to display purchase order details"""
    model = PurchaseOrder
    template_name = 'purchases/purchase_order_detail.html'
    context_object_name = 'purchase_order'
    
    def get_queryset(self):
        """Filter by tenant ID"""
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            return PurchaseOrder.objects.filter(tenant_id=tenant_id)
        return PurchaseOrder.objects.none()
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['items'] = self.object.items.all()
        context['missing_tenant'] = getattr(self.request, 'tenant_id', None) is None
        return context


class PurchaseOrderCreateView(LoginRequiredMixin, CreateView):
    """View to create a new purchase order"""
    model = PurchaseOrder
    template_name = 'purchases/purchase_order_form.html'
    fields = ['supplier', 'po_number', 'expected_delivery_date', 'notes', 'payment_terms']
    
    def get_form(self, form_class=None):
        form = super().get_form(form_class)
        tenant_id = getattr(self.request, 'tenant_id', None)
        
        if tenant_id:
            # Filter supplier choices by tenant
            form.fields['supplier'].queryset = Supplier.objects.filter(tenant_id=tenant_id)
        
        return form
    
    def form_valid(self, form):
        tenant_id = getattr(self.request, 'tenant_id', None)
        if not tenant_id:
            messages.error(self.request, _("Tenant ID is missing. Please set the X-Tenant-ID header."))
            return redirect('purchases:purchase_order_list')
            
        # Set tenant ID on the object
        form.instance.tenant_id = tenant_id
        
        # Set default status as 'pending'
        form.instance.status = 'pending'
        
        return super().form_valid(form)
    
    def get_success_url(self):
        return reverse_lazy('purchases:purchase_order_detail', kwargs={'pk': self.object.pk})


class PurchaseOrderUpdateView(LoginRequiredMixin, UpdateView):
    """View to update an existing purchase order"""
    model = PurchaseOrder
    template_name = 'purchases/purchase_order_form.html'
    fields = ['supplier', 'po_number', 'expected_delivery_date', 'status', 'notes', 'payment_terms']
    
    def get_queryset(self):
        """Filter by tenant ID"""
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            return PurchaseOrder.objects.filter(tenant_id=tenant_id)
        return PurchaseOrder.objects.none()
    
    def get_form(self, form_class=None):
        form = super().get_form(form_class)
        tenant_id = getattr(self.request, 'tenant_id', None)
        
        if tenant_id:
            # Filter supplier choices by tenant
            form.fields['supplier'].queryset = Supplier.objects.filter(tenant_id=tenant_id)
        
        return form
    
    def get_success_url(self):
        return reverse_lazy('purchases:purchase_order_detail', kwargs={'pk': self.object.pk})


class SupplierListView(LoginRequiredMixin, ListView):
    """View to list all suppliers"""
    model = Supplier
    template_name = 'purchases/supplier_list.html'
    context_object_name = 'suppliers'
    paginate_by = 20
    
    def get_queryset(self):
        """Filter by tenant ID"""
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            queryset = Supplier.objects.filter(tenant_id=tenant_id)
            
            # Apply search if provided
            search = self.request.GET.get('search')
            if search:
                queryset = queryset.filter(name__icontains=search)
                
            return queryset.order_by('name')
        return Supplier.objects.none()
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['missing_tenant'] = getattr(self.request, 'tenant_id', None) is None
        context['current_search'] = self.request.GET.get('search', '')
        return context


class SupplierDetailView(LoginRequiredMixin, DetailView):
    """View to display supplier details"""
    model = Supplier
    template_name = 'purchases/supplier_detail.html'
    context_object_name = 'supplier'
    
    def get_queryset(self):
        """Filter by tenant ID"""
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            return Supplier.objects.filter(tenant_id=tenant_id)
        return Supplier.objects.none()
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['purchase_orders'] = PurchaseOrder.objects.filter(supplier=self.object).order_by('-created_at')
        context['missing_tenant'] = getattr(self.request, 'tenant_id', None) is None
        return context


class SupplierCreateView(LoginRequiredMixin, CreateView):
    """View to create a new supplier"""
    model = Supplier
    template_name = 'purchases/supplier_form.html'
    fields = ['name', 'contact_person', 'phone', 'email', 'address', 'notes']
    
    def form_valid(self, form):
        tenant_id = getattr(self.request, 'tenant_id', None)
        if not tenant_id:
            messages.error(self.request, _("Tenant ID is missing. Please set the X-Tenant-ID header."))
            return redirect('purchases:supplier_list')
            
        # Set tenant ID on the object
        form.instance.tenant_id = tenant_id
        
        return super().form_valid(form)
    
    def get_success_url(self):
        return reverse_lazy('purchases:supplier_detail', kwargs={'pk': self.object.pk})


class SupplierUpdateView(LoginRequiredMixin, UpdateView):
    """View to update an existing supplier"""
    model = Supplier
    template_name = 'purchases/supplier_form.html'
    fields = ['name', 'contact_person', 'phone', 'email', 'address', 'notes']
    
    def get_queryset(self):
        """Filter by tenant ID"""
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            return Supplier.objects.filter(tenant_id=tenant_id)
        return Supplier.objects.none()
    
    def get_success_url(self):
        return reverse_lazy('purchases:supplier_detail', kwargs={'pk': self.object.pk})
