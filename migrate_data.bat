@echo off
echo ===============================================
echo Data Migration: SQLite to PostgreSQL
echo ===============================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python and try again.
    pause
    exit /b 1
)

REM Set environment variables for PostgreSQL connection
REM You can modify these values or set them as system environment variables
set SQL_ENGINE=django.db.backends.postgresql
set SQL_DATABASE=postgres
set SQL_USER=postgres
set SQL_PASSWORD=postgrespw
set SQL_HOST=**************
set SQL_PORT=8136

echo Current PostgreSQL Configuration:
echo Host: %SQL_HOST%
echo Port: %SQL_PORT%
echo Database: %SQL_DATABASE%
echo User: %SQL_USER%
echo.

REM Ask user for confirmation
set /p confirm=Do you want to proceed with the migration? (y/N): 
if /i "%confirm%" neq "y" (
    echo Migration cancelled.
    pause
    exit /b 0
)

echo.
echo Starting migration...
echo.

REM Run the migration script
python migrate_data.py --sqlite-path "db.sqlite3"

echo.
echo Migration script completed.
echo Check the output above for any errors.
echo.
pause 