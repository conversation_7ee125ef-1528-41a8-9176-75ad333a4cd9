from django.shortcuts import render, redirect
from django.utils.translation import gettext as _
from django.contrib import messages
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.forms import AuthenticationForm
from django.contrib.auth.decorators import login_required
from setup.models import Franchise, Company, ServiceCenter
from django.utils import translation
from django.views.generic import TemplateView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.db.models import Count, Sum, F

# Create your views here.

@login_required
def main_dashboard(request):
    """
    Main dashboard view that shows all available apps and their stats
    filtered by franchise, company or service center
    """
    context = {
        'title': _("Main Dashboard"),
    }
    
    # Get filter parameters
    franchise_id = request.GET.get('franchise_id')
    company_id = request.GET.get('company_id')
    service_center_id = request.GET.get('service_center_id')
    
    # Check if the filter by role option is enabled
    filter_by_role = request.GET.get('filter_by_role', 'true')  # Default to true
    filter_by_role = filter_by_role.lower() != 'false'  # Convert to boolean
    
    # Get the user's roles to filter entities based on access
    user = request.user
    is_superuser = user.is_superuser
    
    # Debug: Print user information
    print(f"User: {user.username}, Superuser: {is_superuser}, Filter by role: {filter_by_role}")
    
    # Get user's primary role first, then fall back to any active role if no primary
    primary_role_instance = None
    user_roles = []
    
    if hasattr(user, 'user_roles'):
        user_roles = list(user.user_roles.filter(is_active=True))
        # Debug: Print all user roles
        print(f"User roles count: {len(user_roles)}")
        for role in user_roles:
            print(f"Role: {role.role.name}, Type: {role.role.role_type}, Primary: {role.is_primary}")
            if role.franchise:
                print(f"  Franchise scope: {role.franchise.name}")
            if role.company:
                print(f"  Company scope: {role.company.name}")
            if role.service_center:
                print(f"  Service Center scope: {role.service_center.name}")
        
        # Find primary role
        for user_role in user_roles:
            if user_role.is_primary:
                primary_role_instance = user_role
                break
        
        if not primary_role_instance and user_roles:
            primary_role_instance = user_roles[0]
    
    # Add the primary role (or first active role) to context
    if primary_role_instance:
        context['primary_role'] = primary_role_instance.role
        print(f"Primary role: {primary_role_instance.role.name}")
    else:
        context['primary_role'] = None
        print("No primary role found")
    
    # Initialize filter collections
    allowed_franchises = []
    allowed_companies = []
    allowed_service_centers = []
    
    # If superuser and not filtering by role, show everything
    if is_superuser and not filter_by_role:
        context['franchises'] = Franchise.objects.filter(is_active=True)
        
        # Filter companies by franchise if franchise is selected
        if franchise_id:
            context['selected_franchise'] = Franchise.objects.filter(id=franchise_id).first()
            context['companies'] = Company.objects.filter(franchise_id=franchise_id, is_active=True)
        else:
            context['companies'] = Company.objects.filter(is_active=True)
        
        # Filter service centers by company if company is selected
        if company_id:
            context['selected_company'] = Company.objects.filter(id=company_id).first()
            context['service_centers'] = ServiceCenter.objects.filter(company_id=company_id, is_active=True)
        else:
            context['service_centers'] = ServiceCenter.objects.filter(is_active=True)
            
        print("Superuser access without role filtering: showing all entities")
    else:
        # For all users when filtering by role is enabled
        if user_roles:
            # Collect all entities user has access to from all roles
            for user_role in user_roles:
                role_type = user_role.role.role_type
                
                if role_type == 'system_admin':
                    # System admin can see everything
                    context['franchises'] = Franchise.objects.filter(is_active=True)
                    
                    if franchise_id:
                        context['selected_franchise'] = Franchise.objects.filter(id=franchise_id).first()
                        context['companies'] = Company.objects.filter(franchise_id=franchise_id, is_active=True)
                    else:
                        context['companies'] = Company.objects.filter(is_active=True)
                    
                    if company_id:
                        context['selected_company'] = Company.objects.filter(id=company_id).first()
                        context['service_centers'] = ServiceCenter.objects.filter(company_id=company_id, is_active=True)
                    else:
                        context['service_centers'] = ServiceCenter.objects.filter(is_active=True)
                    
                    print("System admin role: showing all entities")
                    # System admin has all access, no need to check other roles
                    break
                    
                elif role_type == 'franchise_admin' and user_role.franchise:
                    # Franchise admin can only see their franchise and its related entities
                    allowed_franchises.append(user_role.franchise.id)
                    print(f"Franchise admin role: adding franchise {user_role.franchise.name}")
                    
                    # Also get companies under this franchise
                    franchise_companies = Company.objects.filter(franchise=user_role.franchise, is_active=True)
                    company_ids = list(franchise_companies.values_list('id', flat=True))
                    allowed_companies.extend(company_ids)
                    print(f"  Adding {len(company_ids)} companies under franchise")
                    
                    # And service centers under those companies
                    franchise_service_centers = ServiceCenter.objects.filter(
                        company__franchise=user_role.franchise, is_active=True)
                    service_center_ids = list(franchise_service_centers.values_list('id', flat=True))
                    allowed_service_centers.extend(service_center_ids)
                    print(f"  Adding {len(service_center_ids)} service centers under franchise")
                    
                elif role_type == 'company_admin' and user_role.company:
                    # Company admin can see their company and its service centers
                    # And also needs access to the parent franchise
                    allowed_franchises.append(user_role.company.franchise.id)
                    allowed_companies.append(user_role.company.id)
                    print(f"Company admin role: adding company {user_role.company.name} and franchise {user_role.company.franchise.name}")
                    
                    # Get service centers under this company
                    company_service_centers = ServiceCenter.objects.filter(
                        company=user_role.company, is_active=True)
                    service_center_ids = list(company_service_centers.values_list('id', flat=True))
                    allowed_service_centers.extend(service_center_ids)
                    print(f"  Adding {len(service_center_ids)} service centers under company")
                    
                else:
                    # Service center roles can only see their service center
                    if user_role.service_center:
                        allowed_service_centers.append(user_role.service_center.id)
                        print(f"Service center role: adding service center {user_role.service_center.name}")
                        
                        # Also need access to parent company and franchise
                        if user_role.service_center.company:
                            allowed_companies.append(user_role.service_center.company.id)
                            print(f"  Adding parent company {user_role.service_center.company.name}")
                            
                            if user_role.service_center.company.franchise:
                                allowed_franchises.append(user_role.service_center.company.franchise.id)
                                print(f"  Adding parent franchise {user_role.service_center.company.franchise.name}")
            
            # Apply the collected filters to create querysets
            if not context.get('franchises'):
                allowed_franchises = list(set(allowed_franchises))  # Remove duplicates
                franchise_qs = Franchise.objects.filter(id__in=allowed_franchises, is_active=True)
                context['franchises'] = franchise_qs
                print(f"Final franchises queryset: {franchise_qs.count()} items")
                
            # Filter companies based on selected franchise and user's allowed companies
            if franchise_id:
                context['selected_franchise'] = Franchise.objects.filter(id=franchise_id).first()
                if context['selected_franchise']:
                    print(f"Selected franchise: {context['selected_franchise'].name}")
                    company_queryset = Company.objects.filter(franchise_id=franchise_id, is_active=True)
                    
                    # If filtering by role, further filter by allowed companies
                    if filter_by_role:
                        allowed_companies = list(set(allowed_companies))  # Remove duplicates
                        company_queryset = company_queryset.filter(id__in=allowed_companies)
                        
                    context['companies'] = company_queryset
                    print(f"Companies under selected franchise: {company_queryset.count()} items")
                else:
                    print("Selected franchise not found")
                    context['companies'] = Company.objects.none()
            else:
                allowed_companies = list(set(allowed_companies))  # Remove duplicates
                company_qs = Company.objects.filter(id__in=allowed_companies, is_active=True)
                context['companies'] = company_qs
                print(f"All allowed companies: {company_qs.count()} items")
                
            # Filter service centers based on selected company and user's allowed service centers
            if company_id:
                context['selected_company'] = Company.objects.filter(id=company_id).first()
                if context['selected_company']:
                    print(f"Selected company: {context['selected_company'].name}")
                    service_center_queryset = ServiceCenter.objects.filter(company_id=company_id, is_active=True)
                    
                    # If filtering by role, further filter by allowed service centers
                    if filter_by_role:
                        allowed_service_centers = list(set(allowed_service_centers))  # Remove duplicates
                        service_center_queryset = service_center_queryset.filter(id__in=allowed_service_centers)
                        
                    context['service_centers'] = service_center_queryset
                    print(f"Service centers under selected company: {service_center_queryset.count()} items")
                else:
                    print("Selected company not found")
                    context['service_centers'] = ServiceCenter.objects.none()
            else:
                allowed_service_centers = list(set(allowed_service_centers))  # Remove duplicates
                service_center_qs = ServiceCenter.objects.filter(id__in=allowed_service_centers, is_active=True)
                context['service_centers'] = service_center_qs
                print(f"All allowed service centers: {service_center_qs.count()} items")
                
        else:
            # User has no roles, limit what they can see
            context['franchises'] = Franchise.objects.none()
            context['companies'] = Company.objects.none()
            context['service_centers'] = ServiceCenter.objects.none()
            print("No user roles found - showing no entities")
    
    if service_center_id:
        context['selected_service_center'] = ServiceCenter.objects.filter(id=service_center_id).first()
        if context['selected_service_center']:
            print(f"Selected service center: {context['selected_service_center'].name}")
        else:
            print("Selected service center not found")
    
    # Base filters for tenant-aware models
    tenant_filters = {}
    
    # Apply filters based on selection
    if service_center_id and context.get('selected_service_center'):
        tenant_filters['tenant_id'] = context['selected_service_center'].tenant_id
    elif company_id and context.get('selected_company') and hasattr(context['selected_company'], 'tenant_id'):
        tenant_filters['tenant_id'] = context['selected_company'].tenant_id
    elif franchise_id and context.get('selected_franchise') and hasattr(context['selected_franchise'], 'tenant_id'):
        tenant_filters['tenant_id'] = context['selected_franchise'].tenant_id
    
    # Try to get inventory stats if module is available
    try:
        from inventory.models import Item, Movement
        
        # Apply tenant filters for inventory queries
        item_queryset = Item.objects.all()
        movement_queryset = Movement.objects.all()
        
        if tenant_filters:
            item_queryset = item_queryset.filter(**tenant_filters)
            movement_queryset = movement_queryset.filter(**tenant_filters)
            
        context['inventory_count'] = item_queryset.count()
        context['low_stock_count'] = item_queryset.filter(quantity__lt=F('min_stock_level')).count()
        context['movement_count'] = movement_queryset.count()
    except (ImportError, ModuleNotFoundError):
        pass
        
    # Try to get work orders stats if module is available
    try:
        from work_orders.models import WorkOrder
        
        # Apply tenant filters for work orders queries
        work_order_queryset = WorkOrder.objects.all()
        
        if tenant_filters:
            work_order_queryset = work_order_queryset.filter(**tenant_filters)
            
        # If service center is selected, further filter work orders
        if service_center_id:
            work_order_queryset = work_order_queryset.filter(service_center_id=service_center_id)
            
        context['work_orders_count'] = work_order_queryset.count()
        context['active_work_orders_count'] = work_order_queryset.filter(
            status__in=['draft', 'planned', 'in_progress', 'on_hold']
        ).count()
        context['completed_work_orders_count'] = work_order_queryset.filter(
            status='completed'
        ).count()
    except (ImportError, ModuleNotFoundError):
        pass
        
    # Try to get sales stats if module is available  
    try:
        from sales.models import SalesOrder, Customer, SalesOrderItem
        from django.utils import timezone
        import datetime
        
        # Apply tenant filters for sales queries
        sales_order_queryset = SalesOrder.objects.all()
        customer_queryset = Customer.objects.all()
        
        if tenant_filters:
            sales_order_queryset = sales_order_queryset.filter(**tenant_filters)
            customer_queryset = customer_queryset.filter(**tenant_filters)
            
        # If service center is selected, further filter sales orders
        if service_center_id:
            # Check if SalesOrder has service_center_id field before filtering
            try:
                # Get model fields
                sales_order_fields = [f.name for f in SalesOrder._meta.get_fields()]
                if 'service_center_id' in sales_order_fields or 'service_center' in sales_order_fields:
                    # If the field exists, apply the filter
                    sales_order_queryset = sales_order_queryset.filter(service_center_id=service_center_id)
            except Exception as e:
                print(f"Error checking SalesOrder fields: {e}")
        
        # Get total sales
        context['sales_count'] = sales_order_queryset.count()
        
        # Get customer count
        context['customers_count'] = customer_queryset.count()
        
        # Get monthly sales amount
        today = timezone.now()
        start_of_month = today.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        context['monthly_sales_amount'] = sales_order_queryset.filter(
            order_date__gte=start_of_month
        ).aggregate(Sum('total_amount'))['total_amount__sum'] or 0
    except (ImportError, ModuleNotFoundError):
        pass
        
    # Try to get purchases stats if module is available
    try:
        from purchases.models import PurchaseOrder, Supplier
        
        # Apply tenant filters for purchases queries
        purchase_order_queryset = PurchaseOrder.objects.all()
        supplier_queryset = Supplier.objects.all()
        
        if tenant_filters:
            purchase_order_queryset = purchase_order_queryset.filter(**tenant_filters)
            supplier_queryset = supplier_queryset.filter(**tenant_filters)
            
        # If service center is selected, further filter purchase orders
        if service_center_id:
            # Check if PurchaseOrder has service_center_id field before filtering
            try:
                # Get model fields
                purchase_order_fields = [f.name for f in PurchaseOrder._meta.get_fields()]
                if 'service_center_id' in purchase_order_fields or 'service_center' in purchase_order_fields:
                    # If the field exists, apply the filter
                    purchase_order_queryset = purchase_order_queryset.filter(service_center_id=service_center_id)
            except Exception as e:
                print(f"Error checking PurchaseOrder fields: {e}")
        
        # Get total purchases
        context['purchases_count'] = purchase_order_queryset.count()
        
        # Get supplier count
        context['suppliers_count'] = supplier_queryset.count()
        
        # Get pending purchase orders
        context['pending_po_count'] = purchase_order_queryset.filter(
            status__in=['draft', 'sent', 'confirmed']
        ).count()
    except (ImportError, ModuleNotFoundError):
        pass
    
    # Try to get warehouse stats if module is available
    try:
        from warehouse.models import Warehouse, Location
        
        # Apply tenant filters for warehouse queries
        warehouse_queryset = Warehouse.objects.all()
        location_queryset = Location.objects.all()
        
        if tenant_filters:
            warehouse_queryset = warehouse_queryset.filter(**tenant_filters)
            location_queryset = location_queryset.filter(**tenant_filters)
            
        # If service center is selected, further filter warehouses related to that service center
        if service_center_id:
            service_center = context['selected_service_center']
            # Filter by primary warehouse and secondary warehouses
            primary_ids = [service_center.primary_warehouse_id] if service_center.primary_warehouse_id else []
            secondary_ids = service_center.secondary_warehouses.all().values_list('id', flat=True)
            all_warehouse_ids = list(primary_ids) + list(secondary_ids)
            
            warehouse_queryset = warehouse_queryset.filter(id__in=all_warehouse_ids)
            location_queryset = location_queryset.filter(warehouse__in=all_warehouse_ids)
        
        # Get warehouse and location counts
        context['warehouses_count'] = warehouse_queryset.count()
        context['locations_count'] = location_queryset.count()
        
        # Simple warehouse utilization calculation
        total_capacity = location_queryset.aggregate(Sum('capacity'))['capacity__sum'] or 1
        used_capacity = location_queryset.aggregate(Sum('used_capacity'))['used_capacity__sum'] or 0
        context['warehouse_utilization'] = int((used_capacity / total_capacity) * 100)
    except (ImportError, ModuleNotFoundError, Exception):
        pass
    
    # Try to get reports stats if module is available
    try:
        from reports.models import Report, Dashboard, DashboardWidget
        
        # Reports are not filtered by tenant as they're typically global
        context['reports_count'] = Report.objects.count()
        context['dashboards_count'] = Dashboard.objects.count()
        context['widgets_count'] = DashboardWidget.objects.count()
    except (ImportError, ModuleNotFoundError):
        pass
    
    # Try to get setup stats if module is available
    try:
        # Apply filters for setup stats
        franchise_queryset = Franchise.objects.all()
        company_queryset = Company.objects.all()
        service_center_queryset = ServiceCenter.objects.all()
        
        if franchise_id:
            company_queryset = company_queryset.filter(franchise_id=franchise_id)
            service_center_queryset = service_center_queryset.filter(company__franchise_id=franchise_id)
        
        if company_id:
            service_center_queryset = service_center_queryset.filter(company_id=company_id)
        
        context['franchises_count'] = franchise_queryset.count()
        context['companies_count'] = company_queryset.count()
        context['service_centers_count'] = service_center_queryset.count()
    except (ImportError, ModuleNotFoundError):
        pass
        
    # Collect system alerts
    alerts = []
    
    # Add inventory alerts if available
    try:
        from inventory.models import Item
        
        # Apply tenant filters for inventory queries
        item_queryset = Item.objects.all()
        
        if tenant_filters:
            item_queryset = item_queryset.filter(**tenant_filters)
            
        low_stock_items = item_queryset.filter(quantity__lt=F('min_stock_level'))
        if low_stock_items.exists():
            alerts.append({
                'id': '1',
                'level': 'warning',
                'title': _('Low Stock Alert'),
                'message': _('There are {} items below minimum stock level').format(low_stock_items.count()),
                'timestamp': low_stock_items.order_by('-updated_at').first().updated_at
            })
    except (ImportError, ModuleNotFoundError, Exception):
        pass
    
    # Add work order alerts if available
    try:
        from work_orders.models import WorkOrder
        
        # Apply tenant filters for work orders queries
        work_order_queryset = WorkOrder.objects.all()
        
        if tenant_filters:
            work_order_queryset = work_order_queryset.filter(**tenant_filters)
            
        # If service center is selected, further filter work orders
        if service_center_id:
            work_order_queryset = work_order_queryset.filter(service_center_id=service_center_id)
            
        overdue_work_orders = work_order_queryset.filter(
            status__in=['in_progress', 'planned'],
            planned_end_date__lt=timezone.now()
        )
        if overdue_work_orders.exists():
            alerts.append({
                'id': '2',
                'level': 'danger',
                'title': _('Overdue Work Orders'),
                'message': _('There are {} overdue work orders').format(overdue_work_orders.count()),
                'timestamp': timezone.now()
            })
    except (ImportError, ModuleNotFoundError, Exception):
        pass
    
    # Add purchase order alerts if available
    try:
        from purchases.models import PurchaseOrder
        from django.utils import timezone
        import datetime
        
        # Apply tenant filters for purchases queries
        purchase_order_queryset = PurchaseOrder.objects.all()
        
        if tenant_filters:
            purchase_order_queryset = purchase_order_queryset.filter(**tenant_filters)
            
        # If service center is selected, further filter purchase orders
        if service_center_id:
            purchase_order_queryset = purchase_order_queryset.filter(service_center_id=service_center_id)
        
        late_deliveries = purchase_order_queryset.filter(
            status='confirmed',
            expected_delivery_date__lt=timezone.now().date()
        )
        
        if late_deliveries.exists():
            alerts.append({
                'id': '3',
                'level': 'warning',
                'title': _('Late Deliveries'),
                'message': _('There are {} purchase orders with late deliveries').format(late_deliveries.count()),
                'timestamp': timezone.now()
            })
    except (ImportError, ModuleNotFoundError, Exception):
        pass
        
    context['alerts'] = alerts
    
    # Collect recent activities
    recent_activities = []
    
    # Add inventory movements as activities
    try:
        from inventory.models import Movement
        
        # Apply tenant filters for inventory movement queries
        movement_queryset = Movement.objects.all()
        
        if tenant_filters:
            movement_queryset = movement_queryset.filter(**tenant_filters)
            
        recent_movements = movement_queryset.order_by('-created_at')[:5]
        
        for movement in recent_movements:
            recent_activities.append({
                'id': f'inv_{movement.id}',
                'type': 'inventory',
                'description': _('Inventory movement: {} {} of {}').format(
                    movement.get_movement_name(),
                    movement.quantity,
                    movement.item.name
                ),
                'timestamp': movement.created_at
            })
    except (ImportError, ModuleNotFoundError, Exception):
        pass
    
    # Add work orders as activities
    try:
        from work_orders.models import WorkOrder
        
        # Apply tenant filters for work orders queries
        work_order_queryset = WorkOrder.objects.all()
        
        if tenant_filters:
            work_order_queryset = work_order_queryset.filter(**tenant_filters)
            
        # If service center is selected, further filter work orders
        if service_center_id:
            work_order_queryset = work_order_queryset.filter(service_center_id=service_center_id)
            
        recent_work_orders = work_order_queryset.order_by('-created_at')[:5]
        
        for work_order in recent_work_orders:
            recent_activities.append({
                'id': f'wo_{work_order.id}',
                'type': 'work_order',
                'description': _('Work order {} ({}) created').format(
                    work_order.work_order_number,
                    work_order.get_status_display()
                ),
                'timestamp': work_order.created_at
            })
    except (ImportError, ModuleNotFoundError, Exception):
        pass
    
    # Add sales as activities
    try:
        from sales.models import SalesOrder
        
        # Apply tenant filters for sales queries
        sales_order_queryset = SalesOrder.objects.all()
        
        if tenant_filters:
            sales_order_queryset = sales_order_queryset.filter(**tenant_filters)
            
        # If service center is selected, further filter sales orders
        if service_center_id:
            # Check if SalesOrder has service_center_id field before filtering
            try:
                # Get model fields
                sales_order_fields = [f.name for f in SalesOrder._meta.get_fields()]
                if 'service_center_id' in sales_order_fields or 'service_center' in sales_order_fields:
                    # If the field exists, apply the filter
                    sales_order_queryset = sales_order_queryset.filter(service_center_id=service_center_id)
            except Exception as e:
                print(f"Error checking SalesOrder fields: {e}")
        
        recent_sales = sales_order_queryset.order_by('-created_at')[:5]
        
        for sale in recent_sales:
            recent_activities.append({
                'id': f'sale_{sale.id}',
                'type': 'sale',
                'description': _('Sale {} to {}').format(
                    sale.order_number,
                    sale.customer.name
                ),
                'timestamp': sale.created_at
            })
    except (ImportError, ModuleNotFoundError, Exception):
        pass
    
    # Add purchases as activities
    try:
        from purchases.models import PurchaseOrder
        
        # Apply tenant filters for purchases queries
        purchase_order_queryset = PurchaseOrder.objects.all()
        
        if tenant_filters:
            purchase_order_queryset = purchase_order_queryset.filter(**tenant_filters)
            
        # If service center is selected, further filter purchase orders
        if service_center_id:
            # Check if PurchaseOrder has service_center_id field before filtering
            try:
                # Get model fields
                purchase_order_fields = [f.name for f in PurchaseOrder._meta.get_fields()]
                if 'service_center_id' in purchase_order_fields or 'service_center' in purchase_order_fields:
                    # If the field exists, apply the filter
                    purchase_order_queryset = purchase_order_queryset.filter(service_center_id=service_center_id)
            except Exception as e:
                print(f"Error checking PurchaseOrder fields: {e}")
        
        recent_purchases = purchase_order_queryset.order_by('-created_at')[:5]
        
        for purchase in recent_purchases:
            recent_activities.append({
                'id': f'purchase_{purchase.id}',
                'type': 'purchase',
                'description': _('Purchase {} from {}').format(
                    purchase.order_number,
                    purchase.supplier.name
                ),
                'timestamp': purchase.created_at
            })
    except (ImportError, ModuleNotFoundError, Exception):
        pass
    
    # Sort activities by timestamp
    recent_activities.sort(key=lambda x: x['timestamp'], reverse=True)
    context['recent_activities'] = recent_activities[:10]  # Limit to 10 most recent
    
    return render(request, 'core/main_dashboard.html', context)

def language_demo(request):
    """
    Demo view to showcase language switching and RTL/LTR support using a single template.
    """
    messages.success(request, _("Language switched successfully!"))
    
    context = {
        'title': _("Language Demo"),
        'greeting': _("Welcome to the After-Sales Franchise Management System"),
        'description': _("This page demonstrates language switching and RTL/LTR support via Tailwind CSS."),
        'features': [
            _("Multi-tenant architecture"),
            _("Role-based access control"),
            _("Feature flags for module toggling"),
            _("Comprehensive reporting system"),
            _("Document management"),
            _("Internationalization support (LTR/RTL)"),
        ]
    }
    
    return render(request, 'core/language_demo.html', context)

def logout_view(request):
    """
    Custom logout view that redirects to the login page
    """
    logout(request)
    messages.success(request, _("You have been successfully logged out."))
    return redirect('core:login')

@login_required
def post_login_redirect(request):
    """
    Checks if setup data exists and redirects accordingly:
    - If no setup data exists, redirect to the setup page
    - If setup data exists, redirect to the dashboard
    """
    # Force Arabic language
    translation.activate('ar')
    request.LANGUAGE_CODE = 'ar'
    
    # Check if there's any setup data
    has_franchises = Franchise.objects.exists()
    has_companies = Company.objects.exists()
    has_service_centers = ServiceCenter.objects.exists()
    
    if not (has_franchises or has_companies or has_service_centers):
        # No setup data - redirect to setup dashboard
        messages.info(request, _("Please complete the initial setup"))
        return redirect('setup:dashboard')
    else:
        # Setup data exists - redirect to main dashboard
        return redirect('core:main_dashboard')

def login_view(request):
    """
    Custom login view that uses the base template with Tailwind CSS
    """
    # Force Arabic language for login page
    translation.activate('ar')
    request.LANGUAGE_CODE = 'ar'
    
    # Check if user is already authenticated
    if request.user.is_authenticated:
        return redirect('core:post_login_redirect')
        
    if request.method == 'POST':
        form = AuthenticationForm(request, data=request.POST)
        if form.is_valid():
            username = form.cleaned_data.get('username')
            password = form.cleaned_data.get('password')
            user = authenticate(username=username, password=password)
            if user is not None:
                login(request, user)
                messages.success(request, _("You have been logged in successfully!"))
                
                # Check for a 'next' parameter to redirect after login
                next_url = request.POST.get('next', None)
                if next_url:
                    return redirect(next_url)
                    
                return redirect('core:post_login_redirect')  # Redirect to our new view
            else:
                messages.error(request, _("Invalid username or password."))
        else:
            messages.error(request, _("Invalid username or password."))
    else:
        form = AuthenticationForm()
    
    # Add custom attributes to form fields for RTL support
    if form.fields.get('username'):
        form.fields['username'].widget.attrs.update({
            'class': 'rtl-input', 
            'dir': 'rtl',
            'style': 'text-align: right; direction: rtl;'
        })
    if form.fields.get('password'):
        form.fields['password'].widget.attrs.update({
            'class': 'rtl-input',
            'dir': 'rtl',
            'style': 'text-align: right; direction: rtl;'
        })
    
    # Check if this is a modal request
    is_modal = request.GET.get('modal', False)
    
    context = {
        'title': _("Login"),
        'form': form,
        'is_modal': is_modal
    }
    
    if is_modal:
        return render(request, 'core/login_modal.html', context)
    else:
        return render(request, 'core/login.html', context)
