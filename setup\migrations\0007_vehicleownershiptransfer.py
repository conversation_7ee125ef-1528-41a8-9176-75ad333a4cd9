# Generated by Django 4.2.20 on 2025-05-08 16:17

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('setup', '0006_servicecenter_serves_all_vehicle_makes_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='VehicleOwnershipTransfer',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('transfer_date', models.DateField(default=django.utils.timezone.now, verbose_name='Transfer Date')),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('approved', 'Approved'), ('completed', 'Completed'), ('rejected', 'Rejected'), ('cancelled', 'Cancelled')], default='pending', max_length=20, verbose_name='Status')),
                ('sale_price', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True, verbose_name='Sale Price')),
                ('odometer_reading', models.PositiveIntegerField(blank=True, null=True, verbose_name='Odometer Reading')),
                ('documents', models.JSONField(blank=True, default=list, help_text='List of document references related to this transfer', verbose_name='Transfer Documents')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('approved_date', models.DateTimeField(blank=True, null=True, verbose_name='Approval Date')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_transfers', to=settings.AUTH_USER_MODEL, verbose_name='Approved By')),
                ('new_owner', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='vehicle_transfers_in', to='setup.customer', verbose_name='New Owner')),
                ('previous_owner', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='vehicle_transfers_out', to='setup.customer', verbose_name='Previous Owner')),
                ('vehicle', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ownership_transfers', to='setup.vehicle', verbose_name='Vehicle')),
            ],
            options={
                'verbose_name': 'Vehicle Ownership Transfer',
                'verbose_name_plural': 'Vehicle Ownership Transfers',
                'ordering': ['-transfer_date', '-created_at'],
            },
        ),
    ]
