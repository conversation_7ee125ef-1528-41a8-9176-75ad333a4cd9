from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.utils.translation import gettext_lazy as _
from django.contrib import messages
from django.http import JsonResponse
from django.core.paginator import Paginator
from django.db.models import Sum, Avg, Count
from django.views.decorators.http import require_POST
from .models import (
    FranchiseTemplate, 
    FranchiseAgreement, 
    FranchiseFee, 
    RevenueShare,
    FranchiseRequirement,
    FranchiseCompliance
)
from setup.models import Franchise
from user_roles.middleware import role_required


@login_required
@role_required('can_access_setup')
def franchise_dashboard(request):
    """
    Dashboard view showing franchise metrics and compliance summary
    """
    # Get counts
    agreement_count = FranchiseAgreement.objects.count()
    active_agreement_count = FranchiseAgreement.objects.filter(status='active').count()
    franchise_count = Franchise.objects.count()
    
    # Get compliance stats
    compliance_stats = {
        'compliant': FranchiseCompliance.objects.filter(status='compliant').count(),
        'non_compliant': FranchiseCompliance.objects.filter(status='non_compliant').count(),
        'pending': FranchiseCompliance.objects.filter(status='pending').count(),
        'total': FranchiseCompliance.objects.count()
    }
    
    # Get revenue data for chart
    revenue_data = RevenueShare.objects.values('year', 'quarter').annotate(
        total=Sum('total_revenue'),
        royalty=Sum('royalty_amount')
    ).order_by('year', 'quarter')[:8]
    
    # Get upcoming renewals
    upcoming_renewals = FranchiseAgreement.objects.filter(
        status__in=['active', 'renewal']
    ).order_by('end_date')[:5]
    
    context = {
        'agreement_count': agreement_count,
        'active_agreement_count': active_agreement_count,
        'franchise_count': franchise_count,
        'compliance_stats': compliance_stats,
        'revenue_data': list(revenue_data),
        'upcoming_renewals': upcoming_renewals,
        'page_title': _("Franchise Management Dashboard")
    }
    
    return render(request, 'franchise_setup/dashboard.html', context)


@login_required
@role_required('can_access_setup')
def agreement_list(request):
    """
    List view for franchise agreements
    """
    agreements = FranchiseAgreement.objects.all().select_related('franchise')
    
    # Filter by status if provided
    status_filter = request.GET.get('status')
    if status_filter:
        agreements = agreements.filter(status=status_filter)
    
    # Filter by franchise if provided
    franchise_filter = request.GET.get('franchise')
    if franchise_filter:
        agreements = agreements.filter(franchise_id=franchise_filter)
    
    # Pagination
    paginator = Paginator(agreements.order_by('-start_date'), 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'statuses': dict(FranchiseAgreement._meta.get_field('status').choices),
        'franchises': Franchise.objects.all(),
        'page_title': _("Franchise Agreements")
    }
    
    return render(request, 'franchise_setup/agreement_list.html', context)


@login_required
@role_required('can_access_setup')
def agreement_detail(request, pk):
    """
    Detail view for a franchise agreement
    """
    agreement = get_object_or_404(FranchiseAgreement.objects.select_related(
        'franchise', 'template'
    ), pk=pk)
    
    # Get fees associated with this agreement
    fees = agreement.fees.all()
    
    # Get revenue shares for this franchise
    revenue_shares = RevenueShare.objects.filter(
        franchise=agreement.franchise
    ).order_by('-year', '-quarter')[:8]
    
    # Get compliance records for this franchise
    compliance_records = FranchiseCompliance.objects.filter(
        franchise=agreement.franchise
    ).select_related('requirement').order_by('-verification_date')[:10]
    
    context = {
        'agreement': agreement,
        'fees': fees,
        'revenue_shares': revenue_shares,
        'compliance_records': compliance_records,
        'page_title': _("Agreement: ") + agreement.name
    }
    
    return render(request, 'franchise_setup/agreement_detail.html', context)


@login_required
@role_required('can_access_setup')
def compliance_list(request):
    """
    List view for franchise compliance records
    """
    compliance_records = FranchiseCompliance.objects.all().select_related(
        'franchise', 'requirement'
    )
    
    # Filter by status if provided
    status_filter = request.GET.get('status')
    if status_filter:
        compliance_records = compliance_records.filter(status=status_filter)
    
    # Filter by franchise if provided
    franchise_filter = request.GET.get('franchise')
    if franchise_filter:
        compliance_records = compliance_records.filter(franchise_id=franchise_filter)
    
    # Filter by requirement type if provided
    requirement_type = request.GET.get('requirement_type')
    if requirement_type:
        compliance_records = compliance_records.filter(requirement__requirement_type=requirement_type)
    
    # Pagination
    paginator = Paginator(compliance_records.order_by('-verification_date'), 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'statuses': dict(FranchiseCompliance._meta.get_field('status').choices),
        'requirement_types': dict(FranchiseRequirement._meta.get_field('requirement_type').choices),
        'franchises': Franchise.objects.all(),
        'page_title': _("Franchise Compliance")
    }
    
    return render(request, 'franchise_setup/compliance_list.html', context)


@login_required
@role_required('can_access_setup')
def revenue_share_list(request):
    """
    List view for revenue shares
    """
    revenue_shares = RevenueShare.objects.all().select_related('franchise')
    
    # Filter by year if provided
    year_filter = request.GET.get('year')
    if year_filter:
        revenue_shares = revenue_shares.filter(year=year_filter)
    
    # Filter by quarter if provided
    quarter_filter = request.GET.get('quarter')
    if quarter_filter:
        revenue_shares = revenue_shares.filter(quarter=quarter_filter)
    
    # Filter by franchise if provided
    franchise_filter = request.GET.get('franchise')
    if franchise_filter:
        revenue_shares = revenue_shares.filter(franchise_id=franchise_filter)
    
    # Filter by verification status
    verification_filter = request.GET.get('is_verified')
    if verification_filter:
        is_verified = verification_filter == 'true'
        revenue_shares = revenue_shares.filter(is_verified=is_verified)
    
    # Pagination
    paginator = Paginator(revenue_shares.order_by('-year', '-quarter'), 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Get available years for filter
    years = RevenueShare.objects.values_list('year', flat=True).distinct().order_by('-year')
    
    context = {
        'page_obj': page_obj,
        'years': years,
        'quarters': dict(RevenueShare._meta.get_field('quarter').choices),
        'franchises': Franchise.objects.all(),
        'page_title': _("Revenue Shares")
    }
    
    return render(request, 'franchise_setup/revenue_share_list.html', context)


@login_required
@role_required('can_access_setup')
def template_list(request):
    """
    List view for franchise templates
    """
    templates = FranchiseTemplate.objects.all()
    
    # Filter by active status if provided
    active_filter = request.GET.get('is_active')
    if active_filter:
        is_active = active_filter == 'true'
        templates = templates.filter(is_active=is_active)
    
    # Pagination
    paginator = Paginator(templates.order_by('name'), 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'page_title': _("Franchise Templates")
    }
    
    return render(request, 'franchise_setup/template_list.html', context)


@login_required
@role_required('can_access_setup')
def template_detail(request, pk):
    """
    Detail view for a franchise template
    """
    template = get_object_or_404(FranchiseTemplate, pk=pk)
    
    # Get requirements associated with this template
    requirements = template.requirements.all().order_by('requirement_type', 'name')
    
    # Get agreements using this template
    agreements = template.agreements.all().select_related('franchise')[:10]
    
    context = {
        'template': template,
        'requirements': requirements,
        'agreements': agreements,
        'page_title': _("Template: ") + template.name
    }
    
    return render(request, 'franchise_setup/template_detail.html', context)


@login_required
@role_required('can_access_setup')
@require_POST
def update_compliance_status(request, pk):
    """
    AJAX endpoint to update compliance status
    """
    compliance = get_object_or_404(FranchiseCompliance, pk=pk)
    status = request.POST.get('status')
    
    if status and status in dict(FranchiseCompliance._meta.get_field('status').choices):
        compliance.status = status
        compliance.verified_by = request.user
        compliance.save()
        return JsonResponse({
            'success': True,
            'status': compliance.get_status_display()
        })
    else:
        return JsonResponse({
            'success': False,
            'error': _("Invalid status provided")
        }, status=400)
