{% extends "dashboard_base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "المبيعات" %}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="mb-6">
        <h1 class="text-2xl font-bold text-gray-800">{% trans "لوحة تحكم المبيعات" %}</h1>
        <p class="text-gray-600">{% trans "إدارة المبيعات والعملاء" %}</p>
    </div>
    
    {% if missing_tenant %}
        <div class="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-6" role="alert">
            <p class="font-bold">{% trans "Tenant ID Missing" %}</p>
            <p>{% trans "No tenant ID was found. Please make sure your X-Tenant-ID header is set correctly." %}</p>
        </div>
    {% endif %}
    
    <!-- Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Total Sales Orders -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100 text-green-500">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <div class="{% if LANGUAGE_CODE == 'ar' %}mr-4{% else %}ml-4{% endif %}">
                    <p class="text-gray-500 text-sm">{% trans "إجمالي الطلبات" %}</p>
                    <p class="text-2xl font-bold text-gray-800">{{ total_orders|default:"0" }}</p>
                </div>
            </div>
        </div>
        
        <!-- Pending Orders -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-yellow-100 text-yellow-500">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="{% if LANGUAGE_CODE == 'ar' %}mr-4{% else %}ml-4{% endif %}">
                    <p class="text-gray-500 text-sm">{% trans "طلبات قيد التنفيذ" %}</p>
                    <p class="text-2xl font-bold text-gray-800">{{ pending_orders|default:"0" }}</p>
                </div>
            </div>
        </div>
        
        <!-- Completed Orders -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100 text-green-500">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="{% if LANGUAGE_CODE == 'ar' %}mr-4{% else %}ml-4{% endif %}">
                    <p class="text-gray-500 text-sm">{% trans "طلبات مكتملة" %}</p>
                    <p class="text-2xl font-bold text-gray-800">{{ completed_orders|default:"0" }}</p>
                </div>
            </div>
        </div>
        
        <!-- Customers Count -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100 text-blue-500">
                    <i class="fas fa-users"></i>
                </div>
                <div class="{% if LANGUAGE_CODE == 'ar' %}mr-4{% else %}ml-4{% endif %}">
                    <p class="text-gray-500 text-sm">{% trans "عدد العملاء" %}</p>
                    <p class="text-2xl font-bold text-gray-800">{{ customers_count|default:"0" }}</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="bg-white shadow rounded-lg mb-8">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-800">{% trans "إجراءات سريعة" %}</h2>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <a href="{% url 'sales:sales_order_create' %}" class="bg-green-100 hover:bg-green-200 text-green-700 py-3 px-4 rounded-lg text-center transition duration-300">
                    <i class="fas fa-plus-circle mb-2 text-2xl"></i>
                    <p>{% trans "طلب مبيعات جديد" %}</p>
                </a>
                <a href="{% url 'sales:customer_create' %}" class="bg-blue-100 hover:bg-blue-200 text-blue-700 py-3 px-4 rounded-lg text-center transition duration-300">
                    <i class="fas fa-user-plus mb-2 text-2xl"></i>
                    <p>{% trans "عميل جديد" %}</p>
                </a>
                <a href="{% url 'sales:sales_order_list' %}" class="bg-gray-100 hover:bg-gray-200 text-gray-700 py-3 px-4 rounded-lg text-center transition duration-300">
                    <i class="fas fa-list mb-2 text-2xl"></i>
                    <p>{% trans "جميع طلبات المبيعات" %}</p>
                </a>
            </div>
        </div>
    </div>
    
    <!-- Sales Performance -->
    <div class="bg-white shadow rounded-lg mb-8">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-800">{% trans "أداء المبيعات" %}</h2>
        </div>
        <div class="p-6">
            <div class="flex flex-col md:flex-row justify-between items-center mb-4">
                <div class="w-full md:w-1/2 mb-6 md:mb-0">
                    <h3 class="text-lg font-medium text-gray-800 mb-2">{% trans "إجمالي المبيعات" %}</h3>
                    <p class="text-3xl font-bold text-green-600">{{ total_sales_amount|default:"0" }} {% trans "ر.س" %}</p>
                </div>
                <div class="w-full md:w-1/2 flex justify-center">
                    <!-- Placeholder for chart -->
                    <div class="w-full h-40 bg-gray-100 rounded-lg flex items-center justify-center">
                        <p class="text-gray-500">{% trans "رسم بياني" %}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Recent Sales Orders -->
    <div class="bg-white shadow rounded-lg mb-8">
        <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h2 class="text-lg font-semibold text-gray-800">{% trans "أحدث طلبات المبيعات" %}</h2>
            <a href="{% url 'sales:sales_order_list' %}" class="text-green-600 hover:text-green-800 text-sm">
                {% trans "عرض الكل" %} <i class="fas fa-arrow-circle-right {% if LANGUAGE_CODE == 'ar' %}mr-1{% else %}ml-1{% endif %}"></i>
            </a>
        </div>
        {% if recent_orders %}
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "رقم الطلب" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "العميل" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "التاريخ" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "الحالة" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "المبلغ" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "الإجراءات" %}
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for order in recent_orders %}
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">{{ order.order_number }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ order.customer.name }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">{{ order.order_date|date:"d/m/Y" }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    {% if order.status == 'draft' %}
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                                            {% trans "مسودة" %}
                                        </span>
                                    {% elif order.status == 'confirmed' %}
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                                            {% trans "مؤكد" %}
                                        </span>
                                    {% elif order.status == 'shipped' %}
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                            {% trans "تم الشحن" %}
                                        </span>
                                    {% elif order.status == 'delivered' %}
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                            {% trans "تم التسليم" %}
                                        </span>
                                    {% elif order.status == 'cancelled' %}
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                            {% trans "ملغي" %}
                                        </span>
                                    {% elif order.status == 'returned' %}
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-orange-100 text-orange-800">
                                            {% trans "مرتجع" %}
                                        </span>
                                    {% else %}
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                                            {{ order.get_status_display }}
                                        </span>
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">{{ order.total_amount }} {% trans "ر.س" %}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <a href="{% url 'sales:sales_order_detail' order.id %}" class="text-indigo-600 hover:text-indigo-900 mr-3">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'sales:sales_order_update' order.id %}" class="text-green-600 hover:text-green-900">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="p-6 text-center text-gray-500">
                {% trans "لا توجد طلبات مبيعات حديثة" %}
            </div>
        {% endif %}
    </div>
</div>
{% endblock %} 