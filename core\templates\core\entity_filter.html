{% load i18n %}

<div class="bg-white rounded-lg shadow mb-6">
    <form method="get" action="{{ action_url }}" class="p-4 filter-form" id="entity-filter-form">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div>
                <label for="franchise_id" class="block text-sm font-medium text-gray-700 mb-1">
                    <i class="fas fa-building text-blue-500 ml-1"></i> {% trans "المؤسسة" %} 
                    <small class="text-xs text-gray-500">({{ franchises|length }})</small>
                </label>
                <div class="relative">
                    <select id="franchise_id" name="franchise_id" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 {% if LANGUAGE_CODE == 'ar' %}rtl-select{% endif %} {% if selected_franchise %}filter-active{% endif %}" dir="{{ dir|default:"rtl" }}" onchange="this.form.submit()">
                        <option value="">{% trans "جميع المؤسسات" %}</option>
                        {% for franchise in franchises %}
                        <option value="{{ franchise.id }}" {% if selected_franchise.id == franchise.id %}selected{% endif %}>{{ franchise.name }}</option>
                        {% endfor %}
                    </select>
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none chevron-icon">
                        <i class="fas fa-chevron-down text-gray-400"></i>
                    </div>
                </div>
            </div>
            
            <div>
                <label for="company_id" class="block text-sm font-medium text-gray-700 mb-1">
                    <i class="fas fa-briefcase text-blue-500 ml-1"></i> {% trans "الشركة" %} 
                    <small class="text-xs text-gray-500">
                        {% if selected_franchise %}
                        ({{ companies|length }} {% trans "في" %} {{ selected_franchise.name }})
                        {% else %}
                        ({{ companies|length }})
                        {% endif %}
                    </small>
                </label>
                <div class="relative">
                    <select id="company_id" name="company_id" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 {% if LANGUAGE_CODE == 'ar' %}rtl-select{% endif %} {% if selected_company %}filter-active{% endif %}" dir="{{ dir|default:"rtl" }}" onchange="this.form.submit()">
                        <option value="">
                            {% if selected_franchise %}
                            {% trans "جميع الشركات في" %} {{ selected_franchise.name }}
                            {% else %}
                            {% trans "جميع الشركات" %}
                            {% endif %}
                        </option>
                        {% for company in companies %}
                        <option value="{{ company.id }}" {% if selected_company.id == company.id %}selected{% endif %} data-franchise-id="{{ company.franchise_id|default:'' }}">{{ company.name }}</option>
                        {% endfor %}
                    </select>
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none chevron-icon">
                        <i class="fas fa-chevron-down text-gray-400"></i>
                    </div>
                </div>
            </div>
            
            <div>
                <label for="service_center_id" class="block text-sm font-medium text-gray-700 mb-1">
                    <i class="fas fa-store text-blue-500 ml-1"></i> {% trans "مركز الخدمة" %} 
                    <small class="text-xs text-gray-500">
                        {% if selected_company %}
                        ({{ service_centers|length }} {% trans "في" %} {{ selected_company.name }})
                        {% elif selected_franchise %}
                        ({{ service_centers|length }} {% trans "في" %} {{ selected_franchise.name }})
                        {% else %}
                        ({{ service_centers|length }})
                        {% endif %}
                    </small>
                </label>
                <div class="relative">
                    <select id="service_center_id" name="service_center_id" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 {% if LANGUAGE_CODE == 'ar' %}rtl-select{% endif %} {% if selected_service_center %}filter-active{% endif %}" dir="{{ dir|default:"rtl" }}" onchange="this.form.submit()">
                        <option value="">
                            {% if selected_company %}
                            {% trans "جميع مراكز الخدمة في" %} {{ selected_company.name }}
                            {% elif selected_franchise %}
                            {% trans "جميع مراكز الخدمة في" %} {{ selected_franchise.name }}
                            {% else %}
                            {% trans "جميع مراكز الخدمة" %}
                            {% endif %}
                        </option>
                        {% for service_center in service_centers %}
                        <option value="{{ service_center.id }}" {% if selected_service_center.id == service_center.id %}selected{% endif %} data-company-id="{{ service_center.company_id|default:'' }}">{{ service_center.name }}</option>
                        {% endfor %}
                    </select>
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none chevron-icon">
                        <i class="fas fa-chevron-down text-gray-400"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="flex justify-end">
            <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition duration-200 flex items-center">
                <i class="fas fa-filter ml-2"></i> {% trans "تطبيق الفلتر" %}
            </button>
        </div>
        
        {% if preserve_params %}
            {% for key, value in request.GET.items %}
                {% if key != 'franchise_id' and key != 'company_id' and key != 'service_center_id' and key != 'page' %}
                    <input type="hidden" name="{{ key }}" value="{{ value }}">
                {% endif %}
            {% endfor %}
        {% endif %}
    </form>
</div>

<script>
    // Ensure filtering consistency across all pages
    document.addEventListener('DOMContentLoaded', function() {
        const franchiseSelect = document.getElementById('franchise_id');
        const companySelect = document.getElementById('company_id');
        const serviceCenterSelect = document.getElementById('service_center_id');
        
        // Function to ensure company options are filtered by franchise
        function updateCompanyOptions() {
            if (!franchiseSelect || !companySelect) return;
            
            const franchiseId = franchiseSelect.value;
            
            // If no franchise is selected, we've already loaded the user-scoped companies
            if (!franchiseId) return;
            
            // This should be handled by the server-side filtering,
            // but we'll add a reload here to ensure consistency
            if (companySelect.options.length > 1) {
                // The company options should be filtered by franchise
                // If they're not, reload the page
                const companyOptions = Array.from(companySelect.options).slice(1); // Skip the first "All" option
                const franchiseCompanies = companyOptions.filter(opt => 
                    opt.dataset.franchiseId === franchiseId
                );
                
                // If the filtered companies don't match what's shown, reload
                if (franchiseCompanies.length !== companyOptions.length) {
                    // Change should trigger reload through onchange event
                    franchiseSelect.dispatchEvent(new Event('change'));
                }
            }
        }
        
        // Function to ensure service center options are filtered by company
        function updateServiceCenterOptions() {
            if (!companySelect || !serviceCenterSelect) return;
            
            const companyId = companySelect.value;
            
            // If no company is selected, we rely on franchise filtering
            if (!companyId) return;
            
            // This should be handled by the server-side filtering,
            // but we'll add a reload here to ensure consistency
            if (serviceCenterSelect.options.length > 1) {
                // The service center options should be filtered by company
                // If they're not, reload the page
                const serviceCenterOptions = Array.from(serviceCenterSelect.options).slice(1); // Skip the first "All" option
                const companyServiceCenters = serviceCenterOptions.filter(opt => 
                    opt.dataset.companyId === companyId
                );
                
                // If the filtered service centers don't match what's shown, reload
                if (companyServiceCenters.length !== serviceCenterOptions.length) {
                    // Change should trigger reload through onchange event
                    companySelect.dispatchEvent(new Event('change'));
                }
            }
        }
        
        // Check if we need to force a reload to apply proper filtering
        window.addEventListener('load', function() {
            // We'll run a small check to ensure filtering is working
            setTimeout(function() {
                updateCompanyOptions();
                updateServiceCenterOptions();
            }, 200);
        });
    });
</script>

{% if selected_franchise or selected_company or selected_service_center %}
<div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
    <div class="flex items-center">
        <i class="fas fa-filter text-blue-500 mr-2"></i>
        <div>
            <h3 class="font-medium text-blue-800">{% trans "التصفية الحالية" %}:</h3>
            <p class="text-blue-600">
                {% if selected_franchise %}
                <span class="inline-block bg-blue-100 text-blue-800 rounded-full px-3 py-1 text-sm font-semibold mr-2 mb-2">
                    <i class="fas fa-building mr-1"></i> {{ selected_franchise.name }}
                </span>
                {% endif %}
                
                {% if selected_company %}
                <span class="inline-block bg-blue-100 text-blue-800 rounded-full px-3 py-1 text-sm font-semibold mr-2 mb-2">
                    <i class="fas fa-industry mr-1"></i> {{ selected_company.name }}
                </span>
                {% endif %}
                
                {% if selected_service_center %}
                <span class="inline-block bg-blue-100 text-blue-800 rounded-full px-3 py-1 text-sm font-semibold mr-2 mb-2">
                    <i class="fas fa-store mr-1"></i> {{ selected_service_center.name }}
                </span>
                {% endif %}
            </p>
        </div>
    </div>
</div>

<div class="flex justify-start mt-4">
    <a href="{{ action_url }}{% if preserved_query %}?{{ preserved_query }}{% endif %}" class="inline-flex items-center px-3 py-1.5 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors duration-200">
        <i class="fas fa-times {% if LANGUAGE_CODE == 'ar' %}ml-1{% else %}mr-1{% endif %}"></i> {% trans "إلغاء التصفية" %}
    </a>
</div>
{% endif %} 