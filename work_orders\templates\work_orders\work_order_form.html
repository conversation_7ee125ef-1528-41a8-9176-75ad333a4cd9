{% extends "dashboard_base.html" %}
{% load i18n %}
{% load multi_step_form_tags %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <h1 class="mt-4">{{ title }}</h1>
    <div class="mb-4">
        <nav class="breadcrumb mb-4">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'inventory:dashboard' %}">{% trans "Dashboard" %}</a></li>
                <li class="breadcrumb-item"><a href="{% url 'work_orders:work_order_list' %}">{% trans "Work Orders" %}</a></li>
                <li class="breadcrumb-item active">{{ title }}</li>
            </ol>
        </nav>
    </div>

    <!-- Multi-step Navigation -->
    <div class="mb-4">
        {% with steps_data=work_order_steps %}
            {% render_multi_step_nav steps_data.steps current_step "step" request.path %}
        {% endwith %}
    </div>

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-clipboard-list me-1"></i>
            {% if current_step == 'customer' %}
                {% trans "Step 1: Customer Information" %}
            {% elif current_step == 'vehicle' %}
                {% trans "Step 2: Vehicle Information" %}
            {% elif current_step == 'details' %}
                {% trans "Step 3: Work Order Details" %}
            {% elif current_step == 'operations' %}
                {% trans "Step 4: Operations" %}
            {% elif current_step == 'parts' %}
                {% trans "Step 5: Parts" %}
            {% elif current_step == 'summary' %}
                {% trans "Step 6: Summary" %}
            {% else %}
                {% trans "Work Order Information" %}
            {% endif %}
        </div>
        <div class="card-body">
            <form method="post" id="work-order-form" enctype="multipart/form-data" x-data="workOrderForm()">
                {% csrf_token %}
                <input type="hidden" name="current_step" value="{{ current_step|default:'customer' }}">
                
                {% if current_step == 'customer' or not current_step %}
                    <!-- Customer Step -->
                    <div id="customer-step" class="step-content">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> {% trans "Select an existing customer or create a new one." %}
                        </div>
                        
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <!-- Search Existing Customer Card -->
                            <div class="bg-white p-5 rounded-lg shadow-sm border border-gray-200">
                                <h5 class="text-lg font-semibold mb-4 flex items-center text-gray-700">
                                    <i class="fas fa-search text-blue-500 mr-2"></i>
                                    {% trans "Search Existing Customer" %}
                                </h5>
                                
                                <div class="mb-4">
                                    <label for="customer-search" class="block text-sm font-medium text-gray-700 mb-1">
                                        {% trans "Search by Name, Phone, or ID" %}
                                    </label>
                                    <div class="relative">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <i class="fas fa-search text-gray-400"></i>
                                        </div>
                                        <input type="text" class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                               id="customer-search" 
                                               placeholder="{% trans 'Enter name, phone, or ID number' %}"
                                               hx-get="{% url 'work_orders:htmx_search_customers' %}"
                                               hx-trigger="keyup changed delay:500ms, search"
                                               hx-target="#customer-search-results"
                                               hx-params="search"
                                               name="search">
                                    </div>
                                </div>
                                
                                <div id="customer-search-results" class="border rounded-lg max-h-60 overflow-y-auto bg-white"></div>
                                <div id="customer-details" class="mt-3"></div>
                            </div>
                            
                            <!-- Create New Customer Card -->
                            <div class="bg-white p-5 rounded-lg shadow-sm border border-gray-200">
                                <h5 class="text-lg font-semibold mb-4 flex items-center text-gray-700">
                                    <i class="fas fa-user-plus text-green-500 mr-2"></i>
                                    {% trans "Create New Customer" %}
                                </h5>
                                
                                <div class="mb-4">
                                    <label for="new-customer-name" class="block text-sm font-medium text-gray-700 mb-1">
                                        {% trans "Full Name" %} *
                                    </label>
                                    <input type="text" class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500" 
                                           id="new-customer-name" name="new_customer_name" x-model="formData.newCustomerName">
                                    <div class="text-red-500 text-sm mt-1" x-show="errors.newCustomerName" x-text="errors.newCustomerName"></div>
                                </div>
                                
                                <div class="mb-4">
                                    <label for="new-customer-phone" class="block text-sm font-medium text-gray-700 mb-1">
                                        {% trans "Phone Number" %} *
                                    </label>
                                    <input type="tel" class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500" 
                                           id="new-customer-phone" name="new_customer_phone" x-model="formData.newCustomerPhone">
                                    <div class="text-red-500 text-sm mt-1" x-show="errors.newCustomerPhone" x-text="errors.newCustomerPhone"></div>
                                </div>
                                
                                <div class="mb-4">
                                    <label for="new-customer-email" class="block text-sm font-medium text-gray-700 mb-1">
                                        {% trans "Email" %}
                                    </label>
                                    <input type="email" class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500" 
                                           id="new-customer-email" name="new_customer_email" x-model="formData.newCustomerEmail">
                                    <div class="text-red-500 text-sm mt-1" x-show="errors.newCustomerEmail" x-text="errors.newCustomerEmail"></div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="flex justify-between mt-6">
                            <button type="button" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 flex items-center" 
                                    onclick="window.history.back()">
                                <i class="fas fa-times mr-2"></i>
                                {% trans "Cancel" %}
                            </button>
                            <button type="button" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center" 
                                    data-next="vehicle" @click.prevent="validateCustomerStep">
                                {% trans "Next: Vehicle" %} 
                                <i class="fas fa-arrow-right ml-2"></i>
                            </button>
                        </div>
                    </div>
                {% endif %}
                
                {% if current_step == 'vehicle' %}
                    <!-- Vehicle Step -->
                    <div id="vehicle-step" class="step-content">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> {% trans "Select an existing vehicle or create a new one." %}
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <h5>{% trans "Search Existing Vehicle" %}</h5>
                                <div class="form-group mb-3">
                                    <label for="vehicle-search">{% trans "Search by Make, Model, License Plate, or VIN" %}</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="vehicle-search"
                                               placeholder="{% trans 'Enter make, model, license plate, or VIN' %}"
                                               hx-get="{% url 'work_orders:htmx_search_vehicles' %}"
                                               hx-trigger="keyup changed delay:500ms, search"
                                               hx-target="#vehicle-search-results"
                                               hx-params="search"
                                               name="search">
                                    </div>
                                </div>
                                
                                <div id="vehicle-search-results" class="mt-2 border rounded-lg max-h-60 overflow-y-auto bg-white">
                                    <!-- Vehicle search results will be loaded here via HTMX -->
                                </div>
                                
                                <div id="vehicle-details" class="mt-3">
                                    <!-- Vehicle details will be loaded here via HTMX -->
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <h5>{% trans "Create New Vehicle" %}</h5>
                                <div class="form-group mb-3">
                                    <label for="new-vehicle-make">{% trans "Make" %} *</label>
                                    <input type="text" class="form-control" id="new-vehicle-make" name="new_vehicle_make" x-model="formData.newVehicleMake">
                                    <div class="text-red-500 text-sm mt-1" x-show="errors.newVehicleMake" x-text="errors.newVehicleMake"></div>
                                </div>
                                
                                <div class="form-group mb-3">
                                    <label for="new-vehicle-model">{% trans "Model" %} *</label>
                                    <input type="text" class="form-control" id="new-vehicle-model" name="new_vehicle_model" x-model="formData.newVehicleModel">
                                    <div class="text-red-500 text-sm mt-1" x-show="errors.newVehicleModel" x-text="errors.newVehicleModel"></div>
                                </div>
                                
                                <div class="form-group mb-3">
                                    <label for="new-vehicle-year">{% trans "Year" %}</label>
                                    <input type="number" class="form-control" id="new-vehicle-year" name="new_vehicle_year" x-model="formData.newVehicleYear">
                                    <div class="text-red-500 text-sm mt-1" x-show="errors.newVehicleYear" x-text="errors.newVehicleYear"></div>
                                </div>
                                
                                <div class="form-group mb-3">
                                    <label for="new-vehicle-license-plate">{% trans "License Plate" %}</label>
                                    <input type="text" class="form-control" id="new-vehicle-license-plate" name="new_vehicle_license_plate" x-model="formData.newVehicleLicensePlate">
                                </div>
                                
                                <div class="form-group mb-3">
                                    <label for="new-vehicle-vin">{% trans "VIN" %}</label>
                                    <input type="text" class="form-control" id="new-vehicle-vin" name="new_vehicle_vin" x-model="formData.newVehicleVin">
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between mt-4">
                            <button type="button" class="btn btn-outline-secondary prev-step" data-prev="customer">
                                <i class="fas fa-arrow-left"></i> {% trans "Previous: Customer" %}
                            </button>
                            <button type="button" class="btn btn-primary next-step" data-next="details" @click.prevent="validateVehicleStep">
                                {% trans "Next: Details" %} <i class="fas fa-arrow-right"></i>
                            </button>
                        </div>
                    </div>
                {% endif %}
                
                {% if current_step == 'details' %}
                    <!-- Work Order Details Step -->
                    <div id="details-step" class="step-content">
                        <div class="bg-blue-50 p-4 rounded-lg mb-4">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-info-circle text-blue-600"></i>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm text-blue-700">{% trans "Enter the basic work order information." %}</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                {% load form_tags %}
                                {% render_field form.work_order_number icon="fa-hashtag" %}
                                {% render_field form.service_center icon="fa-building" %}
                                {% render_field form.work_order_type icon="fa-tasks" %}
                                {% render_field form.bill_of_materials icon="fa-clipboard-list" %}
                                {% render_field form.priority icon="fa-flag" %}
                                {% render_field form.status icon="fa-info-circle" %}
                            </div>
                            
                            <div>
                                {% render_field form.description icon="fa-align-left" %}
                                {% render_field form.planned_start_date icon="fa-calendar-alt" %}
                                {% render_field form.planned_end_date icon="fa-calendar-check" %}
                                {% render_field form.estimated_cost icon="fa-dollar-sign" %}
                                {% render_field form.current_odometer icon="fa-tachometer-alt" %}
                                {% render_field form.fuel_level icon="fa-gas-pump" %}
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            {% render_field form.notes %}
                        </div>
                        
                        <div class="flex flex-col md:flex-row justify-between mt-6">
                            <button type="button" class="btn-secondary mb-2 md:mb-0" onclick="window.location.href='{% url 'work_orders:work_order_list' %}'">
                                <i class="fas fa-times mr-1"></i> {% trans "Cancel" %}
                            </button>
                            <div class="flex flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-2">
                                <button type="button" class="btn-outline-primary prev-step" data-prev="vehicle">
                                    <i class="fas fa-arrow-left mr-1"></i> {% trans "Previous: Vehicle" %}
                                </button>
                                <button type="button" class="btn-primary next-step" data-next="operations">
                                    {% trans "Next: Operations" %} <i class="fas fa-arrow-right ml-1"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                {% endif %}
                
                {% if current_step == 'operations' %}
                    <!-- Operations Step -->
                    <div id="operations-step" class="step-content">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> {% trans "Add operations to be performed in this work order." %}
                        </div>
                        
                        <div class="mb-3">
                            <button type="button" class="btn btn-success" id="add-operation-btn">
                                <i class="fas fa-plus"></i> {% trans "Add Operation" %}
                            </button>
                        </div>
                        
                        <div class="table-responsive">
                            <table class="table table-striped" id="operations-table">
                                <thead>
                                    <tr>
                                        <th>{% trans "Description" %}</th>
                                        <th>{% trans "Estimated Time (hours)" %}</th>
                                        <th>{% trans "Actions" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Operations will be added here dynamically -->
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="d-flex justify-content-between mt-4">
                            <button type="button" class="btn btn-outline-secondary prev-step" data-prev="details">
                                <i class="fas fa-arrow-left"></i> {% trans "Previous: Details" %}
                            </button>
                            <button type="button" class="btn btn-primary next-step" data-next="parts">
                                {% trans "Next: Parts" %} <i class="fas fa-arrow-right"></i>
                            </button>
                        </div>
                    </div>
                {% endif %}
                
                {% if current_step == 'parts' %}
                    <!-- Parts Step -->
                    <div id="parts-step" class="step-content">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> {% trans "Add parts and materials needed for this work order." %}
                        </div>
                        
                        <div class="mb-3">
                            <button type="button" class="btn btn-success" id="add-part-btn">
                                <i class="fas fa-plus"></i> {% trans "Add Part" %}
                            </button>
                        </div>
                        
                        <div class="table-responsive">
                            <table class="table table-striped" id="parts-table">
                                <thead>
                                    <tr>
                                        <th>{% trans "Part" %}</th>
                                        <th>{% trans "Quantity" %}</th>
                                        <th>{% trans "Unit" %}</th>
                                        <th>{% trans "Operation" %}</th>
                                        <th>{% trans "Actions" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Parts will be added here dynamically -->
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="d-flex justify-content-between mt-4">
                            <button type="button" class="btn btn-outline-secondary prev-step" data-prev="operations">
                                <i class="fas fa-arrow-left"></i> {% trans "Previous: Operations" %}
                            </button>
                            <button type="button" class="btn btn-primary next-step" data-next="summary">
                                {% trans "Next: Summary" %} <i class="fas fa-arrow-right"></i>
                            </button>
                        </div>
                    </div>
                {% endif %}
                
                {% if current_step == 'summary' %}
                    <!-- Summary Step -->
                    <div id="summary-step" class="step-content">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> {% trans "Review the work order details before saving." %}
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card mb-3">
                                    <div class="card-header">
                                        <h5 class="mb-0">{% trans "Customer Information" %}</h5>
                                    </div>
                                    <div class="card-body" id="summary-customer">
                                        <!-- Customer summary will be loaded here -->
                                    </div>
                                </div>
                                
                                <div class="card mb-3">
                                    <div class="card-header">
                                        <h5 class="mb-0">{% trans "Vehicle Information" %}</h5>
                                    </div>
                                    <div class="card-body" id="summary-vehicle">
                                        <!-- Vehicle summary will be loaded here -->
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="card mb-3">
                                    <div class="card-header">
                                        <h5 class="mb-0">{% trans "Work Order Details" %}</h5>
                                    </div>
                                    <div class="card-body" id="summary-details">
                                        <!-- Work order details summary will be loaded here -->
                                    </div>
                                </div>
                                
                                <div class="card mb-3">
                                    <div class="card-header">
                                        <h5 class="mb-0">{% trans "Operations" %}</h5>
                                    </div>
                                    <div class="card-body" id="summary-operations">
                                        <!-- Operations summary will be loaded here -->
                                    </div>
                                </div>
                                
                                <div class="card mb-3">
                                    <div class="card-header">
                                        <h5 class="mb-0">{% trans "Parts" %}</h5>
                                    </div>
                                    <div class="card-body" id="summary-parts">
                                        <!-- Parts summary will be loaded here -->
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between mt-4">
                            <button type="button" class="btn btn-outline-secondary prev-step" data-prev="parts">
                                <i class="fas fa-arrow-left"></i> {% trans "Previous: Parts" %}
                            </button>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save"></i> {% trans "Save Work Order" %}
                            </button>
                        </div>
                    </div>
                {% endif %}
                
                {% if not current_step %}
                    <!-- If no specific step is active, show the form fields -->
                    {{ form.as_p }}
                    
                    <div class="d-flex justify-content-between mt-4">
                        <button type="button" class="btn btn-outline-secondary" onclick="window.history.back()">{% trans "Cancel" %}</button>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save"></i> {% trans "Save Work Order" %}
                        </button>
                    </div>
                {% endif %}
            </form>
        </div>
    </div>
</div>

<!-- Modal for adding operation -->
<div class="modal fade" id="add-operation-modal" tabindex="-1" aria-labelledby="add-operation-modal-label" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="add-operation-modal-label">{% trans "Add Operation" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="form-group mb-3">
                    <label for="operation-description">{% trans "Description" %} *</label>
                    <input type="text" class="form-control" id="operation-description">
                </div>
                
                <div class="form-group mb-3">
                    <label for="operation-estimated-time">{% trans "Estimated Time (hours)" %}</label>
                    <input type="number" class="form-control" id="operation-estimated-time" step="0.25" min="0">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                <button type="button" class="btn btn-primary" id="save-operation-btn">{% trans "Add" %}</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal for adding part -->
<div class="modal fade" id="add-part-modal" tabindex="-1" aria-labelledby="add-part-modal-label" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="add-part-modal-label">{% trans "Add Part" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="form-group mb-3">
                    <label for="part-search">{% trans "Search Part" %}</label>
                    <input type="text" class="form-control" id="part-search">
                </div>
                
                <div class="form-group mb-3">
                    <label for="part-select">{% trans "Select Part" %} *</label>
                    <select class="form-select" id="part-select">
                        <option value="">{% trans "Select a part" %}</option>
                    </select>
                </div>
                
                <div class="form-group mb-3">
                    <label for="part-quantity">{% trans "Quantity" %} *</label>
                    <input type="number" class="form-control" id="part-quantity" min="0.01" step="0.01" value="1">
                </div>
                
                <div class="form-group mb-3">
                    <label for="part-operation">{% trans "Associated Operation" %}</label>
                    <select class="form-select" id="part-operation">
                        <option value="">{% trans "Select an operation" %}</option>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                <button type="button" class="btn btn-primary" id="save-part-btn">{% trans "Add" %}</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'work_orders/js/work_order_form.js' %}"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize the multi-step form navigation
        const workOrderForm = document.getElementById('work-order-form');
        const nextButtons = document.querySelectorAll('.next-step');
        const prevButtons = document.querySelectorAll('.prev-step');
        
        // Handle next step buttons
        nextButtons.forEach(button => {
            button.addEventListener('click', function() {
                const nextStep = this.dataset.next;
                window.location.href = `${window.location.pathname}?step=${nextStep}`;
            });
        });
        
        // Handle previous step buttons
        prevButtons.forEach(button => {
            button.addEventListener('click', function() {
                const prevStep = this.dataset.prev;
                window.location.href = `${window.location.pathname}?step=${prevStep}`;
            });
        });
        
        // Handle form submission
        workOrderForm.addEventListener('submit', function(e) {
            // Additional validation can be added here
        });
    });
    
    // Customer selection functions
    function selectCustomer(customerId, customerName) {
        // Update Alpine.js model
        if (typeof Alpine !== 'undefined') {
            const form = document.getElementById('work-order-form');
            const alpineComponent = Alpine.getComponent(form);
            if (alpineComponent) {
                alpineComponent.formData.customer = customerId;
            }
        }
        
        // Make HTMX request to load customer details
        htmx.ajax('POST', "{% url 'work_orders:htmx_select_customer' %}", {
            target: '#customer-details',
            swap: 'innerHTML',
            values: { customer_id: customerId }
        });
    }
    
    function clearCustomerSelection() {
        // Clear Alpine.js model
        if (typeof Alpine !== 'undefined') {
            const form = document.getElementById('work-order-form');
            const alpineComponent = Alpine.getComponent(form);
            if (alpineComponent) {
                alpineComponent.formData.customer = '';
            }
        }
        
        // Clear the customer details div
        document.getElementById('customer-details').innerHTML = '';
    }
    
    // Vehicle selection functions
    function selectVehicle(vehicleId, vehicleDisplay) {
        // Update Alpine.js model
        if (typeof Alpine !== 'undefined') {
            const form = document.getElementById('work-order-form');
            const alpineComponent = Alpine.getComponent(form);
            if (alpineComponent) {
                alpineComponent.formData.vehicle = vehicleId;
            }
        }
        
        // Make HTMX request to load vehicle details
        htmx.ajax('POST', "{% url 'work_orders:htmx_select_vehicle' %}", {
            target: '#vehicle-details',
            swap: 'innerHTML',
            values: { vehicle_id: vehicleId }
        });
    }
    
    function clearVehicleSelection() {
        // Clear Alpine.js model
        if (typeof Alpine !== 'undefined') {
            const form = document.getElementById('work-order-form');
            const alpineComponent = Alpine.getComponent(form);
            if (alpineComponent) {
                alpineComponent.formData.vehicle = '';
            }
        }
        
        // Clear the vehicle details div
        document.getElementById('vehicle-details').innerHTML = '';
    }
</script>

<script>
    function workOrderForm() {
        return {
            formData: {
                // Customer step
                customer: '',
                newCustomerName: '',
                newCustomerPhone: '',
                newCustomerEmail: '',
                
                // Vehicle step
                vehicle: '',
                newVehicleMake: '',
                newVehicleModel: '',
                newVehicleYear: '',
                newVehicleLicensePlate: '',
                newVehicleVin: ''
            },
            errors: {},
            
            validateCustomerStep() {
                this.errors = {};
                let isValid = true;
                
                // Validate either existing customer is selected or new customer fields are filled
                if (!this.formData.customer) {
                    // No customer selected, check new customer fields
                    if (!this.formData.newCustomerName) {
                        this.errors.newCustomerName = "{% trans 'Customer name is required' %}";
                        isValid = false;
                    }
                    
                    if (!this.formData.newCustomerPhone) {
                        this.errors.newCustomerPhone = "{% trans 'Customer phone is required' %}";
                        isValid = false;
                    } else if (!/^\d{10,15}$/.test(this.formData.newCustomerPhone.replace(/\D/g, ''))) {
                        this.errors.newCustomerPhone = "{% trans 'Please enter a valid phone number' %}";
                        isValid = false;
                    }
                    
                    if (this.formData.newCustomerEmail && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(this.formData.newCustomerEmail)) {
                        this.errors.newCustomerEmail = "{% trans 'Please enter a valid email address' %}";
                        isValid = false;
                    }
                }
                
                if (isValid) {
                    // Proceed to next step
                    window.location.href = `${window.location.pathname}?step=vehicle`;
                }
            },
            
            validateVehicleStep() {
                this.errors = {};
                let isValid = true;
                
                // Validate either existing vehicle is selected or new vehicle fields are filled
                if (!this.formData.vehicle) {
                    // No vehicle selected, check new vehicle fields
                    if (!this.formData.newVehicleMake) {
                        this.errors.newVehicleMake = "{% trans 'Vehicle make is required' %}";
                        isValid = false;
                    }
                    
                    if (!this.formData.newVehicleModel) {
                        this.errors.newVehicleModel = "{% trans 'Vehicle model is required' %}";
                        isValid = false;
                    }
                    
                    if (this.formData.newVehicleYear) {
                        const year = parseInt(this.formData.newVehicleYear);
                        const currentYear = new Date().getFullYear();
                        
                        if (isNaN(year) || year < 1900 || year > currentYear + 1) {
                            this.errors.newVehicleYear = `{% trans 'Year must be between 1900 and' %} ${currentYear + 1}`;
                            isValid = false;
                        }
                    }
                }
                
                if (isValid) {
                    // Proceed to next step
                    window.location.href = `${window.location.pathname}?step=details`;
                }
            }
        };
    }
</script>
{% endblock %} 