{% extends "dashboard_base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Inventory Dashboard" %}{% endblock %}
{% block page_title %}{% trans "Inventory Dashboard" %}{% endblock %}
{% block header_title %}{% trans "Inventory Dashboard" %}{% endblock %}
{% block header_subtitle %}{% trans "Manage your inventory items, stock levels, and movements" %}{% endblock %}

{% block breadcrumbs %}
<li class="flex items-center">
  <i class="fas fa-chevron-right text-gray-400 mx-2 rtl:rotate-180"></i>
  <span class="text-gray-700">{% trans "Inventory" %}</span>
</li>
{% endblock %}

{% block content %}
<!-- Stats Cards Row -->
<div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
  <!-- Total Items Card -->
  <div class="card hover-shadow hover-scale animate-fade-in">
    <div class="p-5">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-sm font-medium text-gray-500">{% trans "Total Items" %}</p>
          <p class="text-3xl font-bold text-gray-800 mt-1">{{ total_items }}</p>
        </div>
        <div class="p-3 rounded-full bg-primary-500 text-white">
          <i class="fas fa-boxes w-6 h-6"></i>
        </div>
      </div>
      <div class="mt-4">
        <a href="{{ inventory_item_list_url }}" class="text-sm font-medium text-primary-600 hover:text-primary-500">
          {% trans "View all items" %} <i class="fas fa-arrow-right ml-1 rtl:hidden"></i><i class="fas fa-arrow-left mr-1 hidden rtl:inline-block"></i>
        </a>
      </div>
    </div>
  </div>
  
  <!-- Low Stock Items Card -->
  <div class="card hover-shadow hover-scale animate-fade-in">
    <div class="p-5">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-sm font-medium text-gray-500">{% trans "Low Stock Items" %}</p>
          <p class="text-3xl font-bold text-gray-800 mt-1">{{ low_stock_count }}</p>
        </div>
        <div class="p-3 rounded-full {% if low_stock_count > 0 %}bg-yellow-400{% else %}bg-green-500{% endif %} text-white">
          <i class="fas fa-exclamation-triangle w-6 h-6"></i>
        </div>
      </div>
      <div class="mt-4">
        <a href="{{ inventory_low_stock_url }}" class="text-sm font-medium text-primary-600 hover:text-primary-500">
          {% trans "View low stock items" %} <i class="fas fa-arrow-right ml-1 rtl:hidden"></i><i class="fas fa-arrow-left mr-1 hidden rtl:inline-block"></i>
        </a>
      </div>
    </div>
  </div>
  
  <!-- Stock Movements Card -->
  <div class="card hover-shadow hover-scale animate-fade-in">
    <div class="p-5">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-sm font-medium text-gray-500">{% trans "Stock Movements" %}</p>
          <p class="text-3xl font-bold text-gray-800 mt-1">{{ total_movements }}</p>
          <p class="text-sm text-gray-500 mt-1">{% trans "Last 30 days" %}</p>
        </div>
        <div class="p-3 rounded-full bg-blue-400 text-white">
          <i class="fas fa-exchange-alt w-6 h-6"></i>
        </div>
      </div>
      <div class="mt-4">
        <a href="{{ inventory_movement_list_url }}" class="text-sm font-medium text-primary-600 hover:text-primary-500">
          {% trans "View all movements" %} <i class="fas fa-arrow-right ml-1 rtl:hidden"></i><i class="fas fa-arrow-left mr-1 hidden rtl:inline-block"></i>
        </a>
      </div>
    </div>
  </div>
  
  <!-- Quick Actions Card -->
  <div class="card hover-shadow hover-scale animate-fade-in">
    <div class="p-5">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-sm font-medium text-gray-500">{% trans "Quick Actions" %}</p>
          <p class="text-sm text-gray-700 mt-1">{% trans "Manage your inventory" %}</p>
        </div>
        <div class="p-3 rounded-full bg-accent-500 text-white">
          <i class="fas fa-bolt w-6 h-6"></i>
        </div>
      </div>
      <div class="mt-4 space-y-2">
        <a href="{{ inventory_item_create_url }}" class="block text-sm font-medium text-primary-600 hover:text-primary-500">
          {% trans "Add new item" %} <i class="fas fa-arrow-right ml-1 rtl:hidden"></i><i class="fas fa-arrow-left mr-1 hidden rtl:inline-block"></i>
        </a>
        <a href="{{ inventory_movement_create_url }}" class="block text-sm font-medium text-primary-600 hover:text-primary-500">
          {% trans "Record movement" %} <i class="fas fa-arrow-right ml-1 rtl:hidden"></i><i class="fas fa-arrow-left mr-1 hidden rtl:inline-block"></i>
        </a>
        <a href="{{ inventory_scan_barcode_url }}" class="block text-sm font-medium text-primary-600 hover:text-primary-500">
          {% trans "Scan barcode" %} <i class="fas fa-arrow-right ml-1 rtl:hidden"></i><i class="fas fa-arrow-left mr-1 hidden rtl:inline-block"></i>
        </a>
      </div>
    </div>
  </div>
</div>

<!-- Recent Items Section -->
<div class="mt-8">
  <div class="flex items-center justify-between mb-4">
    <h2 class="text-lg font-medium text-gray-900">{% trans "Recent Items" %}</h2>
    <a href="{{ inventory_item_list_url }}" class="text-sm font-medium text-primary-600 hover:text-primary-500">
      {% trans "View all" %} <i class="fas fa-arrow-right ml-1 rtl:hidden"></i><i class="fas fa-arrow-left mr-1 hidden rtl:inline-block"></i>
    </a>
  </div>
  
  <div class="overflow-hidden bg-white rounded-lg shadow">
    <table class="min-w-full divide-y divide-gray-200">
      <thead class="bg-gray-50">
        <tr>
          <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase rtl:text-right">
            {% trans "Item Code" %}
          </th>
          <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase rtl:text-right">
            {% trans "Name" %}
          </th>
          <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase rtl:text-right">
            {% trans "Category" %}
          </th>
          <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase rtl:text-right">
            {% trans "Current Stock" %}
          </th>
          <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-right text-gray-500 uppercase rtl:text-left">
            {% trans "Actions" %}
          </th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200">
        {% for item in recent_items %}
        <tr class="hover:bg-gray-50 transition-colors duration-150">
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="text-sm font-medium text-gray-900">{{ item.sku }}</div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="text-sm text-gray-900">{{ item.name }}</div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="text-sm text-gray-900">{{ item.category|default:"-" }}</div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <span class="inline-flex px-2 py-1 text-xs font-medium rounded-full 
              {% if item.current_stock < item.min_stock_level %}
                text-red-800 bg-red-100
              {% elif item.current_stock <= item.min_stock_level|add:"5" %}
                text-yellow-800 bg-yellow-100
              {% else %}
                text-blue-800 bg-blue-100
              {% endif %}">
              {{ item.current_stock }}
            </span>
          </td>
          <td class="px-6 py-4 text-sm font-medium text-right whitespace-nowrap rtl:text-left">
            <a href="{{ item.get_absolute_url }}" class="text-primary-600 hover:text-primary-900 mr-3 rtl:ml-3 rtl:mr-0">
              {% trans "View" %}
            </a>
            <a href="{{ item.get_edit_url }}" class="text-primary-600 hover:text-primary-900">
              {% trans "Edit" %}
            </a>
          </td>
        </tr>
        {% empty %}
        <tr>
          <td colspan="5" class="px-6 py-4 text-sm text-gray-500 text-center">
            {% trans "No items found" %}
          </td>
        </tr>
        {% endfor %}
      </tbody>
    </table>
  </div>
</div>

<!-- Recent Movements Section -->
<div class="mt-8">
  <div class="flex items-center justify-between mb-4">
    <h2 class="text-lg font-medium text-gray-900">{% trans "Recent Movements" %}</h2>
    <a href="{{ inventory_movement_list_url }}" class="text-sm font-medium text-primary-600 hover:text-primary-500">
      {% trans "View all" %} <i class="fas fa-arrow-right ml-1 rtl:hidden"></i><i class="fas fa-arrow-left mr-1 hidden rtl:inline-block"></i>
    </a>
  </div>
  
  <div class="overflow-hidden bg-white rounded-lg shadow">
    <table class="min-w-full divide-y divide-gray-200">
      <thead class="bg-gray-50">
        <tr>
          <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase rtl:text-right">
            {% trans "Date" %}
          </th>
          <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase rtl:text-right">
            {% trans "Item" %}
          </th>
          <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase rtl:text-right">
            {% trans "Type" %}
          </th>
          <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase rtl:text-right">
            {% trans "Quantity" %}
          </th>
          <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-right text-gray-500 uppercase rtl:text-left">
            {% trans "Actions" %}
          </th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200">
        {% for movement in recent_movements %}
        <tr class="hover:bg-gray-50 transition-colors duration-150">
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="text-sm text-gray-900">{{ movement.created_at|date:"Y-m-d H:i" }}</div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="text-sm text-gray-900">{{ movement.item.name }}</div>
            <div class="text-xs text-gray-500">{{ movement.item.sku }}</div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <span class="inline-flex px-2 py-1 text-xs font-medium rounded-full 
              {% if movement.is_inbound %}
                text-blue-800 bg-blue-100
              {% elif movement.is_outbound %}
                text-red-800 bg-red-100
              {% else %}
                text-gray-800 bg-gray-100
              {% endif %}">
              {{ movement.get_movement_name }}
            </span>
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="text-sm font-medium 
              {% if movement.is_inbound %}
                text-blue-600
              {% elif movement.is_outbound %}
                text-red-600
              {% endif %}">
              {{ movement.quantity }}
            </div>
          </td>
          <td class="px-6 py-4 text-sm font-medium text-right whitespace-nowrap rtl:text-left">
            <a href="#" class="text-primary-600 hover:text-primary-900">
              {% trans "View" %}
            </a>
          </td>
        </tr>
        {% empty %}
        <tr>
          <td colspan="5" class="px-6 py-4 text-sm text-gray-500 text-center">
            {% trans "No movements found" %}
          </td>
        </tr>
        {% endfor %}
      </tbody>
    </table>
  </div>
</div>

<!-- Operation Compatibilities Section -->
<div class="mt-8">
  <div class="flex items-center justify-between mb-4">
    <h2 class="text-lg font-medium text-gray-900">{% trans "Common Operations" %}</h2>
    <a href="{{ inventory_item_list_url }}" class="text-sm font-medium text-primary-600 hover:text-primary-500">
      {% trans "View all items" %} <i class="fas fa-arrow-right ml-1 rtl:hidden"></i><i class="fas fa-arrow-left mr-1 hidden rtl:inline-block"></i>
    </a>
  </div>
  
  <div class="overflow-hidden bg-white rounded-lg shadow">
    <table class="min-w-full divide-y divide-gray-200">
      <thead class="bg-gray-50">
        <tr>
          <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase rtl:text-right">
            {% trans "Operation" %}
          </th>
          <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase rtl:text-right">
            {% trans "Item" %}
          </th>
          <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase rtl:text-right">
            {% trans "Description" %}
          </th>
          <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase rtl:text-right">
            {% trans "Duration" %}
          </th>
          <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase rtl:text-right">
            {% trans "Stock" %}
          </th>
          <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-right text-gray-500 uppercase rtl:text-left">
            {% trans "Actions" %}
          </th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200">
        {% for compat in operation_compatibilities %}
        <tr class="hover:bg-gray-50 transition-colors duration-150">
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="text-sm text-gray-900">{{ compat.operation_type.name }}</div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="text-sm text-gray-900">{{ compat.item.name }}</div>
            <div class="text-xs text-gray-500">{{ compat.item.sku }}</div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="text-sm text-gray-900">{{ compat.get_operation_description_display }}</div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="text-sm text-gray-900">{{ compat.duration_minutes_display }}</div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <span class="inline-flex px-2 py-1 text-xs font-medium rounded-full 
              {% if compat.item.current_stock < compat.item.min_stock_level %}
                text-red-800 bg-red-100
              {% elif compat.item.current_stock <= compat.item.min_stock_level|add:"5" %}
                text-yellow-800 bg-yellow-100
              {% else %}
                text-blue-800 bg-blue-100
              {% endif %}">
              {{ compat.item.current_stock }} {{ compat.item.unit_of_measurement.symbol|default:"" }}
            </span>
          </td>
          <td class="px-6 py-4 text-sm font-medium text-right whitespace-nowrap rtl:text-left">
            <a href="{% url 'work_orders:work_order_create' %}?operation_type_id={{ compat.operation_type.id }}&item_id={{ compat.item.id }}&duration={{ compat.duration_minutes }}&description={{ compat.operation_description }}" class="text-primary-600 hover:text-primary-900 mr-3 rtl:ml-3 rtl:mr-0">
              {% trans "Create Work Order" %}
            </a>
          </td>
        </tr>
        {% empty %}
        <tr>
          <td colspan="6" class="px-6 py-4 text-sm text-gray-500 text-center">
            {% trans "No operation compatibilities found" %}
          </td>
        </tr>
        {% endfor %}
      </tbody>
    </table>
  </div>
</div>

<!-- Stock Alerts Section -->
{% if low_stock_items %}
<div class="mt-8">
  <div class="bg-red-50 border-l-4 border-red-400 p-4 rounded">
    <div class="flex">
      <div class="flex-shrink-0">
        <i class="fas fa-exclamation-circle text-red-400"></i>
      </div>
      <div class="ml-3 rtl:mr-3 rtl:ml-0">
        <h3 class="text-sm font-medium text-red-800">
          {% trans "Low Stock Alerts" %}
        </h3>
        <div class="mt-2 text-sm text-red-700">
          <ul class="list-disc pl-5 rtl:pr-5 rtl:pl-0 space-y-1">
            {% for item in low_stock_items %}
            <li>
              {{ item.name }} ({{ item.sku }}): {{ item.current_stock }} {% trans "remaining" %} 
              ({% trans "Min" %}: {{ item.min_stock_level }})
            </li>
            {% endfor %}
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>
{% endif %}
{% endblock %} 