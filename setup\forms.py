from django import forms
from django.utils.translation import gettext_lazy as _
from .models import VehicleOwnershipTransfer, Vehicle, Customer

class VehicleOwnershipTransferForm(forms.ModelForm):
    """Form for creating vehicle ownership transfers"""
    
    class Meta:
        model = VehicleOwnershipTransfer
        fields = ['vehicle', 'new_owner', 'transfer_date', 'sale_price', 
                 'odometer_reading', 'notes']
        widgets = {
            'transfer_date': forms.DateInput(attrs={'type': 'date'}),
            'notes': forms.Textarea(attrs={'rows': 3}),
        }
    
    def __init__(self, *args, **kwargs):
        # Allow restricting vehicle choices
        vehicle_choices = kwargs.pop('vehicle_choices', None)
        super().__init__(*args, **kwargs)
        
        if vehicle_choices:
            self.fields['vehicle'].queryset = vehicle_choices
            
        # Always limit new_owner choices to active customers
        self.fields['new_owner'].queryset = Customer.objects.filter(is_active=True)
        
        # Set the previous_owner field based on the vehicle selected
        self.fields['vehicle'].widget.attrs['onchange'] = 'updatePreviousOwner(this.value)'
    
    def clean(self):
        """Custom form validation"""
        cleaned_data = super().clean()
        vehicle = cleaned_data.get('vehicle')
        new_owner = cleaned_data.get('new_owner')
        
        if vehicle and new_owner:
            # Automatically set previous_owner based on vehicle's current owner
            cleaned_data['previous_owner'] = vehicle.owner
            
            # Validate that new_owner is different from current owner
            if vehicle.owner == new_owner:
                self.add_error('new_owner', _('New owner must be different from the current owner.'))
                
            # Check if there's already a pending transfer for this vehicle
            existing_transfers = VehicleOwnershipTransfer.objects.filter(
                vehicle=vehicle, status='pending'
            )
            if existing_transfers.exists():
                self.add_error('vehicle', _('There is already a pending transfer for this vehicle.'))
                
        return cleaned_data

class BulkVehicleOwnershipTransferForm(forms.Form):
    """Form for creating multiple vehicle ownership transfers at once"""
    new_owner = forms.ModelChoiceField(
        queryset=Customer.objects.filter(is_active=True),
        label=_('New Owner'),
        help_text=_('Select the new owner for all selected vehicles')
    )
    transfer_date = forms.DateField(
        label=_('Transfer Date'),
        widget=forms.DateInput(attrs={'type': 'date'})
    )
    sale_price = forms.DecimalField(
        label=_('Sale Price'),
        max_digits=15,
        decimal_places=2,
        required=False
    )
    odometer_reading = forms.IntegerField(
        label=_('Odometer Reading'),
        required=False
    )
    notes = forms.CharField(
        label=_('Notes'),
        widget=forms.Textarea(attrs={'rows': 3}),
        required=False
    ) 