import os
import sys
import django
import random
import uuid
from datetime import datetime, timedelta

# Add the parent directory to the Python path so imports work correctly
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

# Import Django models
from django.db import transaction
from django.utils import timezone

# Import models from inventory app
try:
    from inventory.models import (
        Item, ItemClassification, Movement, MovementType, 
        OperationCompatibility, UnitConversion, UnitOfMeasurement,
        VehicleCompatibility
    )
except ImportError as e:
    print(f"Could not import inventory models: {e}")
    print("Please check app structure and model names.")
    sys.exit(1)

# Import vehicle models for compatibility
try:
    from setup.models import Vehicle
except ImportError as e:
    print(f"Could not import Vehicle model: {e}")

class InventoryDataGenerator:
    def __init__(self):
        print("Inventory data generator initialized")
        # Get a tenant ID if available
        self.tenant_id = self._get_tenant_id()
    
    def _get_tenant_id(self):
        """Get a tenant ID from an existing record"""
        try:
            item = Item.objects.first()
            if item:
                return item.tenant_id
        except:
            pass
            
        try:
            vehicle = Vehicle.objects.first()
            if vehicle:
                return vehicle.tenant_id
        except:
            pass
            
        # Create a new one if none found
        return uuid.uuid4()
        
    def generate_units_of_measurement(self):
        """Generate basic units of measurement"""
        print("\nGenerating units of measurement...")
        
        units = [
            {"name": "قطعة", "symbol": "PC", "description": "قطعة واحدة", "is_base_unit": True},
            {"name": "مجموعة", "symbol": "SET", "description": "مجموعة من القطع", "is_base_unit": False},
            {"name": "لتر", "symbol": "L", "description": "لتر واحد من السائل", "is_base_unit": True},
            {"name": "كيلوجرام", "symbol": "KG", "description": "كيلوجرام من الوزن", "is_base_unit": True},
            {"name": "جرام", "symbol": "G", "description": "جرام من الوزن", "is_base_unit": False},
            {"name": "متر", "symbol": "M", "description": "متر من الطول", "is_base_unit": True},
            {"name": "سنتيمتر", "symbol": "CM", "description": "سنتيمتر من الطول", "is_base_unit": False},
            {"name": "طقم", "symbol": "KIT", "description": "طقم كامل من القطع", "is_base_unit": False},
            {"name": "عبوة", "symbol": "BTL", "description": "عبوة واحدة", "is_base_unit": False},
            {"name": "علبة", "symbol": "BOX", "description": "علبة واحدة", "is_base_unit": False}
        ]
        
        created_count = 0
        for unit_data in units:
            try:
                unit, created = UnitOfMeasurement.objects.get_or_create(
                    name=unit_data["name"],
                    defaults={
                        "symbol": unit_data["symbol"],
                        "description": unit_data["description"],
                        "is_base_unit": unit_data["is_base_unit"],
                        "tenant_id": self.tenant_id
                    }
                )
                
                if created:
                    created_count += 1
                    print(f"Created unit: {unit.name} ({unit.symbol})")
                
            except Exception as e:
                print(f"Error creating unit {unit_data['name']}: {e}")
        
        print(f"Created {created_count} units of measurement")
        return created_count
    
    def generate_unit_conversions(self):
        """Generate unit conversion factors"""
        print("\nGenerating unit conversions...")
        
        # Get existing units
        units = list(UnitOfMeasurement.objects.all())
        if len(units) < 2:
            print("Not enough units to create conversions. Please generate units first.")
            return 0
        
        conversions = [
            {"from_unit_name": "لتر", "to_unit_name": "عبوة", "factor": 1.0},
            {"from_unit_name": "كيلوجرام", "to_unit_name": "جرام", "factor": 1000.0},
            {"from_unit_name": "متر", "to_unit_name": "سنتيمتر", "factor": 100.0},
            {"from_unit_name": "مجموعة", "to_unit_name": "قطعة", "factor": 4.0},
            {"from_unit_name": "طقم", "to_unit_name": "قطعة", "factor": 6.0}
        ]
        
        created_count = 0
        for conv_data in conversions:
            try:
                # Find the units by name
                from_unit = next((u for u in units if u.name == conv_data["from_unit_name"]), None)
                to_unit = next((u for u in units if u.name == conv_data["to_unit_name"]), None)
                
                if not from_unit or not to_unit:
                    print(f"Could not find units for conversion: {conv_data['from_unit_name']} -> {conv_data['to_unit_name']}")
                    continue
                
                conversion, created = UnitConversion.objects.get_or_create(
                    from_unit=from_unit,
                    to_unit=to_unit,
                    defaults={
                        "conversion_factor": conv_data["factor"],
                        "tenant_id": self.tenant_id
                    }
                )
                
                if created:
                    created_count += 1
                    print(f"Created conversion: {from_unit.name} -> {to_unit.name} (x{conversion.conversion_factor})")
                
            except Exception as e:
                print(f"Error creating conversion {conv_data['from_unit_name']} -> {conv_data['to_unit_name']}: {e}")
        
        print(f"Created {created_count} unit conversions")
        return created_count
    
    def generate_item_classifications(self):
        """Generate item classifications"""
        print("\nGenerating item classifications...")
        
        classifications = [
            {"name": "قطع غيار محرك", "code": "ENG-PARTS", "description": "قطع غيار خاصة بالمحرك"},
            {"name": "زيوت وسوائل", "code": "OILS-FLUID", "description": "زيوت وسوائل للسيارة"},
            {"name": "قطع غيار فرامل", "code": "BRAKE-PARTS", "description": "قطع غيار خاصة بالفرامل"},
            {"name": "إطارات وعجلات", "code": "TIRE-WHEEL", "description": "إطارات وعجلات للسيارة"},
            {"name": "قطع غيار كهربائية", "code": "ELEC-PARTS", "description": "قطع غيار كهربائية للسيارة"},
            {"name": "قطع غيار تعليق", "code": "SUSP-PARTS", "description": "قطع غيار خاصة بنظام التعليق"},
            {"name": "قطع غيار تكييف", "code": "AC-PARTS", "description": "قطع غيار خاصة بنظام التكييف"},
            {"name": "قطع غيار ناقل الحركة", "code": "TRANS-PARTS", "description": "قطع غيار خاصة بناقل الحركة"},
            {"name": "مستهلكات", "code": "CONSUMABLES", "description": "مواد مستهلكة للصيانة"},
            {"name": "أدوات وعدد", "code": "TOOLS", "description": "أدوات وعدد للصيانة"}
        ]
        
        # Create parent-child relationships
        sub_classifications = [
            {"name": "فلاتر", "code": "FILTERS", "description": "فلاتر مختلفة للسيارة", "parent": "قطع غيار محرك"},
            {"name": "زيت محرك", "code": "ENG-OIL", "description": "زيوت للمحرك", "parent": "زيوت وسوائل"},
            {"name": "زيت ناقل حركة", "code": "TRANS-OIL", "description": "زيوت لناقل الحركة", "parent": "زيوت وسوائل"},
            {"name": "سائل تبريد", "code": "COOLANT", "description": "سوائل تبريد للمحرك", "parent": "زيوت وسوائل"},
            {"name": "وسادات فرامل", "code": "BRAKE-PADS", "description": "وسادات فرامل", "parent": "قطع غيار فرامل"}
        ]
        
        created_count = 0
        classification_map = {}  # To store created classifications for parent-child relationship
        
        # First create all main classifications
        for class_data in classifications:
            try:
                classification, created = ItemClassification.objects.get_or_create(
                    name=class_data["name"],
                    defaults={
                        "code": class_data["code"],
                        "description": class_data["description"],
                        "tenant_id": self.tenant_id
                    }
                )
                
                classification_map[class_data["name"]] = classification
                
                if created:
                    created_count += 1
                    print(f"Created classification: {classification.name} ({classification.code})")
                
            except Exception as e:
                print(f"Error creating classification {class_data['name']}: {e}")
        
        # Then create sub-classifications with parent references
        for subclass_data in sub_classifications:
            try:
                parent = classification_map.get(subclass_data["parent"])
                if not parent:
                    print(f"Could not find parent classification: {subclass_data['parent']}")
                    continue
                
                sub_classification, created = ItemClassification.objects.get_or_create(
                    name=subclass_data["name"],
                    defaults={
                        "code": subclass_data["code"],
                        "description": subclass_data["description"],
                        "parent": parent,
                        "tenant_id": self.tenant_id
                    }
                )
                
                if created:
                    created_count += 1
                    print(f"Created sub-classification: {sub_classification.name} under {parent.name}")
                
            except Exception as e:
                print(f"Error creating sub-classification {subclass_data['name']}: {e}")
        
        print(f"Created {created_count} item classifications")
        return created_count
    
    def generate_movement_types(self):
        """Generate movement types"""
        print("\nGenerating movement types...")
        
        movement_types = [
            {"name": "استلام بضاعة", "code": "RECEIPT", "is_inbound": True, "is_outbound": False, "description": "استلام بضاعة من المورد", "color": "#28a745", "icon": "arrow-down", "sequence": 10},
            {"name": "بيع", "code": "SALE", "is_inbound": False, "is_outbound": True, "description": "بيع بضاعة للعميل", "color": "#dc3545", "icon": "tag", "sequence": 20},
            {"name": "إرجاع من العميل", "code": "RETURN-IN", "is_inbound": True, "is_outbound": False, "description": "إرجاع بضاعة من العميل", "color": "#17a2b8", "icon": "reply", "sequence": 30},
            {"name": "إرجاع للمورد", "code": "RETURN-OUT", "is_inbound": False, "is_outbound": True, "description": "إرجاع بضاعة للمورد", "color": "#dc3545", "icon": "reply-all", "sequence": 40},
            {"name": "تحويل داخلي", "code": "TRANSFER", "is_inbound": True, "is_outbound": True, "description": "تحويل بين المخازن", "color": "#fd7e14", "icon": "exchange-alt", "sequence": 50},
            {"name": "تعديل مخزون", "code": "ADJUSTMENT", "is_inbound": True, "is_outbound": True, "description": "تعديل كمية المخزون", "color": "#6c757d", "icon": "edit", "sequence": 60},
            {"name": "استخدام في صيانة", "code": "MAINTENANCE", "is_inbound": False, "is_outbound": True, "description": "استخدام في عملية صيانة", "color": "#007bff", "icon": "wrench", "sequence": 70},
            {"name": "تالف", "code": "DAMAGED", "is_inbound": False, "is_outbound": True, "description": "إتلاف بضاعة", "color": "#dc3545", "icon": "trash-alt", "sequence": 80}
        ]
        
        created_count = 0
        for type_data in movement_types:
            try:
                movement_type, created = MovementType.objects.get_or_create(
                    name=type_data["name"],
                    defaults={
                        "code": type_data["code"],
                        "is_inbound": type_data["is_inbound"],
                        "is_outbound": type_data["is_outbound"],
                        "description": type_data["description"],
                        "color": type_data["color"],
                        "icon": type_data["icon"],
                        "sequence": type_data["sequence"],
                        "is_active": True,
                        "requires_reference": True,
                        "requires_approval": False,
                        "tenant_id": self.tenant_id
                    }
                )
                
                if created:
                    created_count += 1
                    print(f"Created movement type: {movement_type.name} ({movement_type.code})")
                
            except Exception as e:
                print(f"Error creating movement type {type_data['name']}: {e}")
        
        print(f"Created {created_count} movement types")
        return created_count
    
    def generate_items(self, count=50):
        """Generate inventory items"""
        print(f"\nGenerating {count} inventory items...")
        
        # Get units and classifications
        units = list(UnitOfMeasurement.objects.all())
        classifications = list(ItemClassification.objects.all())
        
        if not units:
            print("No units found. Please generate units first.")
            return 0
            
        if not classifications:
            print("No classifications found. Please generate classifications first.")
            return 0
        
        # Arabic item names by classification
        item_names_by_class = {
            "قطع غيار محرك": [
                "بستم", "شنبر", "عمود كامات", "صباب", "كاتينة توقيت", "زيتونة", "وش سلندر",
                "جوان وش سلندر", "بلوف", "دينامو", "مارش", "رأس محرك", "بلية عمود كامات"
            ],
            "فلاتر": [
                "فلتر زيت", "فلتر هواء", "فلتر بنزين", "فلتر تكييف", "فلتر تنقية هواء"
            ],
            "زيوت وسوائل": [
                "زيت محرك 5W-30", "زيت محرك 10W-40", "زيت محرك 15W-40", "زيت فرامل",
                "زيت باور", "سائل تبريد", "ماء مقطر", "سائل غسيل زجاج"
            ],
            "قطع غيار فرامل": [
                "تيل فرامل أمامي", "تيل فرامل خلفي", "اسطوانة فرامل", "ديسك فرامل أمامي",
                "ديسك فرامل خلفي", "خرطوم فرامل", "طنبورة فرامل"
            ],
            "إطارات وعجلات": [
                "إطار 185/65 R15", "إطار 195/55 R16", "إطار 205/55 R16", "إطار 215/55 R17",
                "جنط سبور 15 بوصة", "جنط سبور 16 بوصة", "غطاء جنط"
            ],
            "قطع غيار كهربائية": [
                "بطارية 60 أمبير", "بطارية 70 أمبير", "لمبة أمامية", "لمبة خلفية", "فيوز",
                "سلك كهرباء", "ريلاي", "مفتاح إضاءة", "دينامو", "منظم كهرباء"
            ]
        }
        
        created_count = 0
        
        for i in range(count):
            try:
                # Select a random classification
                classification = random.choice(classifications)
                
                # Get item names for this classification or parent classification
                if classification.name in item_names_by_class:
                    possible_names = item_names_by_class[classification.name]
                elif classification.parent and classification.parent.name in item_names_by_class:
                    possible_names = item_names_by_class[classification.parent.name]
                else:
                    # Fallback to engine parts if no matching classification found
                    possible_names = item_names_by_class["قطع غيار محرك"]
                
                # Generate a unique SKU
                sku = f"{classification.code[:3]}-{random.randint(1000, 9999)}"
                
                # Select a random name
                item_name = random.choice(possible_names)
                
                # Add some variations to ensure uniqueness
                if random.choice([True, False]):
                    brands = ["تويوتا", "نيسان", "هيونداي", "كيا", "شيفروليه", "ميتسوبيشي", "هوندا"]
                    item_name += f" {random.choice(brands)}"
                
                # Add "أصلي" or "صيني" or "تايواني" to some items
                if random.choice([True, False, False]):
                    item_name += f" {random.choice(['أصلي', 'صيني', 'تايواني', 'ياباني'])}"
                
                # Select a random unit
                unit = random.choice(units)
                
                # Generate prices
                unit_price = random.randint(50, 5000)
                
                # Min stock level
                min_stock_level = random.randint(5, 20)
                
                # Set attributes
                attributes = {
                    "brand": random.choice(["تويوتا", "نيسان", "هيونداي", "كيا", "شيفروليه", "ميتسوبيشي", "هوندا", "بوش", "دنسو"]),
                    "country_of_origin": random.choice(["اليابان", "كوريا", "الصين", "ألمانيا", "تايوان", "مصر"]),
                    "warranty_days": random.choice([30, 60, 90, 180, 365])
                }
                
                # Create the item
                item = Item.objects.create(
                    sku=sku,
                    name=item_name,
                    description=f"وصف {item_name}",
                    classification=classification,
                    unit_of_measurement=unit,
                    unit_price=unit_price,
                    min_stock_level=min_stock_level,
                    attributes=attributes,
                    tenant_id=self.tenant_id
                )
                
                created_count += 1
                if created_count % 10 == 0:
                    print(f"Created {created_count} items...")
                
            except Exception as e:
                print(f"Error creating item {i+1}: {e}")
        
        print(f"Successfully created {created_count} inventory items")
        return created_count
    
    def generate_vehicle_compatibilities(self, count=100):
        """Generate vehicle compatibility records"""
        print(f"\nGenerating {count} vehicle compatibility records...")
        
        # Get all items and vehicles
        items = list(Item.objects.all())
        
        try:
            vehicles = list(Vehicle.objects.all())
        except:
            vehicles = []
        
        if not items:
            print("No items found. Please generate items first.")
            return 0
            
        if not vehicles:
            print("No vehicles found in the database or Vehicle model not accessible.")
            print("Skipping vehicle compatibility generation.")
            return 0
        
        created_count = 0
        
        for i in range(count):
            try:
                # Select a random item and vehicle
                item = random.choice(items)
                vehicle = random.choice(vehicles)
                
                # Create compatibility
                compatibility, created = VehicleCompatibility.objects.get_or_create(
                    item=item,
                    make=vehicle.make,
                    model=vehicle.model,
                    defaults={
                        "year_from": vehicle.year - random.randint(0, 5),
                        "year_to": vehicle.year + random.randint(0, 5),
                        "notes": f"متوافق مع {vehicle.make} {vehicle.model}",
                        "tenant_id": self.tenant_id
                    }
                )
                
                if created:
                    created_count += 1
                    if created_count % 10 == 0:
                        print(f"Created {created_count} vehicle compatibilities...")
                
            except Exception as e:
                print(f"Error creating vehicle compatibility {i+1}: {e}")
        
        print(f"Successfully created {created_count} vehicle compatibility records")
        return created_count
    
    def generate_movements(self, count=200):
        """Generate inventory movements"""
        print(f"\nGenerating {count} inventory movements...")
        
        # Get all items, movement types and units
        items = list(Item.objects.all())
        movement_types = list(MovementType.objects.all())
        units = list(UnitOfMeasurement.objects.all())
        
        if not items:
            print("No items found. Please generate items first.")
            return 0
            
        if not movement_types:
            print("No movement types found. Please generate movement types first.")
            return 0
        
        created_count = 0
        
        for i in range(count):
            try:
                # Select a random item and movement type
                item = random.choice(items)
                movement_type = random.choice(movement_types)
                
                # Determine quantity based on movement type
                if movement_type.is_inbound and not movement_type.is_outbound:
                    quantity = random.randint(10, 100)
                elif movement_type.is_outbound and not movement_type.is_inbound:
                    quantity = -random.randint(1, 20)
                else:  # Both inbound and outbound (transfers or adjustments)
                    quantity = random.randint(-10, 10)
                
                # Generate reference number
                ref_prefix = movement_type.code[:3] if movement_type.code else "REF"
                reference = f"{ref_prefix}-{random.randint(10000, 99999)}"
                
                # Generate movement date (within the last year)
                days_ago = random.randint(1, 365)
                movement_date = timezone.now() - timedelta(days=days_ago)
                
                # Create the movement
                unit = item.unit_of_measurement if item.unit_of_measurement else random.choice(units)
                
                movement = Movement.objects.create(
                    item=item,
                    quantity=quantity,
                    unit_of_measurement=unit,
                    movement_type=movement_type,
                    reference=reference,
                    notes=f"{movement_type.name} - {item.name}",
                    created_at=movement_date,
                    updated_at=movement_date,
                    tenant_id=self.tenant_id
                )
                
                created_count += 1
                if created_count % 20 == 0:
                    print(f"Created {created_count} movements...")
                
            except Exception as e:
                print(f"Error creating movement {i+1}: {e}")
        
        print(f"Successfully created {created_count} inventory movements")
        return created_count
    
    def generate_operation_compatibilities(self, count=30):
        """Generate operation compatibility records"""
        print(f"\nGenerating {count} operation compatibility records...")
        
        # Get all items
        items = list(Item.objects.all())
        
        if not items:
            print("No items found. Please generate items first.")
            return 0
        
        # Sample operations for Egyptian context - using UUIDs for operation_type_id
        operations = [
            {"name": "تغيير زيت", "type_id": str(uuid.uuid4())},
            {"name": "صيانة فرامل", "type_id": str(uuid.uuid4())},
            {"name": "ضبط محرك", "type_id": str(uuid.uuid4())},
            {"name": "تغيير فلتر هواء", "type_id": str(uuid.uuid4())},
            {"name": "تغيير فلتر زيت", "type_id": str(uuid.uuid4())},
            {"name": "إصلاح تكييف", "type_id": str(uuid.uuid4())},
            {"name": "ضبط زوايا", "type_id": str(uuid.uuid4())},
            {"name": "موازنة إطارات", "type_id": str(uuid.uuid4())},
            {"name": "تغيير سير", "type_id": str(uuid.uuid4())},
            {"name": "فحص كمبيوتر", "type_id": str(uuid.uuid4())},
            {"name": "تنظيف رشاشات", "type_id": str(uuid.uuid4())},
            {"name": "ضبط دريكسيون", "type_id": str(uuid.uuid4())}
        ]
        
        created_count = 0
        
        for i in range(count):
            try:
                # Select a random item and operation
                item = random.choice(items)
                operation = random.choice(operations)
                
                # Create compatibility
                compatibility, created = OperationCompatibility.objects.get_or_create(
                    item=item,
                    operation_type_id=operation["type_id"],
                    defaults={
                        "is_required": random.choice([True, False]),
                        "is_common": random.choice([True, False]),
                        "default_quantity": random.randint(1, 5),
                        "notes": f"مطلوب لعملية {operation['name']}",
                        "tenant_id": self.tenant_id
                    }
                )
                
                if created:
                    created_count += 1
                    print(f"Created operation compatibility: {item.name} for {operation['name']}")
                
            except Exception as e:
                print(f"Error creating operation compatibility {i+1}: {e}")
        
        print(f"Successfully created {created_count} operation compatibility records")
        return created_count
    
    def run(self):
        """Run the complete inventory data generation process"""
        print("Starting inventory data generation...")
        
        try:
            # Generate basic catalog data with separate transactions
            with transaction.atomic():
                self.generate_units_of_measurement()
        except Exception as e:
            print(f"Error generating units of measurement: {e}")
        
        try:
            with transaction.atomic():
                self.generate_unit_conversions()
        except Exception as e:
            print(f"Error generating unit conversions: {e}")
        
        try:
            with transaction.atomic():
                self.generate_item_classifications()
        except Exception as e:
            print(f"Error generating item classifications: {e}")
        
        try:
            with transaction.atomic():
                self.generate_movement_types()
        except Exception as e:
            print(f"Error generating movement types: {e}")
        
        try:
            # Generate items
            with transaction.atomic():
                self.generate_items(50)
        except Exception as e:
            print(f"Error generating items: {e}")
        
        try:
            # Generate relationships
            with transaction.atomic():
                self.generate_vehicle_compatibilities(100)
        except Exception as e:
            print(f"Error generating vehicle compatibilities: {e}")
        
        try:
            with transaction.atomic():
                self.generate_operation_compatibilities(30)
        except Exception as e:
            print(f"Error generating operation compatibilities: {e}")
        
        # For movements, let's generate them in smaller batches to avoid FK issues
        try:
            with transaction.atomic():
                self.generate_movements(50)  # Reduced from 200
            print("Generated first batch of movements")
        except Exception as e:
            print(f"Error generating first batch of movements: {e}")
        
        try:
            with transaction.atomic():
                self.generate_movements(50)
            print("Generated second batch of movements")
        except Exception as e:
            print(f"Error generating second batch of movements: {e}")
        
        try:
            with transaction.atomic():
                self.generate_movements(50)
            print("Generated third batch of movements")
        except Exception as e:
            print(f"Error generating third batch of movements: {e}")
        
        try:
            with transaction.atomic():
                self.generate_movements(50)
            print("Generated fourth batch of movements")
        except Exception as e:
            print(f"Error generating fourth batch of movements: {e}")
        
        print("\nInventory data generation complete!")

if __name__ == "__main__":
    generator = InventoryDataGenerator()
    generator.run() 