from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from notifications.models import Notification, WebhookEndpoint, WebhookDelivery


@admin.register(Notification)
class NotificationAdmin(admin.ModelAdmin):
    list_display = ('title', 'level', 'created_at', 'read_at', 'tenant_id')
    search_fields = ('title', 'message')
    list_filter = ('level', 'created_at', 'read_at', 'tenant_id')
    readonly_fields = ('created_at',)
    date_hierarchy = 'created_at'


@admin.register(WebhookEndpoint)
class WebhookEndpointAdmin(admin.ModelAdmin):
    list_display = ('name', 'url', 'is_active', 'tenant_id')
    list_filter = ('is_active', 'subscribe_item_created', 'subscribe_stock_low', 'subscribe_movement_created')
    search_fields = ('name', 'url', 'description')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        (None, {
            'fields': ('name', 'url', 'description', 'is_active', 'tenant_id')
        }),
        (_('Security'), {
            'fields': ('secret_key',),
            'classes': ('collapse',),
        }),
        (_('Subscriptions'), {
            'fields': ('subscribe_item_created', 'subscribe_item_updated', 
                       'subscribe_stock_low', 'subscribe_movement_created'),
        }),
    )


@admin.register(WebhookDelivery)
class WebhookDeliveryAdmin(admin.ModelAdmin):
    list_display = ('event_type', 'endpoint', 'is_success', 'response_status', 'attempts', 'created_at', 'tenant_id')
    list_filter = ('event_type', 'is_success', 'attempts', 'created_at')
    search_fields = ('endpoint__name', 'event_type', 'response_body')
    readonly_fields = ('endpoint', 'event_type', 'payload', 'response_status', 
                       'response_body', 'is_success', 'attempts', 'created_at', 'tenant_id')
    date_hierarchy = 'created_at'
    
    def has_add_permission(self, request):
        return False
    
    def has_change_permission(self, request, obj=None):
        return False
