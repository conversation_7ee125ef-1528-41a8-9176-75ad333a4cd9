import os
import sys
import django
import random
import uuid
from datetime import datetime

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

# Import models
from django.utils import timezone
from setup.models import Vehicle, Customer, ServiceCenter
from setup.signals import vehicle_saved  # Import the signal that's causing the issue

# Egyptian vehicle data
MAKES_MODELS = [
    {"make": "تويوتا", "models": ["كورولا", "لاند كروزر", "كامري"]},
    {"make": "هيونداي", "models": ["النترا", "اكسنت", "توسان"]},
    {"make": "نيسان", "models": ["صني", "سنترا", "قشقاي"]},
    {"make": "كيا", "models": ["سيراتو", "سبورتاج", "بيكانتو"]}
]

# Add owner_name property to Vehicle model
def add_owner_name_property():
    """Add owner_name property to Vehicle model to fix signal issue"""
    
    # Define a property that returns the owner's name
    def owner_name(self):
        if self.owner:
            return str(self.owner)
        return "Unknown"
    
    # Add the property to the Vehicle class
    Vehicle.owner_name = property(owner_name)
    
    print("Added owner_name property to Vehicle model")

def create_vehicles_with_patch():
    print("Creating vehicles with monkey patch...")
    
    # Apply our monkey patch
    add_owner_name_property()
    
    # Get a tenant_id from an existing object
    tenant_id = None
    try:
        # Try to get tenant_id from an existing customer
        customer = Customer.objects.first()
        if customer:
            tenant_id = customer.tenant_id
            print(f"Using tenant_id from customer: {tenant_id}")
    except Exception as e:
        print(f"Error getting tenant_id: {e}")
    
    if not tenant_id:
        tenant_id = uuid.uuid4()
        print(f"Created new tenant_id: {tenant_id}")
    
    # Get customers and service centers
    customers = list(Customer.objects.all())
    service_centers = list(ServiceCenter.objects.all())
    
    if not customers:
        print("No customers found. Please generate customers first.")
        return
        
    if not service_centers:
        print("No service centers found. Please generate service centers first.")
        return
    
    # Create vehicles
    vehicles_created = 0
    total_to_create = 20
    
    current_year = datetime.now().year
    
    for i in range(total_to_create):
        try:
            # Select random data
            make_model = random.choice(MAKES_MODELS)
            make = make_model["make"]
            model = random.choice(make_model["models"])
            owner = random.choice(customers)
            service_center = random.choice(service_centers)
            
            # Generate random vehicle details
            year = random.randint(current_year - 10, current_year)
            vin = f"EGT{random.randint(100000, 999999)}"
            license_plate = f"{random.randint(100, 999)} {random.choice('أبتثجحخدذرزسشصضطظعغفقكلمنهوي')}{random.choice('أبتثجحخدذرزسشصضطظعغفقكلمنهوي')}{random.choice('أبتثجحخدذرزسشصضطظعغفقكلمنهوي')}"
            
            # Create the vehicle directly using Django ORM
            v = Vehicle()
            v.id = uuid.uuid4()
            v.tenant_id = tenant_id
            v.make = make
            v.model = model
            v.year = year
            v.vin = vin
            v.license_plate = license_plate
            v.color = "أبيض"
            v.owner = owner
            v.service_center = service_center
            
            # Set purchase_date and warranty_end_date
            purchase_date = datetime(year, random.randint(1, 12), random.randint(1, 28)).date()
            warranty_end_date = datetime(year + 3, 1, 1).date()
            v.purchase_date = purchase_date
            v.warranty_end_date = warranty_end_date
            
            # Set notes and attributes
            v.notes = f"سيارة {make} {model} موديل {year}"
            v.attributes = {}
            
            # Test that our monkey patch works
            print(f"Vehicle owner name: {v.owner_name}")
            
            # Save the vehicle
            v.save()
            
            vehicles_created += 1
            print(f"Created vehicle {vehicles_created}: {make} {model}")
            
        except Exception as e:
            print(f"Error creating vehicle {i+1}: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\nCreated {vehicles_created} out of {total_to_create} vehicles.")

if __name__ == "__main__":
    create_vehicles_with_patch() 