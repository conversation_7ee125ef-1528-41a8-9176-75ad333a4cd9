import os
import sys
import django
import random
from datetime import datetime, timedelta, date
from django.db import transaction
from faker import Faker
import uuid
from decimal import Decimal

# Add the parent directory to sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

# Import necessary models
from setup.models import ServiceCenter, Company, Customer, Vehicle
from inventory.models import Item, OperationCompatibility
from work_orders.models import WorkOrderType, WorkOrder
from billing.models import (
    CustomerPreference, InsuranceCompany, InsurancePolicy, WarrantyType, 
    VehicleWarranty, DiscountType, PaymentMethod, Invoice, InvoiceItem, Payment
)
from purchases.models import Supplier, PurchaseOrder, PurchaseOrderItem, PurchaseReceipt, PurchaseReceiptItem
from django.utils import timezone
from django.contrib.auth.models import User

# Initialize Faker
fake = Faker('ar_EG')  # Using Egyptian Arabic locale for Egyptian market specifics

class AdditionalDemoDataGenerator:
    """
    Generate additional demo data for Operation Compatibilities, 
    Billing, Purchases, and Reports tables.
    """
    
    def __init__(self):
        # Get the first tenant ID from existing data or create a new one
        try:
            tenant_item = Item.objects.first()
            if tenant_item:
                self.tenant_id = tenant_item.tenant_id
            else:
                self.tenant_id = str(uuid.uuid4())
        except:
            self.tenant_id = str(uuid.uuid4())
            
        print(f"Using tenant ID: {self.tenant_id}")
        
        # Check if we have a user, create one if not
        if not User.objects.filter(is_superuser=True).exists():
            User.objects.create_superuser('admin', '<EMAIL>', 'admin')
            print("Created superuser 'admin' with password 'admin'")
    
    @transaction.atomic
    def generate_operation_compatibilities(self):
        """Generate Operation Compatibilities data."""
        print("\nGenerating Operation Compatibilities data...")
        
        # Get all items and work order types
        items = list(Item.objects.filter(tenant_id=self.tenant_id))
        
        # Create WorkOrderTypes if they don't exist
        work_order_types_data = [
            {"name": "صيانة عادية", "description": "صيانة دورية عادية"},
            {"name": "إصلاح محرك", "description": "إصلاح أعطال المحرك"},
            {"name": "ضبط كهرباء", "description": "ضبط وإصلاح النظام الكهربائي"},
            {"name": "إصلاح فرامل", "description": "إصلاح وضبط نظام الفرامل"},
            {"name": "فحص شامل", "description": "فحص شامل للسيارة"},
            {"name": "تغيير زيت", "description": "تغيير زيت المحرك والفلاتر"},
            {"name": "إصلاح تكييف", "description": "إصلاح وشحن نظام التكييف"},
            {"name": "إصلاح عفشة", "description": "إصلاح نظام التعليق"},
            {"name": "تصليح هيكل", "description": "إصلاح وصيانة هيكل السيارة"}
        ]
        
        work_order_types = []
        for type_data in work_order_types_data:
            wo_type, created = WorkOrderType.objects.get_or_create(
                tenant_id=self.tenant_id,
                name=type_data["name"],
                defaults={
                    "description": type_data["description"]
                }
            )
            work_order_types.append(wo_type)
            if created:
                print(f"Created Work Order Type: {wo_type.name}")
        
        # Create Operation Compatibilities between items and work order types
        compatibilities_created = 0
        
        # Define which item classifications go with which work order types
        # We'll use the name instead of code since we don't have that field
        type_classifications = {
            'صيانة عادية': ['FILTER', 'FLUID', 'ENG'],  # Regular maintenance
            'إصلاح محرك': ['ENG'],  # Engine repair
            'ضبط كهرباء': ['ELEC'],  # Electrical adjustment
            'إصلاح فرامل': ['BRAKE'],  # Brake repair
            'فحص شامل': ['ENG', 'ELEC', 'BRAKE', 'SUSP'],  # Full diagnostics
            'تغيير زيت': ['FLUID', 'FILTER'],  # Oil change
            'إصلاح تكييف': ['ELEC'],  # AC repair
            'إصلاح عفشة': ['SUSP'],  # Suspension repair
            'تصليح هيكل': ['BODY']  # Body repair
        }
        
        # Map each work order type to relevant items based on classification
        for wo_type in work_order_types:
            # Find which classifications are relevant for this type
            relevant_class_codes = type_classifications.get(wo_type.name, [])
            
            # Get items that might have these classifications
            relevant_items = []
            for item in items:
                # Check if the item has a classification that matches our target codes
                if (hasattr(item, 'classification') and 
                    item.classification and 
                    hasattr(item.classification, 'code') and 
                    item.classification.code in relevant_class_codes):
                    relevant_items.append(item)
            
            # If we couldn't find matching items, use a random selection
            if not relevant_items:
                relevant_items = random.sample(items, min(10, len(items)))
            
            # Create compatibilities
            for item in relevant_items:
                compatibility, created = OperationCompatibility.objects.get_or_create(
                    tenant_id=self.tenant_id,
                    item=item,
                    operation_type=wo_type,
                    defaults={
                        'is_required': random.choice([True, False]),
                        'is_common': random.choice([True, False]),
                        'default_quantity': Decimal(str(round(random.uniform(1, 5), 2))),
                        'notes': fake.sentence()
                    }
                )
                if created:
                    compatibilities_created += 1
                    
        print(f"Created {compatibilities_created} Operation Compatibilities")
    
    @transaction.atomic
    def generate_billing_data(self):
        """Generate billing data with Egyptian market specifics."""
        print("\nGenerating billing data...")
        
        # 1. Create Customer Preferences
        customers = list(Customer.objects.filter(tenant_id=self.tenant_id))
        if not customers:
            print("No customers found. Please run generate_setup_data first.")
            return
        
        preferences_created = 0
        for customer in customers:
            preference, created = CustomerPreference.objects.get_or_create(
                tenant_id=self.tenant_id,
                customer=customer,
                defaults={
                    'status': random.choice(['regular', 'vip', 'gold', 'platinum']),
                    'payment_terms': random.choice(['cash', 'credit_30', 'credit_60', 'credit_90']),
                    'credit_limit': Decimal(str(random.randint(5000, 50000))),
                    'default_discount_percentage': Decimal(str(random.randint(0, 10))),
                    'send_sms_notifications': random.choice([True, False]),
                    'send_email_notifications': random.choice([True, False]),
                    'special_instructions': fake.paragraph(nb_sentences=2) if random.choice([True, False]) else ''
                }
            )
            if created:
                preferences_created += 1
        
        print(f"Created {preferences_created} Customer Preferences")
        
        # 2. Create Insurance Companies (Egyptian insurance companies)
        insurance_companies_data = [
            {'name': 'شركة مصر للتأمين', 'code': 'MISR', 'contact_person': fake.name(), 'phone': fake.phone_number()},
            {'name': 'المجموعة العربية المصرية للتأمين', 'code': 'AMIG', 'contact_person': fake.name(), 'phone': fake.phone_number()},
            {'name': 'شركة الدلتا للتأمين', 'code': 'DELTA', 'contact_person': fake.name(), 'phone': fake.phone_number()},
            {'name': 'الشركة المصرية للتأمين التكافلي', 'code': 'ETMCT', 'contact_person': fake.name(), 'phone': fake.phone_number()},
            {'name': 'شركة قناة السويس للتأمين', 'code': 'SUEZ', 'contact_person': fake.name(), 'phone': fake.phone_number()},
            {'name': 'شركة متلايف مصر', 'code': 'METLIFE', 'contact_person': fake.name(), 'phone': fake.phone_number()},
            {'name': 'شركة أليانز مصر', 'code': 'ALLIANZ', 'contact_person': fake.name(), 'phone': fake.phone_number()},
            {'name': 'مصر للتأمين التكافلي', 'code': 'MISR-T', 'contact_person': fake.name(), 'phone': fake.phone_number()},
        ]
        
        insurance_companies = []
        for data in insurance_companies_data:
            company, created = InsuranceCompany.objects.get_or_create(
                tenant_id=self.tenant_id,
                code=data['code'],
                defaults={
                    'name': data['name'],
                    'contact_person': data['contact_person'],
                    'phone': data['phone'],
                    'email': f"contact@{data['code'].lower()}.com",
                    'address': fake.address(),
                    'contract_number': f"INS-{fake.random_number(digits=6)}",
                    'contract_start_date': fake.date_between(start_date='-2y', end_date='-1y'),
                    'contract_end_date': fake.date_between(start_date='+1y', end_date='+3y'),
                    'approval_required': random.choice([True, False]),
                    'standard_approval_time_hours': random.choice([24, 48, 72]),
                    'is_active': True
                }
            )
            insurance_companies.append(company)
            if created:
                print(f"Created Insurance Company: {company.name}")
        
        # 3. Create Insurance Policies
        vehicles = list(Vehicle.objects.filter(tenant_id=self.tenant_id))
        if not vehicles:
            print("No vehicles found. Please run generate_setup_data first.")
            return
        
        policies_created = 0
        for i in range(min(len(vehicles), 50)):  # Create policies for up to 50 vehicles
            vehicle = vehicles[i]
            company = random.choice(insurance_companies)
            
            # Find the customer for this vehicle
            if hasattr(vehicle, 'owner') and vehicle.owner:
                customer = vehicle.owner
            elif hasattr(vehicle, 'customer') and vehicle.customer:
                customer = vehicle.customer
            else:
                customer = random.choice(customers)
                
            # Generate policy number based on company code
            policy_number = f"{company.code}-{fake.random_number(digits=6)}"
            
            start_date = fake.date_between(start_date='-2y', end_date='-6m')
            end_date = start_date + timedelta(days=365)  # 1 year policy
            
            policy, created = InsurancePolicy.objects.get_or_create(
                tenant_id=self.tenant_id,
                vehicle=vehicle,
                policy_number=policy_number,
                defaults={
                    'insurance_company': company,
                    'customer': customer,
                    'policy_type': random.choice(['comprehensive', 'third_party', 'partial']),
                    'start_date': start_date,
                    'end_date': end_date,
                    'coverage_amount': Decimal(str(random.randint(50000, 500000))),
                    'deductible': Decimal(str(random.randint(1000, 10000))),
                    'is_active': end_date > timezone.now().date(),
                    'notes': fake.paragraph(nb_sentences=1) if random.choice([True, False]) else ''
                }
            )
            if created:
                policies_created += 1
        
        print(f"Created {policies_created} Insurance Policies")
        
        # 4. Create Warranty Types
        warranty_types_data = [
            {'name': 'ضمان المصنع الأساسي', 'description': 'ضمان أساسي من المصنع', 'parts_covered': 'كافة الأجزاء', 'labor_covered': True, 'default_duration_months': 36, 'default_mileage_limit': 100000},
            {'name': 'ضمان موتور ونقل حركة', 'description': 'ضمان المحرك وناقل الحركة', 'parts_covered': 'المحرك وناقل الحركة', 'labor_covered': True, 'default_duration_months': 60, 'default_mileage_limit': 150000},
            {'name': 'ضمان ضد الصدأ', 'description': 'ضمان ضد الصدأ والتآكل', 'parts_covered': 'هيكل السيارة الخارجي', 'labor_covered': False, 'default_duration_months': 72, 'default_mileage_limit': None},
            {'name': 'ضمان الخدمة', 'description': 'ضمان على الخدمات المقدمة', 'parts_covered': 'الأجزاء المستبدلة', 'labor_covered': True, 'default_duration_months': 6, 'default_mileage_limit': 10000},
            {'name': 'ضمان قطع الغيار', 'description': 'ضمان قطع الغيار فقط', 'parts_covered': 'قطع الغيار المستبدلة', 'labor_covered': False, 'default_duration_months': 12, 'default_mileage_limit': 20000}
        ]
        
        warranty_types = []
        for data in warranty_types_data:
            warranty_type, created = WarrantyType.objects.get_or_create(
                tenant_id=self.tenant_id,
                name=data['name'],
                defaults=data
            )
            warranty_types.append(warranty_type)
            if created:
                print(f"Created Warranty Type: {warranty_type.name}")
        
        # 5. Create Vehicle Warranties
        warranties_created = 0
        for i in range(min(len(vehicles), 40)):  # Create warranties for up to 40 vehicles
            vehicle = vehicles[i]
            warranty_type = random.choice(warranty_types)
            
            warranty_number = f"WAR-{fake.random_number(digits=8)}"
            start_date = fake.date_between(start_date='-3y', end_date='-1m')
            end_date = start_date + timedelta(days=30*warranty_type.default_duration_months)
            
            warranty, created = VehicleWarranty.objects.get_or_create(
                tenant_id=self.tenant_id,
                vehicle=vehicle,
                warranty_type=warranty_type,
                warranty_number=warranty_number,
                defaults={
                    'provider': random.choice(['manufacturer', 'dealer', 'third_party', 'extended']),
                    'provider_name': fake.company(),
                    'start_date': start_date,
                    'end_date': end_date,
                    'mileage_limit': warranty_type.default_mileage_limit,
                    'is_active': end_date > timezone.now().date(),
                    'notes': fake.paragraph(nb_sentences=1) if random.choice([True, False]) else ''
                }
            )
            if created:
                warranties_created += 1
        
        print(f"Created {warranties_created} Vehicle Warranties")
        
        # 6. Create Discount Types
        discount_types_data = [
            {'name': 'خصم الشركات', 'description': 'خصم خاص للشركات', 'discount_method': 'percentage', 'percentage': Decimal('15.00')},
            {'name': 'خصم السوق', 'description': 'خصم تسويقي خاص', 'discount_method': 'percentage', 'percentage': Decimal('10.00')},
            {'name': 'خصم العميل الدائم', 'description': 'خصم للعملاء الدائمين', 'discount_method': 'percentage', 'percentage': Decimal('5.00')},
            {'name': 'خصم قطع الغيار', 'description': 'خصم على قطع الغيار فقط', 'discount_method': 'percentage', 'percentage': Decimal('7.00')},
            {'name': 'خصم نقدي', 'description': 'خصم للدفع النقدي', 'discount_method': 'fixed', 'fixed_amount': Decimal('100.00')},
            {'name': 'خصم أول خدمة', 'description': 'خصم للعملاء الجدد', 'discount_method': 'percentage', 'percentage': Decimal('20.00')},
            {'name': 'خصم موسمي', 'description': 'خصم موسمي', 'discount_method': 'percentage', 'percentage': Decimal('12.00')}
        ]
        
        discount_types = []
        for data in discount_types_data:
            if data['discount_method'] == 'percentage':
                data['fixed_amount'] = Decimal('0.00')
            else:
                data['percentage'] = Decimal('0.00')
                
            discount_type, created = DiscountType.objects.get_or_create(
                tenant_id=self.tenant_id,
                name=data['name'],
                defaults={
                    **data,
                    'valid_from': timezone.now().date() - timedelta(days=30),
                    'valid_to': timezone.now().date() + timedelta(days=365),
                    'min_order_amount': Decimal(str(random.randint(0, 500))),
                    'max_discount_amount': Decimal(str(random.randint(1000, 5000))) if random.choice([True, False]) else None,
                    'is_active': True,
                    'apply_to_parts': random.choice([True, False]),
                    'apply_to_labor': random.choice([True, False])
                }
            )
            discount_types.append(discount_type)
            if created:
                print(f"Created Discount Type: {discount_type.name}")
        
        # 7. Create Payment Methods
        payment_methods_data = [
            {'name': 'نقدي', 'payment_type': 'cash', 'processing_fee_percentage': Decimal('0.00')},
            {'name': 'فيزا', 'payment_type': 'credit_card', 'processing_fee_percentage': Decimal('2.50')},
            {'name': 'ماستر كارد', 'payment_type': 'credit_card', 'processing_fee_percentage': Decimal('2.50')},
            {'name': 'تحويل بنكي', 'payment_type': 'bank_transfer', 'processing_fee_percentage': Decimal('1.00')},
            {'name': 'شيك', 'payment_type': 'check', 'processing_fee_percentage': Decimal('0.50')},
            {'name': 'محفظة إلكترونية', 'payment_type': 'digital_wallet', 'processing_fee_percentage': Decimal('1.50')},
            {'name': 'فودافون كاش', 'payment_type': 'digital_wallet', 'processing_fee_percentage': Decimal('1.25')},
            {'name': 'تأمين', 'payment_type': 'insurance', 'processing_fee_percentage': Decimal('0.00')}
        ]
        
        payment_methods = []
        for data in payment_methods_data:
            payment_method, created = PaymentMethod.objects.get_or_create(
                tenant_id=self.tenant_id,
                name=data['name'],
                defaults={
                    'payment_type': data['payment_type'],
                    'description': f"طريقة دفع عن طريق {data['name']}",
                    'processing_fee_percentage': data['processing_fee_percentage'],
                    'processing_fee_fixed': Decimal('0.00'),
                    'is_active': True,
                    'requires_approval': data['payment_type'] in ['check', 'insurance']
                }
            )
            payment_methods.append(payment_method)
            if created:
                print(f"Created Payment Method: {payment_method.name}")
    
    @transaction.atomic
    def generate_purchases_data(self):
        """Generate purchases data with Egyptian market specifics."""
        print("\nGenerating purchases data...")
        
        # 1. Create Suppliers (Egyptian suppliers)
        suppliers_data = [
            {'name': 'شركة الأهرام لقطع غيار السيارات', 'email': '<EMAIL>', 'phone': fake.phone_number()},
            {'name': 'توكيل تويوتا مصر', 'email': '<EMAIL>', 'phone': fake.phone_number()},
            {'name': 'مستورد قطع غيار كوري', 'email': '<EMAIL>', 'phone': fake.phone_number()},
            {'name': 'شركة النيل للزيوت', 'email': '<EMAIL>', 'phone': fake.phone_number()},
            {'name': 'المصرية لقطع غيار الفرامل', 'email': '<EMAIL>', 'phone': fake.phone_number()},
            {'name': 'توكيل هيونداي مصر', 'email': '<EMAIL>', 'phone': fake.phone_number()},
            {'name': 'شركة المصريين للإطارات', 'email': '<EMAIL>', 'phone': fake.phone_number()},
            {'name': 'جيد للبطاريات', 'email': '<EMAIL>', 'phone': fake.phone_number()},
            {'name': 'مستورد قطع غيار أوروبي', 'email': '<EMAIL>', 'phone': fake.phone_number()},
            {'name': 'الدلتا للفلاتر', 'email': '<EMAIL>', 'phone': fake.phone_number()}
        ]
        
        suppliers = []
        for data in suppliers_data:
            supplier, created = Supplier.objects.get_or_create(
                tenant_id=self.tenant_id,
                name=data['name'],
                defaults={
                    'email': data['email'],
                    'phone': data['phone'],
                    'address': fake.address(),
                    'is_active': True
                }
            )
            suppliers.append(supplier)
            if created:
                print(f"Created Supplier: {supplier.name}")
        
        # 2. Create Purchase Orders and Items
        items = list(Item.objects.filter(tenant_id=self.tenant_id))
        if not items:
            print("No inventory items found. Please run generate_inventory_data first.")
            return
        
        purchase_orders_created = 0
        purchase_receipts_created = 0
        for i in range(20):  # Create 20 purchase orders
            supplier = random.choice(suppliers)
            order_date = fake.date_between(start_date='-6m', end_date='-1d')
            expected_delivery = order_date + timedelta(days=random.randint(3, 14))
            
            # Generate an order number based on supplier and date
            order_number = f"PO-{supplier.name[:3]}-{order_date.strftime('%y%m%d')}-{fake.random_number(digits=3)}"
            
            purchase_order, created = PurchaseOrder.objects.get_or_create(
                tenant_id=self.tenant_id,
                order_number=order_number,
                defaults={
                    'supplier': supplier,
                    'order_date': order_date,
                    'expected_delivery_date': expected_delivery,
                    'status': random.choice(['draft', 'sent', 'confirmed', 'received', 'cancelled']),
                    'notes': fake.paragraph(nb_sentences=1) if random.choice([True, False]) else '',
                    'total_amount': Decimal('0.00')  # Will be updated as items are added
                }
            )
            
            if created:
                purchase_orders_created += 1
                
                # Add items to purchase order
                item_count = random.randint(3, 10)
                selected_items = random.sample(items, item_count)
                
                for item in selected_items:
                    quantity = random.randint(5, 50)
                    unit_price = Decimal(str(round(random.uniform(10, 1000), 2)))
                    
                    PurchaseOrderItem.objects.create(
                        tenant_id=self.tenant_id,
                        purchase_order=purchase_order,
                        item=item,
                        quantity=quantity,
                        unit_price=unit_price
                    )
                
                # Update total amount
                purchase_order.update_total_amount()
                
                # Create a receipt for this order (if status is 'received')
                if purchase_order.status == 'received':
                    receipt_date = expected_delivery + timedelta(days=random.randint(0, 3))
                    receipt_number = f"RCV-{order_number[3:]}"
                    
                    receipt = PurchaseReceipt.objects.create(
                        tenant_id=self.tenant_id,
                        receipt_number=receipt_number,
                        purchase_order=purchase_order,
                        receipt_date=receipt_date,
                        notes=f"استلام طلب شراء {purchase_order.order_number}"
                    )
                    purchase_receipts_created += 1
                    
                    # Create receipt items for all order items
                    for order_item in purchase_order.items.all():
                        # Sometimes receive less than ordered
                        received_quantity = order_item.quantity
                        if random.random() < 0.2:  # 20% chance of partial receipt
                            received_quantity = Decimal(str(round(float(order_item.quantity) * random.uniform(0.7, 0.95), 2)))
                            
                        PurchaseReceiptItem.objects.create(
                            tenant_id=self.tenant_id,
                            purchase_receipt=receipt,
                            purchase_order_item=order_item,
                            quantity=received_quantity
                        )
        
        print(f"Created {purchase_orders_created} Purchase Orders")
        print(f"Created {purchase_receipts_created} Purchase Receipts")
    
    def run(self):
        """Run all data generation steps."""
        self.generate_operation_compatibilities()
        self.generate_billing_data()
        self.generate_purchases_data()
        print("\nCompleted additional demo data generation")

def main():
    """Main entry point."""
    generator = AdditionalDemoDataGenerator()
    generator.run()

if __name__ == "__main__":
    main() 