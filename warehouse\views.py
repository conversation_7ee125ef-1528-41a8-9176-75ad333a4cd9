from django.shortcuts import render
from django.utils.translation import gettext_lazy as _
from django.contrib.auth.decorators import login_required
from django.db.models import Count, Sum, F
from warehouse.models import Location, BinLocation, Transfer, TransferOrder, ItemLocation


@login_required
def dashboard(request):
    """
    Warehouse dashboard with overview of locations, transfers, and inventory
    """
    # Get tenant_id from session or context
    tenant_id = request.session.get('tenant_id')
    
    # Check for selected entities that might determine tenant context
    selected_franchise_id = request.GET.get('franchise_id')
    selected_company_id = request.GET.get('company_id')
    selected_service_center_id = request.GET.get('service_center_id')
    
    # Use empty filters if no tenant_id is found
    location_filter = {}
    if tenant_id:
        location_filter['tenant_id'] = tenant_id
    elif selected_franchise_id:
        # Assuming franchise_id might be used as tenant_id in some cases
        location_filter['tenant_id'] = selected_franchise_id
    
    # Get basic counts for dashboard stats
    locations_count = Location.objects.filter(**location_filter).count()
    bin_locations_count = BinLocation.objects.filter(**location_filter).count()
    transfers_count = Transfer.objects.filter(**location_filter).count()
    transfer_orders_count = TransferOrder.objects.filter(**location_filter).count()
    
    # Calculate storage utilization (placeholder - actual calculation would depend on business logic)
    storage_utilization = 0
    if locations_count > 0:
        # Example calculation: occupied items / total capacity
        total_items = ItemLocation.objects.filter(**location_filter).count()
        total_capacity = Location.objects.filter(
            **location_filter,
            max_items__isnull=False
        ).aggregate(Sum('max_items'))['max_items__sum'] or 1
        
        storage_utilization = min(round((total_items / total_capacity) * 100), 100)
    
    # Get recent locations (limit to 5)
    locations = Location.objects.filter(
        **location_filter
    ).select_related('location_type').order_by('-created_at')[:5]
    
    # Get recent transfers (limit to 5)
    transfers = Transfer.objects.filter(
        **location_filter
    ).select_related('source_location', 'destination_location').order_by('-created_at')[:5]
    
    # Get low stock items
    low_stock_items = ItemLocation.objects.filter(
        **location_filter,
        min_stock__isnull=False
    ).filter(quantity__lt=F('min_stock')).select_related('item', 'location')[:10]
    
    # Sample warehouse activities (in a real app, this would come from an activity log)
    warehouse_activities = []
    
    context = {
        'locations_count': locations_count,
        'bin_locations_count': bin_locations_count,
        'transfers_count': transfers_count,
        'transfer_orders_count': transfer_orders_count,
        'storage_utilization': storage_utilization,
        'locations': locations,
        'transfers': transfers,
        'low_stock_items': low_stock_items,
        'warehouse_activities': warehouse_activities,
    }
    
    return render(request, 'warehouse/dashboard.html', context)


@login_required
def location_list(request):
    """
    List all warehouse locations
    """
    # Get tenant_id from session or context
    tenant_id = request.session.get('tenant_id')
    
    # Check for selected entities that might determine tenant context
    selected_franchise_id = request.GET.get('franchise_id')
    selected_company_id = request.GET.get('company_id')
    
    # Use empty filters if no tenant_id is found
    location_filter = {}
    if tenant_id:
        location_filter['tenant_id'] = tenant_id
    elif selected_franchise_id:
        # Assuming franchise_id might be used as tenant_id in some cases
        location_filter['tenant_id'] = selected_franchise_id
    
    locations = Location.objects.filter(
        **location_filter
    ).select_related('location_type', 'parent').order_by('name')
    
    context = {
        'locations': locations,
    }
    
    return render(request, 'warehouse/location_list.html', context)


@login_required
def transfer_list(request):
    """
    List all transfers
    """
    # Get tenant_id from session or context
    tenant_id = request.session.get('tenant_id')
    
    # Check for selected entities that might determine tenant context
    selected_franchise_id = request.GET.get('franchise_id')
    selected_company_id = request.GET.get('company_id')
    
    # Use empty filters if no tenant_id is found
    location_filter = {}
    if tenant_id:
        location_filter['tenant_id'] = tenant_id
    elif selected_franchise_id:
        # Assuming franchise_id might be used as tenant_id in some cases
        location_filter['tenant_id'] = selected_franchise_id
    
    transfers = Transfer.objects.filter(
        **location_filter
    ).select_related('source_location', 'destination_location').order_by('-created_at')
    
    context = {
        'transfers': transfers,
    }
    
    return render(request, 'warehouse/transfer_list.html', context)


@login_required
def transfer_order_list(request):
    """
    List all transfer orders
    """
    # Get tenant_id from session or context
    tenant_id = request.session.get('tenant_id')
    
    # Check for selected entities that might determine tenant context
    selected_franchise_id = request.GET.get('franchise_id')
    selected_company_id = request.GET.get('company_id')
    
    # Use empty filters if no tenant_id is found
    location_filter = {}
    if tenant_id:
        location_filter['tenant_id'] = tenant_id
    elif selected_franchise_id:
        # Assuming franchise_id might be used as tenant_id in some cases
        location_filter['tenant_id'] = selected_franchise_id
    
    transfer_orders = TransferOrder.objects.filter(
        **location_filter
    ).select_related('source_location', 'destination_location').order_by('-created_at')
    
    context = {
        'transfer_orders': transfer_orders,
    }
    
    return render(request, 'warehouse/transfer_order_list.html', context)


@login_required
def low_stock_items(request):
    """
    List all items with low stock
    """
    # Get tenant_id from session or context
    tenant_id = request.session.get('tenant_id')
    
    # Check for selected entities that might determine tenant context
    selected_franchise_id = request.GET.get('franchise_id')
    selected_company_id = request.GET.get('company_id')
    
    # Use empty filters if no tenant_id is found
    location_filter = {}
    if tenant_id:
        location_filter['tenant_id'] = tenant_id
    elif selected_franchise_id:
        # Assuming franchise_id might be used as tenant_id in some cases
        location_filter['tenant_id'] = selected_franchise_id
    
    low_stock_items = ItemLocation.objects.filter(
        **location_filter,
        min_stock__isnull=False
    ).filter(quantity__lt=F('min_stock')).select_related('item', 'location')
    
    context = {
        'low_stock_items': low_stock_items,
    }
    
    return render(request, 'warehouse/low_stock_items.html', context)
