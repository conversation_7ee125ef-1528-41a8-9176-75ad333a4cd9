@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import Flowbite */
@import 'flowbite/dist/flowbite.css';

/* Import Google Tajawal Arabic Font */
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap');

/* RTL and Arabic language support */
@layer base {
  html[dir="rtl"] {
    font-family: 'Tajawal', sans-serif;
  }
  
  body {
    @apply transition-colors duration-200;
  }
}

/* Custom RTL utility classes */
@layer utilities {
  .rtl-flip {
    @apply rtl:rotate-180;
  }
  
  .rtl-space-reverse {
    @apply rtl:space-x-reverse;
  }
  
  .rtl-text-right {
    @apply rtl:text-right ltr:text-left;
  }
  
  .rtl-text-left {
    @apply rtl:text-left ltr:text-right;
  }
  
  .rtl-mr-auto {
    @apply rtl:mr-auto rtl:ml-0 ltr:ml-auto ltr:mr-0;
  }
  
  .rtl-ml-auto {
    @apply rtl:ml-auto rtl:mr-0 ltr:mr-auto ltr:ml-0;
  }
}

/* Component layer customizations */
@layer components {
  /* Form Components */
  .form-input {
    @apply bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5;
    @apply rtl:text-right;
  }
  
  .form-label {
    @apply block mb-2 text-sm font-medium text-gray-900;
    @apply rtl:text-right;
  }
  
  .form-select {
    @apply bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5;
    @apply rtl:text-right;
  }
  
  .form-checkbox {
    @apply w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500;
    @apply rtl:ml-2 ltr:mr-2;
  }
  
  .form-radio {
    @apply w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500;
    @apply rtl:ml-2 ltr:mr-2;
  }
  
  /* Card Components */
  .card {
    @apply bg-white rounded-lg border border-gray-200 shadow-md overflow-hidden;
  }
  
  .card-header {
    @apply px-5 py-4 border-b border-gray-200 bg-gray-50;
  }
  
  .card-body {
    @apply p-5;
  }
  
  .card-footer {
    @apply px-5 py-4 border-t border-gray-200 bg-gray-50;
  }
  
  /* Table Components */
  .table-container {
    @apply overflow-x-auto relative shadow-md sm:rounded-lg;
  }
  
  .table {
    @apply w-full text-sm text-left text-gray-500;
    @apply rtl:text-right;
  }
  
  .table-header {
    @apply text-xs text-gray-700 uppercase bg-gray-50;
  }
  
  .table-row {
    @apply bg-white border-b hover:bg-gray-50;
  }
  
  .table-cell {
    @apply py-4 px-6;
  }
  
  /* Button Components */
  .btn {
    @apply font-medium rounded-lg text-sm px-5 py-2.5 focus:outline-none;
  }
  
  .btn-primary {
    @apply text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300;
  }
  
  .btn-secondary {
    @apply text-gray-900 bg-white border border-gray-300 hover:bg-gray-100 focus:ring-4 focus:ring-gray-200;
  }
  
  .btn-success {
    @apply text-white bg-green-700 hover:bg-green-800 focus:ring-4 focus:ring-green-300;
  }
  
  .btn-danger {
    @apply text-white bg-red-700 hover:bg-red-800 focus:ring-4 focus:ring-red-300;
  }
  
  .btn-warning {
    @apply text-white bg-yellow-400 hover:bg-yellow-500 focus:ring-4 focus:ring-yellow-300;
  }
  
  /* Badge Components */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-success {
    @apply bg-green-100 text-green-800;
  }
  
  .badge-warning {
    @apply bg-yellow-100 text-yellow-800;
  }
  
  .badge-danger {
    @apply bg-red-100 text-red-800;
  }
  
  .badge-info {
    @apply bg-blue-100 text-blue-800;
  }
}

/* Animation utilities */
@layer utilities {
  .fade-in {
    animation: fadeIn 0.5s ease-in;
  }
  
  .hover-scale {
    @apply transition-transform duration-300;
  }
  
  .hover-scale:hover {
    @apply transform scale-105;
  }
  
  .hover-shadow {
    @apply transition-shadow duration-300;
  }
  
  .hover-shadow:hover {
    @apply shadow-lg;
  }
}

@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
} 