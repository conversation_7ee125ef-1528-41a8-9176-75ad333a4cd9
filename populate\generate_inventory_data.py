import os
import sys
import django
import random
import uuid

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

# Now import Django models after setup
from django.db import transaction

# Only import inventory models
from inventory.models import Item, UnitOfMeasurement, ItemClassification, Movement

# Egyptian specific data for inventory items
PART_CATEGORIES = [
    "زيت المحرك", "فلتر زيت", "فلتر هواء", "شمعات الإحتراق", "فلتر وقود", "بطارية", "مساحات", "لمبات إضاءة", 
    "ممتص صدمات", "وسادة فرامل", "اسطوانة فرامل", "سير التوقيت", "سير المروحة", "منظم حرارة", "رادياتير",
    "طقم دبرياج", "عمود توازن", "بلف هواء", "أقمشة فرامل", "حساس أكسجين"
]

CAR_MAKES = [
    "مرسيدس", "بي إم دبليو", "أودي", "كيا", "هيونداي", "نيسان", "تويوتا", "فولكس فاجن", "سكودا", "شيفروليه",
    "فيات", "بيجو", "رينو", "مازدا", "سوزوكي", "ميتسوبيشي", "جيلي", "بريليانس", "سنجراي", "بي واي دي"
]

class InventoryDataGenerator:
    def __init__(self):
        self.tenant_id = str(uuid.uuid4())
        self.num_items = 200
        print(f"Tenant ID: {self.tenant_id}")
        
    def delete_existing_data(self):
        print("Deleting existing inventory data...")
        Movement.objects.all().delete()
        Item.objects.all().delete()
        ItemClassification.objects.all().delete()
        UnitOfMeasurement.objects.all().delete()
        print("Existing inventory data deleted.")
        
    def generate_basic_data(self):
        print("Generating basic inventory data (units, classifications)...")
        
        # Create units of measurement
        units = [
            {'name': 'قطعة', 'symbol': 'قطعة', 'is_base_unit': True},
            {'name': 'لتر', 'symbol': 'لتر', 'is_base_unit': True},
            {'name': 'كيلوجرام', 'symbol': 'كجم', 'is_base_unit': True},
            {'name': 'متر', 'symbol': 'م', 'is_base_unit': True},
            {'name': 'صندوق', 'symbol': 'صندوق', 'is_base_unit': False},
            {'name': 'عبوة', 'symbol': 'عبوة', 'is_base_unit': False}
        ]
        
        for unit_data in units:
            UnitOfMeasurement.objects.get_or_create(
                tenant_id=self.tenant_id,
                name=unit_data['name'],
                defaults={
                    'symbol': unit_data['symbol'],
                    'is_base_unit': unit_data['is_base_unit']
                }
            )
        print(f"Created {len(units)} units of measurement")
        
        # Create item classifications
        classifications = [
            {'name': 'قطع غيار أصلية', 'code': 'OEM', 'level': 0},
            {'name': 'قطع غيار متوافقة', 'code': 'COMPAT', 'level': 0},
            {'name': 'سوائل', 'code': 'FLUID', 'level': 0},
            {'name': 'إطارات', 'code': 'TIRE', 'level': 0},
            {'name': 'بطاريات', 'code': 'BATT', 'level': 0},
            {'name': 'إكسسوارات', 'code': 'ACC', 'level': 0},
        ]
        
        for class_data in classifications:
            ItemClassification.objects.get_or_create(
                tenant_id=self.tenant_id,
                code=class_data['code'],
                defaults={
                    'name': class_data['name'],
                    'level': class_data['level']
                }
            )
        print(f"Created {len(classifications)} item classifications")
        
    def generate_inventory_items(self):
        print(f"Generating {self.num_items} inventory items...")
        
        # Get all units and classifications
        units = list(UnitOfMeasurement.objects.filter(tenant_id=self.tenant_id))
        classifications = list(ItemClassification.objects.filter(tenant_id=self.tenant_id))
        
        if not units or not classifications:
            print("Error: No units or classifications found. Run generate_basic_data first.")
            return
        
        # Create items
        items_created = 0
        for i in range(self.num_items):
            category = random.choice(PART_CATEGORIES)
            make = random.choice(CAR_MAKES)
            
            # Create item
            try:
                item, created = Item.objects.get_or_create(
                    tenant_id=self.tenant_id,
                    sku=f"P{str(i+1).zfill(5)}",
                    defaults={
                        'name': f"{category} {make}",
                        'description': f"وصف {category} لسيارات {make}",
                        'quantity': random.randint(5, 100),
                        'unit_of_measurement': random.choice(units),
                        'unit_price': random.randint(50, 5000),
                        'min_stock_level': random.randint(2, 10),
                        'classification': random.choice(classifications),
                        'category': 'part'
                    }
                )
                
                if created:
                    items_created += 1
                    
                    # Create movements for this item
                    for _ in range(random.randint(1, 3)):
                        movement_type = random.choice(['purchase', 'adjustment'])
                        quantity = random.randint(1, 20)
                        
                        Movement.objects.create(
                            tenant_id=self.tenant_id,
                            item=item,
                            quantity=quantity,
                            movement_type=movement_type,
                            reference=f"REF-{uuid.uuid4().hex[:8].upper()}",
                            unit_of_measurement=item.unit_of_measurement
                        )
            except Exception as e:
                print(f"Error creating item {i+1}: {e}")
        
        print(f"Successfully created {items_created} inventory items with movements")
        
    @transaction.atomic
    def run(self, delete_existing=True):
        print("Starting inventory data generation...")
        
        if delete_existing:
            self.delete_existing_data()
            
        self.generate_basic_data()
        self.generate_inventory_items()
        
        print("Inventory data generation complete!")
        
if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser(description='Generate inventory demo data for Egyptian market')
    parser.add_argument('--no-delete', dest='delete', action='store_false',
                        help='Do not delete existing data before generating new data')
    parser.add_argument('--items', type=int, default=200,
                        help='Number of inventory items to generate')
    parser.set_defaults(delete=True)
    
    args = parser.parse_args()
    
    generator = InventoryDataGenerator()
    generator.num_items = args.items
    generator.run(delete_existing=args.delete) 