# Generated by Django 4.2.20 on 2025-05-08 18:49

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0005_item_category_item_item_type_vehiclecompatibility_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='ItemClassification',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('name', models.CharField(max_length=100, verbose_name='Classification Name')),
                ('code', models.CharField(max_length=50, verbose_name='Classification Code')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('level', models.PositiveSmallIntegerField(default=0, help_text='Hierarchical level (0=top level)', verbose_name='Level')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('icon', models.CharField(blank=True, help_text='Icon name for UI display', max_length=50, verbose_name='Icon')),
                ('color', models.CharField(blank=True, help_text='Color code for UI display', max_length=20, verbose_name='Color')),
                ('attributes', models.JSONField(blank=True, default=dict, verbose_name='Custom Attributes')),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='inventory.itemclassification', verbose_name='Parent Classification')),
            ],
            options={
                'verbose_name': 'Item Classification',
                'verbose_name_plural': 'Item Classifications',
                'ordering': ['level', 'name'],
                'unique_together': {('tenant_id', 'code')},
            },
        ),
        migrations.AddField(
            model_name='item',
            name='classification',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='items', to='inventory.itemclassification', verbose_name='Classification'),
        ),
    ]
