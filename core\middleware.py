from django.utils.deprecation import MiddlewareMixin
from django.utils.functional import SimpleLazyObject
from django.utils import translation
import uuid


def get_tenant_id_from_request(request):
    """
    Extract tenant ID from request.
    This could be from a session, a subdomain, a header, etc.
    For now, we'll use a simple header for demonstration.
    """
    tenant_id = request.headers.get('X-Tenant-ID')
    
    # Return None if no tenant ID is provided
    if not tenant_id:
        return None
    
    # Try to convert tenant_id to a valid UUID
    try:
        return uuid.UUID(tenant_id)
    except (ValueError, TypeError):
        # If tenant_id is not a valid UUID, return None
        return None


class CurrentTenantMiddleware(MiddlewareMixin):
    """
    Middleware that sets the current tenant ID on the request object.
    """
    
    def process_request(self, request):
        request.tenant_id = SimpleLazyObject(
            lambda: get_tenant_id_from_request(request)
        ) 


class AdminLanguageMiddleware(MiddlewareMixin):
    """
    Middleware that forces the admin site to use Arabic language by default.
    """
    
    def process_request(self, request):
        if request.path.startswith('/admin/'):
            translation.activate('ar')
            request.LANGUAGE_CODE = 'ar'


class AdminEnglishMiddleware(MiddlewareMixin):
    """
    Middleware that forces the admin site to use English language,
    overriding any other language settings.
    This takes precedence over ForceArabicLanguageMiddleware.
    """
    
    def process_request(self, request):
        # Check for both standard admin path and custom admin path
        if request.path.startswith('/admin/') or request.path.startswith('/dzJAMvwB/'):
            translation.activate('en')
            request.LANGUAGE_CODE = 'en'


class ForceArabicLanguageMiddleware(MiddlewareMixin):
    """
    Middleware that forces the frontend site to use Arabic language by default,
    regardless of user preferences or URL language prefix.
    Admin pages are excluded from this (handled by AdminEnglishMiddleware).
    """
    
    def process_request(self, request):
        # Skip forcing Arabic for admin pages (both standard and custom paths)
        if not (request.path.startswith('/admin/') or request.path.startswith('/dzJAMvwB/')):
            translation.activate('ar')
            request.LANGUAGE_CODE = 'ar' 