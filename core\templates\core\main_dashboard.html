{% extends "dashboard_base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "لوحة التحكم الرئيسية" %} | {% trans "نظام Aftersails" %}{% endblock %}
{% block page_title %}{% trans "لوحة التحكم" %}{% endblock %}
{% block header_title %}{% trans "لوحة التحكم الرئيسية" %}{% endblock %}
{% block header_subtitle %}{% trans "الوصول إلى جميع تطبيقات النظام من مكان واحد" %}{% endblock %}

{% block extra_css %}
<!-- Add any dashboard-specific CSS here -->
<style>
  .dashboard-widget {
    transition: all 0.3s ease;
  }
  .dashboard-widget:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }
  .metric-value {
    direction: ltr; /* Always show numbers left-to-right */
    display: inline-block;
    font-family: '<PERSON><PERSON><PERSON>', sans-serif;
    text-align: start;
  }
  .stats-label {
    color: #64748b; 
    font-size: 0.875rem;
    display: flex;
    align-items: center;
  }
  .stats-value {
    font-size: 1.25rem;
    font-weight: 700;
    line-height: 1.25;
  }
  .stats-card {
    display: flex;
    flex-direction: column;
    border-radius: 0.5rem;
    padding: 0.75rem;
    background-color: white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    height: 100%;
  }
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 0.75rem;
    margin-bottom: 1.5rem;
  }
  .stats-icon {
    font-size: 1.5rem;
    margin-right: 0.5rem;
  }
  .label-icon {
    margin-right: 0.25rem;
    font-size: 0.875rem;
  }
  
  /* RTL-specific styling */
  html[dir="rtl"] .stats-icon {
    margin-right: 0;
    margin-left: 0.5rem;
  }
  html[dir="rtl"] .label-icon {
    margin-right: 0;
    margin-left: 0.25rem;
  }
  html[dir="rtl"] .filter-form-icon {
    margin-right: 0;
    margin-left: 0.5rem;
  }
  html[dir="rtl"] .filter-action-buttons {
    flex-direction: row-reverse;
  }
  html[dir="rtl"] .filter-form select {
    padding-right: 1rem;
    background-position: left 0.5rem center;
  }
  
  @media (max-width: 1024px) {
    .stats-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }
  @media (max-width: 640px) {
    .stats-grid {
      grid-template-columns: 1fr;
    }
  }
  .collapsed-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-out;
  }
  
  .show-more-btn {
    cursor: pointer;
    color: #3b82f6;
    font-weight: 500;
    text-align: center;
    padding: 0.5rem;
    margin-top: 0.5rem;
  }
  
  .show-more-btn:hover {
    text-decoration: underline;
  }
  
  .activity-item, .alert-item {
    padding: 0.35rem 0;
    border-bottom: 1px solid #f3f4f6;
    cursor: pointer;
    transition: background-color 0.2s;
    display: block;
  }
  
  .activity-item:hover, .alert-item:hover {
    background-color: #f9fafb;
  }
  
  .activity-item:last-child, .alert-item:last-child {
    border-bottom: none;
  }
  
  .compact-list {
    margin: 0;
    padding: 0;
  }
  
  .compact-list li {
    margin-bottom: 0.5rem;
  }
  
  .compact-list li:last-child {
    margin-bottom: 0;
  }
  
  .stats-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.35rem 0;
    border-bottom: 1px solid #f3f4f6;
    cursor: pointer;
    transition: background-color 0.2s;
  }
  
  .stats-row:last-child {
    border-bottom: none;
  }
  
  .stats-row:hover {
    background-color: #f9fafb;
  }
  
  .app-row {
    display: flex;
    overflow-x: auto;
    padding: 0.5rem 0;
    gap: 1rem;
    scrollbar-width: thin;
    scrollbar-color: #cbd5e1 #f1f5f9;
    justify-content: center;
    flex-wrap: wrap;
    max-width: 1200px;
    margin: 0 auto;
  }
  
  .app-row::-webkit-scrollbar {
    height: 6px;
  }
  
  .app-row::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
  }
  
  .app-row::-webkit-scrollbar-thumb {
    background-color: #cbd5e1;
    border-radius: 3px;
  }
  
  .app-card {
    flex: 0 0 auto;
    width: 140px;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1.25rem 0.75rem;
    border-radius: 0.75rem;
    background-color: white;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    text-align: center;
  }
  
  .app-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }
  
  .app-icon {
    width: 48px;
    height: 48px;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 0.75rem;
  }

  /* Filter form custom styling */
  .filter-form select {
    appearance: none;
    -webkit-appearance: none; 
    -moz-appearance: none;
    background-image: none !important;
    transition: all 0.2s ease;
  }
  
  /* Remove default arrow in IE10 & IE11 */
  .filter-form select::-ms-expand {
    display: none;
  }
  
  .filter-form select:hover {
    border-color: #93c5fd;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
  }
  
  .filter-form .relative {
    position: relative;
  }
  
  /* RTL specific styles for filter dropdowns */
  html[dir="rtl"] .filter-form select {
    padding-left: 2rem !important;
    padding-right: 0.75rem !important;
    text-align: right;
  }
  
  html[dir="rtl"] .filter-form .relative .absolute {
    right: auto !important;
    left: 0 !important;
  }
  
  html[dir="rtl"] .filter-form .chevron-icon {
    left: 0.75rem;
    right: auto;
  }
  
  /* Filter highlight on active filters */
  .filter-active {
    background-color: #f0f9ff;
    border-color: #bae6fd;
  }
  
  /* Animated icon for active filters */
  .filter-form select.filter-active + div i {
    color: #3b82f6;
    animation: pulse 2s infinite;
  }
  
  @keyframes pulse {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.2);
    }
    100% {
      transform: scale(1);
    }
  }
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 pt-0 pb-6">
 
 
  {% if selected_franchise or selected_company or selected_service_center %}
  {% if not inventory_count and not work_orders_count and not sales_count and not purchases_count and not warehouses_count %}
  <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
    <div class="flex items-center">
      <i class="fas fa-exclamation-circle text-yellow-500 mr-2 text-xl"></i>
      <div>
        <h3 class="font-medium text-yellow-800">{% trans "لا توجد بيانات" %}</h3>
        <p class="text-yellow-700">
          {% trans "لم يتم العثور على بيانات مطابقة للتصفية الحالية. يرجى تجربة معايير تصفية أخرى." %}
        </p>
      </div>
    </div>
  </div>
  {% endif %}
  {% endif %}

  <!-- Stats Cards - Similar to the screenshot -->
  <div class="stats-grid mb-4">
    {% if user.is_superuser or primary_role.can_access_inventory %}
    <div class="stats-card">
      <div class="flex items-center mb-2">
        <i class="fas fa-warehouse stats-icon text-blue-600"></i>
        <h3 class="text-lg font-semibold text-gray-800">{% trans "المخزون" %}</h3>
      </div>
      
      <a href="{% url 'inventory:dashboard' %}" class="stats-row">
        <span class="stats-label"><i class="fas fa-exchange-alt label-icon text-blue-500"></i>{% trans "حركات" %}</span>
        <span class="stats-value text-blue-600 metric-value">{{ inventory_count|default:"٠" }}</span>
      </a>
      
      <a href="{% url 'inventory:dashboard' %}?filter=low_stock" class="stats-row">
        <span class="stats-label"><i class="fas fa-exclamation-triangle label-icon text-red-500"></i>{% trans "أصناف نفدت" %}</span>
        <span class="stats-value text-red-500 metric-value">{{ low_stock_count|default:"٠" }}</span>
      </a>
      
      <a href="{% url 'inventory:dashboard' %}" class="stats-row">
        <span class="stats-label"><i class="fas fa-boxes label-icon text-blue-500"></i>{% trans "إجمالي الأصناف" %}</span>
        <span class="stats-value text-blue-600 metric-value">{{ movement_count|default:"٠" }}</span>
      </a>
    </div>
    {% endif %}

    {% if user.is_superuser or primary_role.can_access_work_orders %}
    <div class="stats-card">
      <div class="flex items-center mb-2">
        <i class="fas fa-tasks stats-icon text-indigo-600"></i>
        <h3 class="text-lg font-semibold text-gray-800">{% trans "أوامر العمل" %}</h3>
      </div>
      
      <a href="{% url 'work_orders:work_order_list' %}?status=completed" class="stats-row">
        <span class="stats-label"><i class="fas fa-check-circle label-icon text-green-500"></i>{% trans "المكتملة" %}</span>
        <span class="stats-value text-green-600 metric-value">{{ completed_work_orders_count|default:"٠" }}</span>
      </a>
      
      <a href="{% url 'work_orders:work_order_list' %}?status=active" class="stats-row">
        <span class="stats-label"><i class="fas fa-spinner label-icon text-amber-500"></i>{% trans "قيد التنفيذ" %}</span>
        <span class="stats-value text-amber-500 metric-value">{{ active_work_orders_count|default:"٠" }}</span>
      </a>
      
      <a href="{% url 'work_orders:work_order_list' %}" class="stats-row">
        <span class="stats-label"><i class="fas fa-clipboard-list label-icon text-indigo-500"></i>{% trans "الإجمالي" %}</span>
        <span class="stats-value text-indigo-600 metric-value">{{ work_orders_count|default:"٠" }}</span>
      </a>
    </div>
    {% endif %}

    {% if user.is_superuser or primary_role.can_access_sales %}
    <div class="stats-card">
      <div class="flex items-center mb-2">
        <i class="fas fa-shopping-cart stats-icon text-green-600"></i>
        <h3 class="text-lg font-semibold text-gray-800">{% trans "المبيعات" %}</h3>
      </div>
      
      <a href="#" class="stats-row">
        <span class="stats-label"><i class="fas fa-users label-icon text-green-500"></i>{% trans "العملاء" %}</span>
        <span class="stats-value text-green-600 metric-value">{{ customers_count|default:"٠" }}</span>
      </a>
      
      <a href="#" class="stats-row">
        <span class="stats-label"><i class="fas fa-file-invoice label-icon text-green-500"></i>{% trans "العمليات" %}</span>
        <span class="stats-value text-green-600 metric-value">{{ sales_count|default:"٠" }}</span>
      </a>
      
      <a href="#" class="stats-row">
        <span class="stats-label"><i class="fas fa-money-bill-wave label-icon text-green-500"></i>{% trans "المبيعات الشهرية" %}</span>
        <span class="stats-value text-green-600 metric-value">{{ monthly_sales_amount|default:"٠" }} {% trans "ج.م" %}</span>
      </a>
    </div>
    {% endif %}

    {% if user.is_superuser or primary_role.can_access_purchases %}
    <div class="stats-card">
      <div class="flex items-center mb-2">
        <i class="fas fa-shopping-basket stats-icon text-amber-600"></i>
        <h3 class="text-lg font-semibold text-gray-800">{% trans "المشتريات" %}</h3>
      </div>
      
      <a href="#" class="stats-row">
        <span class="stats-label"><i class="fas fa-file-alt label-icon text-amber-500"></i>{% trans "أوامر الشراء" %}</span>
        <span class="stats-value text-amber-600 metric-value">{{ purchases_count|default:"٠" }}</span>
      </a>
      
      <a href="#" class="stats-row">
        <span class="stats-label"><i class="fas fa-truck label-icon text-amber-500"></i>{% trans "الموردين" %}</span>
        <span class="stats-value text-amber-600 metric-value">{{ suppliers_count|default:"٠" }}</span>
      </a>
      
      <a href="#" class="stats-row">
        <span class="stats-label"><i class="fas fa-clock label-icon text-red-500"></i>{% trans "طلبات معلقة" %}</span>
        <span class="stats-value text-red-500 metric-value">{{ pending_po_count|default:"٠" }}</span>
      </a>
    </div>
    {% endif %}
  </div>

  <!-- Recent Activities & Alerts -->
  <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
    <!-- System Alerts -->
    <div class="bg-white rounded-lg shadow">
      <div class="border-b px-6 py-3">
        <h3 class="text-lg font-semibold text-gray-700 flex items-center">
          <i class="fas fa-bell text-amber-500 ml-2"></i>
          {% trans "تنبيهات النظام" %}
        </h3>
      </div>
      <div class="px-6 py-3">
        {% if alerts %}
          <ul class="compact-list">
            {% for alert in alerts %}
              {% if forloop.counter <= 2 %}
              <a href="#alert-detail-{{ forloop.counter }}" class="alert-item" onclick="showAlertDetails('{{ alert.id }}')">
                <div class="flex items-start">
                  {% if alert.level == 'warning' %}
                    <span class="flex-shrink-0 w-6 h-6 rounded-full bg-amber-100 flex items-center justify-center">
                      <i class="fas fa-exclamation-triangle text-amber-500 text-xs"></i>
                    </span>
                  {% elif alert.level == 'danger' %}
                    <span class="flex-shrink-0 w-6 h-6 rounded-full bg-red-100 flex items-center justify-center">
                      <i class="fas fa-exclamation-circle text-red-500 text-xs"></i>
                    </span>
                  {% elif alert.level == 'info' %}
                    <span class="flex-shrink-0 w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center">
                      <i class="fas fa-info-circle text-blue-500 text-xs"></i>
                    </span>
                  {% elif alert.level == 'success' %}
                    <span class="flex-shrink-0 w-6 h-6 rounded-full bg-green-100 flex items-center justify-center">
                      <i class="fas fa-check-circle text-green-500 text-xs"></i>
                    </span>
                  {% endif %}
                  <div class="{% if LANGUAGE_CODE == 'ar' %}mr-2{% else %}ml-2{% endif %} flex-1">
                    <p class="text-sm font-medium text-gray-900">{{ alert.title }}</p>
                    <p class="text-xs text-gray-600">{{ alert.message }}</p>
                    <p class="text-xs text-gray-400">{{ alert.timestamp|date:"d/m/Y H:i" }}</p>
                  </div>
                </div>
              </a>
              {% endif %}
            {% endfor %}
          </ul>
          
          {% if alerts|length > 2 %}
          <div class="collapsed-content" id="alerts-collapsed">
            <ul class="compact-list">
              {% for alert in alerts %}
                {% if forloop.counter > 2 %}
                <a href="#alert-detail-{{ forloop.counter }}" class="alert-item" onclick="showAlertDetails('{{ alert.id }}')">
                  <div class="flex items-start">
                    {% if alert.level == 'warning' %}
                      <span class="flex-shrink-0 w-6 h-6 rounded-full bg-amber-100 flex items-center justify-center">
                        <i class="fas fa-exclamation-triangle text-amber-500 text-xs"></i>
                      </span>
                    {% elif alert.level == 'danger' %}
                      <span class="flex-shrink-0 w-6 h-6 rounded-full bg-red-100 flex items-center justify-center">
                        <i class="fas fa-exclamation-circle text-red-500 text-xs"></i>
                      </span>
                    {% elif alert.level == 'info' %}
                      <span class="flex-shrink-0 w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center">
                        <i class="fas fa-info-circle text-blue-500 text-xs"></i>
                      </span>
                    {% elif alert.level == 'success' %}
                      <span class="flex-shrink-0 w-6 h-6 rounded-full bg-green-100 flex items-center justify-center">
                        <i class="fas fa-check-circle text-green-500 text-xs"></i>
                      </span>
                    {% endif %}
                    <div class="{% if LANGUAGE_CODE == 'ar' %}mr-2{% else %}ml-2{% endif %} flex-1">
                      <p class="text-sm font-medium text-gray-900">{{ alert.title }}</p>
                      <p class="text-xs text-gray-600">{{ alert.message }}</p>
                      <p class="text-xs text-gray-400">{{ alert.timestamp|date:"d/m/Y H:i" }}</p>
                    </div>
                  </div>
                </a>
                {% endif %}
              {% endfor %}
            </ul>
          </div>
          <div class="show-more-btn text-sm pt-0 mt-1" onclick="toggleContent('alerts-collapsed', this)">
            <i class="fas fa-chevron-down mr-1"></i> {% trans "عرض المزيد" %}
          </div>
          {% endif %}
          
          <!-- Alert Details Modal -->
          <div id="alert-details-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center">
            <div class="bg-white rounded-lg shadow-lg max-w-md w-full mx-4">
              <div class="flex justify-between items-center border-b px-4 py-3">
                <h3 class="text-lg font-semibold text-gray-800" id="modal-alert-title"></h3>
                <button onclick="closeAlertModal()" class="text-gray-500 hover:text-gray-700">
                  <i class="fas fa-times"></i>
                </button>
              </div>
              <div class="p-4">
                <p class="text-sm text-gray-600 mb-3" id="modal-alert-message"></p>
                <div class="flex items-center text-xs text-gray-500">
                  <i class="fas fa-clock mr-1"></i>
                  <span id="modal-alert-time"></span>
                </div>
              </div>
              <div class="bg-gray-50 px-4 py-3 flex justify-end rounded-b-lg">
                <button onclick="closeAlertModal()" class="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300">
                  {% trans "إغلاق" %}
                </button>
              </div>
            </div>
          </div>
        {% else %}
          <div class="flex flex-col items-center justify-center py-3 text-gray-500">
            <i class="fas fa-check-circle text-green-500 text-xl mb-1"></i>
            <p class="text-sm">{% trans "لا توجد تنبيهات حالية" %}</p>
          </div>
        {% endif %}
      </div>
    </div>

    <!-- Recent Activity -->
    <div class="bg-white rounded-lg shadow">
      <div class="border-b px-6 py-3">
        <h3 class="text-lg font-semibold text-gray-700 flex items-center">
          <i class="fas fa-history text-blue-500 ml-2"></i>
          {% trans "النشاط الحديث" %}
        </h3>
      </div>
      <div class="px-6 py-3">
        {% if recent_activities %}
          <ul class="compact-list">
            {% for activity in recent_activities %}
              {% if forloop.counter <= 2 %}
              <a href="#activity-detail-{{ forloop.counter }}" class="activity-item" onclick="showActivityDetails('{{ activity.id }}')">
                <div class="flex items-start">
                  {% if activity.type == 'inventory' %}
                    <span class="flex-shrink-0 w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center">
                      <i class="fas fa-warehouse text-blue-500 text-xs"></i>
                    </span>
                  {% elif activity.type == 'work_order' %}
                    <span class="flex-shrink-0 w-6 h-6 rounded-full bg-indigo-100 flex items-center justify-center">
                      <i class="fas fa-tasks text-indigo-500 text-xs"></i>
                    </span>
                  {% elif activity.type == 'sale' %}
                    <span class="flex-shrink-0 w-6 h-6 rounded-full bg-green-100 flex items-center justify-center">
                      <i class="fas fa-shopping-cart text-green-500 text-xs"></i>
                    </span>
                  {% elif activity.type == 'purchase' %}
                    <span class="flex-shrink-0 w-6 h-6 rounded-full bg-amber-100 flex items-center justify-center">
                      <i class="fas fa-shopping-basket text-amber-500 text-xs"></i>
                    </span>
                  {% else %}
                    <span class="flex-shrink-0 w-6 h-6 rounded-full bg-gray-100 flex items-center justify-center">
                      <i class="fas fa-history text-gray-500 text-xs"></i>
                    </span>
                  {% endif %}
                  <div class="{% if LANGUAGE_CODE == 'ar' %}mr-2{% else %}ml-2{% endif %} flex-1">
                    <p class="text-sm text-gray-900">{{ activity.description }}</p>
                    <p class="text-xs text-gray-400">{{ activity.timestamp|date:"d/m/Y H:i" }}</p>
                  </div>
                </div>
              </a>
              {% endif %}
            {% endfor %}
          </ul>
          
          {% if recent_activities|length > 2 %}
          <div class="collapsed-content" id="activities-collapsed">
            <ul class="compact-list">
              {% for activity in recent_activities %}
                {% if forloop.counter > 2 %}
                <a href="#activity-detail-{{ forloop.counter }}" class="activity-item" onclick="showActivityDetails('{{ activity.id }}')">
                  <div class="flex items-start">
                    {% if activity.type == 'inventory' %}
                      <span class="flex-shrink-0 w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center">
                        <i class="fas fa-warehouse text-blue-500 text-xs"></i>
                      </span>
                    {% elif activity.type == 'work_order' %}
                      <span class="flex-shrink-0 w-6 h-6 rounded-full bg-indigo-100 flex items-center justify-center">
                        <i class="fas fa-tasks text-indigo-500 text-xs"></i>
                      </span>
                    {% elif activity.type == 'sale' %}
                      <span class="flex-shrink-0 w-6 h-6 rounded-full bg-green-100 flex items-center justify-center">
                        <i class="fas fa-shopping-cart text-green-500 text-xs"></i>
                      </span>
                    {% elif activity.type == 'purchase' %}
                      <span class="flex-shrink-0 w-6 h-6 rounded-full bg-amber-100 flex items-center justify-center">
                        <i class="fas fa-shopping-basket text-amber-500 text-xs"></i>
                      </span>
                    {% else %}
                      <span class="flex-shrink-0 w-6 h-6 rounded-full bg-gray-100 flex items-center justify-center">
                        <i class="fas fa-history text-gray-500 text-xs"></i>
                      </span>
                    {% endif %}
                    <div class="{% if LANGUAGE_CODE == 'ar' %}mr-2{% else %}ml-2{% endif %} flex-1">
                      <p class="text-sm text-gray-900">{{ activity.description }}</p>
                      <p class="text-xs text-gray-400">{{ activity.timestamp|date:"d/m/Y H:i" }}</p>
                    </div>
                  </div>
                </a>
                {% endif %}
              {% endfor %}
            </ul>
          </div>
          <div class="show-more-btn text-sm pt-0 mt-1" onclick="toggleContent('activities-collapsed', this)">
            <i class="fas fa-chevron-down mr-1"></i> {% trans "عرض المزيد" %}
          </div>
          {% endif %}
          
          <!-- Activity Details Modal -->
          <div id="activity-details-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center">
            <div class="bg-white rounded-lg shadow-lg max-w-md w-full mx-4">
              <div class="flex justify-between items-center border-b px-4 py-3">
                <h3 class="text-lg font-semibold text-gray-800" id="modal-activity-title"></h3>
                <button onclick="closeActivityModal()" class="text-gray-500 hover:text-gray-700">
                  <i class="fas fa-times"></i>
                </button>
              </div>
              <div class="p-4">
                <p class="text-sm text-gray-600 mb-3" id="modal-activity-description"></p>
                <div class="flex items-center text-xs text-gray-500">
                  <i class="fas fa-clock mr-1"></i>
                  <span id="modal-activity-time"></span>
                </div>
              </div>
              <div class="bg-gray-50 px-4 py-3 flex justify-end rounded-b-lg">
                <button onclick="closeActivityModal()" class="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300">
                  {% trans "إغلاق" %}
                </button>
              </div>
            </div>
          </div>
        {% else %}
          <div class="flex flex-col items-center justify-center py-3 text-gray-500">
            <i class="fas fa-calendar-check text-gray-400 text-xl mb-1"></i>
            <p class="text-sm">{% trans "لا يوجد نشاط حديث" %}</p>
          </div>
        {% endif %}
      </div>
    </div>
  </div>

  <!-- Applications Row -->
  <div class="mt-8 mb-6">
    <div class="app-row">
      <!-- Inventory App -->
      {% if user.is_superuser or primary_role.can_access_inventory %}
      <a href="{% url 'inventory:dashboard' %}" class="app-card">
        <div class="app-icon text-blue-600">
          <i class="fas fa-warehouse text-xl"></i>
        </div>
        <h3 class="text-sm font-semibold text-gray-800 mb-1">{% trans "المخزون" %}</h3>
        <p class="text-xs text-gray-500">{% trans "إدارة العناصر" %}</p>
      </a>
      {% endif %}
      
      <!-- Work Orders App -->
      {% if user.is_superuser or primary_role.can_access_work_orders %}
      <a href="{% url 'work_orders:work_order_list' %}" class="app-card">
        <div class="app-icon text-indigo-600">
          <i class="fas fa-tasks text-xl"></i>
        </div>
        <h3 class="text-sm font-semibold text-gray-800 mb-1">{% trans "أوامر العمل" %}</h3>
        <p class="text-xs text-gray-500">{% trans "إدارة الصيانة" %}</p>
      </a>
      {% endif %}
      
      <!-- Sales App -->
      {% if user.is_superuser or primary_role.can_access_sales %}
      <a href="#" class="app-card">
        <div class="app-icon text-green-600">
          <i class="fas fa-shopping-cart text-xl"></i>
        </div>
        <h3 class="text-sm font-semibold text-gray-800 mb-1">{% trans "المبيعات" %}</h3>
        <p class="text-xs text-gray-500">{% trans "إدارة المبيعات" %}</p>
      </a>
      {% endif %}
      
      <!-- Purchases App -->
      {% if user.is_superuser or primary_role.can_access_purchases %}
      <a href="#" class="app-card">
        <div class="app-icon text-gray-800">
          <i class="fas fa-shopping-basket text-xl"></i>
        </div>
        <h3 class="text-sm font-semibold text-gray-800 mb-1">{% trans "المشتريات" %}</h3>
        <p class="text-xs text-gray-500">{% trans "إدارة المشتريات" %}</p>
      </a>
      {% endif %}
      
      <!-- Warehouse App -->
      {% if user.is_superuser or primary_role.can_access_warehouse %}
      <a href="#" class="app-card">
        <div class="app-icon text-teal-600">
          <i class="fas fa-boxes text-xl"></i>
        </div>
        <h3 class="text-sm font-semibold text-gray-800 mb-1">{% trans "المستودع" %}</h3>
        <p class="text-xs text-gray-500">{% trans "إدارة المستودعات" %}</p>
      </a>
      {% endif %}
      
      <!-- Reports App -->
      {% if user.is_superuser or primary_role.can_access_reports %}
      <a href="{% url 'reports:report_list' %}" class="app-card">
        <div class="app-icon text-purple-600">
          <i class="fas fa-chart-bar text-xl"></i>
        </div>
        <h3 class="text-sm font-semibold text-gray-800 mb-1">{% trans "التقارير" %}</h3>
        <p class="text-xs text-gray-500">{% trans "إنشاء التقارير" %}</p>
      </a>
      {% endif %}
      
      <!-- Setup App -->
      {% if user.is_superuser or primary_role.can_access_setup %}
      <a href="{% url 'setup:dashboard' %}" class="app-card">
        <div class="app-icon text-gray-600">
          <i class="fas fa-cogs text-xl"></i>
        </div>
        <h3 class="text-sm font-semibold text-gray-800 mb-1">{% trans "الإعدادات" %}</h3>
        <p class="text-xs text-gray-500">{% trans "إعدادات النظام" %}</p>
      </a>
      {% endif %}
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Add any dashboard-specific JavaScript here -->
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Dashboard initialization code here
    console.log('Dashboard initialized');
    
    // Initialize cascading dropdowns for entity selection
    initEntityFilters();
  });
  
  function initEntityFilters() {
    const franchiseSelect = document.getElementById('franchise_id');
    const companySelect = document.getElementById('company_id');
    const serviceCenterSelect = document.getElementById('service_center_id');
    
    if (franchiseSelect && companySelect) {
      franchiseSelect.addEventListener('change', function() {
        if (this.value) {
          // Reset company and service center selections when franchise changes
          companySelect.value = '';
          if (serviceCenterSelect) {
            serviceCenterSelect.value = '';
          }
          
          // Submit the form to update the page
          this.form.submit();
        }
      });
    }
    
    if (companySelect && serviceCenterSelect) {
      companySelect.addEventListener('change', function() {
        if (this.value) {
          // Reset service center selection when company changes
          serviceCenterSelect.value = '';
          
          // Submit the form to update the page
          this.form.submit();
        }
      });
    }
  }
  
  function toggleContent(elementId, button) {
    const element = document.getElementById(elementId);
    if (element.style.maxHeight) {
      element.style.maxHeight = null;
      button.innerHTML = '<i class="fas fa-chevron-down mr-1"></i> {% trans "عرض المزيد" %}';
    } else {
      element.style.maxHeight = element.scrollHeight + "px";
      button.innerHTML = '<i class="fas fa-chevron-up mr-1"></i> {% trans "عرض أقل" %}';
    }
  }
  
  function showAlertDetails(alertId) {
    // In a real app, you might fetch the details from the server
    // For now, we'll just use the data already in the page
    const modal = document.getElementById('alert-details-modal');
    const clickedAlert = event.currentTarget;
    
    // Extract data from the clicked element
    const title = clickedAlert.querySelector('.text-gray-900').textContent;
    const message = clickedAlert.querySelector('.text-gray-600').textContent;
    const time = clickedAlert.querySelector('.text-gray-400').textContent;
    
    // Set modal content
    document.getElementById('modal-alert-title').textContent = title;
    document.getElementById('modal-alert-message').textContent = message;
    document.getElementById('modal-alert-time').textContent = time;
    
    // Show modal
    modal.classList.remove('hidden');
    modal.classList.add('flex');
    
    // Prevent the default anchor behavior
    event.preventDefault();
  }
  
  function closeAlertModal() {
    const modal = document.getElementById('alert-details-modal');
    modal.classList.add('hidden');
    modal.classList.remove('flex');
  }
  
  function showActivityDetails(activityId) {
    // Similar to the alert details function
    const modal = document.getElementById('activity-details-modal');
    const clickedActivity = event.currentTarget;
    
    // Extract data from the clicked element
    const description = clickedActivity.querySelector('.text-gray-900').textContent;
    const time = clickedActivity.querySelector('.text-gray-400').textContent;
    
    // Set modal content
    document.getElementById('modal-activity-title').textContent = '{% trans "تفاصيل النشاط" %}';
    document.getElementById('modal-activity-description').textContent = description;
    document.getElementById('modal-activity-time').textContent = time;
    
    // Show modal
    modal.classList.remove('hidden');
    modal.classList.add('flex');
    
    // Prevent the default anchor behavior
    event.preventDefault();
  }
  
  function closeActivityModal() {
    const modal = document.getElementById('activity-details-modal');
    modal.classList.add('hidden');
    modal.classList.remove('flex');
  }
</script>
{% endblock %} 