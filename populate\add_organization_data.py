import os
import sys
import django
import random
import uuid
from datetime import datetime, timedelta

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

# Import Django models
from django.db import transaction, IntegrityError
from django.utils import timezone
from django.contrib.auth.models import User

# Import setup models for organization structure
from setup.models import Franchise, Company, ServiceCenter, ServiceCenterType, ServiceLevel, VehicleOwnershipTransfer, ServiceHistory
from setup.models import Customer, Vehicle

# Egyptian cities
EGYPTIAN_CITIES = [
    "القاهرة", "الإسكندرية", "الجيزة", "شرم الشيخ", "الغردقة", 
    "المنصورة", "طنطا", "أسيوط", "الزقازيق", "الإسماعيلية",
    "بورسعيد", "السويس", "الأقصر", "أسوان", "المنيا", 
    "سوهاج", "بني سويف", "دمياط", "مرسى مطروح", "الفيوم"
]

class OrganizationSetupGenerator:
    def __init__(self):
        self.tenant_id = str(uuid.uuid4())
        print(f"Tenant ID: {self.tenant_id}")
        print("Organization setup generator initialized")
    
    def generate_service_levels(self):
        print("\nGenerating service levels...")
        
        if ServiceLevel.objects.exists():
            print("Service levels already exist, skipping.")
            return
        
        service_levels = [
            {
                'name': 'بلاتينيوم',
                'description': 'أعلى مستوى خدمة مع دعم على مدار الساعة وأولوية قصوى',
                'priority': 1,
                'response_time_hours': 2,
                'resolution_time_hours': 24,
                'support_hours': '24/7',
                'emergency_response_time_hours': 1,
                'onsite_response_time_hours': 4,
                'availability_target_percent': 99.9
            },
            {
                'name': 'ذهبي',
                'description': 'مستوى خدمة عالي مع دعم ممتاز وأولوية عالية',
                'priority': 2,
                'response_time_hours': 4,
                'resolution_time_hours': 36,
                'support_hours': '24/7',
                'emergency_response_time_hours': 2,
                'onsite_response_time_hours': 8,
                'availability_target_percent': 99.5
            },
            {
                'name': 'فضي',
                'description': 'مستوى خدمة متوسط مع دعم جيد',
                'priority': 3,
                'response_time_hours': 8,
                'resolution_time_hours': 48,
                'support_hours': '8AM-8PM, 7 أيام',
                'emergency_response_time_hours': 4,
                'onsite_response_time_hours': 24,
                'availability_target_percent': 99.0
            },
            {
                'name': 'برونزي',
                'description': 'مستوى خدمة أساسي',
                'priority': 4,
                'response_time_hours': 24,
                'resolution_time_hours': 72,
                'support_hours': '9AM-5PM, الأحد-الخميس',
                'emergency_response_time_hours': 8,
                'onsite_response_time_hours': 48,
                'availability_target_percent': 98.0
            }
        ]
        
        created_count = 0
        for level_data in service_levels:
            try:
                ServiceLevel.objects.create(**level_data)
                created_count += 1
            except Exception as e:
                print(f"Error creating service level {level_data['name']}: {e}")
        
        print(f"Created {created_count} service levels")
        return created_count > 0
    
    def generate_service_center_types(self):
        print("\nGenerating service center types...")
        
        if ServiceCenterType.objects.exists():
            print("Service center types already exist, skipping.")
            return
        
        center_types = [
            {
                'name': 'مركز شامل',
                'description': 'مركز خدمة كامل مع جميع الخدمات المتاحة',
                'max_capacity': 50,
                'color_code': '#007bff',
                'is_active': True
            },
            {
                'name': 'مركز صيانة سريعة',
                'description': 'مركز متخصص في الصيانة السريعة والخدمات البسيطة',
                'max_capacity': 30,
                'color_code': '#28a745',
                'is_active': True
            },
            {
                'name': 'مركز متخصص',
                'description': 'مركز متخصص في أنواع معينة من المركبات أو الخدمات',
                'max_capacity': 20,
                'color_code': '#dc3545',
                'is_active': True
            }
        ]
        
        created_count = 0
        for type_data in center_types:
            try:
                ServiceCenterType.objects.create(**type_data)
                created_count += 1
            except Exception as e:
                print(f"Error creating service center type {type_data['name']}: {e}")
        
        print(f"Created {created_count} service center types")
        return created_count > 0
    
    def generate_franchise_structure(self):
        print("\nGenerating franchise structure...")
        
        # Create franchise if it doesn't exist
        if Franchise.objects.exists():
            print("Franchise already exists, using existing franchise.")
            franchise = Franchise.objects.first()
        else:
            try:
                franchise = Franchise.objects.create(
                    name="أفتر سيلز للصيانة",
                    code="AFTERSALES",
                    address="شارع التحرير، القاهرة",
                    city="القاهرة",
                    country="مصر",
                    phone="+20 ************",
                    email="<EMAIL>",
                    website="www.aftersales-eg.com",
                    is_active=True
                )
                print(f"Created franchise: {franchise.name}")
            except Exception as e:
                print(f"Error creating franchise: {e}")
                return False
        
        # Get service level for companies
        service_level = ServiceLevel.objects.first()
        
        # Create companies under the franchise
        companies_data = [
            {
                'name': 'أفتر سيلز مصر',
                'code': 'AFSALES-EG',
                'address': 'شارع رمسيس، القاهرة',
                'city': 'القاهرة',
                'phone': '+20 ************',
                'email': '<EMAIL>'
            },
            {
                'name': 'دلتا للصيانة',
                'code': 'DELTA-AUTO',
                'address': 'شارع الجلاء، المنصورة',
                'city': 'المنصورة',
                'phone': '+20 ************',
                'email': '<EMAIL>'
            },
            {
                'name': 'الإسكندرية لصيانة السيارات',
                'code': 'ALEX-AUTO',
                'address': 'طريق الكورنيش، الإسكندرية',
                'city': 'الإسكندرية',
                'phone': '+20 ************',
                'email': '<EMAIL>'
            }
        ]
        
        # Create or get companies
        companies = []
        for company_data in companies_data:
            try:
                company, created = Company.objects.get_or_create(
                    franchise=franchise,
                    code=company_data['code'],
                    defaults={
                        'name': company_data['name'],
                        'address': company_data['address'],
                        'city': company_data['city'],
                        'country': 'مصر',
                        'phone': company_data['phone'],
                        'email': company_data['email'],
                        'is_active': True,
                        'service_level': service_level
                    }
                )
                companies.append(company)
                if created:
                    print(f"Created company: {company.name}")
                else:
                    print(f"Using existing company: {company.name}")
            except Exception as e:
                print(f"Error creating company {company_data['name']}: {e}")
        
        # Create service centers for each company
        center_types = list(ServiceCenterType.objects.all())
        
        if not center_types:
            print("No service center types found. Please run generate_service_center_types first.")
            return False
        
        # Create service centers
        center_count = 0
        for company in companies:
            # Choose 2-4 random cities for this company's service centers
            cities = random.sample(EGYPTIAN_CITIES, min(4, len(EGYPTIAN_CITIES)))
            
            for i, city in enumerate(cities):
                center_type = random.choice(center_types)
                center_size = random.choice(['small', 'medium', 'large'])
                
                try:
                    center_code = f"{company.code}-{city[:2]}{i+1}"
                    center, created = ServiceCenter.objects.get_or_create(
                        company=company,
                        code=center_code,
                        defaults={
                            'name': f"مركز {company.name} - {city}",
                            'center_type': center_type,
                            'size': center_size,
                            'address': f"شارع الرئيسي، {city}",
                            'city': city,
                            'country': 'مصر',
                            'phone': f"+20 10{random.randint(1000000, 9999999)}",
                            'email': f"service.{city}@{company.code.lower()}.com",
                            'is_active': True,
                            'capacity': random.randint(5, 30),
                            'tenant_id': self.tenant_id
                        }
                    )
                    
                    if created:
                        center_count += 1
                        print(f"Created service center: {center.name}")
                    else:
                        print(f"Using existing service center: {center.name}")
                        
                except Exception as e:
                    print(f"Error creating service center in {city}: {e}")
        
        print(f"Created {center_count} service centers")
        print(f"Franchise structure generation complete")
        return True
    
    def generate_vehicle_history(self):
        print("\nGenerating vehicle history...")
        
        # Check if we already have vehicle history
        if ServiceHistory.objects.exists():
            print("Vehicle history records already exist, skipping.")
            return
        
        # Get vehicles
        vehicles = Vehicle.objects.filter(tenant_id=self.tenant_id)[:50]
        if not vehicles:
            print("No vehicles found. Please ensure Vehicle table has data.")
            return
        
        service_centers = ServiceCenter.objects.filter(tenant_id=self.tenant_id)
        if not service_centers:
            print("No service centers found. Please ensure ServiceCenter table has data.")
            return
        
        histories_created = 0
        
        # Service types
        service_types = [
            "صيانة دورية", "تغيير زيت", "إصلاح أعطال", 
            "تغيير إطارات", "فحص شامل", "إصلاح حادث",
            "تغيير فلتر", "ضبط محاذاة", "إصلاح فرامل"
        ]
        
        for vehicle in vehicles:
            # Create 1-3 history records per vehicle
            for i in range(random.randint(1, 3)):
                # Random date in last 2 years
                service_date = datetime.now().date() - timedelta(days=random.randint(1, 730))
                service_center = random.choice(service_centers)
                
                # Build services performed
                num_services = random.randint(1, 3)
                services = [random.choice(service_types) for _ in range(num_services)]
                
                # Build parts used
                parts_used = []
                for _ in range(random.randint(0, 3)):
                    parts_used.append({
                        "name": f"قطعة غيار #{random.randint(1000, 9999)}",
                        "quantity": random.randint(1, 3),
                        "price": round(random.uniform(100, 2000), 2)
                    })
                
                # Calculate total cost
                parts_cost = sum(part["quantity"] * part["price"] for part in parts_used)
                service_cost = random.uniform(200, 1000)
                total_cost = parts_cost + service_cost
                
                try:
                    ServiceHistory.objects.create(
                        vehicle=vehicle,
                        service_center=service_center,
                        service_date=service_date,
                        odometer=random.randint(1000, 100000),
                        description=f"صيانة رقم {i+1} للمركبة {vehicle.make} {vehicle.model}",
                        work_order_number=f"WO-{random.randint(10000, 99999)}",
                        services_performed=services,
                        parts_used=parts_used,
                        technician=f"فني #{random.randint(100, 999)}",
                        total_cost=total_cost,
                        notes="تمت الصيانة بنجاح",
                        tenant_id=self.tenant_id
                    )
                    histories_created += 1
                except Exception as e:
                    print(f"Error creating service history for vehicle {vehicle.id}: {e}")
        
        print(f"Created {histories_created} service history records")
    
    def add_vehicle_drivers(self):
        print("\nAdding drivers to vehicles...")
        
        vehicles = Vehicle.objects.filter(tenant_id=self.tenant_id)[:50]
        if not vehicles:
            print("No vehicles found.")
            return
            
        customers = Customer.objects.filter(tenant_id=self.tenant_id)
        if not customers or len(customers) < 5:
            print("Not enough customers found.")
            return
            
        updates = 0
        
        for vehicle in vehicles:
            # Skip some vehicles randomly
            if random.random() > 0.7:
                continue
                
            # Add 1-2 additional drivers besides the owner
            num_drivers = random.randint(1, 2)
            potential_drivers = list(customers.exclude(id=vehicle.owner_id) if vehicle.owner_id else customers)
            
            if len(potential_drivers) < num_drivers:
                continue
                
            drivers = random.sample(list(potential_drivers), num_drivers)
            
            try:
                # Clear existing and add new
                vehicle.drivers.clear()
                for driver in drivers:
                    vehicle.drivers.add(driver)
                updates += 1
            except Exception as e:
                print(f"Error adding drivers to vehicle {vehicle.id}: {e}")
        
        print(f"Added drivers to {updates} vehicles")
    
    @transaction.atomic
    def run(self):
        print("Starting organization setup data generation...")
        
        # Generate service levels
        self.generate_service_levels()
        
        # Generate service center types
        self.generate_service_center_types()
        
        # Generate franchise structure
        self.generate_franchise_structure()
        
        # Add history to vehicles
        self.generate_vehicle_history()
        
        # Add drivers to vehicles
        self.add_vehicle_drivers()
        
        print("\nOrganization setup data generation complete!")

if __name__ == "__main__":
    generator = OrganizationSetupGenerator()
    generator.run() 