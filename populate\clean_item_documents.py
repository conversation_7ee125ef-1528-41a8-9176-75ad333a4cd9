import os
import sys
import django

# Add the parent directory to the Python path so imports work correctly
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

# Import Django models
from django.db import transaction

try:
    from inventory.models import ItemDocument
except ImportError as e:
    print(f"Could not import inventory models: {e}")
    print("Please check app structure and model names.")
    sys.exit(1)

def clean_item_documents():
    """Clean up ItemDocument records with missing files"""
    print("Cleaning up ItemDocument records with missing files...")
    
    # Find all ItemDocument records
    all_documents = ItemDocument.objects.all()
    print(f"Found {all_documents.count()} ItemDocument records")
    
    # Records to delete (those with missing files)
    documents_to_delete = []
    
    for doc in all_documents:
        try:
            # Check if the file exists
            if not doc.file or not doc.file.name:
                documents_to_delete.append(doc)
            else:
                # Try to access file properties to verify it exists
                try:
                    doc.file.url
                    doc.file.size
                except (ValueError, FileNotFoundError):
                    documents_to_delete.append(doc)
        except Exception as e:
            print(f"Error checking document {doc.id}: {e}")
            documents_to_delete.append(doc)
    
    print(f"Found {len(documents_to_delete)} ItemDocument records with missing files")
    
    # Delete the records with missing files
    with transaction.atomic():
        for doc in documents_to_delete:
            try:
                doc.delete()
                print(f"Deleted document: {doc.id} - {doc.title}")
            except Exception as e:
                print(f"Error deleting document {doc.id}: {e}")
    
    # Verify the cleanup
    remaining_documents = ItemDocument.objects.count()
    print(f"Cleanup complete. {remaining_documents} valid ItemDocument records remain")

if __name__ == "__main__":
    clean_item_documents() 