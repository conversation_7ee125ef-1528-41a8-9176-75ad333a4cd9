from django.db import models
from django.utils.translation import gettext_lazy as _
from core.models.common import TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel
from core.querysets import BaseQuerySet
from setup.models import ServiceCenter, Vehicle, Customer
from django.core.exceptions import ValidationError

class WorkOrderType(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """Type of work order (e.g., repair, maintenance, assembly)"""
    name = models.CharField(_("Name"), max_length=100)
    description = models.TextField(_("Description"), blank=True)
    color_code = models.CharField(_("Color Code"), max_length=20, blank=True, help_text=_("For UI display"))
    attributes = models.JSONField(_("Custom Attributes"), default=dict, blank=True)
    is_active = models.BooleanField(_("Is Active"), default=True)
    
    objects = BaseQuerySet.as_manager()

    class Meta:
        verbose_name = _("Work Order Type")
        verbose_name_plural = _("Work Order Types")
        ordering = ['name']

    def __str__(self):
        return self.name

class MaintenanceSchedule(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """Standard maintenance schedule templates (e.g., 10,000km service)"""
    INTERVAL_TYPE_CHOICES = [
        ('mileage', _('Mileage')),
        ('time', _('Time')),
        ('both', _('Both')),
    ]
    
    name = models.CharField(_("Name"), max_length=100)
    description = models.TextField(_("Description"), blank=True)
    interval_type = models.CharField(_("Interval Type"), max_length=20, choices=INTERVAL_TYPE_CHOICES, default='mileage')
    mileage_interval = models.PositiveIntegerField(_("Mileage Interval (km)"), null=True, blank=True)
    time_interval_months = models.PositiveIntegerField(_("Time Interval (months)"), null=True, blank=True)
    vehicle_make = models.CharField(_("Vehicle Make"), max_length=100, blank=True)
    vehicle_model = models.CharField(_("Vehicle Model"), max_length=100, blank=True)
    year_from = models.PositiveIntegerField(_("Year From"), null=True, blank=True)
    year_to = models.PositiveIntegerField(_("Year To"), null=True, blank=True)
    is_active = models.BooleanField(_("Is Active"), default=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Maintenance Schedule")
        verbose_name_plural = _("Maintenance Schedules")
        ordering = ['name']
        
    def __str__(self):
        return self.name
        
    def clean(self):
        """Validate interval settings"""
        from django.core.exceptions import ValidationError
        
        if self.interval_type in ['mileage', 'both'] and not self.mileage_interval:
            raise ValidationError(_("Mileage interval is required for this interval type"))
            
        if self.interval_type in ['time', 'both'] and not self.time_interval_months:
            raise ValidationError(_("Time interval is required for this interval type"))
            
        super().clean()

    def suggest_compatible_parts(self, vehicle=None):
        """
        Suggest compatible parts for this maintenance schedule, 
        optionally filtered by specific vehicle
        
        Args:
            vehicle: Optional Vehicle instance to filter compatible parts
            
        Returns:
            List of compatible VehicleModelPart instances
        """
        from inventory.models import VehicleModelPart
        
        # Base query for parts linked to this schedule
        parts_query = VehicleModelPart.objects.filter(
            maintenance_schedule=self
        )
        
        # If vehicle is provided, filter by its details
        if vehicle:
            parts_query = parts_query.filter(
                make__iexact=vehicle.make,
                model__iexact=vehicle.model
            )
            
            # Filter by year if available
            if vehicle.year:
                parts_query = parts_query.filter(
                    year_from__lte=vehicle.year
                ).filter(
                    models.Q(year_to__isnull=True) |
                    models.Q(year_to__gte=vehicle.year)
                )
            
            # Filter by engine type if available
            if vehicle.engine_type:
                parts_query = parts_query.filter(
                    models.Q(engine_type='') |
                    models.Q(engine_type__iexact=vehicle.engine_type)
                )
                
        return parts_query.select_related('item')
        
    def suggest_compatible_operations(self, vehicle=None):
        """
        Suggest compatible operations for this maintenance schedule,
        optionally filtered by specific vehicle
        
        Args:
            vehicle: Optional Vehicle instance to filter compatible operations
            
        Returns:
            List of compatible OperationCompatibility instances
        """
        from inventory.models import OperationCompatibility
        
        # Base query for operations linked to this schedule
        ops_query = OperationCompatibility.objects.filter(
            maintenance_schedule=self
        )
        
        # If vehicle is provided, filter by its details
        if vehicle:
            ops_query = ops_query.filter(
                models.Q(vehicle_make='') | 
                models.Q(vehicle_make__iexact=vehicle.make)
            ).filter(
                models.Q(vehicle_model='') | 
                models.Q(vehicle_model__iexact=vehicle.model)
            )
            
            # Filter by year if available
            if vehicle.year:
                ops_query = ops_query.filter(
                    models.Q(year_from__isnull=True) |
                    models.Q(year_from__lte=vehicle.year)
                ).filter(
                    models.Q(year_to__isnull=True) |
                    models.Q(year_to__gte=vehicle.year)
                )
            
            # Filter by engine type if available
            if vehicle.engine_type:
                ops_query = ops_query.filter(
                    models.Q(engine_type='') |
                    models.Q(engine_type__iexact=vehicle.engine_type)
                )
                
        return ops_query.select_related('item', 'operation_type')

class ScheduleOperation(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """Operations included in a maintenance schedule"""
    maintenance_schedule = models.ForeignKey(
        MaintenanceSchedule,
        on_delete=models.CASCADE,
        related_name="operations",
        verbose_name=_("Maintenance Schedule")
    )
    name = models.CharField(_("Operation Name"), max_length=100)
    description = models.TextField(_("Description"), blank=True)
    duration_minutes = models.PositiveIntegerField(_("Duration (minutes)"), default=0)
    sequence = models.PositiveIntegerField(_("Sequence"), default=10)
    is_required = models.BooleanField(_("Required"), default=True)
    operation_type = models.ForeignKey(
        WorkOrderType,
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name="schedule_operations",
        verbose_name=_("Operation Type")
    )
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Schedule Operation")
        verbose_name_plural = _("Schedule Operations")
        ordering = ['maintenance_schedule', 'sequence']
        
    def __str__(self):
        return f"{self.name} ({self.maintenance_schedule.name})"

class OperationPart(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """Parts associated with schedule operations"""
    schedule_operation = models.ForeignKey(
        ScheduleOperation,
        on_delete=models.CASCADE,
        related_name="parts",
        verbose_name=_("Schedule Operation")
    )
    item = models.ForeignKey(
        'inventory.Item',
        on_delete=models.CASCADE,
        related_name="schedule_parts",
        verbose_name=_("Item")
    )
    quantity = models.DecimalField(_("Quantity"), max_digits=15, decimal_places=5, default=1.0)
    is_required = models.BooleanField(_("Required"), default=True)
    notes = models.TextField(_("Notes"), blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Operation Part")
        verbose_name_plural = _("Operation Parts")
        unique_together = [['tenant_id', 'schedule_operation', 'item']]
        
    def __str__(self):
        return f"{self.item.name} for {self.schedule_operation.name}"

class BillOfMaterials(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """Bill of Materials for assemblies and manufacturing processes"""
    name = models.CharField(_("Name"), max_length=100)
    description = models.TextField(_("Description"), blank=True)
    finished_item = models.ForeignKey(
        'inventory.Item', 
        on_delete=models.CASCADE, 
        related_name="bom_finished_items",
        verbose_name=_("Finished Item")
    )
    version = models.CharField(_("Version"), max_length=50, default="1.0")
    is_active = models.BooleanField(_("Is Active"), default=True)
    notes = models.TextField(_("Notes"), blank=True)
    attributes = models.JSONField(_("Custom Attributes"), default=dict, blank=True)
    
    objects = BaseQuerySet.as_manager()

    class Meta:
        verbose_name = _("Bill of Materials")
        verbose_name_plural = _("Bills of Materials")
        ordering = ['name']
        unique_together = [['tenant_id', 'name', 'version']]

    def __str__(self):
        return f"{self.name} (v{self.version})"

class BOMItem(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """Individual item within a Bill of Materials"""
    bom = models.ForeignKey(
        BillOfMaterials, 
        on_delete=models.CASCADE, 
        related_name="items",
        verbose_name=_("Bill of Materials")
    )
    item = models.ForeignKey(
        'inventory.Item',
        on_delete=models.CASCADE, 
        related_name="bom_component_items",
        verbose_name=_("Component Item")
    )
    quantity = models.DecimalField(_("Quantity"), max_digits=15, decimal_places=5)
    unit_of_measure = models.CharField(_("Unit of Measure"), max_length=50)
    is_optional = models.BooleanField(_("Is Optional"), default=False)
    sequence = models.PositiveIntegerField(_("Sequence"), default=10)
    notes = models.TextField(_("Notes"), blank=True)
    attributes = models.JSONField(_("Custom Attributes"), default=dict, blank=True)
    
    objects = BaseQuerySet.as_manager()

    class Meta:
        verbose_name = _("BOM Item")
        verbose_name_plural = _("BOM Items")
        ordering = ['sequence']

    def __str__(self):
        return f"{self.item.name} ({self.quantity} {self.unit_of_measure})"

class WorkOrder(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """Work Order for repairs, maintenance or manufacturing"""
    PRIORITY_CHOICES = [
        ('low', _('Low')),
        ('medium', _('Medium')),
        ('high', _('High')),
        ('critical', _('Critical')),
    ]
    
    STATUS_CHOICES = [
        ('draft', _('Draft')),
        ('planned', _('Planned')),
        ('in_progress', _('In Progress')),
        ('on_hold', _('On Hold')),
        ('completed', _('Completed')),
        ('cancelled', _('Cancelled')),
    ]
    
    WORK_ORDER_TYPES = [
        ('custom', _('Custom')),
        ('scheduled', _('Scheduled Maintenance')),
    ]
    
    work_order_number = models.CharField(_("Work Order #"), max_length=50, unique=True)
    work_order_type = models.ForeignKey(
        WorkOrderType, 
        on_delete=models.PROTECT,
        related_name="work_orders",
        verbose_name=_("Work Order Type")
    )
    bill_of_materials = models.ForeignKey(
        BillOfMaterials, 
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name="work_orders",
        verbose_name=_("Bill of Materials")
    )
    description = models.TextField(_("Description"))
    priority = models.CharField(_("Priority"), max_length=20, choices=PRIORITY_CHOICES, default='medium')
    status = models.CharField(_("Status"), max_length=20, choices=STATUS_CHOICES, default='draft')
    operation_category = models.CharField(_("Operation Category"), max_length=20, choices=WORK_ORDER_TYPES, default='custom')
    maintenance_schedule = models.ForeignKey(
        MaintenanceSchedule,
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name="work_orders",
        verbose_name=_("Maintenance Schedule")
    )
    
    # Service Center and Vehicle Information
    service_center = models.ForeignKey(
        ServiceCenter,
        on_delete=models.PROTECT,
        related_name="work_orders",
        verbose_name=_("Service Center"),
        null=True, blank=True
    )
    vehicle = models.ForeignKey(
        Vehicle,
        on_delete=models.SET_NULL,
        related_name="work_orders",
        verbose_name=_("Vehicle"),
        null=True, blank=True
    )
    current_odometer = models.PositiveIntegerField(_("Current Odometer Reading (km)"), null=True, blank=True)
    fuel_level = models.DecimalField(_("Fuel Level (%)"), max_digits=5, decimal_places=2, null=True, blank=True)
    
    # Scheduling
    planned_start_date = models.DateTimeField(_("Planned Start Date"), null=True, blank=True)
    planned_end_date = models.DateTimeField(_("Planned End Date"), null=True, blank=True)
    actual_start_date = models.DateTimeField(_("Actual Start Date"), null=True, blank=True)
    actual_end_date = models.DateTimeField(_("Actual End Date"), null=True, blank=True)
    
    # Customer Information
    customer = models.ForeignKey(
        Customer,
        on_delete=models.PROTECT,
        related_name="work_orders",
        verbose_name=_("Customer"),
        null=True, blank=True
    )
    # Legacy customer fields (to be migrated)
    customer_name = models.CharField(_("Customer Name"), max_length=255, blank=True)
    customer_phone = models.CharField(_("Customer Phone"), max_length=50, blank=True)
    customer_email = models.CharField(_("Customer Email"), max_length=100, blank=True)
    
    # Service Information
    service_item_serial = models.CharField(_("Item Serial #"), max_length=100, blank=True)
    warranty_status = models.BooleanField(_("Under Warranty"), default=False)
    
    # Financial
    estimated_cost = models.DecimalField(_("Estimated Cost"), max_digits=15, decimal_places=2, null=True, blank=True)
    actual_cost = models.DecimalField(_("Actual Cost"), max_digits=15, decimal_places=2, null=True, blank=True)
    
    # Other fields
    notes = models.TextField(_("Notes"), blank=True)
    attributes = models.JSONField(_("Custom Attributes"), default=dict, blank=True)
    
    objects = BaseQuerySet.as_manager()

    class Meta:
        verbose_name = _("Work Order")
        verbose_name_plural = _("Work Orders")
        ordering = ['-created_at']
        
    def __str__(self):
        return self.work_order_number
        
    def clean(self):
        """
        Validate that a vehicle doesn't have active work orders
        """
        if self.vehicle and not self.pk:  # Only for new work orders
            active_statuses = ['draft', 'planned', 'in_progress', 'on_hold']
            if WorkOrder.objects.filter(vehicle=self.vehicle, status__in=active_statuses).exists():
                raise ValidationError(_("This vehicle already has an active work order. Please complete or cancel it before creating a new one."))
        
        super().clean()
        
    def apply_maintenance_schedule(self):
        """Add operations and parts from the maintenance schedule to this work order"""
        if not self.maintenance_schedule:
            return
            
        # Create operations from the schedule
        for schedule_op in self.maintenance_schedule.operations.all():
            # Create the work order operation
            work_order_op = WorkOrderOperation.objects.create(
                tenant_id=self.tenant_id,
                work_order=self,
                sequence=schedule_op.sequence,
                name=schedule_op.name,
                description=schedule_op.description,
                duration_minutes=schedule_op.duration_minutes
            )
            
            # Add materials from the operation parts
            for part in schedule_op.parts.all():
                WorkOrderMaterial.objects.create(
                    tenant_id=self.tenant_id,
                    work_order=self,
                    item=part.item,
                    quantity=part.quantity,
                    unit_of_measure=part.item.unit_of_measurement.symbol if part.item.unit_of_measurement else '',
                    notes=part.notes
                )
    
    def auto_assign_customer_from_vehicle(self):
        """
        Automatically assign customer from vehicle if available
        """
        if not self.customer and self.vehicle and self.vehicle.owner:
            self.customer = self.vehicle.owner
            if not self.customer_name:
                self.customer_name = self.customer.full_name
            if not self.customer_phone:
                self.customer_phone = self.customer.phone
            if not self.customer_email:
                self.customer_email = self.customer.email
                
    def suggest_compatible_parts(self):
        """
        Suggest compatible parts based on the assigned vehicle and operation type
        
        Returns:
            List of compatible VehicleModelPart instances
        """
        from inventory.models import VehicleModelPart, OperationCompatibility
        
        if not self.vehicle:
            return []
            
        # 1. Check if we have a maintenance schedule
        if self.maintenance_schedule:
            return self.maintenance_schedule.suggest_compatible_parts(self.vehicle)
            
        # 2. Otherwise, find parts compatible with this vehicle and operation type
        vehicle = self.vehicle
        
        # Query for parts compatible with this vehicle
        parts_query = VehicleModelPart.objects.filter(
            make__iexact=vehicle.make,
            model__iexact=vehicle.model
        )
        
        # Filter by year if available
        if vehicle.year:
            parts_query = parts_query.filter(
                year_from__lte=vehicle.year
            ).filter(
                models.Q(year_to__isnull=True) |
                models.Q(year_to__gte=vehicle.year)
            )
        
        # Filter by engine type if available
        if hasattr(vehicle, 'engine_type') and vehicle.engine_type:
            parts_query = parts_query.filter(
                models.Q(engine_type='') |
                models.Q(engine_type__iexact=vehicle.engine_type)
            )
            
        # 3. Also consider operation type compatibility
        if self.work_order_type:
            # Find compatible items for this operation type
            op_compat_items = OperationCompatibility.objects.filter(
                operation_type=self.work_order_type
            ).values_list('item_id', flat=True)
            
            # Union with vehicle-specific parts
            parts_query = parts_query.filter(
                models.Q(item_id__in=op_compat_items)
            )
            
        return parts_query.select_related('item').order_by('item__name')

class WorkOrderOperation(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """Individual operation/step within a work order"""
    work_order = models.ForeignKey(
        WorkOrder, 
        on_delete=models.CASCADE, 
        related_name="operations",
        verbose_name=_("Work Order")
    )
    sequence = models.PositiveIntegerField(_("Sequence"), default=10)
    name = models.CharField(_("Operation Name"), max_length=100)
    description = models.TextField(_("Description"), blank=True)
    duration_minutes = models.PositiveIntegerField(_("Duration (minutes)"), default=0)
    is_completed = models.BooleanField(_("Is Completed"), default=False)
    completed_at = models.DateTimeField(_("Completed At"), null=True, blank=True)
    notes = models.TextField(_("Notes"), blank=True)
    attributes = models.JSONField(_("Custom Attributes"), default=dict, blank=True)
    
    objects = BaseQuerySet.as_manager()

    class Meta:
        verbose_name = _("Work Order Operation")
        verbose_name_plural = _("Work Order Operations")
        ordering = ['work_order', 'sequence']

    def __str__(self):
        return f"{self.name} ({self.work_order.work_order_number})"

class WorkOrderMaterial(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """Materials used in a work order"""
    work_order = models.ForeignKey(
        WorkOrder, 
        on_delete=models.CASCADE, 
        related_name="materials",
        verbose_name=_("Work Order")
    )
    item = models.ForeignKey(
        'inventory.Item',
        on_delete=models.CASCADE, 
        related_name="work_order_usages",
        verbose_name=_("Item")
    )
    quantity = models.DecimalField(_("Quantity"), max_digits=15, decimal_places=5)
    unit_of_measure = models.CharField(_("Unit of Measure"), max_length=50)
    is_consumed = models.BooleanField(_("Is Consumed"), default=False)
    notes = models.TextField(_("Notes"), blank=True)
    attributes = models.JSONField(_("Custom Attributes"), default=dict, blank=True)
    
    objects = BaseQuerySet.as_manager()

    class Meta:
        verbose_name = _("Work Order Material")
        verbose_name_plural = _("Work Order Materials")
        ordering = ['work_order', 'item__name']

    def __str__(self):
        return f"{self.item.name} ({self.quantity} {self.unit_of_measure})"
