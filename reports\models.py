from django.db import models
from django.utils.translation import gettext_lazy as _
from core.models import TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel
from core.querysets import BaseQuerySet


class Report(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """
    Report model for storing user-generated reports
    """
    REPORT_TYPES = (
        ('inventory', _('Inventory')),
        ('sales', _('Sales')),
        ('purchases', _('Purchases')),
        ('warehouse', _('Warehouse')),
        ('custom', _('Custom')),
    )
    
    name = models.Char<PERSON>ield(_("Name"), max_length=255)
    description = models.TextField(_("Description"), blank=True)
    report_type = models.CharField(_("Report Type"), max_length=20, choices=REPORT_TYPES)
    parameters = models.JSONField(_("Parameters"), default=dict)
    query = models.TextField(_("Query"), blank=True)
    is_scheduled = models.BooleanField(_("Scheduled"), default=False)
    schedule = models.JSONField(_("Schedule"), default=dict, blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Report")
        verbose_name_plural = _("Reports")
        
    def __str__(self):
        return f"{self.name} ({self.get_report_type_display()})"


class ReportExecution(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """
    Report execution model for tracking report runs
    """
    STATUS_CHOICES = (
        ('pending', _('Pending')),
        ('running', _('Running')),
        ('completed', _('Completed')),
        ('failed', _('Failed')),
    )
    
    report = models.ForeignKey(
        Report, 
        on_delete=models.CASCADE, 
        related_name='executions',
        verbose_name=_("Report")
    )
    parameters = models.JSONField(_("Parameters Used"), default=dict)
    status = models.CharField(_("Status"), max_length=20, choices=STATUS_CHOICES, default='pending')
    start_time = models.DateTimeField(_("Start Time"), null=True, blank=True)
    end_time = models.DateTimeField(_("End Time"), null=True, blank=True)
    result_file = models.FileField(_("Result File"), upload_to='reports', blank=True, null=True)
    error_message = models.TextField(_("Error Message"), blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Report Execution")
        verbose_name_plural = _("Report Executions")
        ordering = ['-created_at']
        
    def __str__(self):
        return f"{self.report.name} - {self.created_at}"
        
    @property
    def duration(self):
        """
        Calculate duration of report execution in seconds
        """
        if self.start_time and self.end_time:
            return (self.end_time - self.start_time).total_seconds()
        return None


class Dashboard(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """
    Dashboard model for user-created dashboards
    """
    name = models.CharField(_("Name"), max_length=255)
    description = models.TextField(_("Description"), blank=True)
    layout = models.JSONField(_("Layout"), default=dict)
    is_default = models.BooleanField(_("Default"), default=False)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Dashboard")
        verbose_name_plural = _("Dashboards")
        
    def __str__(self):
        return self.name


class DashboardWidget(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """
    Dashboard widget model for items on a dashboard
    """
    WIDGET_TYPES = (
        ('chart', _('Chart')),
        ('number', _('Number')),
        ('table', _('Table')),
        ('text', _('Text')),
        ('custom', _('Custom')),
    )
    
    dashboard = models.ForeignKey(
        Dashboard, 
        on_delete=models.CASCADE, 
        related_name='widgets',
        verbose_name=_("Dashboard")
    )
    report = models.ForeignKey(
        Report, 
        on_delete=models.SET_NULL, 
        related_name='widgets',
        verbose_name=_("Report"),
        null=True,
        blank=True
    )
    title = models.CharField(_("Title"), max_length=255)
    widget_type = models.CharField(_("Widget Type"), max_length=20, choices=WIDGET_TYPES)
    config = models.JSONField(_("Configuration"), default=dict)
    position = models.IntegerField(_("Position"), default=0)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Dashboard Widget")
        verbose_name_plural = _("Dashboard Widgets")
        ordering = ['position']
        
    def __str__(self):
        return f"{self.title} ({self.get_widget_type_display()})"
