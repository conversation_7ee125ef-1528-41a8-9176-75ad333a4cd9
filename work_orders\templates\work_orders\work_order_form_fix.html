{% extends "base.html" %}
{% load i18n %}
{% load static %}

{% block extra_js %}
<script>
    // Wait for the page to load
    document.addEventListener('DOMContentLoaded', function() {
        // Fix for removing operations
        function fixOperationRemoval() {
            // Add a mutation observer to watch for new operations
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                        // Check if any of the added nodes are operation items
                        mutation.addedNodes.forEach(function(node) {
                            if (node.classList && node.classList.contains('operation-item')) {
                                // Find the remove button in this operation
                                const removeBtn = node.querySelector('.remove-operation-btn');
                                if (removeBtn) {
                                    // Add our custom event listener that updates the parts list
                                    removeBtn.addEventListener('click', function() {
                                        // Run the preparePartsStep function after a short delay
                                        setTimeout(function() {
                                            if (typeof preparePartsStep === 'function') {
                                                preparePartsStep();
                                                console.log('Updated parts list after operation removal');
                                            }
                                        }, 100);
                                    });
                                }
                            }
                        });
                    }
                });
            });
            
            // Start observing the operations list
            const operationsList = document.getElementById('operations-list');
            if (operationsList) {
                observer.observe(operationsList, { childList: true });
                console.log('Initialized operation removal fix');
            }
        }
        
        // Run the fix
        fixOperationRemoval();
    });
</script>
{% endblock %}

{% block content %}
<!-- This file is just a fix and doesn't need content -->
<!-- Include the original template -->
{% include "work_orders/work_order_form.html" %}
{% endblock %} 