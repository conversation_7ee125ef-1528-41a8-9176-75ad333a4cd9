# Generated by Django 4.2.20 on 2025-05-07 14:16

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0002_itemdocument'),
    ]

    operations = [
        migrations.CreateModel(
            name='UnitOfMeasurement',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('name', models.CharField(max_length=50, verbose_name='Name')),
                ('symbol', models.Char<PERSON>ield(max_length=10, verbose_name='Symbol')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('is_base_unit', models.BooleanField(default=False, help_text='If checked, this unit will be used as a reference for conversions', verbose_name='Is Base Unit')),
            ],
            options={
                'verbose_name': 'Unit of Measurement',
                'verbose_name_plural': 'Units of Measurement',
                'unique_together': {('tenant_id', 'symbol'), ('tenant_id', 'name')},
            },
        ),
        migrations.AddField(
            model_name='item',
            name='unit_of_measurement',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='items', to='inventory.unitofmeasurement', verbose_name='Unit of Measurement'),
        ),
        migrations.AddField(
            model_name='movement',
            name='unit_of_measurement',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='movements', to='inventory.unitofmeasurement', verbose_name='Unit of Measurement'),
        ),
        migrations.CreateModel(
            name='UnitConversion',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('conversion_factor', models.DecimalField(decimal_places=10, help_text="Multiply quantity in 'from_unit' by this factor to get quantity in 'to_unit'", max_digits=20, verbose_name='Conversion Factor')),
                ('from_unit', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='conversions_from', to='inventory.unitofmeasurement', verbose_name='From Unit')),
                ('to_unit', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='conversions_to', to='inventory.unitofmeasurement', verbose_name='To Unit')),
            ],
            options={
                'verbose_name': 'Unit Conversion',
                'verbose_name_plural': 'Unit Conversions',
                'unique_together': {('tenant_id', 'from_unit', 'to_unit')},
            },
        ),
    ]
