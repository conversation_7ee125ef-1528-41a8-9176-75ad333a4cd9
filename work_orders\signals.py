import logging
from django.db.models.signals import post_save, pre_save
from django.dispatch import receiver
from .models import WorkOrder, WorkOrderMaterial

logger = logging.getLogger(__name__)

@receiver(pre_save, sender=WorkOrder)
def work_order_pre_save(sender, instance, **kwargs):
    """Track status changes before save"""
    if instance.pk:
        try:
            old_instance = WorkOrder.objects.get(pk=instance.pk)
            instance._old_status = old_instance.status
        except WorkOrder.DoesNotExist:
            instance._old_status = None
    else:
        instance._old_status = None

@receiver(post_save, sender=WorkOrder)
def work_order_post_save(sender, instance, created, **kwargs):
    """
    Signal handler for work order status changes
    """
    if not created and hasattr(instance, '_old_status') and instance._old_status != instance.status:
        old_status = instance._old_status
        new_status = instance.status
        logger.info(f"Work order {instance.work_order_number} status changed: {old_status} -> {new_status}")
        
        # If the work order is now completed, handle material consumption
        if new_status == 'completed' and old_status != 'completed':
            for material in instance.materials.all():
                if not material.is_consumed:
                    # Here we would connect to inventory to reduce stock
                    # This would need to be implemented with inventory movement records
                    logger.info(f"Material {material.item.name} marked as consumed for work order {instance.work_order_number}")
                    material.is_consumed = True
                    material.save()

@receiver(post_save, sender=WorkOrderMaterial)
def work_order_material_added(sender, instance, created, **kwargs):
    """
    Signal handler for when materials are added to a work order
    """
    if created:
        logger.info(f"Material {instance.item.name} added to work order {instance.work_order.work_order_number}")
        
        # If the work order is already completed, mark the material as consumed immediately
        if instance.work_order.status == 'completed':
            instance.is_consumed = True
            instance.save(update_fields=['is_consumed']) 