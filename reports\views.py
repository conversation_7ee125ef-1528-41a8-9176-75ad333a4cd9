from django.shortcuts import render, get_object_or_404, redirect
from django.http import JsonResponse, HttpResponse
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView
from django.views.generic.base import View
from django.contrib.auth.mixins import LoginRequiredMixin
from django.urls import reverse_lazy
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.contrib import messages
import json
import pandas as pd
import logging

from feature_flags.services import is_feature_active
from .models import Report, ReportExecution, Dashboard, DashboardWidget
from .services import execute_report, get_dashboard_data

logger = logging.getLogger(__name__)


class AdvancedReportingRequiredMixin:
    """Mixin to check if advanced reporting feature is enabled"""
    
    def dispatch(self, request, *args, **kwargs):
        if not is_feature_active('advanced_reporting', request):
            messages.warning(request, _("Advanced reporting feature is not enabled."))
            return redirect('reports:basic_reports')
        return super().dispatch(request, *args, **kwargs)


class ReportListView(LoginRequiredMixin, ListView):
    """View to list all available reports"""
    model = Report
    template_name = 'reports/report_list.html'
    context_object_name = 'reports'
    
    def get_queryset(self):
        """Filter reports by tenant"""
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            return Report.objects.filter(tenant_id=tenant_id)
        # If no tenant_id is available, return an empty queryset
        # You could also redirect to an error page or setup page instead
        return Report.objects.none()
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['has_advanced_reporting'] = is_feature_active('advanced_reporting', self.request)
        # Add a flag to indicate if tenant_id is missing
        context['missing_tenant'] = getattr(self.request, 'tenant_id', None) is None
        return context


class ReportDetailView(LoginRequiredMixin, DetailView):
    """View to display a report and its parameters"""
    model = Report
    template_name = 'reports/report_detail.html'
    context_object_name = 'report'
    
    def get_queryset(self):
        """Filter reports by tenant"""
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            return Report.objects.filter(tenant_id=tenant_id)
        # If no tenant_id is available, return an empty queryset
        return Report.objects.none()
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['parameters'] = self.object.parameters
        context['executions'] = self.object.executions.order_by('-created_at')[:5]
        context['has_advanced_reporting'] = is_feature_active('advanced_reporting', self.request)
        context['missing_tenant'] = getattr(self.request, 'tenant_id', None) is None
        return context


class ExecuteReportView(LoginRequiredMixin, View):
    """View to execute a report and display results"""
    
    def get(self, request, pk):
        """Display form to set report parameters"""
        tenant_id = getattr(request, 'tenant_id', None)
        if not tenant_id:
            messages.error(request, _("Tenant ID is missing. Please set the X-Tenant-ID header."))
            return redirect('reports:report_list')
            
        report = get_object_or_404(Report, pk=pk, tenant_id=tenant_id)
        return render(request, 'reports/execute_report.html', {
            'report': report,
            'parameters': report.parameters,
            'has_advanced_reporting': is_feature_active('advanced_reporting', request)
        })
    
    def post(self, request, pk):
        """Execute report with provided parameters"""
        tenant_id = getattr(request, 'tenant_id', None)
        if not tenant_id:
            messages.error(request, _("Tenant ID is missing. Please set the X-Tenant-ID header."))
            return redirect('reports:report_list')
            
        report = get_object_or_404(Report, pk=pk, tenant_id=tenant_id)
        
        # Parse parameters from form
        try:
            parameters = {}
            for key, value in request.POST.items():
                if key.startswith('param_'):
                    param_name = key[6:]  # Remove 'param_' prefix
                    parameters[param_name] = value
                    
            # Handle export format
            export_format = request.POST.get('export_format', None)
            if export_format and export_format != 'none':
                parameters['export_format'] = export_format
            
            # Execute report
            execution, data = execute_report(
                report_id=pk,
                parameters=parameters,
                tenant_id=tenant_id,
                user=request.user
            )
            
            # Render results
            return render(request, 'reports/report_results.html', {
                'report': report,
                'execution': execution,
                'data': data,
                'has_advanced_reporting': is_feature_active('advanced_reporting', request)
            })
            
        except Exception as e:
            logger.exception(f"Error executing report: {e}")
            messages.error(request, _("Error executing report: {}").format(str(e)))
            return redirect('reports:report_detail', pk=pk)


class ReportExecutionDetailView(LoginRequiredMixin, DetailView):
    """View to display execution details and results"""
    model = ReportExecution
    template_name = 'reports/execution_detail.html'
    context_object_name = 'execution'
    
    def get_queryset(self):
        """Filter executions by tenant"""
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            return ReportExecution.objects.filter(tenant_id=tenant_id)
        # If no tenant_id is available, return an empty queryset
        return ReportExecution.objects.none()
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['report'] = self.object.report
        
        # Try to reconstruct data from the result file if available
        if self.object.result_file:
            try:
                # Determine file type by extension
                filename = self.object.result_file.name
                if filename.endswith('.json'):
                    with open(self.object.result_file.path, 'r') as f:
                        context['data'] = json.load(f)
                elif filename.endswith('.csv'):
                    df = pd.read_csv(self.object.result_file.path)
                    context['data'] = {
                        'title': self.object.report.name,
                        'generated_at': self.object.created_at.isoformat(),
                        'data': df.to_dict('records')
                    }
                elif filename.endswith('.xlsx'):
                    df = pd.read_excel(self.object.result_file.path)
                    context['data'] = {
                        'title': self.object.report.name,
                        'generated_at': self.object.created_at.isoformat(),
                        'data': df.to_dict('records')
                    }
            except Exception as e:
                logger.exception(f"Error loading result file: {e}")
                context['error'] = _("Error loading result file: {}").format(str(e))
        
        context['has_advanced_reporting'] = is_feature_active('advanced_reporting', self.request)
        context['missing_tenant'] = getattr(self.request, 'tenant_id', None) is None
        return context


# Dashboard views (requires advanced reporting feature)

class DashboardListView(LoginRequiredMixin, AdvancedReportingRequiredMixin, ListView):
    """View to list all available dashboards"""
    model = Dashboard
    template_name = 'reports/dashboard_list.html'
    context_object_name = 'dashboards'
    
    def get_queryset(self):
        """Filter dashboards by tenant"""
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            return Dashboard.objects.filter(tenant_id=tenant_id)
        # If no tenant_id is available, return an empty queryset
        return Dashboard.objects.none()
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['missing_tenant'] = getattr(self.request, 'tenant_id', None) is None
        return context


class DashboardDetailView(LoginRequiredMixin, AdvancedReportingRequiredMixin, DetailView):
    """View to display a dashboard with its widgets"""
    model = Dashboard
    template_name = 'reports/dashboard_detail.html'
    context_object_name = 'dashboard'
    
    def get_queryset(self):
        """Filter dashboards by tenant"""
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            return Dashboard.objects.filter(tenant_id=tenant_id)
        # If no tenant_id is available, return an empty queryset
        return Dashboard.objects.none()
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Get widgets sorted by position
        context['widgets'] = self.object.widgets.all().order_by('position')
        context['missing_tenant'] = getattr(self.request, 'tenant_id', None) is None
        
        tenant_id = getattr(self.request, 'tenant_id', None)
        try:
            # Get initial data for each widget
            for widget in context['widgets']:
                if widget.report:
                    widget.data = get_dashboard_data(
                        widget_id=widget.id,
                        tenant_id=tenant_id
                    )
        except Exception as e:
            logger.exception(f"Error loading dashboard data: {e}")
            messages.error(self.request, _("Error loading dashboard data: {}").format(str(e)))
            
        return context


class DashboardWidgetDataView(LoginRequiredMixin, AdvancedReportingRequiredMixin, View):
    """API view to get data for a dashboard widget"""
    
    def get(self, request, pk):
        """Get data for the specified widget"""
        tenant_id = getattr(request, 'tenant_id', None)
        if not tenant_id:
            return JsonResponse({
                'success': False,
                'error': 'Tenant ID is missing'
            }, status=400)
            
        try:
            widget = get_object_or_404(DashboardWidget, pk=pk, tenant_id=tenant_id)
            data = get_dashboard_data(widget_id=pk, tenant_id=tenant_id)
            
            return JsonResponse({
                'success': True,
                'widget_id': str(widget.id),
                'title': widget.title,
                'data': data
            })
        except Exception as e:
            logger.exception(f"Error generating widget data: {e}")
            return JsonResponse({
                'success': False,
                'widget_id': str(pk),
                'error': str(e)
            }, status=500)


# Basic reports (available without advanced reporting feature)

class BasicInventoryReportView(LoginRequiredMixin, View):
    """View for basic inventory report (available without advanced reporting)"""
    
    def get(self, request):
        """Display basic inventory report"""
        from inventory.models import Item
        
        tenant_id = getattr(request, 'tenant_id', None)
        if not tenant_id:
            messages.error(request, _("Tenant ID is missing. Please set the X-Tenant-ID header."))
            return redirect('reports:report_list')
        
        # Get basic inventory data
        items = Item.objects.filter(tenant_id=tenant_id)
        
        # Calculate summary statistics
        total_items = items.count()
        total_value = sum(item.quantity * item.unit_price for item in items)
        low_stock_items = sum(1 for item in items if item.quantity < item.min_stock_level)
        
        context = {
            'items': items,
            'total_items': total_items,
            'total_value': total_value,
            'low_stock_items': low_stock_items,
            'has_advanced_reporting': is_feature_active('advanced_reporting', request)
        }
        
        return render(request, 'reports/basic_inventory_report.html', context)


class BasicSalesReportView(LoginRequiredMixin, View):
    """View for basic sales report (available without advanced reporting)"""
    
    def get(self, request):
        """Display basic sales report"""
        from sales.models import SalesOrder
        
        tenant_id = getattr(request, 'tenant_id', None)
        if not tenant_id:
            messages.error(request, _("Tenant ID is missing. Please set the X-Tenant-ID header."))
            return redirect('reports:report_list')
        
        # Get last 30 days of sales
        end_date = timezone.now()
        start_date = end_date - timezone.timedelta(days=30)
        sales = SalesOrder.objects.filter(
            tenant_id=tenant_id,
            created_at__gte=start_date,
            created_at__lte=end_date
        )
        
        # Calculate summary statistics
        total_sales = sales.count()
        total_revenue = sum(sale.total_amount for sale in sales)
        
        context = {
            'sales': sales,
            'total_sales': total_sales,
            'total_revenue': total_revenue,
            'start_date': start_date,
            'end_date': end_date,
            'has_advanced_reporting': is_feature_active('advanced_reporting', request)
        }
        
        return render(request, 'reports/basic_sales_report.html', context)
