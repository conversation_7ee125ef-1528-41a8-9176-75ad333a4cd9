from django.db import models
from django.utils.translation import gettext_lazy as _
from core.models import TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel
from core.querysets import BaseQuerySet


class TenantSetting(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """
    Settings for a specific tenant
    """
    name = models.CharField(_("Name"), max_length=100)
    description = models.TextField(_("Description"), blank=True)
    value = models.JSONField(_("Value"), default=dict)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Tenant Setting")
        verbose_name_plural = _("Tenant Settings")
        unique_together = [['tenant_id', 'name']]
        
    def __str__(self):
        return f"{self.name} for tenant {self.tenant_id}"


class SystemSetting(TimeStampedModel, UUIDPrimaryKeyModel):
    """
    System-wide settings
    """
    name = models.Char<PERSON><PERSON>(_("Name"), max_length=100, unique=True)
    description = models.TextField(_("Description"), blank=True)
    value = models.JSONField(_("Value"), default=dict)
    
    class Meta:
        verbose_name = _("System Setting")
        verbose_name_plural = _("System Settings")
        
    def __str__(self):
        return f"{self.name}"


class TenantProfile(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """
    Profile information for a tenant
    """
    name = models.CharField(_("Company Name"), max_length=255)
    logo = models.ImageField(_("Logo"), upload_to='tenant_logos', blank=True, null=True)
    contact_email = models.EmailField(_("Contact Email"), blank=True)
    contact_phone = models.CharField(_("Contact Phone"), max_length=50, blank=True)
    address = models.TextField(_("Address"), blank=True)
    website = models.URLField(_("Website"), blank=True)
    subscription_tier = models.CharField(
        _("Subscription Tier"),
        max_length=50,
        choices=(
            ('free', _('Free')),
            ('basic', _('Basic')),
            ('professional', _('Professional')),
            ('enterprise', _('Enterprise')),
        ),
        default='free'
    )
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Tenant Profile")
        verbose_name_plural = _("Tenant Profiles")
        
    def __str__(self):
        return f"{self.name} ({self.tenant_id})"
