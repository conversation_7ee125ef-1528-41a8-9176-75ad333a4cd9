# Generated by Django 4.2.20 on 2025-05-07 09:45

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='ModuleFlag',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='Name')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('is_active', models.BooleanField(default=False, verbose_name='Active')),
            ],
            options={
                'verbose_name': 'Module Flag',
                'verbose_name_plural': 'Module Flags',
            },
        ),
        migrations.CreateModel(
            name='FeatureFlag',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='Name')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('is_active', models.BooleanField(default=False, verbose_name='Active')),
                ('module', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='features', to='feature_flags.moduleflag')),
            ],
            options={
                'verbose_name': 'Feature Flag',
                'verbose_name_plural': 'Feature Flags',
            },
        ),
        migrations.CreateModel(
            name='TenantModuleFlag',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('is_active', models.BooleanField(default=False, verbose_name='Active')),
                ('module_flag', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tenant_flags', to='feature_flags.moduleflag')),
            ],
            options={
                'verbose_name': 'Tenant Module Flag',
                'verbose_name_plural': 'Tenant Module Flags',
                'unique_together': {('tenant_id', 'module_flag')},
            },
        ),
        migrations.CreateModel(
            name='TenantFeatureFlag',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('is_active', models.BooleanField(default=False, verbose_name='Active')),
                ('feature_flag', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tenant_flags', to='feature_flags.featureflag')),
            ],
            options={
                'verbose_name': 'Tenant Feature Flag',
                'verbose_name_plural': 'Tenant Feature Flags',
                'unique_together': {('tenant_id', 'feature_flag')},
            },
        ),
    ]
