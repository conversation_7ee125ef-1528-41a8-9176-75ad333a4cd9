# Generated by Django 4.2.20 on 2025-05-08 19:47

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('user_roles', '0002_alter_role_role_type'),
    ]

    operations = [
        migrations.AlterField(
            model_name='role',
            name='role_type',
            field=models.CharField(
                choices=[
                    ('system_admin', 'System Administrator'),
                    ('franchise_admin', 'Franchise Administrator'),
                    ('company_admin', 'Company Administrator'),
                    ('service_center_manager', 'Service Center Manager'),
                    ('service_advisor', 'Service Advisor'),
                    ('technician', 'Technician'),
                    ('inventory_manager', 'Inventory Manager'),
                    ('parts_clerk', 'Parts Clerk'),
                    ('accountant', 'Accountant'),
                    ('receptionist', 'Receptionist'),
                    ('customer_service', 'Customer Service'),
                    ('readonly', 'Read Only User'),
                    ('warranty_admin', 'Warranty Administrator'),
                    ('finance_manager', 'Finance Manager'),
                    ('marketing_manager', 'Marketing Manager'),
                    ('quality_inspector', 'Quality Control Inspector'),
                    ('fleet_manager', 'Fleet Account Manager'),
                    ('regional_manager', 'Regional Manager'),
                    ('cashier', 'Cashier/Billing Specialist'),
                    # Small center specific roles
                    ('small_center_manager', 'Small Center Manager'),
                    ('small_center_advisor', 'Small Center Advisor'),
                    ('small_center_technician', 'Small Center Technician'),
                    # Medium center specific roles
                    ('medium_center_manager', 'Medium Center Manager'),
                    ('medium_center_advisor', 'Medium Center Advisor'),
                    ('medium_center_parts', 'Medium Center Parts Specialist'),
                    ('medium_center_cashier', 'Medium Center Cashier'),
                    # Large center specific roles
                    ('large_center_service_manager', 'Large Center Service Manager'),
                    ('large_center_parts_manager', 'Large Center Parts Manager'),
                    ('large_center_customer_manager', 'Large Center Customer Service Manager'),
                ],
                max_length=50,
                verbose_name='Role Type'
            ),
        ),
    ]
