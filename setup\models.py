from django.db import models
from django.utils.translation import gettext_lazy as _
from core.models.common import TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel
from core.querysets import BaseQuerySet
from django.contrib.auth.models import User
from warehouse.models import Location
from django.utils import timezone

class ServiceLevel(TimeStampedModel, UUIDPrimaryKeyModel):
    """
    Service levels for franchise agreements (Gold, Silver, Bronze, etc.)
    """
    name = models.CharField(_("Service Level Name"), max_length=100)
    description = models.TextField(_("Description"), blank=True)
    priority = models.PositiveSmallIntegerField(_("Priority"), default=0)
    response_time_hours = models.PositiveIntegerField(_("Response Time (Hours)"), default=24)
    resolution_time_hours = models.PositiveIntegerField(_("Resolution Time (Hours)"), default=72)
    support_hours = models.CharField(_("Support Hours"), max_length=100, default="24/7")
    is_active = models.BooleanField(_("Is Active"), default=True)
    
    # Extended SLA metrics
    emergency_response_time_hours = models.PositiveIntegerField(
        _("Emergency Response Time (Hours)"), 
        default=4,
        help_text=_("Response time for emergency/critical issues")
    )
    onsite_response_time_hours = models.PositiveIntegerField(
        _("Onsite Response Time (Hours)"),
        default=48,
        help_text=_("Time to arrive onsite if required")
    )
    parts_delivery_time_hours = models.PositiveIntegerField(
        _("Parts Delivery Time (Hours)"),
        default=72,
        help_text=_("Maximum time for parts delivery")
    )
    
    # Availability targets
    availability_target_percent = models.DecimalField(
        _("Availability Target (%)"),
        max_digits=5,
        decimal_places=2,
        default=99.5,
        help_text=_("Uptime/availability target in percent")
    )
    
    # Maintenance windows
    scheduled_maintenance_frequency = models.CharField(
        _("Maintenance Frequency"),
        max_length=100,
        blank=True,
        help_text=_("Frequency of scheduled maintenance (e.g., 'Monthly', 'Quarterly')")
    )
    maintenance_window_hours = models.PositiveIntegerField(
        _("Maintenance Window (Hours)"),
        null=True,
        blank=True,
        help_text=_("Maximum duration for scheduled maintenance")
    )
    
    # Service desk and support
    service_desk_hours = models.CharField(
        _("Service Desk Hours"),
        max_length=100,
        default="9AM-5PM, Mon-Fri",
        help_text=_("Hours when service desk is available")
    )
    max_incidents_per_month = models.PositiveIntegerField(
        _("Maximum Incidents per Month"),
        null=True,
        blank=True,
        help_text=_("Maximum number of support incidents included in the SLA")
    )
    
    # Financial terms
    penalty_per_hour_downtime = models.DecimalField(
        _("Penalty per Hour of Downtime"),
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        help_text=_("Financial penalty per hour of downtime exceeding SLA")
    )
    
    # Custom attributes for additional SLA terms
    attributes = models.JSONField(_("Custom Attributes"), default=dict, blank=True)

    class Meta:
        verbose_name = _("Service Level")
        verbose_name_plural = _("Service Levels")
        ordering = ['priority']

    def __str__(self):
        return self.name

class Customer(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """
    Customer model for storing customer information
    """
    # User information
    first_name = models.CharField(_('First Name'), max_length=255)
    second_name = models.CharField(_('Second Name'), max_length=255, blank=True)
    third_name = models.CharField(_('Third Name'), max_length=255, blank=True)
    last_name = models.CharField(_('Last Name'), max_length=255)
    email = models.EmailField(_('Email'), blank=True)
    phone = models.CharField(_('Phone'), max_length=20)
    alternative_phone = models.CharField(_('Alternative Phone'), max_length=20, blank=True)
    # Identification Fields
    id_number = models.CharField(_('ID Number'), max_length=100, blank=True)
    ID_TYPE_CHOICES = [
        ('national_id', _('National ID')),
        ('passport', _('Passport')),
        ('residence_permit', _('Residence Permit')),
        ('driving_license', _('Driving License')),
        ('other', _('Other')),
    ]
    id_type = models.CharField(_('Identification Type'), max_length=20, choices=ID_TYPE_CHOICES, blank=True)
    
    # Classification
    classification = models.ForeignKey(
        'billing.CustomerClassification',
        on_delete=models.SET_NULL,
        related_name='customers',
        verbose_name=_('Classification'),
        null=True, blank=True
    )
    
    # Customer type
    CUSTOMER_TYPES = (
        ('individual', _('Individual')),
        ('corporate', _('Corporate')),
    )
    customer_type = models.CharField(_("Customer Type"), max_length=20, choices=CUSTOMER_TYPES, default='individual')
    
    # Demographics
    GENDER_CHOICES = (
        ('male', _('Male')),
        ('female', _('Female')),
        ('other', _('Other')),
    )
    gender = models.CharField(_("Gender"), max_length=10, choices=GENDER_CHOICES, blank=True)
    date_of_birth = models.DateField(_("Date of Birth"), null=True, blank=True)
    
    # Company information (for corporate customers)
    company_name = models.CharField(_("Company Name"), max_length=255, blank=True)
    company_registration = models.CharField(_("Company Registration"), max_length=100, blank=True)
    
    # Contact information
    address = models.TextField(_("Address"), blank=True)
    city = models.CharField(_("City"), max_length=100, blank=True)
    state = models.CharField(_("State/Province"), max_length=100, blank=True)
    postal_code = models.CharField(_("Postal Code"), max_length=20, blank=True)
    country = models.CharField(_("Country"), max_length=100, blank=True)
    
    # Service center association
    service_center = models.ForeignKey(
        'ServiceCenter',
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name="customers",
        verbose_name=_("Service Center")
    )
    
    # Additional information
    notes = models.TextField(_("Notes"), blank=True)
    is_active = models.BooleanField(_("Is Active"), default=True)
    attributes = models.JSONField(_("Custom Attributes"), default=dict, blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Customer")
        verbose_name_plural = _("Customers")
        ordering = ['first_name', 'last_name']
        
    def __str__(self):
        return f"{self.first_name} {self.last_name}"
    
    @property
    def full_name(self):
        """Return the customer's full Arabic name"""
        # Assuming names might be stored across multiple fields
        parts = [self.first_name, self.second_name, self.third_name, self.last_name]
        return " ".join(part for part in parts if part)
    
    @property
    def age(self):
        """Calculate age based on date of birth"""
        if not self.date_of_birth:
            return None
            
        today = timezone.now().date()
        born = self.date_of_birth
        return today.year - born.year - ((today.month, today.day) < (born.month, born.day))

class Franchise(TimeStampedModel, UUIDPrimaryKeyModel):
    """
    Top-level organization that owns multiple companies
    """
    name = models.CharField(_("Franchise Name"), max_length=255)
    code = models.CharField(_("Franchise Code"), max_length=50, unique=True)
    logo = models.ImageField(_("Logo"), upload_to='franchise_logos', blank=True, null=True)
    
    # Contact Information
    address = models.TextField(_("Address"), blank=True)
    city = models.CharField(_("City"), max_length=100, blank=True)
    state = models.CharField(_("State/Province"), max_length=100, blank=True)
    country = models.CharField(_("Country"), max_length=100, blank=True)
    postal_code = models.CharField(_("Postal Code"), max_length=20, blank=True)
    phone = models.CharField(_("Phone"), max_length=50, blank=True)
    email = models.EmailField(_("Email"), blank=True)
    website = models.URLField(_("Website"), blank=True)
    
    # Business Information
    tax_id = models.CharField(_("Tax ID"), max_length=100, blank=True)
    registration_number = models.CharField(_("Registration Number"), max_length=100, blank=True)
    founding_date = models.DateField(_("Founding Date"), null=True, blank=True)
    
    # Other Information
    notes = models.TextField(_("Notes"), blank=True)
    is_active = models.BooleanField(_("Is Active"), default=True)
    attributes = models.JSONField(_("Custom Attributes"), default=dict, blank=True)
    
    class Meta:
        verbose_name = _("Franchise")
        verbose_name_plural = _("Franchises")
        ordering = ['name']
        
    def __str__(self):
        return self.name

class Company(TimeStampedModel, UUIDPrimaryKeyModel):
    """
    Company belonging to a franchise, may have multiple service centers
    """
    franchise = models.ForeignKey(
        Franchise, 
        on_delete=models.CASCADE, 
        related_name="companies",
        verbose_name=_("Franchise")
    )
    name = models.CharField(_("Company Name"), max_length=255)
    code = models.CharField(_("Company Code"), max_length=50, unique=True)
    logo = models.ImageField(_("Logo"), upload_to='company_logos', blank=True, null=True)
    
    # Contact Information
    address = models.TextField(_("Address"), blank=True)
    city = models.CharField(_("City"), max_length=100, blank=True)
    state = models.CharField(_("State/Province"), max_length=100, blank=True)
    country = models.CharField(_("Country"), max_length=100, blank=True)
    postal_code = models.CharField(_("Postal Code"), max_length=20, blank=True)
    phone = models.CharField(_("Phone"), max_length=50, blank=True)
    email = models.EmailField(_("Email"), blank=True)
    website = models.URLField(_("Website"), blank=True)
    
    # Business Information
    tax_id = models.CharField(_("Tax ID"), max_length=100, blank=True)
    registration_number = models.CharField(_("Registration Number"), max_length=100, blank=True)
    founding_date = models.DateField(_("Founding Date"), null=True, blank=True)
    
    # Other Information
    notes = models.TextField(_("Notes"), blank=True)
    is_active = models.BooleanField(_("Is Active"), default=True)
    service_level = models.ForeignKey(
        ServiceLevel,
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name="companies",
        verbose_name=_("Service Level")
    )
    attributes = models.JSONField(_("Custom Attributes"), default=dict, blank=True)
    
    class Meta:
        verbose_name = _("Company")
        verbose_name_plural = _("Companies")
        ordering = ['name']
        
    def __str__(self):
        return self.name

class ServiceCenterType(TimeStampedModel, UUIDPrimaryKeyModel):
    """
    Types of service centers (e.g., Small, Medium, Large, Specialized)
    """
    name = models.CharField(_("Type Name"), max_length=100)
    description = models.TextField(_("Description"), blank=True)
    max_capacity = models.PositiveIntegerField(_("Maximum Capacity"), help_text=_("Maximum number of work orders per day"), null=True, blank=True)
    color_code = models.CharField(_("Color Code"), max_length=20, blank=True, help_text=_("For UI display"))
    is_active = models.BooleanField(_("Is Active"), default=True)
    attributes = models.JSONField(_("Custom Attributes"), default=dict, blank=True)

    class Meta:
        verbose_name = _("Service Center Type")
        verbose_name_plural = _("Service Center Types")
        ordering = ['name']

    def __str__(self):
        return self.name

class ServiceCenter(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """
    Service center belonging to a company, representing a physical location
    """
    SIZE_CHOICES = [
        ('small', _('Small')),
        ('medium', _('Medium')),
        ('large', _('Large')),
    ]
    
    company = models.ForeignKey(
        Company, 
        on_delete=models.CASCADE, 
        related_name="service_centers",
        verbose_name=_("Company")
    )
    name = models.CharField(_("Service Center Name"), max_length=255)
    code = models.CharField(_("Service Center Code"), max_length=50)
    center_type = models.ForeignKey(
        ServiceCenterType,
        on_delete=models.PROTECT,
        related_name="service_centers",
        verbose_name=_("Center Type")
    )
    size = models.CharField(
        _("Center Size"),
        max_length=10,
        choices=SIZE_CHOICES,
        default='medium',
        help_text=_("Determines staffing and role requirements")
    )
    
    # Vehicle Service Capability
    serves_all_vehicle_makes = models.BooleanField(
        _("Serves All Vehicle Makes"), 
        default=True,
        help_text=_("If true, this center can service all makes and models. If false, only specific makes defined in ServiceCenterMakeModel.")
    )
    
    # Contact Information
    address = models.TextField(_("Address"), blank=True)
    city = models.CharField(_("City"), max_length=100, blank=True)
    state = models.CharField(_("State/Province"), max_length=100, blank=True)
    country = models.CharField(_("Country"), max_length=100, blank=True)
    postal_code = models.CharField(_("Postal Code"), max_length=20, blank=True)
    latitude = models.DecimalField(_("Latitude"), max_digits=10, decimal_places=7, null=True, blank=True)
    longitude = models.DecimalField(_("Longitude"), max_digits=10, decimal_places=7, null=True, blank=True)
    phone = models.CharField(_("Phone"), max_length=50, blank=True)
    email = models.EmailField(_("Email"), blank=True)
    
    # Warehouse Information
    primary_warehouse = models.ForeignKey(
        Location,
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name="primary_for_service_centers",
        verbose_name=_("Primary Warehouse")
    )
    secondary_warehouses = models.ManyToManyField(
        Location,
        blank=True,
        related_name="secondary_for_service_centers",
        verbose_name=_("Secondary Warehouses")
    )
    
    # Operational Information
    manager = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name="managed_centers",
        verbose_name=_("Manager")
    )
    opening_hours = models.JSONField(_("Opening Hours"), default=dict, blank=True)
    capacity = models.PositiveIntegerField(_("Daily Capacity"), help_text=_("Maximum work orders per day"), default=10)
    
    # Other Information
    notes = models.TextField(_("Notes"), blank=True)
    is_active = models.BooleanField(_("Is Active"), default=True)
    service_level = models.ForeignKey(
        ServiceLevel,
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name="service_centers",
        verbose_name=_("Service Level")
    )
    attributes = models.JSONField(_("Custom Attributes"), default=dict, blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Service Center")
        verbose_name_plural = _("Service Centers")
        ordering = ['name']
        unique_together = [['tenant_id', 'code']]
        
    def __str__(self):
        return f"{self.name} ({self.code})"
        
    def get_available_warehouses(self):
        """
        Get all warehouses (primary + secondary) available to this service center
        """
        warehouses = set()
        if self.primary_warehouse:
            warehouses.add(self.primary_warehouse)
            
        secondary = self.secondary_warehouses.all()
        if secondary:
            warehouses.update(secondary)
            
        return list(warehouses)
    
    def supports_vehicle_make_model(self, make, model=None):
        """
        Check if this service center supports the given make/model
        
        Args:
            make (str): Vehicle make (manufacturer)
            model (str, optional): Vehicle model. If None, checks only for make.
            
        Returns:
            bool: True if supported, False otherwise
        """
        if self.serves_all_vehicle_makes:
            return True
            
        # If model is not provided, check if any models of this make are supported
        if model is None:
            return self.supported_makes_models.filter(make=make, is_active=True).exists()
            
        # Check for exact make-model match or make with blank model (all models of this make)
        return self.supported_makes_models.filter(
            make=make,
            is_active=True
        ).filter(
            models.Q(model=model) | models.Q(model='')
        ).exists()

    def get_suggested_roles(self):
        """
        Get the suggested roles for this service center based on its size
        
        Returns:
            dict: Dictionary mapping job functions to role codes
        """
        from user_roles.models import Role
        return Role.get_suggested_roles_by_center_size(self.size)

class ServiceCenterMakeModel(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """
    Defines which makes and models a service center can work with
    when serves_all_vehicle_makes is False
    """
    service_center = models.ForeignKey(
        ServiceCenter,
        on_delete=models.CASCADE,
        related_name="supported_makes_models",
        verbose_name=_("Service Center")
    )
    make = models.CharField(_("Make"), max_length=100)
    model = models.CharField(_("Model"), max_length=100, blank=True, 
                           help_text=_("If left blank, all models for this make are supported"))
    is_active = models.BooleanField(_("Is Active"), default=True)
    notes = models.TextField(_("Notes"), blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Service Center Make & Model")
        verbose_name_plural = _("Service Center Makes & Models")
        unique_together = [['tenant_id', 'service_center', 'make', 'model']]
        
    def __str__(self):
        if self.model:
            return f"{self.service_center.name} - {self.make} {self.model}"
        return f"{self.service_center.name} - {self.make} (All Models)"

class VehicleMake(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """
    Standard vehicle makes/brands (e.g., Toyota, Honda, BMW)
    """
    name = models.CharField(_("Make Name"), max_length=100, unique=True)
    description = models.TextField(_("Description"), blank=True)
    logo = models.ImageField(_("Logo"), upload_to='vehicle_makes', blank=True, null=True)
    country_of_origin = models.CharField(_("Country of Origin"), max_length=100, blank=True)
    is_active = models.BooleanField(_("Is Active"), default=True)
    attributes = models.JSONField(_("Custom Attributes"), default=dict, blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Vehicle Make")
        verbose_name_plural = _("Vehicle Makes")
        ordering = ['name']
        
    def __str__(self):
        return self.name

class VehicleModel(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """
    Standard vehicle models associated with makes (e.g., Camry, Civic, X5)
    """
    make = models.ForeignKey(
        VehicleMake,
        on_delete=models.CASCADE,
        related_name="models",
        verbose_name=_("Make")
    )
    name = models.CharField(_("Model Name"), max_length=100)
    description = models.TextField(_("Description"), blank=True)
    year_introduced = models.PositiveIntegerField(_("Year Introduced"), null=True, blank=True)
    year_discontinued = models.PositiveIntegerField(_("Year Discontinued"), null=True, blank=True)
    vehicle_class = models.CharField(_("Vehicle Class"), max_length=50, blank=True,
                                   help_text=_("E.g., Sedan, SUV, Truck, etc."))
    is_active = models.BooleanField(_("Is Active"), default=True)
    attributes = models.JSONField(_("Custom Attributes"), default=dict, blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Vehicle Model")
        verbose_name_plural = _("Vehicle Models")
        ordering = ['make__name', 'name']
        unique_together = [['tenant_id', 'make', 'name']]
        
    def __str__(self):
        return f"{self.make.name} {self.name}"
    
    def get_years_available(self):
        """Get years this model was available"""
        if self.year_introduced:
            from_year = self.year_introduced
            to_year = self.year_discontinued or timezone.now().year
            return list(range(from_year, to_year + 1))
        return []

class Vehicle(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """
    Vehicle information for service tracking
    """
    # Vehicle Information
    make = models.CharField(_("Make"), max_length=100)
    model = models.CharField(_("Model"), max_length=100)
    year = models.PositiveIntegerField(_("Year"), null=True, blank=True)
    vin = models.CharField(_("VIN"), max_length=100, blank=True)
    license_plate = models.CharField(_("License Plate"), max_length=50, blank=True)
    color = models.CharField(_("Color"), max_length=50, blank=True)
    
    # Reference to standard make and model (optional)
    standard_make = models.ForeignKey(
        VehicleMake,
        on_delete=models.SET_NULL,
        related_name="vehicles",
        verbose_name=_("Standard Make"),
        null=True, blank=True,
        help_text=_("Reference to standardized make/brand")
    )
    standard_model = models.ForeignKey(
        VehicleModel,
        on_delete=models.SET_NULL,
        related_name="vehicles",
        verbose_name=_("Standard Model"),
        null=True, blank=True,
        help_text=_("Reference to standardized model")
    )
    
    # Owners and drivers
    owner = models.ForeignKey(
        Customer,
        on_delete=models.PROTECT,
        related_name="owned_vehicles",
        verbose_name=_("Owner"),
        null=True, blank=True
    )
    
    drivers = models.ManyToManyField(
        Customer,
        related_name="driven_vehicles",
        verbose_name=_("Drivers"),
        blank=True
    )
    
    # Service Center Association
    service_center = models.ForeignKey(
        ServiceCenter,
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name="vehicles",
        verbose_name=_("Service Center")
    )
    
    # Other Information
    purchase_date = models.DateField(_("Purchase Date"), null=True, blank=True)
    warranty_end_date = models.DateField(_("Warranty End Date"), null=True, blank=True)
    notes = models.TextField(_("Notes"), blank=True)
    attributes = models.JSONField(_("Custom Attributes"), default=dict, blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Vehicle")
        verbose_name_plural = _("Vehicles")
        ordering = ['-created_at']
        
    def __str__(self):
        return f"{self.make} {self.model} ({self.license_plate})"
    
    def has_active_work_orders(self):
        """Check if vehicle has any active work orders"""
        from work_orders.models import WorkOrder
        active_statuses = ['draft', 'planned', 'in_progress', 'on_hold']
        return WorkOrder.objects.filter(vehicle=self, status__in=active_statuses).exists()
    
    def clean(self):
        """Validate that the vehicle can be serviced by the assigned service center"""
        from django.core.exceptions import ValidationError
        
        if self.service_center and not self.service_center.serves_all_vehicle_makes:
            # Check if this make/model is supported by the service center
            if not self.service_center.supports_vehicle_make_model(self.make, self.model):
                raise ValidationError(_("This service center does not support this vehicle make and model."))
    
    def get_compatible_service_centers(self, active_only=True, location=None, distance=None):
        """
        Find service centers that can service this vehicle
        
        Args:
            active_only (bool): Only include active service centers
            location (tuple): Optional (latitude, longitude) tuple for location-based search
            distance (int): Optional distance in kilometers to search within
            
        Returns:
            queryset: QuerySet of compatible ServiceCenter objects
        """
        from django.db.models import Q
        
        # Start with all service centers or filter by active status
        queryset = ServiceCenter.objects.all()
        if active_only:
            queryset = queryset.filter(is_active=True)
            
        # Filter by location if provided
        if location and distance and hasattr(ServiceCenter, 'distance'):
            # This requires django.contrib.gis to work properly
            latitude, longitude = location
            queryset = queryset.filter(
                latitude__isnull=False,
                longitude__isnull=False
            ).distance(
                Point(longitude, latitude, srid=4326)
            ).filter(
                distance__lte=distance * 1000  # Convert km to meters
            )
            
        # Get centers that service all makes or this specific make/model
        queryset = queryset.filter(
            # Centers that service all makes
            Q(serves_all_vehicle_makes=True) |
            # Centers with specific support for this make/model
            (
                Q(serves_all_vehicle_makes=False) &
                Q(supported_makes_models__make=self.make) &
                (
                    Q(supported_makes_models__model='') |  # Empty model means all models
                    Q(supported_makes_models__model=self.model)
                ) &
                Q(supported_makes_models__is_active=True)
            )
        ).distinct()
        
        return queryset

class ServiceHistory(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """
    Service history for a vehicle
    """
    vehicle = models.ForeignKey(
        Vehicle,
        on_delete=models.CASCADE,
        related_name="service_history",
        verbose_name=_("Vehicle")
    )
    service_center = models.ForeignKey(
        ServiceCenter,
        on_delete=models.PROTECT,
        related_name="service_records",
        verbose_name=_("Service Center")
    )
    service_date = models.DateField(_("Service Date"))
    odometer = models.PositiveIntegerField(_("Odometer Reading"), null=True, blank=True)
    description = models.TextField(_("Description"))
    work_order_number = models.CharField(_("Work Order #"), max_length=50, blank=True)
    
    # Service Details
    services_performed = models.JSONField(_("Services Performed"), default=list, blank=True)
    parts_used = models.JSONField(_("Parts Used"), default=list, blank=True)
    technician = models.CharField(_("Technician"), max_length=255, blank=True)
    
    # Financial
    total_cost = models.DecimalField(_("Total Cost"), max_digits=15, decimal_places=2, null=True, blank=True)
    
    # Other Information
    notes = models.TextField(_("Notes"), blank=True)
    attributes = models.JSONField(_("Custom Attributes"), default=dict, blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Service History")
        verbose_name_plural = _("Service History")
        ordering = ['-service_date']
        
    def __str__(self):
        return f"{self.vehicle} - {self.service_date}"

class VehicleOwnershipTransfer(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """
    Records vehicle ownership transfers between customers
    """
    TRANSFER_STATUS_CHOICES = [
        ('pending', _('Pending')),
        ('approved', _('Approved')),
        ('completed', _('Completed')),
        ('rejected', _('Rejected')),
        ('cancelled', _('Cancelled')),
    ]
    
    vehicle = models.ForeignKey(
        Vehicle, 
        on_delete=models.CASCADE,
        related_name='ownership_transfers',
        verbose_name=_('Vehicle')
    )
    previous_owner = models.ForeignKey(
        Customer,
        on_delete=models.PROTECT,
        related_name='vehicle_transfers_out',
        verbose_name=_('Previous Owner')
    )
    new_owner = models.ForeignKey(
        Customer,
        on_delete=models.PROTECT,
        related_name='vehicle_transfers_in',
        verbose_name=_('New Owner')
    )
    transfer_date = models.DateField(_('Transfer Date'), default=timezone.now)
    status = models.CharField(
        _('Status'),
        max_length=20,
        choices=TRANSFER_STATUS_CHOICES,
        default='pending'
    )
    
    # Transfer documents and information
    sale_price = models.DecimalField(
        _('Sale Price'),
        max_digits=15,
        decimal_places=2,
        blank=True,
        null=True
    )
    odometer_reading = models.PositiveIntegerField(
        _('Odometer Reading'),
        blank=True,
        null=True
    )
    documents = models.JSONField(
        _('Transfer Documents'),
        default=list,
        blank=True,
        help_text=_('List of document references related to this transfer')
    )
    notes = models.TextField(_('Notes'), blank=True)
    
    # Approval information
    approved_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_transfers',
        verbose_name=_('Approved By')
    )
    approved_date = models.DateTimeField(
        _('Approval Date'),
        null=True,
        blank=True
    )
    
    class Meta:
        verbose_name = _('Vehicle Ownership Transfer')
        verbose_name_plural = _('Vehicle Ownership Transfers')
        ordering = ['-transfer_date', '-created_at']
        
    def __str__(self):
        return f"{self.vehicle} - {self.previous_owner} to {self.new_owner}"
    
    def clean(self):
        """Validate the transfer data"""
        from django.core.exceptions import ValidationError
        
        # Check that previous_owner is the current owner of the vehicle
        if self.vehicle.owner != self.previous_owner:
            raise ValidationError({
                'previous_owner': _('Previous owner must be the current owner of the vehicle.')
            })
            
        # Check that previous and new owners are different
        if self.previous_owner == self.new_owner:
            raise ValidationError({
                'new_owner': _('New owner must be different from the previous owner.')
            })
            
        # Check if there's already a pending transfer for this vehicle
        if not self.pk:  # Only check on new transfers
            pending_transfers = VehicleOwnershipTransfer.objects.filter(
                vehicle=self.vehicle,
                status='pending'
            )
            if pending_transfers.exists():
                raise ValidationError(_('There is already a pending transfer for this vehicle.'))
    
    def approve(self, user):
        """
        Approve the ownership transfer
        
        Args:
            user: The user approving the transfer
        """
        if self.status != 'pending':
            raise ValueError(_('Only pending transfers can be approved.'))
        
        self.status = 'approved'
        self.approved_by = user
        self.approved_date = timezone.now()
        self.save()
    
    def complete(self):
        """
        Complete the ownership transfer and update vehicle owner
        """
        if self.status != 'approved':
            raise ValueError(_('Only approved transfers can be completed.'))
        
        # Update vehicle owner
        self.vehicle.owner = self.new_owner
        self.vehicle.save()
        
        self.status = 'completed'
        self.save()
    
    def reject(self, reason=None):
        """
        Reject the ownership transfer
        
        Args:
            reason: Optional reason for rejection
        """
        if self.status not in ['pending', 'approved']:
            raise ValueError(_('Only pending or approved transfers can be rejected.'))
        
        self.status = 'rejected'
        if reason:
            self.notes += f"\nRejection reason: {reason}"
        self.save()
    
    def cancel(self):
        """
        Cancel the ownership transfer
        """
        if self.status not in ['pending', 'approved']:
            raise ValueError(_('Only pending or approved transfers can be cancelled.'))
        
        self.status = 'cancelled'
        self.save()
