{% extends "dashboard_base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{{ supplier.name }}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="mb-6 flex flex-wrap items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-800">{{ supplier.name }}</h1>
            <p class="text-gray-600">{% trans "معلومات المورد" %}</p>
        </div>
        <div class="mt-4 md:mt-0 flex space-x-2 {% if LANGUAGE_CODE == 'ar' %}space-x-reverse{% endif %}">
            <a href="{% url 'purchases:supplier_update' supplier.id %}" class="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg inline-flex items-center">
                <i class="fas fa-edit {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i>
                {% trans "تعديل" %}
            </a>
            <a href="{% url 'purchases:supplier_list' %}" class="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-lg inline-flex items-center">
                <i class="fas fa-arrow-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i>
                {% trans "العودة" %}
            </a>
        </div>
    </div>
    
    <!-- Supplier Info Card -->
    <div class="bg-white shadow rounded-lg overflow-hidden mb-8">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-800">{% trans "معلومات المورد" %}</h2>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h3 class="text-sm font-medium text-gray-500">{% trans "اسم المورد" %}</h3>
                    <p class="mt-1 text-base text-gray-900">{{ supplier.name }}</p>
                </div>
                
                <div>
                    <h3 class="text-sm font-medium text-gray-500">{% trans "شخص الاتصال" %}</h3>
                    <p class="mt-1 text-base text-gray-900">{{ supplier.contact_person|default:"-" }}</p>
                </div>
                
                <div>
                    <h3 class="text-sm font-medium text-gray-500">{% trans "رقم الهاتف" %}</h3>
                    <p class="mt-1 text-base text-gray-900">
                        {% if supplier.phone %}
                            <a href="tel:{{ supplier.phone }}" class="text-blue-600 hover:text-blue-800">
                                {{ supplier.phone }}
                            </a>
                        {% else %}
                            -
                        {% endif %}
                    </p>
                </div>
                
                <div>
                    <h3 class="text-sm font-medium text-gray-500">{% trans "البريد الإلكتروني" %}</h3>
                    <p class="mt-1 text-base text-gray-900">
                        {% if supplier.email %}
                            <a href="mailto:{{ supplier.email }}" class="text-blue-600 hover:text-blue-800">
                                {{ supplier.email }}
                            </a>
                        {% else %}
                            -
                        {% endif %}
                    </p>
                </div>
                
                <div class="md:col-span-2">
                    <h3 class="text-sm font-medium text-gray-500">{% trans "العنوان" %}</h3>
                    <p class="mt-1 text-base text-gray-900">{{ supplier.address|default:"-" }}</p>
                </div>
                
                {% if supplier.notes %}
                <div class="md:col-span-2">
                    <h3 class="text-sm font-medium text-gray-500">{% trans "ملاحظات" %}</h3>
                    <p class="mt-1 text-base text-gray-900">{{ supplier.notes }}</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="bg-white shadow rounded-lg overflow-hidden mb-8">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-800">{% trans "إجراءات سريعة" %}</h2>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <a href="{% url 'purchases:purchase_order_create' %}?supplier={{ supplier.id }}" class="bg-amber-100 hover:bg-amber-200 text-amber-700 py-3 px-4 rounded-lg text-center transition duration-300">
                    <i class="fas fa-plus-circle mb-2 text-2xl"></i>
                    <p>{% trans "طلب شراء جديد" %}</p>
                </a>
                <a href="tel:{{ supplier.phone }}" class="bg-green-100 hover:bg-green-200 text-green-700 py-3 px-4 rounded-lg text-center transition duration-300 {% if not supplier.phone %}opacity-50 pointer-events-none{% endif %}">
                    <i class="fas fa-phone-alt mb-2 text-2xl"></i>
                    <p>{% trans "اتصال" %}</p>
                </a>
                <a href="mailto:{{ supplier.email }}" class="bg-blue-100 hover:bg-blue-200 text-blue-700 py-3 px-4 rounded-lg text-center transition duration-300 {% if not supplier.email %}opacity-50 pointer-events-none{% endif %}">
                    <i class="fas fa-envelope mb-2 text-2xl"></i>
                    <p>{% trans "إرسال بريد إلكتروني" %}</p>
                </a>
            </div>
        </div>
    </div>
    
    <!-- Purchase Order History -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h2 class="text-lg font-semibold text-gray-800">{% trans "سجل طلبات الشراء" %}</h2>
            <a href="{% url 'purchases:purchase_order_create' %}?supplier={{ supplier.id }}" class="bg-amber-500 hover:bg-amber-600 text-white py-1 px-3 rounded-lg text-sm">
                <i class="fas fa-plus {% if LANGUAGE_CODE == 'ar' %}ml-1{% else %}mr-1{% endif %}"></i>
                {% trans "طلب جديد" %}
            </a>
        </div>
        
        {% if purchase_orders %}
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "رقم الطلب" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "التاريخ" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "الحالة" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "العناصر" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "الإجراءات" %}
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for order in purchase_orders %}
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">{{ order.po_number }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">{{ order.created_at|date:"d/m/Y" }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    {% if order.status == 'pending' %}
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                            {% trans "قيد الانتظار" %}
                                        </span>
                                    {% elif order.status == 'ordered' %}
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                                            {% trans "تم الطلب" %}
                                        </span>
                                    {% elif order.status == 'received' %}
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                            {% trans "مستلم" %}
                                        </span>
                                    {% elif order.status == 'cancelled' %}
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                            {% trans "ملغي" %}
                                        </span>
                                    {% else %}
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                                            {{ order.get_status_display }}
                                        </span>
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ order.items.count }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <a href="{% url 'purchases:purchase_order_detail' order.id %}" class="text-indigo-600 hover:text-indigo-900">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            {% if purchase_orders.has_other_pages %}
                <div class="px-6 py-4 border-t border-gray-200">
                    <div class="flex justify-center">
                        <nav class="relative z-0 inline-flex shadow-sm -space-x-px" aria-label="{% trans 'صفحات' %}">
                            {% if purchase_orders.has_previous %}
                                <a href="?page={{ purchase_orders.previous_page_number }}" class="relative inline-flex items-center px-2 py-2 rounded-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                    <span class="sr-only">{% trans "السابق" %}</span>
                                    <i class="fas fa-chevron-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %}"></i>
                                </a>
                            {% endif %}
                            
                            {% for num in purchase_orders.paginator.page_range %}
                                {% if purchase_orders.number == num %}
                                    <a href="#" aria-current="page" class="z-10 bg-blue-50 border-blue-500 text-blue-600 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                        {{ num }}
                                    </a>
                                {% elif num > purchase_orders.number|add:'-3' and num < purchase_orders.number|add:'3' %}
                                    <a href="?page={{ num }}" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                        {{ num }}
                                    </a>
                                {% endif %}
                            {% endfor %}
                            
                            {% if purchase_orders.has_next %}
                                <a href="?page={{ purchase_orders.next_page_number }}" class="relative inline-flex items-center px-2 py-2 rounded-{% if LANGUAGE_CODE == 'ar' %}left{% else %}right{% endif %} border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                    <span class="sr-only">{% trans "التالي" %}</span>
                                    <i class="fas fa-chevron-{% if LANGUAGE_CODE == 'ar' %}left{% else %}right{% endif %}"></i>
                                </a>
                            {% endif %}
                        </nav>
                    </div>
                </div>
            {% endif %}
        {% else %}
            <div class="p-6 text-center text-gray-500">
                {% trans "لا توجد طلبات شراء لهذا المورد بعد" %}
                <p class="mt-2">
                    <a href="{% url 'purchases:purchase_order_create' %}?supplier={{ supplier.id }}" class="text-amber-600 hover:text-amber-800 font-medium">
                        {% trans "إنشاء طلب جديد الآن" %}
                    </a>
                </p>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %} 