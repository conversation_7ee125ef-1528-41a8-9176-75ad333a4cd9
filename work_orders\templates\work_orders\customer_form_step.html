{% load i18n %}

<div id="step-customer-content" class="form-step-content space-y-6">
    <div class="flex justify-between items-center mb-2">
        <h2 class="text-xl font-bold text-gray-700 flex items-center">
        <i class="fas fa-user-circle mr-2 text-blue-500"></i>
        {% trans "معلومات العميل" %}
    </h2>
        <a href="{% url 'work_orders:work_order_list' %}" class="flex items-center text-blue-600 hover:text-blue-800">
            <i class="fas fa-arrow-left ml-2"></i>
            {% trans "العودة إلى القائمة" %}
        </a>
    </div>
    
    <!-- Customer Search -->
    <div class="mb-4">
        <label for="customer_search_term" class="form-label block text-sm font-bold text-gray-700 mb-1 flex items-center">
            <i class="fas fa-search text-blue-500 mr-2"></i>
            {% trans "مصطلح البحث" %}
        </label>
        <div class="flex space-x-2 rtl:space-x-reverse">
            <input type="text" id="customer_search_term" name="customer_search_term" 
                   class="form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm min-h-[44px]" 
                   placeholder="{% trans 'ادخل...' %}">
            <select id="customer_search_criteria" name="customer_search_criteria" 
                    class="form-select mt-1 block rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm min-h-[44px]">
                <option value="all" selected>{% trans "الكل" %}</option>
                <option value="name">{% trans "الاسم" %}</option>
                <option value="phone">{% trans "رقم الهاتف" %}</option>
                <option value="id_number">{% trans "رقم الهوية" %}</option>
            </select>
        </div>
        <div id="customer-search-results" class="mt-2 border border-gray-200 rounded-md max-h-60 overflow-y-auto"></div>
    </div>

    <!-- Selected Customer Info -->
    <div id="selected-customer-info" class="mb-4 p-4 border border-green-200 rounded-md bg-green-50" style="display: none;">
        <h3 class="text-lg font-bold mb-2 text-green-800 flex items-center">
            <i class="fas fa-user-check mr-2"></i>
            {% trans "العميل المختار" %}
        </h3>
        <p id="selected-customer-name" class="text-green-700 flex items-center">
            <i class="fas fa-id-card mr-2"></i>
            <span></span>
        </p>
        <p id="selected-customer-phone" class="text-green-700 flex items-center">
            <i class="fas fa-phone mr-2"></i>
            <span></span>
        </p>
    </div>

    <!-- New Customer Form Fields -->
    <div id="new-customer-fields" class="space-y-4 p-4 border border-gray-200 rounded-md" style="display: none;">
        <h3 class="text-lg font-bold mb-3 text-gray-700 flex items-center">
            <i class="fas fa-user-plus mr-2 text-blue-500"></i>
            {% trans "إضافة عميل جديد" %}
        </h3>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
                <label for="new_customer_first_name" class="form-label block text-sm font-bold text-gray-700 mb-1 flex items-center">
                    <i class="fas fa-user text-blue-500 mr-2"></i>
                    {% trans "الاسم الأول" %}
                </label>
                <input type="text" id="new_customer_first_name" name="new_customer_first_name" 
                       class="form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm min-h-[44px]">
            </div>
            <div>
                <label for="new_customer_second_name" class="form-label block text-sm font-bold text-gray-700 mb-1 flex items-center">
                    <i class="fas fa-user text-blue-500 mr-2"></i>
                    {% trans "الاسم الثاني" %}
                </label>
                <input type="text" id="new_customer_second_name" name="new_customer_second_name" 
                       class="form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm min-h-[44px]">
            </div>
            <div>
                <label for="new_customer_third_name" class="form-label block text-sm font-bold text-gray-700 mb-1 flex items-center">
                    <i class="fas fa-user text-blue-500 mr-2"></i>
                    {% trans "الاسم الثالث" %}
                </label>
                <input type="text" id="new_customer_third_name" name="new_customer_third_name" 
                       class="form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm min-h-[44px]">
            </div>
            <div>
                <label for="new_customer_last_name" class="form-label block text-sm font-bold text-gray-700 mb-1 flex items-center">
                    <i class="fas fa-user text-blue-500 mr-2"></i>
                    {% trans "الاسم الأخير" %}
                </label>
                <input type="text" id="new_customer_last_name" name="new_customer_last_name" 
                       class="form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm min-h-[44px]">
            </div>
            <div>
                <label for="new_customer_phone" class="form-label block text-sm font-bold text-gray-700 mb-1 flex items-center">
                    <i class="fas fa-phone text-blue-500 mr-2"></i>
                    {% trans "رقم الهاتف" %}
                </label>
                <input type="text" id="new_customer_phone" name="new_customer_phone" 
                       class="form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm min-h-[44px]">
            </div>
            <div>
                <label for="new_customer_email" class="form-label block text-sm font-bold text-gray-700 mb-1 flex items-center">
                    <i class="fas fa-envelope text-blue-500 mr-2"></i>
                    {% trans "البريد الإلكتروني (اختياري)" %}
                </label>
                <input type="email" id="new_customer_email" name="new_customer_email" 
                       class="form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm min-h-[44px]">
            </div>
            <div>
                <label for="new_customer_id_type" class="form-label block text-sm font-bold text-gray-700 mb-1 flex items-center">
                    <i class="fas fa-id-card text-blue-500 mr-2"></i>
                    {% trans "نوع إثبات الشخصية" %}
                </label>
                <select id="new_customer_id_type" name="new_customer_id_type" 
                        class="form-select mt-1 block w-full rounded-md border-gray-300 shadow-sm min-h-[44px]">
                    <option value="">{% trans "اختر النوع" %}</option>
                    <option value="national_id">{% trans "بطاقة شخصية" %}</option>
                    <option value="passport">{% trans "جواز سفر" %}</option>
                    <option value="residence_permit">{% trans "إقامة" %}</option>
                    <option value="driving_license">{% trans "رخصة قيادة" %}</option>
                    <option value="other">{% trans "آخر" %}</option>
                </select>
            </div>
            <div>
                <label for="new_customer_id_number" class="form-label block text-sm font-bold text-gray-700 mb-1 flex items-center">
                    <i class="fas fa-fingerprint text-blue-500 mr-2"></i>
                    {% trans "رقم إثبات الشخصية" %}
                </label>
                <input type="text" id="new_customer_id_number" name="new_customer_id_number" 
                       class="form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm min-h-[44px]">
            </div>
             <div>
                <label for="new_customer_gender" class="form-label block text-sm font-bold text-gray-700 mb-1 flex items-center">
                    <i class="fas fa-venus-mars text-blue-500 mr-2"></i>
                    {% trans "النوع" %}
                </label>
                <select id="new_customer_gender" name="new_customer_gender" 
                        class="form-select mt-1 block w-full rounded-md border-gray-300 shadow-sm min-h-[44px]">
                    <option value="">{% trans "اختر النوع" %}</option>
                    <option value="male">{% trans "ذكر" %}</option>
                    <option value="female">{% trans "أنثى" %}</option>
                    <option value="other">{% trans "آخر" %}</option>
                </select>
            </div>
             <div>
                <label for="new_customer_type" class="form-label block text-sm font-bold text-gray-700 mb-1 flex items-center">
                    <i class="fas fa-users text-blue-500 mr-2"></i>
                    {% trans "نوع العميل" %}             
                </label>
                <select id="new_customer_type" name="new_customer_type" 
                        class="form-select mt-1 block w-full rounded-md border-gray-300 shadow-sm min-h-[44px]">
                    <option value="individual">{% trans "فرد" %}</option>
                    <option value="corporate">{% trans "شركة" %}</option>
                </select>
            </div>
        </div>

        <div>
            <label for="new_customer_dob" class="form-label block text-sm font-bold text-gray-700 mb-1 flex items-center">
                <i class="fas fa-calendar-alt text-blue-500 mr-2"></i>
                {% trans "تاريخ الميلاد" %}
            </label>
            <input type="date" id="new_customer_dob" name="new_customer_dob" 
                   class="form-input mt-1 block w-full rounded-md border-gray-300 shadow-sm min-h-[44px]">
        </div>

        <div>
            <label for="new_customer_address" class="form-label block text-sm font-bold text-gray-700 mb-1 flex items-center">
                <i class="fas fa-map-marker-alt text-blue-500 mr-2"></i>
                {% trans "العنوان" %}
            </label>
            <textarea id="new_customer_address" name="new_customer_address" rows="3" 
                      class="form-textarea mt-1 block w-full rounded-md border-gray-300 shadow-sm min-h-[44px]"></textarea>
        </div>

        <!-- Save and Select Button -->
        <div class="mt-4">
            <button type="button" id="save_and_select_customer" class="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors touch-feedback flex items-center justify-center">
                <i class="fas fa-save mr-2"></i>
                {% trans "حفظ واختيار" %}
            </button>
        </div>
    </div>
    
    <input type="hidden" id="selected_customer_id" name="selected_customer_id">
</div> 

<!-- Validation Error Container -->
<div id="customer-form-step-errors" class="mt-4 p-4 bg-red-50 border border-red-200 rounded-md text-red-700 hidden"></div>

<script>
    // Clear form data on page refresh
    document.addEventListener('DOMContentLoaded', function() {
        // Check if this is a page refresh (not a first load)
        if (performance.navigation.type === 1) {
            console.log('Customer form page was refreshed. Clearing session data...');
            
            // Clear form data from sessionStorage
            sessionStorage.removeItem('currentStep');
            sessionStorage.removeItem('customer');
            sessionStorage.removeItem('ui');
            
            // Also clear any values in the form
            document.getElementById('new_customer_first_name')?.value = '';
            document.getElementById('new_customer_second_name')?.value = '';
            document.getElementById('new_customer_third_name')?.value = '';
            document.getElementById('new_customer_last_name')?.value = '';
            document.getElementById('new_customer_phone')?.value = '';
            document.getElementById('new_customer_email')?.value = '';
            document.getElementById('new_customer_id_type')?.value = '';
            document.getElementById('new_customer_id_number')?.value = '';
            document.getElementById('new_customer_gender')?.value = '';
            document.getElementById('new_customer_address')?.value = '';
            document.getElementById('selected_customer_id')?.value = '';
            
            // If customer info box is shown, hide it
            const customerInfoBox = document.getElementById('selected-customer-info');
            if (customerInfoBox) {
                customerInfoBox.style.display = 'none';
            }
        }
    });

    // Form field validation indicators
    const validationIndicators = {};
    
    // Add validation indicators to required fields
    document.addEventListener('DOMContentLoaded', function() {
        // Required fields with their validation rules
        const requiredFields = [
            {id: 'new_customer_first_name', rule: /^.+$/},
            {id: 'new_customer_last_name', rule: /^.+$/},
            {id: 'new_customer_phone', rule: /^[\d\s\+\-]{8,}$/}, // Phone number with min 8 digits
            {id: 'new_customer_email', rule: /^$|^[^\s@]+@[^\s@]+\.[^\s@]+$/}, // Email or empty
            {id: 'new_customer_id_number', rule: null, dependency: 'new_customer_id_type'} // Required only if ID type is selected
        ];
        
        // Create validation indicators for each field
        requiredFields.forEach(field => {
            const inputElement = document.getElementById(field.id);
            if (!inputElement) return;
            
            // Create validation indicator
            const indicator = document.createElement('div');
            indicator.className = 'validation-indicator absolute right-2 top-1/2 transform -translate-y-1/2';
            indicator.innerHTML = ''; // Initially empty
            validationIndicators[field.id] = indicator;
            
            // Position the indicator inside the input's parent
            const inputContainer = inputElement.parentElement;
            if (inputContainer) {
                inputContainer.style.position = 'relative';
                inputContainer.appendChild(indicator);
            }
            
            // Add real-time validation
            inputElement.addEventListener('input', function() {
                validateField(field.id, field.rule, field.dependency);
            });
            
            // Initial validation state
            validateField(field.id, field.rule, field.dependency);
        });
        
        // If we have a dependency field, add listener to it
        const idTypeField = document.getElementById('new_customer_id_type');
        if (idTypeField) {
            idTypeField.addEventListener('change', function() {
                validateField('new_customer_id_number', null, 'new_customer_id_type');
            });
        }
    });
    
    // Validate individual field
    function validateField(fieldId, rule, dependencyFieldId) {
        const field = document.getElementById(fieldId);
        const indicator = validationIndicators[fieldId];
        if (!field || !indicator) return;
        
        let isValid = true;
        let isEmpty = !field.value.trim();
        
        // Check if field is dependent on another field
        if (dependencyFieldId) {
            const dependencyField = document.getElementById(dependencyFieldId);
            if (dependencyField && dependencyField.value && isEmpty) {
                isValid = false;
            } else if (!dependencyField || !dependencyField.value) {
                // If dependency field is empty or doesn't exist, this field is optional
                isValid = true;
            } else if (rule) {
                // If dependency is satisfied and we have a rule, check against it
                isValid = rule.test(field.value);
            }
        } else if (rule) {
            // Regular rule-based validation
            isValid = rule.test(field.value);
        }
        
        // Update indicator
        if (isEmpty && !dependencyFieldId) {
            // Empty field - neutral state
            indicator.innerHTML = '<i class="fas fa-asterisk text-gray-400 text-xs"></i>';
            field.classList.remove('border-red-500', 'border-green-500');
            field.classList.add('border-gray-300');
        } else if (isValid) {
            // Valid input
            indicator.innerHTML = '<i class="fas fa-check text-green-500"></i>';
            field.classList.remove('border-red-500', 'border-gray-300');
            field.classList.add('border-green-500');
        } else {
            // Invalid input
            indicator.innerHTML = '<i class="fas fa-times text-red-500"></i>';
            field.classList.remove('border-green-500', 'border-gray-300');
            field.classList.add('border-red-500');
        }
        
        return isValid;
    }
    
    // Validate the entire form
    function validateCustomerFormStep() {
        const errors = [];
        
        // Get values from form
        const firstName = document.getElementById('new_customer_first_name')?.value || '';
        const lastName = document.getElementById('new_customer_last_name')?.value || '';
        const phone = document.getElementById('new_customer_phone')?.value || '';
        const email = document.getElementById('new_customer_email')?.value || '';
        const idType = document.getElementById('new_customer_id_type')?.value || '';
        const idNumber = document.getElementById('new_customer_id_number')?.value || '';
        
        // Required fields validation
        if (!firstName.trim()) errors.push("{% trans 'الاسم الأول مطلوب' %}");
        if (!lastName.trim()) errors.push("{% trans 'الاسم الأخير مطلوب' %}");
        if (!phone.trim()) errors.push("{% trans 'رقم الهاتف مطلوب' %}");
        
        // Phone format validation
        if (phone && !/^[\d\s\+\-]{8,}$/.test(phone)) {
            errors.push("{% trans 'رقم الهاتف يجب أن يحتوي على 8 أرقام على الأقل' %}");
        }
        
        // Email validation (if provided)
        if (email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
            errors.push("{% trans 'صيغة البريد الإلكتروني غير صحيحة' %}");
        }
        
        // ID validation (if ID type is selected, ID number is required)
        if (idType && !idNumber.trim()) {
            errors.push("{% trans 'رقم إثبات الشخصية مطلوب' %}");
        }
        
        // ID number validation based on type
        if (idType === 'national_id' && idNumber) {
            // National ID validation (usually numbers only)
            if (!/^\d+$/.test(idNumber)) {
                errors.push("{% trans 'رقم البطاقة الشخصية يجب أن يحتوي على أرقام فقط' %}");
            }
        }
        
        // Instead of displaying grouped errors, highlight individual fields
        let isValid = true;
        
        // Check first name
        const firstNameField = document.getElementById('new_customer_first_name');
        if (firstNameField && !firstNameField.value.trim()) {
            highlightInvalidField(firstNameField);
            isValid = false;
        }
        
        // Check last name
        const lastNameField = document.getElementById('new_customer_last_name');
        if (lastNameField && !lastNameField.value.trim()) {
            highlightInvalidField(lastNameField);
            isValid = false;
        }
        
        // Check phone
        const phoneField = document.getElementById('new_customer_phone');
        if (phoneField) {
            if (!phoneField.value.trim()) {
                highlightInvalidField(phoneField);
                isValid = false;
            } else if (!/^[\d\s\+\-]{8,}$/.test(phoneField.value)) {
                highlightInvalidField(phoneField, '{% trans "رقم هاتف غير صالح" %}');
                isValid = false;
            }
        }
        
        // Check email if provided
        const emailField = document.getElementById('new_customer_email');
        if (emailField && emailField.value.trim() && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(emailField.value)) {
            highlightInvalidField(emailField, '{% trans "بريد إلكتروني غير صالح" %}');
            isValid = false;
        }
        
        // Check ID number if ID type is selected
        const idTypeField = document.getElementById('new_customer_id_type');
        const idNumberField = document.getElementById('new_customer_id_number');
        if (idTypeField && idTypeField.value && idNumberField && !idNumberField.value.trim()) {
            highlightInvalidField(idNumberField);
            isValid = false;
        }
        
        // Focus the first invalid field if any
        if (!isValid) {
            const firstInvalid = document.querySelector('.border-red-500');
            if (firstInvalid) {
                firstInvalid.focus();
            }
        }
        
        return isValid;
        
        // Helper function to highlight invalid fields
        function highlightInvalidField(field, tooltipText) {
            field.classList.add('border-red-500');
            field.classList.remove('border-gray-300', 'border-green-500');
            
            // Add a red asterisk indicator
            const fieldContainer = field.parentElement;
            if (fieldContainer) {
                fieldContainer.style.position = 'relative';
                
                // Add tooltip if provided
                if (tooltipText) {
                    let tooltip = fieldContainer.querySelector('.field-tooltip');
                    if (!tooltip) {
                        tooltip = document.createElement('div');
                        tooltip.className = 'field-tooltip absolute -bottom-6 right-0 bg-red-50 text-red-700 text-xs p-1 rounded-md z-10';
                        tooltip.textContent = tooltipText;
                        fieldContainer.appendChild(tooltip);
                    }
                }
                
                // Add red asterisk
                let indicator = fieldContainer.querySelector('.invalid-indicator');
                if (!indicator) {
                    indicator = document.createElement('div');
                    indicator.className = 'invalid-indicator absolute right-2 top-1/2 transform -translate-y-1/2';
                    indicator.innerHTML = '<i class="fas fa-asterisk text-red-500 text-xs"></i>';
                    fieldContainer.appendChild(indicator);
                }
            }
            
            // Add event listener to clear error on input
            field.addEventListener('input', function() {
                if (field.value.trim()) {
                    field.classList.remove('border-red-500');
                    field.classList.add('border-gray-300');
                    
                    // Remove tooltip if it exists
                    const tooltip = fieldContainer?.querySelector('.field-tooltip');
                    if (tooltip) {
                        tooltip.remove();
                    }
                }
            }, { once: true });
        }
    }
    
    // Add validation to save button
    document.addEventListener('DOMContentLoaded', function() {
        const saveButton = document.getElementById('save_and_select_customer');
        if (saveButton) {
            saveButton.addEventListener('click', function(e) {
                if (!validateCustomerFormStep()) {
                    e.preventDefault();
                    return false;
                }
                
                // If validation passes, continue with original click handler
            });
        }
    });
</script> 