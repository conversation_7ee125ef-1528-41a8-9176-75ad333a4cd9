import os
import sys
import django
import random
from django.db import transaction
from decimal import Decimal
from faker import Faker

# Add the parent directory to sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

# Import necessary models
from inventory.models import Item, ItemClassification, OperationCompatibility
from work_orders.models import WorkOrderType
from setup.models import ServiceCenter, Customer

# Initialize Faker
fake = Faker('ar_EG')  # Using Egyptian Arabic locale for Egyptian market specifics

class InventoryDataGenerator:
    """Generate inventory data for the system."""
    
    def __init__(self):
        # Use an existing tenant_id
        tenant_id = None
        try:
            # Try to get from Customer first
            customer = Customer.objects.first()
            if customer:
                tenant_id = customer.tenant_id
                
            # If no customer, try to get from other models
            if not tenant_id:
                service_center = ServiceCenter.objects.first()
                if service_center:
                    tenant_id = service_center.tenant_id
        except:
            pass
            
        # If no tenant_id found, use a default one
        if not tenant_id:
            tenant_id = "default_tenant"
            
        self.tenant_id = tenant_id
        print(f"Using tenant ID: {self.tenant_id}")
        
    @transaction.atomic
    def generate_inventory_data(self):
        """Generate inventory data."""
        print("Generating Inventory data...")
        
        # Create item classifications if they don't exist
        classifications = self._create_item_classifications()
        
        # Create items if they don't exist
        items = self._create_items(classifications)
        
        # Create operation compatibilities
        self._create_operation_compatibilities(items)
    
    def _create_item_classifications(self):
        """Create item classifications for auto parts and supplies."""
        classifications_data = [
            {'name': 'قطع غيار المحرك', 'code': 'ENG', 'description': 'قطع غيار ومكونات المحرك'},
            {'name': 'قطع غيار نظام التعليق', 'code': 'SUSP', 'description': 'قطع غيار نظام التعليق'},
            {'name': 'قطع غيار نظام الكبح', 'code': 'BRAKE', 'description': 'قطع غيار الفرامل ونظام الكبح'},
            {'name': 'قطع غيار نظام الوقود', 'code': 'FUEL', 'description': 'قطع غيار نظام الوقود'},
            {'name': 'قطع غيار نظام التبريد', 'code': 'COOLING', 'description': 'قطع غيار نظام التبريد'},
            {'name': 'قطع غيار كهربائية', 'code': 'ELEC', 'description': 'قطع غيار النظام الكهربائي'},
            {'name': 'زيوت وسوائل', 'code': 'FLUID', 'description': 'زيوت وسوائل للسيارة'},
            {'name': 'فلاتر', 'code': 'FILTER', 'description': 'أنواع الفلاتر المختلفة'},
            {'name': 'إطارات وعجلات', 'code': 'TIRE', 'description': 'إطارات وعجلات'},
            {'name': 'قطع هيكل خارجي', 'code': 'BODY', 'description': 'قطع غيار الهيكل الخارجي للسيارة'},
            {'name': 'مستهلكات وأدوات', 'code': 'CONS', 'description': 'مستهلكات وأدوات ورش الصيانة'}
        ]
        
        classifications = []
        for data in classifications_data:
            classification, created = ItemClassification.objects.get_or_create(
                tenant_id=self.tenant_id,
                code=data['code'],
                defaults={
                    'name': data['name'],
                    'description': data['description'],
                    'is_active': True
                }
            )
            
            classifications.append(classification)
            if created:
                print(f"Created Item Classification: {classification.name}")
            else:
                print(f"Using existing Item Classification: {classification.name}")
        
        return classifications
    
    def _create_items(self, classifications):
        """Create sample inventory items."""
        items_data = [
            # Engine parts (ENG)
            {'name': 'فلتر زيت تويوتا', 'sku': 'OIL-FILTER-TOYOTA', 'classification': 'ENG', 'price': 120},
            {'name': 'فلتر هواء هيونداي', 'sku': 'AIR-FILTER-HYUNDAI', 'classification': 'FILTER', 'price': 90},
            {'name': 'طقم بساتم 1600cc', 'sku': 'PISTON-SET-1600', 'classification': 'ENG', 'price': 1500},
            {'name': 'طقم عمود كامات', 'sku': 'CAMSHAFT-KIT', 'classification': 'ENG', 'price': 2200},
            
            # Suspension parts (SUSP)
            {'name': 'مساعد أمامي كيا سبورتاج', 'sku': 'SHOCK-F-KIA-SPORT', 'classification': 'SUSP', 'price': 750},
            {'name': 'مساعد خلفي هيونداي النترا', 'sku': 'SHOCK-R-HYUN-ELANTRA', 'classification': 'SUSP', 'price': 680},
            {'name': 'عفشة كاملة تويوتا كورولا', 'sku': 'SUSP-KIT-TOYOTA', 'classification': 'SUSP', 'price': 4500},
            
            # Brake parts (BRAKE)
            {'name': 'تيل فرامل أمامي تويوتا كورولا', 'sku': 'BRAKE-PAD-F-COROLLA', 'classification': 'BRAKE', 'price': 350},
            {'name': 'تيل فرامل خلفي نيسان صني', 'sku': 'BRAKE-PAD-R-SUNNY', 'classification': 'BRAKE', 'price': 280},
            {'name': 'اسطوانة فرامل رئيسية شيفروليه', 'sku': 'MASTER-CYL-CHEV', 'classification': 'BRAKE', 'price': 420},
            {'name': 'طنابير فرامل خلفي كيا', 'sku': 'BRAKE-DRUM-KIA', 'classification': 'BRAKE', 'price': 550},
            
            # Fuel system (FUEL)
            {'name': 'طلمبة بنزين كيا سيراتو', 'sku': 'FUEL-PUMP-KIA-CERATO', 'classification': 'FUEL', 'price': 890},
            {'name': 'فلتر بنزين هيونداي', 'sku': 'FUEL-FILTER-HYUNDAI', 'classification': 'FILTER', 'price': 120},
            {'name': 'حساس أكسجين تويوتا', 'sku': 'O2-SENSOR-TOYOTA', 'classification': 'FUEL', 'price': 650},
            
            # Cooling system (COOLING)
            {'name': 'ثيرموستات تويوتا', 'sku': 'THERMOSTAT-TOYOTA', 'classification': 'COOLING', 'price': 180},
            {'name': 'رادياتير نيسان صني', 'sku': 'RADIATOR-NISSAN-SUNNY', 'classification': 'COOLING', 'price': 1200},
            {'name': 'مروحة تبريد هيونداي', 'sku': 'COOLING-FAN-HYUNDAI', 'classification': 'COOLING', 'price': 450},
            
            # Electrical components (ELEC)
            {'name': 'بطارية 60 أمبير', 'sku': 'BATTERY-60A', 'classification': 'ELEC', 'price': 1500},
            {'name': 'دينامو تويوتا كورولا', 'sku': 'ALTERNATOR-TOYOTA-COROLLA', 'classification': 'ELEC', 'price': 980},
            {'name': 'مارش هيونداي النترا', 'sku': 'STARTER-HYUNDAI-ELANTRA', 'classification': 'ELEC', 'price': 1150},
            {'name': 'طقم أسلاك شمعات', 'sku': 'SPARK-PLUG-WIRES', 'classification': 'ELEC', 'price': 350},
            
            # Fluids (FLUID)
            {'name': 'زيت محرك 5W30 (لتر)', 'sku': 'ENGINE-OIL-5W30-1L', 'classification': 'FLUID', 'price': 120},
            {'name': 'زيت فتيس أوتوماتيك (لتر)', 'sku': 'ATF-1L', 'classification': 'FLUID', 'price': 150},
            {'name': 'سائل تبريد (جالون)', 'sku': 'COOLANT-1GAL', 'classification': 'FLUID', 'price': 200},
            {'name': 'زيت فرامل (لتر)', 'sku': 'BRAKE-FLUID-1L', 'classification': 'FLUID', 'price': 80},
            
            # Tires (TIRE)
            {'name': 'إطار 205/55R16', 'sku': 'TIRE-205-55-R16', 'classification': 'TIRE', 'price': 950},
            {'name': 'إطار 185/65R15', 'sku': 'TIRE-185-65-R15', 'classification': 'TIRE', 'price': 850},
            {'name': 'إطار 225/45R17', 'sku': 'TIRE-225-45-R17', 'classification': 'TIRE', 'price': 1100},
            
            # Body parts (BODY)
            {'name': 'صدام أمامي تويوتا كورولا', 'sku': 'BUMPER-F-COROLLA', 'classification': 'BODY', 'price': 1500},
            {'name': 'كبوت هيونداي النترا', 'sku': 'HOOD-ELANTRA', 'classification': 'BODY', 'price': 2300},
            {'name': 'جناح كيا سيراتو', 'sku': 'FENDER-KIA-CERATO', 'classification': 'BODY', 'price': 850},
            
            # Consumables (CONS)
            {'name': 'قطن تنظيف', 'sku': 'CLEANING-COTTON', 'classification': 'CONS', 'price': 30},
            {'name': 'سبراي تنظيف فرامل', 'sku': 'BRAKE-CLEANER-SPRAY', 'classification': 'CONS', 'price': 45},
            {'name': 'شحم تشحيم (علبة)', 'sku': 'GREASE-TUB', 'classification': 'CONS', 'price': 60}
        ]
        
        # Create a mapping of classification code to object
        classification_map = {c.code: c for c in classifications}
        
        items = []
        for data in items_data:
            # Get the classification object by code
            classification = classification_map.get(data['classification'])
            if not classification:
                print(f"Warning: Classification {data['classification']} not found, skipping item {data['name']}")
                continue
                
            # Try to find or create the item
            try:
                item, created = Item.objects.get_or_create(
                    tenant_id=self.tenant_id,
                    sku=data['sku'],
                    defaults={
                        'name': data['name'],
                        'description': f"وصف {data['name']}",
                        'classification': classification,
                        'unit_price': data['price'],
                        'category': self._get_category_from_classification(classification.code),
                        'min_stock_level': 5.0,
                        'quantity': random.randint(10, 100)
                    }
                )
                
                items.append(item)
                if created:
                    print(f"Created Item: {item.name}")
                else:
                    print(f"Using existing Item: {item.name}")
            except Exception as e:
                print(f"Error creating item {data['name']}: {e}")
        
        return items
    
    def _get_category_from_classification(self, classification_code):
        """Map classification code to item category"""
        category_map = {
            'ENG': 'part',
            'SUSP': 'part',
            'BRAKE': 'part',
            'FUEL': 'part',
            'COOLING': 'part',
            'ELEC': 'part',
            'FLUID': 'consumable',
            'FILTER': 'consumable',
            'TIRE': 'part',
            'BODY': 'part',
            'CONS': 'consumable'
        }
        return category_map.get(classification_code, 'part')
    
    def _create_operation_compatibilities(self, items):
        """Create operation compatibilities between items and work order types."""
        print("\nCreating Operation Compatibilities...")
        
        # Create work order types if they don't exist
        work_order_types_data = [
            {"name": "صيانة دورية", "description": "صيانة دورية للسيارة"},
            {"name": "إصلاح محرك", "description": "إصلاح أعطال المحرك"},
            {"name": "ضبط كهرباء", "description": "ضبط وإصلاح النظام الكهربائي"},
            {"name": "إصلاح فرامل", "description": "إصلاح وضبط نظام الفرامل"},
            {"name": "فحص شامل", "description": "فحص شامل للسيارة"},
            {"name": "تغيير زيت", "description": "تغيير زيت المحرك والفلاتر"},
            {"name": "إصلاح تكييف", "description": "إصلاح وشحن نظام التكييف"},
            {"name": "إصلاح عفشة", "description": "إصلاح نظام التعليق"},
            {"name": "تصليح هيكل", "description": "إصلاح وصيانة هيكل السيارة"}
        ]
        
        work_order_types = []
        for type_data in work_order_types_data:
            wo_type, created = WorkOrderType.objects.get_or_create(
                tenant_id=self.tenant_id,
                name=type_data["name"],
                defaults={
                    "description": type_data["description"]
                }
            )
            work_order_types.append(wo_type)
            if created:
                print(f"Created Work Order Type: {wo_type.name}")
        
        # Define which item classifications go with which work order types
        type_classifications = {
            'صيانة دورية': ['FILTER', 'FLUID', 'ENG'],  # Regular maintenance
            'إصلاح محرك': ['ENG', 'COOLING', 'FUEL'],  # Engine repair
            'ضبط كهرباء': ['ELEC'],  # Electrical adjustment
            'إصلاح فرامل': ['BRAKE', 'FLUID'],  # Brake repair
            'فحص شامل': ['ENG', 'ELEC', 'BRAKE', 'SUSP', 'COOLING', 'FUEL'],  # Full diagnostics
            'تغيير زيت': ['FLUID', 'FILTER'],  # Oil change
            'إصلاح تكييف': ['ELEC', 'COOLING'],  # AC repair
            'إصلاح عفشة': ['SUSP'],  # Suspension repair
            'تصليح هيكل': ['BODY']  # Body repair
        }
        
        # Create operation compatibilities
        compatibilities_created = 0
        
        # Map each work order type to relevant items based on classification
        for wo_type in work_order_types:
            # Find which classifications are relevant for this type
            relevant_class_codes = type_classifications.get(wo_type.name, [])
            
            # Get items that might have these classifications
            relevant_items = []
            for item in items:
                # Check if the item has a classification that matches our target codes
                if (item.classification and 
                    item.classification.code in relevant_class_codes):
                    relevant_items.append(item)
            
            # If we couldn't find matching items, use a random selection
            if not relevant_items:
                relevant_items = random.sample(items, min(5, len(items)))
            
            # Create compatibilities
            for item in relevant_items:
                compatibility, created = OperationCompatibility.objects.get_or_create(
                    tenant_id=self.tenant_id,
                    item=item,
                    operation_type=wo_type,
                    defaults={
                        'is_required': random.choice([True, False]),
                        'is_common': random.choice([True, False]),
                        'default_quantity': Decimal(str(round(random.uniform(1, 5), 2))),
                        'notes': fake.sentence()
                    }
                )
                if created:
                    compatibilities_created += 1
        
        print(f"Created {compatibilities_created} Operation Compatibilities")
    
    def run(self):
        """Run all data generation steps."""
        self.generate_inventory_data()
        print("\nCompleted Inventory data generation")

def main():
    """Main entry point."""
    generator = InventoryDataGenerator()
    generator.run()

if __name__ == "__main__":
    main() 