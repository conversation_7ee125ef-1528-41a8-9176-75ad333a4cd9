@echo off
echo ===============================================
echo Improved Data Migration: SQLite to PostgreSQL
echo ===============================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python and try again.
    pause
    exit /b 1
)

REM Set environment variables for PostgreSQL connection
REM You can modify these values or set them as system environment variables
set SQL_ENGINE=django.db.backends.postgresql
set SQL_DATABASE=postgres
set SQL_USER=postgres
set SQL_PASSWORD=postgrespw
set SQL_HOST=**************
set SQL_PORT=8136
set TZ=Africa/Cairo

echo Current Configuration:
echo PostgreSQL Host: %SQL_HOST%
echo PostgreSQL Port: %SQL_PORT%
echo Database: %SQL_DATABASE%
echo User: %SQL_USER%
echo Timezone: %TZ%
echo.

REM Ask user for dry run first
echo Recommendation: Run a dry-run first to check for issues
set /p dryrun=Do you want to run a dry-run first? (Y/n): 
if /i "%dryrun%" neq "n" (
    echo.
    echo Running dry-run...
    echo.
    python migrate_data_fixed.py --dry-run --sqlite-path "db.sqlite3"
    echo.
    echo Dry-run completed. Review the output above.
    echo.
    set /p proceed=Do you want to proceed with actual migration? (y/N): 
    if /i "%proceed%" neq "y" (
        echo Migration cancelled.
        pause
        exit /b 0
    )
)

echo.
echo Starting actual migration...
echo This may take several minutes depending on your data size.
echo.

REM Run the improved migration script
python migrate_data_fixed.py --sqlite-path "db.sqlite3"

echo.
echo Migration script completed.
echo Check the output above for any errors or warnings.
echo.

REM Ask if user wants to see migration summary
set /p summary=Do you want to see a summary of what was migrated? (y/N): 
if /i "%summary%" == "y" (
    echo.
    echo Checking migrated data...
    python -c "
import os, sys, django
sys.path.insert(0, '.')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()
from django.apps import apps
from django.db import connections

print('=== Migration Summary ===')
total_records = 0
exclude_apps = ['contenttypes', 'auth', 'sessions', 'admin', 'messages', 'django_otp', 'waffle', 'drf_api_logger', 'authtoken']

for app_config in apps.get_app_configs():
    if app_config.label not in exclude_apps:
        app_total = 0
        for model in app_config.get_models():
            if not model._meta.abstract and not model._meta.proxy:
                try:
                    count = model.objects.using('default').count()
                    if count > 0:
                        print(f'{app_config.label}.{model.__name__}: {count} records')
                        app_total += count
                except:
                    pass
        if app_total > 0:
            total_records += app_total
            print(f'--- {app_config.label} total: {app_total} records')

print(f'=== TOTAL: {total_records} records ===')
"
)

echo.
echo Migration process completed!
pause 