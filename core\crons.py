from datetime import timedelta
import logging
from django.core.management.base import BaseCommand
from notifications.services import retry_failed_webhooks
from django.db import models
from django.utils import timezone
from drf_api_logger.models import APILogsModel

from api.models import WebServiceLog
from inventory.models import Item
from notifications.services import create_notification, send_webhook

logger = logging.getLogger(__name__)


def purge_logs():
    print(f'{timezone.now()} Purge logs start...')
    thirty_days_ago = timezone.now() - timedelta(days=7)
    WebServiceLog.objects.filter(created_on__lt=thirty_days_ago).delete()
    APILogsModel.objects.filter(added_on__lt=thirty_days_ago).delete()
    print(f'{timezone.now()} Purge logs end.')


def retry_webhooks_job():
    """
    Cron job to retry failed webhook deliveries
    """
    try:
        retry_count = retry_failed_webhooks()
        if retry_count > 0:
            logger.info(f"Successfully retried {retry_count} failed webhook deliveries")
        else:
            logger.info("No failed webhook deliveries to retry")
    except Exception as e:
        logger.exception(f"Error in webhook retry cron job: {e}")


def check_stock_levels():
    """
    Cron job to check inventory stock levels and trigger notifications
    for items that have fallen below threshold since last check
    """
    try:
        # Get all items that are below min stock level
        low_stock_items = Item.objects.filter(
            quantity__lt=models.F('min_stock_level'),
            is_low_stock=False  # Only those not already marked
        )
        
        for item in low_stock_items:
            # Update the flag
            item.is_low_stock = True
            item.save(update_fields=['is_low_stock'])
            
            # Create a notification
            create_notification(
                tenant_id=item.tenant_id,
                title=f"Low Stock Alert: {item.name}",
                message=f"Item {item.name} (SKU: {item.sku}) is below the minimum stock level. Current quantity: {item.quantity}",
                level='warning',
                object_type='Item',
                object_id=str(item.id)
            )
            
            # Send a webhook
            payload = {
                'id': str(item.id),
                'name': item.name,
                'sku': item.sku,
                'description': item.description,
                'quantity': item.quantity,
                'min_stock_level': item.min_stock_level,
            }
            send_webhook('stock_low', payload, item.tenant_id)
            
        if low_stock_items:
            logger.info(f"Found {low_stock_items.count()} new low stock items")
        
    except Exception as e:
        logger.exception(f"Error in stock level check cron job: {e}")


# Map of cron jobs
CRON_JOBS = {
    'retry_webhooks': retry_webhooks_job,
    'check_stock_levels': check_stock_levels,
}
