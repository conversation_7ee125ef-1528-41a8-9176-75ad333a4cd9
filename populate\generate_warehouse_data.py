import os
import sys
import django
import random
import uuid
from django.db import transaction
from decimal import Decimal
from faker import Faker
from datetime import datetime, timedelta

# Add the parent directory to sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

# Import necessary models
from warehouse.models import (
    LocationType, Location, BinLocation, ItemLocation, 
    TransferOrder, TransferOrderItem, Transfer
)
from inventory.models import Item
from setup.models import ServiceCenter
from django.utils import timezone

# Initialize Faker
fake = Faker('ar_EG')  # Using Egyptian Arabic locale for Egyptian market specifics

class WarehouseDataGenerator:
    """Generate warehouse data for the Aftersails system."""
    
    def __init__(self):
        # Use an existing tenant_id
        from setup.models import Customer
        customer = Customer.objects.first()
        if customer:
            self.tenant_id = customer.tenant_id
        else:
            self.tenant_id = "default_tenant"
            
        print(f"Using tenant ID: {self.tenant_id}")
    
    @transaction.atomic
    def generate_warehouse_data(self):
        """Generate warehouse data with Egyptian specifics."""
        print("Generating warehouse data...")
        
        # Create location types
        location_types = self._create_location_types()
        
        # Create locations
        locations = self._create_locations(location_types)
        
        # Create bin locations
        bin_locations = self._create_bin_locations(locations)
        
        # Assign items to locations
        self._assign_items_to_locations(locations, bin_locations)
        
        # Create transfer orders
        self._create_transfer_orders(locations)
    
    def _create_location_types(self):
        """Create location type records."""
        location_type_data = [
            {
                'name': 'المخزن الرئيسي',
                'code': 'MAIN-WH',
                'description': 'المخزن الرئيسي لقطع الغيار والمواد',
                'icon': 'warehouse',
                'color': '#3498db',
                'is_active': True,
                'requires_bin_locations': True,
                'is_storage': True,
                'is_receiving': True,
                'is_shipping': True,
                'is_service': False,
            },
            {
                'name': 'مخزن مركز الخدمة',
                'code': 'SC-WH',
                'description': 'مخزن مرتبط بمركز خدمة محدد',
                'icon': 'tools',
                'color': '#2ecc71',
                'is_active': True,
                'requires_bin_locations': True,
                'is_storage': True,
                'is_receiving': True,
                'is_shipping': False,
                'is_service': True,
            },
            {
                'name': 'منطقة الاستلام',
                'code': 'RECV',
                'description': 'منطقة استلام البضائع والمواد الواردة',
                'icon': 'truck-loading',
                'color': '#f39c12',
                'is_active': True,
                'requires_bin_locations': False,
                'is_storage': True,
                'is_receiving': True,
                'is_shipping': False,
                'is_service': False,
            },
            {
                'name': 'منطقة الشحن',
                'code': 'SHIP',
                'description': 'منطقة شحن البضائع والمواد الصادرة',
                'icon': 'shipping-fast',
                'color': '#e74c3c',
                'is_active': True,
                'requires_bin_locations': False,
                'is_storage': True,
                'is_receiving': False,
                'is_shipping': True,
                'is_service': False,
            }
        ]
        
        location_types = []
        for data in location_type_data:
            location_type, created = LocationType.objects.get_or_create(
                tenant_id=self.tenant_id,
                code=data['code'],
                defaults={
                    'name': data['name'],
                    'description': data['description'],
                    'icon': data['icon'],
                    'color': data['color'],
                    'is_active': data['is_active'],
                    'requires_bin_locations': data['requires_bin_locations'],
                    'is_storage': data['is_storage'],
                    'is_receiving': data['is_receiving'],
                    'is_shipping': data['is_shipping'],
                    'is_service': data['is_service'],
                }
            )
            
            location_types.append(location_type)
            if created:
                print(f"Created Location Type: {location_type.name}")
            else:
                print(f"Using existing Location Type: {location_type.name}")
        
        return location_types
    
    def _create_locations(self, location_types):
        """Create location records."""
        # Get service centers to associate with service warehouses
        service_centers = list(ServiceCenter.objects.filter(tenant_id=self.tenant_id))
        if not service_centers:
            service_centers = list(ServiceCenter.objects.all()[:5])
            if service_centers:
                print(f"Using service centers with different tenant IDs")
                # Update tenant_id to match what we're using
                self.tenant_id = service_centers[0].tenant_id
                print(f"Switching to tenant ID: {self.tenant_id}")
        
        locations = []
        
        # Map of location type code to the appropriate location type object
        location_type_map = {lt.code: lt for lt in location_types}
        
        # Create main warehouse
        main_warehouse, created = Location.objects.get_or_create(
            tenant_id=self.tenant_id,
            code="MAIN-CAIRO",
            defaults={
                'name': "المخزن الرئيسي - القاهرة",
                'location_type': location_type_map.get('MAIN-WH'),
                'address': "المنطقة الصناعية، القاهرة الجديدة",
                'city': "القاهرة",
                'country': "مصر",
                'postal_code': "11865",
                'contact_name': "محمد عبدالله",
                'phone': "01012345678",
                'email': "<EMAIL>",
                'area_sqm': Decimal('1500.00'),
                'max_items': 5000,
                'is_active': True,
                'notes': "المخزن الرئيسي للشركة وقطع الغيار"
            }
        )
        locations.append(main_warehouse)
        if created:
            print(f"Created Main Warehouse: {main_warehouse.name}")
        else:
            print(f"Using existing Main Warehouse: {main_warehouse.name}")
        
        # Create receiving and shipping areas in main warehouse
        receiving_area, created = Location.objects.get_or_create(
            tenant_id=self.tenant_id,
            code="RECV-MAIN",
            defaults={
                'name': "منطقة الاستلام - المخزن الرئيسي",
                'location_type': location_type_map.get('RECV'),
                'parent': main_warehouse,
                'is_active': True,
                'area_sqm': Decimal('100.00')
            }
        )
        locations.append(receiving_area)
        if created:
            print(f"Created Location: {receiving_area.name}")
        else:
            print(f"Using existing Location: {receiving_area.name}")
        
        shipping_area, created = Location.objects.get_or_create(
            tenant_id=self.tenant_id,
            code="SHIP-MAIN",
            defaults={
                'name': "منطقة الشحن - المخزن الرئيسي",
                'location_type': location_type_map.get('SHIP'),
                'parent': main_warehouse,
                'is_active': True,
                'area_sqm': Decimal('100.00')
            }
        )
        locations.append(shipping_area)
        if created:
            print(f"Created Location: {shipping_area.name}")
        else:
            print(f"Using existing Location: {shipping_area.name}")
        
        # Create service center warehouses
        for i, service_center in enumerate(service_centers[:5]):  # Limit to 5 service centers
            sc_warehouse, created = Location.objects.get_or_create(
                tenant_id=self.tenant_id,
                code=f"SC-WH-{i+1}",
                defaults={
                    'name': f"مخزن {service_center.name}",
                    'location_type': location_type_map.get('SC-WH'),
                    'address': service_center.address,
                    'city': service_center.city,
                    'country': service_center.country,
                    'contact_name': service_center.contact_person if hasattr(service_center, 'contact_person') else "",
                    'phone': service_center.phone,
                    'email': service_center.email,
                    'area_sqm': Decimal(str(random.randint(50, 300))),
                    'max_items': random.randint(500, 1500),
                    'is_active': True
                }
            )
            locations.append(sc_warehouse)
            if created:
                print(f"Created Service Center Warehouse: {sc_warehouse.name}")
            else:
                print(f"Using existing Service Center Warehouse: {sc_warehouse.name}")
            
            try:
                # Associate service center with this warehouse
                service_center.primary_warehouse = sc_warehouse
                service_center.save()
                print(f"Associated {sc_warehouse.name} with {service_center.name}")
            except Exception as e:
                print(f"Error associating warehouse with service center: {e}")
        
        return locations
    
    def _create_bin_locations(self, locations):
        """Create bin location records."""
        bin_locations = []
        
        for location in locations:
            # Only create bin locations for warehouse types that require them
            if location.location_type and location.location_type.requires_bin_locations:
                # Check for existing bin locations for this location
                existing_bins = BinLocation.objects.filter(location=location)
                if existing_bins.exists():
                    print(f"Using {existing_bins.count()} existing Bin Locations for {location.name}")
                    bin_locations.extend(existing_bins)
                    continue
                
                # Determine how many bin locations to create
                num_bins = random.randint(5, 20)
                
                for i in range(num_bins):
                    # Create a structure of aisles, racks, shelves, and positions
                    aisle = random.randint(1, 5)
                    rack = random.randint(1, 10)
                    shelf = random.randint(1, 4)
                    position = random.randint(1, 10)
                    
                    bin_name = f"موقع {aisle}-{rack}-{shelf}-{position}"
                    bin_code = f"BIN-{location.code}-{aisle:02d}{rack:02d}{shelf:02d}{position:02d}-{str(uuid.uuid4())[:6]}"
                    
                    try:
                        bin_location = BinLocation.objects.create(
                            tenant_id=self.tenant_id,
                            location=location,
                            name=bin_name,
                            code=bin_code,
                            description=f"موقع تخزين في {location.name}",
                            is_active=True,
                            aisle=str(aisle),
                            rack=str(rack),
                            shelf=str(shelf),
                            position=str(position)
                        )
                        
                        bin_locations.append(bin_location)
                    except Exception as e:
                        print(f"Error creating bin location {bin_code}: {e}")
                
                print(f"Created {num_bins} Bin Locations for {location.name}")
        
        return bin_locations
    
    def _assign_items_to_locations(self, locations, bin_locations):
        """Assign inventory items to locations."""
        # Get all available items
        items = list(Item.objects.filter(tenant_id=self.tenant_id))
        if not items:
            items = list(Item.objects.all()[:30])
            if not items:
                print("No inventory items found. Cannot assign to locations.")
                return
            print(f"Using {len(items)} items with different tenant IDs")
        
        # Check for existing item locations
        existing_item_locations = ItemLocation.objects.filter(tenant_id=self.tenant_id)
        if existing_item_locations.exists():
            print(f"Found {existing_item_locations.count()} existing Item Locations. Skipping assignment.")
            return
        
        item_locations_created = 0
        
        # Assign items to main warehouse first
        main_warehouse = next((loc for loc in locations if 'الرئيسي' in loc.name), None)
        if main_warehouse:
            # Get bin locations for this warehouse
            warehouse_bins = [bin_loc for bin_loc in bin_locations if bin_loc.location == main_warehouse]
            
            # Assign most items to main warehouse
            for item in items:
                bin_location = random.choice(warehouse_bins) if warehouse_bins else None
                
                # Create item location with random quantity
                quantity = Decimal(str(random.randint(10, 200)))
                
                try:
                    ItemLocation.objects.create(
                        tenant_id=self.tenant_id,
                        item=item,
                        location=main_warehouse,
                        bin_location=bin_location,
                        quantity=quantity,
                        reorder_point=Decimal(str(random.randint(5, 20))),
                        max_stock=Decimal(str(random.randint(200, 500))),
                        min_stock=Decimal(str(random.randint(5, 20)))
                    )
                    
                    item_locations_created += 1
                except Exception as e:
                    print(f"Error creating item location for {item.name} in {main_warehouse.name}: {e}")
            
            print(f"Assigned {len(items)} items to main warehouse")
        
        # Assign a subset of items to service center warehouses
        sc_warehouses = [loc for loc in locations if loc.location_type and loc.location_type.code == 'SC-WH']
        for sc_warehouse in sc_warehouses:
            # Get bin locations for this warehouse
            warehouse_bins = [bin_loc for bin_loc in bin_locations if bin_loc.location == sc_warehouse]
            
            # Select a subset of items to assign to this service center
            subset_size = min(len(items) // 2, 30)  # About half the items, max 30
            subset_items = random.sample(items, subset_size)
            
            for item in subset_items:
                bin_location = random.choice(warehouse_bins) if warehouse_bins else None
                
                # Service centers have less stock
                quantity = Decimal(str(random.randint(2, 50)))
                
                try:
                    ItemLocation.objects.create(
                        tenant_id=self.tenant_id,
                        item=item,
                        location=sc_warehouse,
                        bin_location=bin_location,
                        quantity=quantity,
                        reorder_point=Decimal(str(random.randint(3, 10))),
                        max_stock=Decimal(str(random.randint(50, 100))),
                        min_stock=Decimal(str(random.randint(3, 10)))
                    )
                    
                    item_locations_created += 1
                except Exception as e:
                    print(f"Error creating item location for {item.name} in {sc_warehouse.name}: {e}")
                
            print(f"Assigned {len(subset_items)} items to {sc_warehouse.name}")
        
        print(f"Created {item_locations_created} Item Location entries")
    
    def _create_transfer_orders(self, locations):
        """Create transfer order records."""
        # Need at least 2 locations with items for transfers
        main_warehouse = next((loc for loc in locations if 'الرئيسي' in loc.name), None)
        service_warehouses = [loc for loc in locations if loc.location_type and loc.location_type.code == 'SC-WH']
        
        if not main_warehouse or not service_warehouses:
            print("Insufficient locations for creating transfers.")
            return
        
        # Check for existing transfer orders
        existing_transfers = TransferOrder.objects.filter(tenant_id=self.tenant_id)
        if existing_transfers.exists():
            print(f"Found {existing_transfers.count()} existing Transfer Orders. Will create new ones with unique references.")
        
        # Get items available at main warehouse
        main_wh_items = list(ItemLocation.objects.filter(
            tenant_id=self.tenant_id,
            location=main_warehouse
        ))
        
        if not main_wh_items:
            print("No items found in main warehouse. Cannot create transfers.")
            return
        
        # Get items available at service warehouses
        sc_wh_items = {
            sc: list(ItemLocation.objects.filter(tenant_id=self.tenant_id, location=sc))
            for sc in service_warehouses
        }
        
        transfer_orders_created = 0
        transfer_records_created = 0
        
        # Create transfers from main warehouse to service centers
        for service_warehouse in service_warehouses:
            # Create 1-3 transfer orders for each service center
            for i in range(random.randint(1, 3)):
                # Generate random status
                status = random.choice(['draft', 'pending', 'in_transit', 'completed', 'cancelled'])
                
                # Generate reference number
                reference = f"TO-{str(uuid.uuid4())[:8]}-{datetime.now().strftime('%y%m%d')}-{i+1:04d}"
                
                try:
                    # Create transfer order
                    transfer_order = TransferOrder.objects.create(
                        tenant_id=self.tenant_id,
                        reference=reference,
                        source_location=main_warehouse,
                        destination_location=service_warehouse,
                        status=status,
                        notes=fake.paragraph() if random.random() > 0.7 else "",
                        items_count=0  # Will be updated after adding items
                    )
                    transfer_orders_created += 1
                    
                    # Select items to transfer
                    num_items = random.randint(3, 10)
                    selected_items = random.sample(main_wh_items, min(num_items, len(main_wh_items)))
                    
                    # Add items to transfer order
                    for item_location in selected_items:
                        # Determine quantity to transfer
                        max_quantity = min(item_location.quantity, Decimal('10.0'))
                        transfer_quantity = Decimal(str(round(random.uniform(1, float(max_quantity)), 2)))
                        
                        TransferOrderItem.objects.create(
                            tenant_id=self.tenant_id,
                            transfer_order=transfer_order,
                            item=item_location.item,
                            quantity=transfer_quantity,
                            notes=""
                        )
                    
                    # Update items count
                    transfer_order.items_count = len(selected_items)
                    transfer_order.save()
                    
                    # For completed transfers, create a transfer record
                    if status == 'completed':
                        Transfer.objects.create(
                            tenant_id=self.tenant_id,
                            source_location=main_warehouse,
                            destination_location=service_warehouse,
                            items_count=transfer_order.items_count,
                            reference=reference,
                            notes=transfer_order.notes
                        )
                        transfer_records_created += 1
                except Exception as e:
                    print(f"Error creating transfer order {reference}: {e}")
            
            print(f"Created transfer orders from main warehouse to {service_warehouse.name}")
        
        # Create a few transfers from service centers back to main warehouse
        for service_warehouse in service_warehouses:
            if not sc_wh_items.get(service_warehouse):
                continue
                
            # 50% chance of return transfer for each service center
            if random.random() > 0.5:
                # Generate random status
                status = random.choice(['draft', 'pending', 'in_transit', 'completed'])
                
                # Generate reference number
                reference = f"TO-RETURN-{str(uuid.uuid4())[:8]}-{datetime.now().strftime('%y%m%d')}-{service_warehouse.id.hex[:4]}"
                
                try:
                    # Create transfer order
                    transfer_order = TransferOrder.objects.create(
                        tenant_id=self.tenant_id,
                        reference=reference,
                        source_location=service_warehouse,
                        destination_location=main_warehouse,
                        status=status,
                        notes="إعادة مخزون غير مستخدم إلى المخزن الرئيسي",
                        items_count=0  # Will be updated after adding items
                    )
                    transfer_orders_created += 1
                    
                    # Select items to transfer back
                    sc_items = sc_wh_items.get(service_warehouse, [])
                    num_items = random.randint(1, 5)
                    selected_items = random.sample(sc_items, min(num_items, len(sc_items)))
                    
                    # Add items to transfer order
                    for item_location in selected_items:
                        # Determine quantity to transfer
                        max_quantity = min(item_location.quantity, Decimal('5.0'))
                        transfer_quantity = Decimal(str(round(random.uniform(1, float(max_quantity)), 2)))
                        
                        TransferOrderItem.objects.create(
                            tenant_id=self.tenant_id,
                            transfer_order=transfer_order,
                            item=item_location.item,
                            quantity=transfer_quantity,
                            notes="إعادة إلى المخزن الرئيسي"
                        )
                    
                    # Update items count
                    transfer_order.items_count = len(selected_items)
                    transfer_order.save()
                    
                    # For completed transfers, create a transfer record
                    if status == 'completed':
                        Transfer.objects.create(
                            tenant_id=self.tenant_id,
                            source_location=service_warehouse,
                            destination_location=main_warehouse,
                            items_count=transfer_order.items_count,
                            reference=reference,
                            notes=transfer_order.notes
                        )
                        transfer_records_created += 1
                except Exception as e:
                    print(f"Error creating return transfer order {reference}: {e}")
                
                print(f"Created return transfer order from {service_warehouse.name} to main warehouse")
        
        print(f"Created {transfer_orders_created} Transfer Orders and {transfer_records_created} Transfer Records")
    
    def run(self):
        """Run all data generation steps."""
        self.generate_warehouse_data()
        print("\nCompleted warehouse data generation")

def main():
    """Main entry point."""
    generator = WarehouseDataGenerator()
    generator.run()

if __name__ == "__main__":
    main() 