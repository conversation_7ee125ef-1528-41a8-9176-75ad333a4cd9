from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from django.utils.translation import gettext_lazy as _
from inventory.models import Item, Movement, MovementType
from django.db.models import F
from notifications.services import send_webhook, create_notification
import logging

logger = logging.getLogger(__name__)


@receiver(post_save, sender=Item)
def item_saved(sender, instance, created, **kwargs):
    """
    Handle post-save signal for Item model.
    Create notifications or trigger webhooks when items are created or updated.
    """
    if created:
        logger.info(f"New item created: {instance.name} ({instance.sku})")
        # In a complete implementation, you would send a notification here
        # from notifications.services import create_notification
        # create_notification(
        #    tenant_id=instance.tenant_id,
        #    title=_("New Item Added"),
        #    message=_("Item {name} ({sku}) has been added to inventory").format(
        #        name=instance.name, sku=instance.sku
        #    ),
        #    object_type="Item",
        #    object_id=str(instance.id)
        # )
    
    # Check for low stock
    if instance.is_low_stock:
        logger.warning(f"Low stock alert for item: {instance.name} ({instance.sku})")
        # In a complete implementation, you would send a notification here
        # from notifications.services import create_notification
        # create_notification(
        #    tenant_id=instance.tenant_id,
        #    title=_("Low Stock Alert"),
        #    message=_("Item {name} ({sku}) is below minimum stock level").format(
        #        name=instance.name, sku=instance.sku
        #    ),
        #    object_type="Item",
        #    object_id=str(instance.id),
        #    level="warning"
        # )


@receiver(post_save, sender=Movement)
def movement_saved(sender, instance, created, **kwargs):
    """
    Handle post-save signal for Movement model.
    Log movements and create notifications for significant events.
    """
    if created:
        if instance.movement_type_ref:
            movement_name = instance.movement_type_ref.name
        else:
            movement_name = instance.get_movement_type_display() or "Unknown"
            
        logger.info(
            f"New movement: {movement_name} of {instance.quantity} "
            f"{instance.item.name} ({instance.item.sku})"
        )


@receiver(post_save, sender=Item)
def trigger_item_webhooks(sender, instance, created, **kwargs):
    """
    Trigger webhooks when an item is created or updated
    """
    event_type = 'item_created' if created else 'item_updated'
    
    # Prepare payload
    payload = {
        'id': str(instance.id),
        'name': instance.name,
        'sku': instance.sku,
        'description': instance.description,
        'category': instance.category,
        'quantity': instance.quantity,
        'unit_price': float(instance.unit_price) if instance.unit_price else None,
        'min_stock_level': instance.min_stock_level,
        'is_low_stock': instance.is_low_stock,
    }
    
    # Send webhook
    send_webhook(event_type, payload, instance.tenant_id)
    
    # Create notification for low stock
    if instance.is_low_stock:
        create_notification(
            tenant_id=instance.tenant_id,
            title=f"Low Stock Alert: {instance.name}",
            message=f"Item {instance.name} (SKU: {instance.sku}) is below the minimum stock level. Current quantity: {instance.quantity}",
            level='warning',
            object_type='Item',
            object_id=str(instance.id)
        )
        
        # Also send a webhook for low stock
        send_webhook('stock_low', payload, instance.tenant_id)


@receiver(post_save, sender=Movement)
def trigger_movement_webhooks(sender, instance, created, **kwargs):
    """
    Trigger webhooks when a movement is created
    """
    if not created:
        return  # Only trigger on creation, not updates
    
    # Determine movement type name 
    if instance.movement_type_ref:
        movement_type_name = instance.movement_type_ref.name
        movement_type_code = instance.movement_type_ref.code
    else:
        movement_type_name = instance.get_movement_type_display() or "Unknown"
        movement_type_code = instance.movement_type or "unknown"
    
    # Prepare payload
    payload = {
        'id': str(instance.id),
        'item_id': str(instance.item.id) if instance.item else None,
        'item_name': instance.item.name if instance.item else None,
        'item_sku': instance.item.sku if instance.item else None,
        'quantity': instance.quantity,
        'movement_type': movement_type_name,
        'movement_type_code': movement_type_code,
        'reference': instance.reference,
        'notes': instance.notes,
        'created_at': instance.created_at.isoformat() if instance.created_at else None,
        'is_inbound': instance.is_inbound(),
        'is_outbound': instance.is_outbound(),
    }
    
    # Send webhook
    send_webhook('movement_created', payload, instance.tenant_id)
    
    # Also send a specific event based on movement type
    event_type = f"movement_{movement_type_code}"
    send_webhook(event_type, payload, instance.tenant_id)


@receiver(post_save, sender=MovementType)
def movement_type_saved(sender, instance, created, **kwargs):
    """
    Handle post-save signal for MovementType model.
    Log when new movement types are created.
    """
    if created:
        logger.info(
            f"New movement type created: {instance.name} (code: {instance.code}), "
            f"inbound: {instance.is_inbound}, outbound: {instance.is_outbound}"
        ) 