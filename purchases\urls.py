from django.urls import path
from . import views

app_name = 'purchases'

urlpatterns = [
    # Dashboard
    path('', views.PurchaseDashboardView.as_view(), name='dashboard'),
    
    # Purchase Orders
    path('orders/', views.PurchaseOrderListView.as_view(), name='purchase_order_list'),
    path('orders/<uuid:pk>/', views.PurchaseOrderDetailView.as_view(), name='purchase_order_detail'),
    path('orders/create/', views.PurchaseOrderCreateView.as_view(), name='purchase_order_create'),
    path('orders/<uuid:pk>/update/', views.PurchaseOrderUpdateView.as_view(), name='purchase_order_update'),
    
    # Suppliers
    path('suppliers/', views.SupplierListView.as_view(), name='supplier_list'),
    path('suppliers/<uuid:pk>/', views.SupplierDetailView.as_view(), name='supplier_detail'),
    path('suppliers/create/', views.SupplierCreateView.as_view(), name='supplier_create'),
    path('suppliers/<uuid:pk>/update/', views.SupplierUpdateView.as_view(), name='supplier_update'),
] 