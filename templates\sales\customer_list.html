{% extends "dashboard_base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "العملاء" %}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="mb-6 flex flex-wrap items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-800">{% trans "العملاء" %}</h1>
            <p class="text-gray-600">{% trans "إدارة وعرض بيانات العملاء" %}</p>
        </div>
        <div class="mt-4 md:mt-0">
            <a href="{% url 'sales:customer_create' %}" class="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg inline-flex items-center">
                <i class="fas fa-user-plus {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i>
                {% trans "عميل جديد" %}
            </a>
        </div>
    </div>
    
    {% if missing_tenant %}
        <div class="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-6" role="alert">
            <p class="font-bold">{% trans "Tenant ID Missing" %}</p>
            <p>{% trans "No tenant ID was found. Please make sure your X-Tenant-ID header is set correctly." %}</p>
        </div>
    {% endif %}
    
    <!-- Search Form -->
    <div class="bg-white shadow rounded-lg mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-800">{% trans "بحث" %}</h2>
        </div>
        <div class="p-6">
            <form method="get" class="flex flex-wrap gap-4">
                <div class="w-full md:w-2/3">
                    <label for="search" class="block text-sm font-medium text-gray-700 mb-1">{% trans "اسم العميل أو رقم الهاتف" %}</label>
                    <input type="text" name="search" id="search" value="{{ current_search }}" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md" placeholder="{% trans 'بحث عن عميل' %}">
                </div>
                <div class="w-full md:w-1/3 flex items-end">
                    <button type="submit" class="w-full bg-blue-100 hover:bg-blue-200 text-blue-700 py-2 px-4 rounded-md flex justify-center items-center">
                        <i class="fas fa-search {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i>
                        {% trans "بحث" %}
                    </button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Customers Table -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-800">{% trans "قائمة العملاء" %}</h2>
        </div>
        
        {% if customers %}
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "الاسم" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "البريد الإلكتروني" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "رقم الهاتف" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "عدد الطلبات" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "تاريخ التسجيل" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "الإجراءات" %}
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for customer in customers %}
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                                            <span class="text-blue-800 font-bold">{{ customer.name|slice:":1" }}</span>
                                        </div>
                                        <div class="{% if LANGUAGE_CODE == 'ar' %}mr-4{% else %}ml-4{% endif %}">
                                            <div class="text-sm font-medium text-gray-900">{{ customer.name }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">
                                        {% if customer.email %}
                                            <a href="mailto:{{ customer.email }}" class="text-blue-600 hover:text-blue-800">
                                                {{ customer.email }}
                                            </a>
                                        {% else %}
                                            <span class="text-gray-500">{% trans "غير محدد" %}</span>
                                        {% endif %}
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">
                                        {% if customer.phone %}
                                            {{ customer.phone }}
                                        {% else %}
                                            <span class="text-gray-500">{% trans "غير محدد" %}</span>
                                        {% endif %}
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ customer.orders.count }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">{{ customer.created_at|date:"d/m/Y" }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <a href="{% url 'sales:customer_detail' customer.id %}" class="text-indigo-600 hover:text-indigo-900 mr-3">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'sales:customer_update' customer.id %}" class="text-green-600 hover:text-green-900 mr-3">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            {% if is_paginated %}
                <div class="px-6 py-4 bg-gray-50 border-t border-gray-200">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-700">
                                {% trans "عرض" %}
                                <span class="font-medium">{{ page_obj.start_index }}</span>
                                {% trans "إلى" %}
                                <span class="font-medium">{{ page_obj.end_index }}</span>
                                {% trans "من" %}
                                <span class="font-medium">{{ paginator.count }}</span>
                                {% trans "عميل" %}
                            </p>
                        </div>
                        <div>
                            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                {% if page_obj.has_previous %}
                                    <a href="?page={{ page_obj.previous_page_number }}{% if current_search %}&search={{ current_search }}{% endif %}" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                        <span class="sr-only">{% trans "السابق" %}</span>
                                        <i class="fas fa-chevron-left"></i>
                                    </a>
                                {% endif %}
                                
                                {% for i in paginator.page_range %}
                                    {% if page_obj.number == i %}
                                        <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-700">
                                            {{ i }}
                                        </span>
                                    {% elif i > page_obj.number|add:"-3" and i < page_obj.number|add:"3" %}
                                        <a href="?page={{ i }}{% if current_search %}&search={{ current_search }}{% endif %}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                            {{ i }}
                                        </a>
                                    {% endif %}
                                {% endfor %}
                                
                                {% if page_obj.has_next %}
                                    <a href="?page={{ page_obj.next_page_number }}{% if current_search %}&search={{ current_search }}{% endif %}" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                        <span class="sr-only">{% trans "التالي" %}</span>
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                {% endif %}
                            </nav>
                        </div>
                    </div>
                </div>
            {% endif %}
        {% else %}
            <div class="p-6 text-center text-gray-500">
                {% trans "لا يوجد عملاء" %}
            </div>
        {% endif %}
    </div>
</div>
{% endblock %} 