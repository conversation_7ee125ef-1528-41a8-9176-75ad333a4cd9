import os
import sys
import django
import random
from datetime import datetime, timedelta

# Add the parent directory to the Python path so imports work correctly
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

# Import Django models
from django.db import transaction
from django.utils import timezone
from setup.models import Vehicle, ServiceCenter, ServiceHistory

class VehicleHistoryGenerator:
    def __init__(self):
        print("Vehicle History data generator initialized")
    
    def generate_vehicle_history(self, count=100):
        """
        Generate vehicle history records
        """
        print(f"\nGenerating {count} vehicle history records...")
        
        # Get vehicles
        vehicles = list(Vehicle.objects.all())
        if not vehicles:
            print("No vehicles found. Please ensure Vehicle table has data.")
            return
        
        # Get service centers
        service_centers = list(ServiceCenter.objects.all())
        if not service_centers:
            print("No service centers found. Please ensure ServiceCenter table has data.")
            return
        
        # Service descriptions to randomize data
        service_descriptions = [
            "صيانة دورية",
            "تغيير زيت المحرك",
            "تغيير فلتر الهواء",
            "تغيير فلتر الزيت",
            "فحص وضبط الفرامل",
            "تغيير إطارات",
            "ضبط زوايا العجلات",
            "إصلاح نظام التكييف",
            "إصلاح نظام التعليق",
            "فحص نظام الوقود",
            "إصلاح المحرك",
            "تغيير حزام التوقيت",
            "إصلاح نظام العادم",
            "تغيير البطارية",
            "إصلاح مشكلة كهربائية",
            "تغيير شمعات الإشعال"
        ]
        
        # Parts used templates
        parts_templates = [
            [{"name": "زيت محرك", "quantity": 5, "unit": "لتر"}],
            [{"name": "فلتر زيت", "quantity": 1, "unit": "قطعة"}],
            [{"name": "فلتر هواء", "quantity": 1, "unit": "قطعة"}],
            [{"name": "بطارية", "quantity": 1, "unit": "قطعة"}],
            [{"name": "إطارات", "quantity": 4, "unit": "قطعة"}],
            [{"name": "شمعات إشعال", "quantity": 4, "unit": "قطعة"}],
            [{"name": "زيت محرك", "quantity": 5, "unit": "لتر"}, {"name": "فلتر زيت", "quantity": 1, "unit": "قطعة"}],
            [{"name": "وسادات فرامل", "quantity": 2, "unit": "طقم"}],
            [{"name": "حزام توقيت", "quantity": 1, "unit": "قطعة"}],
            [{"name": "سائل تبريد", "quantity": 3, "unit": "لتر"}],
            [{"name": "زيت ناقل الحركة", "quantity": 4, "unit": "لتر"}],
            [{"name": "مساحات زجاج", "quantity": 2, "unit": "طقم"}],
            []  # Empty parts list for some services
        ]
        
        # Technician names
        technician_names = [
            "محمد علي",
            "أحمد إبراهيم",
            "خالد محمود",
            "محمود سمير",
            "عبد الرحمن عادل",
            "سعيد عبد المنعم",
            "باسم يوسف",
            "طارق حامد",
            "عمرو محمد",
            "حسام علي",
            "أيمن عبد الله",
            "وائل فهمي"
        ]
        
        # Create history records
        histories_created = 0
        
        for i in range(count):
            # Select a random vehicle
            vehicle = random.choice(vehicles)
            
            # Select a random service center
            service_center = random.choice(service_centers)
            
            # Generate a random service date within the last 3 years
            days_ago = random.randint(1, 1095)  # Up to 3 years
            service_date = timezone.now().date() - timedelta(days=days_ago)
            
            # Generate random odometer reading between 5000 and 150000
            odometer = random.randint(5000, 150000)
            
            # Generate a unique work order number
            work_order_number = f"WO-{service_date.year}-{random.randint(1000, 9999)}"
            
            # Select random description, services, parts and technician
            description = random.choice(service_descriptions)
            services_performed = [random.choice(service_descriptions) for _ in range(random.randint(1, 3))]
            parts_used = random.choice(parts_templates)
            technician = random.choice(technician_names)
            
            # Generate random cost between 200 and 5000
            total_cost = random.randint(200, 5000)
            
            # Create the service history record
            try:
                # Get tenant_id from the vehicle
                tenant_id = vehicle.tenant_id
                
                service_history = ServiceHistory.objects.create(
                    vehicle=vehicle,
                    service_center=service_center,
                    service_date=service_date,
                    odometer=odometer,
                    description=description,
                    work_order_number=work_order_number,
                    services_performed=services_performed,
                    parts_used=parts_used,
                    technician=technician,
                    total_cost=total_cost,
                    notes="تمت الصيانة بنجاح وتسليم السيارة للعميل",
                    tenant_id=tenant_id
                )
                
                histories_created += 1
                if histories_created % 10 == 0:
                    print(f"Created {histories_created} history records...")
            except Exception as e:
                print(f"Error creating service history record {i+1}: {e}")
        
        print(f"Successfully created {histories_created} vehicle service history records")
    
    def run(self):
        print("Starting Vehicle History data generation...")
        with transaction.atomic():
            self.generate_vehicle_history()
        print("\nVehicle History data generation complete!")

if __name__ == "__main__":
    generator = VehicleHistoryGenerator()
    generator.run() 
 