import os
import sys
import django
import random
from datetime import timedelta
import uuid

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

# Now import Django models after setup
from django.db import transaction
from django.utils import timezone
from django.contrib.auth.models import User

print("Imports completed, starting data generation...")

# Try importing from proper models
try:
    # First try importing from website models
    from website.models import (
        VehicleHistory, Franchise, UserActivity, StockTransaction, WarehouseSparepartMapping,
        SparePartBatch, ServiceCenterManager, UserNotification, StandardOperation,
        OperationType, VehicleType, SparePartCategory, ServiceCenter, SparePartVehicleModel,
        Vehicle, Warehouse, Item, SparePartCategory
    )
    print("Successfully imported website models")
except ImportError as e:
    print(f"Error importing website models: {e}")
    
    # Fall back to the other app structure
    try:
        # Import models from separate apps
        from inventory.models import Item
        from setup.models import Customer, ServiceCenter, Vehicle, Company
        from warehouse.models import Location, Warehouse, WarehouseStock
        
        # These models might be in different apps, so let's check each
        try:
            from setup.models import VehicleHistory
        except ImportError:
            print("VehicleHistory not in setup.models")
            VehicleHistory = None
            
        try:
            from setup.models import Franchise
        except ImportError:
            print("Franchise not in setup.models")
            Franchise = None
            
        try:
            from website.models import UserActivity
        except ImportError:
            print("UserActivity not in website.models")
            UserActivity = None
            
        try:
            from warehouse.models import StockTransaction
        except ImportError:
            print("StockTransaction not in warehouse.models")
            StockTransaction = None
            
        try:
            from warehouse.models import WarehouseSparepartMapping
        except ImportError:
            print("WarehouseSparepartMapping not in warehouse.models")
            WarehouseSparepartMapping = None
            
        try:
            from inventory.models import SparePartBatch
        except ImportError:
            print("SparePartBatch not in inventory.models")
            SparePartBatch = None
            
        try:
            from setup.models import ServiceCenterManager
        except ImportError:
            print("ServiceCenterManager not in setup.models")
            ServiceCenterManager = None
            
        try:
            from notifications.models import UserNotification
        except ImportError:
            print("UserNotification not in notifications.models")
            UserNotification = None
            
        try:
            from work_orders.models import StandardOperation, OperationType
        except ImportError:
            print("StandardOperation/OperationType not in work_orders.models")
            StandardOperation = None
            OperationType = None
            
        try:
            from setup.models import VehicleType
        except ImportError:
            print("VehicleType not in setup.models")
            VehicleType = None
            
        try:
            from inventory.models import SparePartVehicleModel
        except ImportError:
            print("SparePartVehicleModel not in inventory.models")
            SparePartVehicleModel = None
    
        print("Imported available models from multiple apps")
    except ImportError as e:
        print(f"Error importing models from other apps: {e}")

# Egyptian specific data
EGYPTIAN_CITIES = [
    "القاهرة", "الإسكندرية", "الجيزة", "شرم الشيخ", "الغردقة", "المنصورة", "طنطا", "أسيوط", "الزقازيق", "الإسماعيلية",
    "بورسعيد", "السويس", "الأقصر", "أسوان", "المنيا", "سوهاج", "بني سويف", "دمياط", "مرسى مطروح", "الفيوم"
]

NOTIFICATION_MESSAGES = [
    "تم إنشاء طلب صيانة جديد",
    "تم تحديث حالة طلب الصيانة",
    "تم استلام قطع غيار جديدة في المخزن",
    "طلب نقل قطع غيار بانتظار الموافقة",
    "تنبيه: مستوى المخزون منخفض",
    "تم تأكيد موعد الصيانة",
    "تم إلغاء موعد الصيانة"
]

USER_ACTIVITIES = [
    "تسجيل دخول",
    "تسجيل خروج",
    "إنشاء طلب صيانة",
    "تحديث بيانات عميل",
    "طباعة فاتورة",
    "تحديث حالة طلب صيانة",
    "إضافة قطع غيار للمخزون",
    "نقل قطع غيار بين المخازن"
]

class MissingDataGenerator:
    def __init__(self):
        self.tenant_id = str(uuid.uuid4())
        print(f"Using tenant ID: {self.tenant_id}")
        
    def generate_franchise_data(self):
        print("Generating franchise data...")
        
        if Franchise is None:
            print("Franchise model not available. Skipping.")
            return
        
        # Create main franchise
        try:
            main_franchise, created = Franchise.objects.get_or_create(
                name="أفتر سيلز للصيانة",
                defaults={
                    "address": "شارع التحرير، القاهرة",
                    "phone": "+20 ************",
                    "email": "<EMAIL>",
                    "website": "www.aftersales-eg.com",
                    "logo": "franchises/aftersales-logo.png",
                    "active": True
                }
            )
            
            # Create additional franchises
            franchises = [
                {
                    "name": "الدلتا للصيانة",
                    "address": "شارع الجلاء، المنصورة",
                    "phone": "+20 ************",
                    "email": "<EMAIL>"
                },
                {
                    "name": "الإسكندرية أوتو سيرفيس",
                    "address": "طريق الكورنيش، الإسكندرية",
                    "phone": "+20 ************",
                    "email": "<EMAIL>"
                },
                {
                    "name": "الصعيد للخدمات الفنية",
                    "address": "شارع أسيوط الرئيسي، أسيوط",
                    "phone": "+20 ************",
                    "email": "<EMAIL>"
                }
            ]
            
            for franchise_data in franchises:
                Franchise.objects.get_or_create(
                    name=franchise_data["name"],
                    defaults={
                        "address": franchise_data["address"],
                        "phone": franchise_data["phone"],
                        "email": franchise_data["email"],
                        "active": True
                    }
                )
                
            print(f"Created {len(franchises) + 1} franchises")
        except Exception as e:
            print(f"Error generating franchise data: {e}")
    
    def generate_service_center_management(self):
        print("Generating service center management data...")
        
        if ServiceCenterManager is None or ServiceCenter is None:
            print("ServiceCenterManager or ServiceCenter model not available. Skipping.")
            return
        
        try:
            # Get service centers and create managers
            service_centers = ServiceCenter.objects.all()
            users = list(User.objects.all())
            
            if not users:
                print("No users found. Creating a demo user...")
                user = User.objects.create_user(
                    username="demo_manager",
                    email="<EMAIL>",
                    password="demo1234"
                )
                users = [user]
            
            managers_created = 0
            for service_center in service_centers:
                # Skip if already has a manager
                if ServiceCenterManager.objects.filter(service_center=service_center).exists():
                    continue
                    
                try:
                    # Get model fields to ensure we're only providing valid fields
                    model_fields = [f.name for f in ServiceCenterManager._meta.fields]
                    
                    # Build data dictionary based on available fields
                    data = {
                        'user': random.choice(users)
                    }
                    
                    if 'service_center' in model_fields:
                        data['service_center'] = service_center
                    
                    if 'name' in model_fields:
                        data['name'] = f"مدير {service_center.name}"
                    
                    if 'phone' in model_fields:
                        data['phone'] = f"+20 10{random.randint(10000000, 99999999)}"
                    
                    if 'email' in model_fields:
                        data['email'] = f"manager_{service_center.id}@aftersales.com"
                    
                    if 'active' in model_fields:
                        data['active'] = True
                    
                    ServiceCenterManager.objects.create(**data)
                    managers_created += 1
                except Exception as e:
                    print(f"Error creating manager for {service_center.name}: {e}")
            
            print(f"Created {managers_created} service center managers")
        except Exception as e:
            print(f"Error in generate_service_center_management: {e}")
    
    def generate_vehicle_history(self):
        print("Generating vehicle history...")
        
        if VehicleHistory is None or Vehicle is None:
            print("VehicleHistory or Vehicle model not available. Skipping.")
            return
        
        try:
            # Get vehicles
            vehicles = Vehicle.objects.all()
            
            if not vehicles:
                print("No vehicles found. Skipping vehicle history generation.")
                return
            
            # Get model fields to ensure we're only providing valid fields
            model_fields = [f.name for f in VehicleHistory._meta.fields]
            
            histories_created = 0
            for vehicle in vehicles:
                # Create 1-3 history records per vehicle
                for i in range(random.randint(1, 3)):
                    event_date = timezone.now() - timedelta(days=random.randint(1, 365))
                    
                    try:
                        # Build data dictionary based on available fields
                        data = {}
                        
                        if 'vehicle' in model_fields:
                            data['vehicle'] = vehicle
                        
                        if 'event_date' in model_fields:
                            data['event_date'] = event_date
                        
                        if 'event_type' in model_fields:
                            data['event_type'] = random.choice(['purchase', 'service', 'accident', 'inspection', 'repair'])
                        
                        if 'description' in model_fields:
                            data['description'] = f"سجل تاريخي {i+1} للمركبة {vehicle.make} {vehicle.model}"
                        
                        if 'odometer' in model_fields:
                            data['odometer'] = random.randint(1000, 100000)
                        
                        if 'cost' in model_fields and random.random() > 0.3:
                            data['cost'] = random.randint(500, 5000)
                        
                        if 'notes' in model_fields and random.random() > 0.5:
                            data['notes'] = f"ملاحظات إضافية لسجل تاريخ المركبة {i+1}"
                        
                        VehicleHistory.objects.create(**data)
                        histories_created += 1
                    except Exception as e:
                        print(f"Error creating vehicle history for {vehicle.id}: {e}")
            
            print(f"Created {histories_created} vehicle history records")
        except Exception as e:
            print(f"Error in generate_vehicle_history: {e}")
    
    def generate_warehouse_mappings(self):
        print("Generating warehouse spare part mappings...")
        
        if WarehouseSparepartMapping is None:
            print("WarehouseSparepartMapping model not available. Skipping.")
            return
            
        try:
            # First check if Warehouse model is available
            try:
                warehouses = Warehouse.objects.all()
                if not warehouses:
                    print("No warehouses found. Skipping warehouse mapping generation.")
                    return
            except:
                print("Warehouse model not accessible. Skipping warehouse mapping generation.")
                return
                
            # Then check if Item/SparePart model is available
            try:
                # Try to get spare parts from Item model
                spare_parts = Item.objects.all()
            except:
                try:
                    # Try SparePart model as fallback
                    from website.models import SparePart
                    spare_parts = SparePart.objects.all()
                except:
                    print("Neither Item nor SparePart model accessible. Skipping warehouse mapping generation.")
                    return
            
            if not spare_parts:
                print("No spare parts found. Skipping warehouse mapping generation.")
                return
            
            # Get model fields to ensure we're only providing valid fields
            model_fields = [f.name for f in WarehouseSparepartMapping._meta.fields]
            
            mappings_created = 0
            # Create mappings for a subset of parts in each warehouse
            for warehouse in warehouses:
                # Select random parts for this warehouse (30-70% of all parts)
                num_parts = min(10, len(spare_parts))  # Limit to 10 parts for performance
                selected_parts = random.sample(list(spare_parts), num_parts)
                
                for part in selected_parts:
                    try:
                        # Create data dictionary based on available fields
                        data = {}
                        
                        if 'warehouse' in model_fields:
                            data['warehouse'] = warehouse
                        
                        if 'spare_part' in model_fields:
                            data['spare_part'] = part
                        
                        if 'shelf' in model_fields:
                            data['shelf'] = f"Shelf-{random.choice('ABCDEF')}{random.randint(1,20)}"
                        
                        if 'bin' in model_fields:
                            data['bin'] = f"Bin-{random.randint(1,100)}"
                        
                        if 'section' in model_fields:
                            data['section'] = random.choice(['A', 'B', 'C', 'D'])
                        
                        # Create warehouse-spare part mapping
                        WarehouseSparepartMapping.objects.create(**data)
                        mappings_created += 1
                    except Exception as e:
                        print(f"Error creating warehouse mapping: {e}")
            
            print(f"Created {mappings_created} warehouse-spare part mappings")
        except Exception as e:
            print(f"Error in generate_warehouse_mappings: {e}")
    
    @transaction.atomic
    def run(self):
        print("Starting missing data generation process...")
        
        # Generate data for empty tables
        self.generate_franchise_data()
        self.generate_service_center_management()
        self.generate_vehicle_history()
        self.generate_warehouse_mappings()
        
        print("Missing data generation complete!")

if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser(description='Generate missing demo data for all tables')
    
    args = parser.parse_args()
    
    # Run the generator
    generator = MissingDataGenerator()
    generator.run() 