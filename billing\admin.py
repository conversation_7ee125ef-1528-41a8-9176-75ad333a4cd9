from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from .models import (
    CustomerPreference, 
    InsuranceCompany, 
    InsurancePolicy, 
    WarrantyType, 
    VehicleWarranty, 
    DiscountType, 
    PaymentMethod, 
    Invoice, 
    InvoiceItem, 
    Payment
)
from .models_rules import (
    PromotionRule,
    RuleCondition,
    RuleEffect,
    RuleLog
)
from .models_classification import (
    CustomerClassification, 
    ClassificationCriteria,
    CustomerClassificationHistory
)


class CustomerPreferenceAdmin(admin.ModelAdmin):
    list_display = ('customer', 'status', 'payment_terms', 'credit_limit', 'default_discount_percentage')
    list_filter = ('status', 'payment_terms', 'send_sms_notifications', 'send_email_notifications')
    search_fields = ('customer__first_name', 'customer__last_name', 'customer__phone', 'customer__email')
    fieldsets = (
        (None, {
            'fields': ('tenant_id', 'customer')
        }),
        (_('Status & Preferences'), {
            'fields': ('status', 'payment_terms', 'credit_limit', 'default_discount_percentage')
        }),
        (_('Notifications'), {
            'fields': ('send_sms_notifications', 'send_email_notifications')
        }),
        (_('Additional Information'), {
            'fields': ('special_instructions', 'attributes')
        }),
    )


class InsuranceCompanyAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'phone', 'email', 'approval_required', 'is_active')
    list_filter = ('is_active', 'approval_required')
    search_fields = ('name', 'code', 'contact_person', 'phone', 'email')
    fieldsets = (
        (None, {
            'fields': ('tenant_id', 'name', 'code', 'is_active')
        }),
        (_('Contact Information'), {
            'fields': ('contact_person', 'phone', 'email', 'address')
        }),
        (_('Contract Details'), {
            'fields': ('contract_number', 'contract_start_date', 'contract_end_date')
        }),
        (_('Processing Details'), {
            'fields': ('approval_required', 'standard_approval_time_hours')
        }),
        (_('Additional Information'), {
            'fields': ('notes', 'attributes')
        }),
    )


class InsurancePolicyAdmin(admin.ModelAdmin):
    list_display = ('policy_number', 'insurance_company', 'vehicle', 'customer', 'policy_type', 'start_date', 'end_date', 'is_active', 'is_expired')
    list_filter = ('policy_type', 'is_active', 'insurance_company')
    search_fields = ('policy_number', 'vehicle__make', 'vehicle__model', 'vehicle__license_plate', 'customer__first_name', 'customer__last_name')
    readonly_fields = ('is_expired',)
    fieldsets = (
        (None, {
            'fields': ('tenant_id', 'insurance_company', 'vehicle', 'customer')
        }),
        (_('Policy Details'), {
            'fields': ('policy_number', 'policy_type', 'start_date', 'end_date', 'is_active', 'is_expired')
        }),
        (_('Coverage'), {
            'fields': ('coverage_amount', 'deductible')
        }),
        (_('Additional Information'), {
            'fields': ('notes', 'attributes')
        }),
    )
    
    def is_expired(self, obj):
        return obj.is_expired
    is_expired.boolean = True
    is_expired.short_description = _('Expired')


class WarrantyTypeAdmin(admin.ModelAdmin):
    list_display = ('name', 'default_duration_months', 'default_mileage_limit', 'labor_covered', 'is_active')
    list_filter = ('is_active', 'labor_covered')
    search_fields = ('name', 'description', 'parts_covered')
    fieldsets = (
        (None, {
            'fields': ('tenant_id', 'name', 'description', 'is_active')
        }),
        (_('Coverage'), {
            'fields': ('parts_covered', 'labor_covered')
        }),
        (_('Duration'), {
            'fields': ('default_duration_months', 'default_mileage_limit')
        }),
        (_('Additional Information'), {
            'fields': ('attributes',)
        }),
    )


class VehicleWarrantyAdmin(admin.ModelAdmin):
    list_display = ('vehicle', 'warranty_type', 'provider', 'start_date', 'end_date', 'mileage_limit', 'is_active')
    list_filter = ('warranty_type', 'provider', 'is_active')
    search_fields = ('warranty_number', 'vehicle__make', 'vehicle__model', 'vehicle__license_plate', 'provider_name')
    readonly_fields = ('is_expired_by_date', 'is_expired_by_mileage')
    fieldsets = (
        (None, {
            'fields': ('tenant_id', 'vehicle', 'warranty_type', 'is_active')
        }),
        (_('Warranty Details'), {
            'fields': ('warranty_number', 'provider', 'provider_name')
        }),
        (_('Coverage Period'), {
            'fields': ('start_date', 'end_date', 'mileage_limit', 'is_expired_by_date', 'is_expired_by_mileage')
        }),
        (_('Additional Information'), {
            'fields': ('notes', 'attributes')
        }),
    )
    
    def is_expired_by_date(self, obj):
        return obj.is_expired_by_date
    is_expired_by_date.boolean = True
    is_expired_by_date.short_description = _('Expired (Date)')
    
    def is_expired_by_mileage(self, obj):
        return obj.is_expired_by_mileage
    is_expired_by_mileage.boolean = True
    is_expired_by_mileage.short_description = _('Expired (Mileage)')


class DiscountTypeAdmin(admin.ModelAdmin):
    list_display = ('name', 'discount_method', 'percentage', 'fixed_amount', 'valid_from', 'valid_to', 'is_active', 'is_valid')
    list_filter = ('discount_method', 'is_active', 'apply_to_parts', 'apply_to_labor')
    search_fields = ('name', 'description')
    readonly_fields = ('is_valid',)
    fieldsets = (
        (None, {
            'fields': ('tenant_id', 'name', 'description', 'is_active')
        }),
        (_('Discount Details'), {
            'fields': ('discount_method', 'percentage', 'fixed_amount')
        }),
        (_('Validity'), {
            'fields': ('valid_from', 'valid_to', 'is_valid')
        }),
        (_('Restrictions'), {
            'fields': ('min_order_amount', 'max_discount_amount', 'apply_to_parts', 'apply_to_labor')
        }),
        (_('Additional Information'), {
            'fields': ('attributes',)
        }),
    )
    
    def is_valid(self, obj):
        return obj.is_valid
    is_valid.boolean = True
    is_valid.short_description = _('Currently Valid')


class PaymentMethodAdmin(admin.ModelAdmin):
    list_display = ('name', 'payment_type', 'processing_fee_percentage', 'processing_fee_fixed', 'requires_approval', 'is_active')
    list_filter = ('payment_type', 'is_active', 'requires_approval')
    search_fields = ('name', 'description')
    fieldsets = (
        (None, {
            'fields': ('tenant_id', 'name', 'payment_type', 'description', 'is_active')
        }),
        (_('Processing Details'), {
            'fields': ('processing_fee_percentage', 'processing_fee_fixed', 'requires_approval')
        }),
        (_('Additional Information'), {
            'fields': ('attributes',)
        }),
    )


class InvoiceItemInline(admin.TabularInline):
    model = InvoiceItem
    extra = 1
    fields = ('item_type', 'description', 'quantity', 'unit_price', 'discount_percentage', 'tax_percentage', 'line_total')
    readonly_fields = ('line_total',)


class PaymentInline(admin.TabularInline):
    model = Payment
    extra = 0
    fields = ('payment_method', 'amount', 'payment_date', 'status', 'reference_number')
    readonly_fields = ('payment_date',)


class InvoiceAdmin(admin.ModelAdmin):
    list_display = ('invoice_number', 'customer', 'work_order', 'invoice_date', 'due_date', 'total_amount', 'amount_paid', 'amount_due', 'status')
    list_filter = ('status', 'invoice_date', 'service_center')
    search_fields = ('invoice_number', 'customer__first_name', 'customer__last_name', 'work_order__work_order_number')
    readonly_fields = ('subtotal', 'tax_amount', 'total_amount', 'amount_paid', 'amount_due', 'is_overdue')
    inlines = [InvoiceItemInline, PaymentInline]
    fieldsets = (
        (None, {
            'fields': ('tenant_id', 'work_order', 'customer', 'service_center')
        }),
        (_('Invoice Details'), {
            'fields': ('invoice_number', 'invoice_date', 'due_date', 'status', 'is_overdue')
        }),
        (_('Insurance & Warranty'), {
            'fields': ('insurance_policy', 'vehicle_warranty')
        }),
        (_('Discount'), {
            'fields': ('discount_type', 'discount_amount')
        }),
        (_('Amounts'), {
            'fields': ('subtotal', 'tax_method', 'tax_percentage', 'tax_amount', 'total_amount', 'amount_paid', 'amount_due')
        }),
        (_('Additional Information'), {
            'fields': ('notes', 'terms_and_conditions', 'attributes')
        }),
    )
    
    def is_overdue(self, obj):
        return obj.is_overdue
    is_overdue.boolean = True
    is_overdue.short_description = _('Overdue')


class PaymentAdmin(admin.ModelAdmin):
    list_display = ('invoice', 'customer', 'payment_method', 'amount', 'payment_date', 'status')
    list_filter = ('status', 'payment_method', 'payment_date')
    search_fields = ('invoice__invoice_number', 'customer__first_name', 'customer__last_name', 'reference_number', 'transaction_id')
    readonly_fields = ('payment_date',)
    fieldsets = (
        (None, {
            'fields': ('tenant_id', 'invoice', 'customer', 'payment_method')
        }),
        (_('Payment Details'), {
            'fields': ('amount', 'payment_date', 'status')
        }),
        (_('Reference Information'), {
            'fields': ('reference_number', 'transaction_id')
        }),
        (_('Bank/Card Information'), {
            'fields': ('card_last_four', 'card_type', 'bank_name', 'check_number')
        }),
        (_('Insurance Payment'), {
            'fields': ('insurance_claim_number', 'insurance_payment_date')
        }),
        (_('Additional Information'), {
            'fields': ('notes', 'attributes')
        }),
    )


# Rule Engine Admin Classes
class RuleConditionInline(admin.TabularInline):
    model = RuleCondition
    extra = 1
    fields = ('condition_type', 'operator', 'value', 'value2', 'group_id', 'is_or_condition')


class RuleEffectInline(admin.TabularInline):
    model = RuleEffect
    extra = 1
    fields = ('effect_type', 'effect_value', 'apply_to', 'max_items', 'max_amount', 'description')


class PromotionRuleAdmin(admin.ModelAdmin):
    list_display = ('name', 'rule_type', 'priority', 'start_date', 'end_date', 'is_active', 'is_valid', 'requires_approval')
    list_filter = ('rule_type', 'is_active', 'requires_approval', 'approval_level')
    search_fields = ('name', 'description')
    readonly_fields = ('is_valid', 'current_usages')
    inlines = [RuleConditionInline, RuleEffectInline]
    fieldsets = (
        (None, {
            'fields': ('tenant_id', 'name', 'description', 'rule_type', 'is_active')
        }),
        (_('Priority & Validity'), {
            'fields': ('priority', 'start_date', 'end_date', 'is_valid')
        }),
        (_('Usage Limits'), {
            'fields': ('max_usages', 'current_usages')
        }),
        (_('Approval Requirements'), {
            'fields': ('requires_approval', 'approval_level')
        }),
        (_('Additional Information'), {
            'fields': ('created_by', 'notes', 'attributes')
        }),
    )
    
    def is_valid(self, obj):
        return obj.is_valid
    is_valid.boolean = True
    is_valid.short_description = _('Currently Valid')


class RuleLogAdmin(admin.ModelAdmin):
    list_display = ('rule', 'customer', 'invoice', 'effect_type', 'amount', 'created_at', 'required_approval', 'approved')
    list_filter = ('effect_type', 'required_approval', 'approved', 'created_at')
    search_fields = ('rule__name', 'customer__first_name', 'customer__last_name', 'invoice__invoice_number')
    readonly_fields = ('rule', 'invoice', 'work_order', 'customer', 'vehicle', 'effect_type', 'amount', 
                      'required_approval', 'approved', 'approved_by', 'approval_date', 'created_at')
    fieldsets = (
        (None, {
            'fields': ('tenant_id', 'rule', 'created_at')
        }),
        (_('Applied To'), {
            'fields': ('invoice', 'work_order', 'customer', 'vehicle')
        }),
        (_('Effect Details'), {
            'fields': ('effect_type', 'amount', 'applied_items')
        }),
        (_('Approval Details'), {
            'fields': ('required_approval', 'approved', 'approved_by', 'approval_date', 'approval_notes')
        }),
        (_('Rule Data'), {
            'fields': ('rule_data',)
        }),
    )


# Register all models
admin.site.register(CustomerPreference, CustomerPreferenceAdmin)
admin.site.register(InsuranceCompany, InsuranceCompanyAdmin)
admin.site.register(InsurancePolicy, InsurancePolicyAdmin)
admin.site.register(WarrantyType, WarrantyTypeAdmin)
admin.site.register(VehicleWarranty, VehicleWarrantyAdmin)
admin.site.register(DiscountType, DiscountTypeAdmin)
admin.site.register(PaymentMethod, PaymentMethodAdmin)
admin.site.register(Invoice, InvoiceAdmin)
admin.site.register(Payment, PaymentAdmin)

# Register Rule Engine models
admin.site.register(PromotionRule, PromotionRuleAdmin)
admin.site.register(RuleLog, RuleLogAdmin)

# Register Customer Classification models
class ClassificationCriteriaInline(admin.TabularInline):
    model = ClassificationCriteria
    extra = 1
    fields = ('criteria_type', 'operator', 'value', 'value2', 'period', 'weight', 'is_required')


@admin.register(CustomerClassification)
class CustomerClassificationAdmin(admin.ModelAdmin):
    list_display = ('name', 'level', 'discount_percentage', 'is_active')
    list_filter = ('is_active',)
    search_fields = ('name', 'description')
    ordering = ('-level',)
    inlines = [ClassificationCriteriaInline]
    fieldsets = (
        (None, {
            'fields': ('name', 'description', 'level', 'is_active')
        }),
        (_('Benefits'), {
            'fields': ('discount_percentage', 'service_priority', 
                      'credit_limit_multiplier', 'extended_payment_days')
        }),
        (_('Appearance'), {
            'fields': ('icon', 'color')
        }),
        (_('Other Settings'), {
            'fields': ('notes', 'attributes')
        }),
    )
    

@admin.register(ClassificationCriteria)
class ClassificationCriteriaAdmin(admin.ModelAdmin):
    list_display = ('classification', 'criteria_type', 'operator', 'value', 'period', 'is_required')
    list_filter = ('classification', 'criteria_type', 'operator', 'period', 'is_required')
    search_fields = ('description',)
    ordering = ('classification__level', 'criteria_type')
    fieldsets = (
        (None, {
            'fields': ('classification', 'criteria_type', 'description')
        }),
        (_('Criteria Settings'), {
            'fields': ('operator', 'value', 'value2', 'period', 'weight', 'is_required')
        }),
        (_('Custom Options'), {
            'fields': ('custom_evaluation', 'notes'),
            'classes': ('collapse',)
        }),
    )


@admin.register(CustomerClassificationHistory)
class CustomerClassificationHistoryAdmin(admin.ModelAdmin):
    list_display = ('customer', 'previous_classification', 'new_classification', 
                   'change_date', 'automatic', 'notification_sent')
    list_filter = ('automatic', 'notification_sent', 'change_date', 'new_classification')
    search_fields = ('customer__first_name', 'customer__last_name', 'reason')
    ordering = ('-change_date',)
    readonly_fields = ('customer', 'previous_classification', 'new_classification', 
                      'change_date', 'automatic', 'changed_by', 'evaluation_data',
                      'notification_sent', 'notification_date')
    fieldsets = (
        (None, {
            'fields': ('customer', 'previous_classification', 'new_classification', 'change_date')
        }),
        (_('Change Details'), {
            'fields': ('automatic', 'changed_by', 'reason')
        }),
        (_('Notification'), {
            'fields': ('notification_sent', 'notification_date')
        }),
        (_('Evaluation Data'), {
            'fields': ('evaluation_data',),
            'classes': ('collapse',)
        }),
    )
    
    def has_add_permission(self, request):
        return False
    
    def has_change_permission(self, request, obj=None):
        # Only allow changing the notification fields
        return False 