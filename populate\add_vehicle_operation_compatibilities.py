import os
import sys
import django
import random
from datetime import datetime

# Add the parent directory to sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

# Import necessary models
from django.db import transaction
from inventory.models import OperationCompatibility, VehicleOperationCompatibility, Item
from setup.models import VehicleMake, VehicleModel
from work_orders.models import WorkOrderType, MaintenanceSchedule

class VehicleOperationCompatibilityGenerator:
    """Generate VehicleOperationCompatibility data for the system."""
    
    def __init__(self):
        # Use tenant_id from existing records
        try:
            # Try to get tenant_id from an existing record
            op_compat = OperationCompatibility.objects.first()
            if op_compat:
                self.tenant_id = op_compat.tenant_id
            else:
                vehicle_make = VehicleMake.objects.first()
                if vehicle_make:
                    self.tenant_id = vehicle_make.tenant_id
                else:
                    self.tenant_id = "default_tenant"
        except Exception as e:
            print(f"Error getting tenant_id: {e}")
            self.tenant_id = "default_tenant"
            
        print(f"Using tenant ID: {self.tenant_id}")
        
    @transaction.atomic
    def generate_vehicle_operation_compatibilities(self):
        """Generate VehicleOperationCompatibility data."""
        print("Generating VehicleOperationCompatibility data...")
        
        # Get all operation compatibilities
        operation_compatibilities = list(OperationCompatibility.objects.all())
        if not operation_compatibilities:
            print("No operation compatibilities found. Please generate operation compatibilities first.")
            return
            
        # Get all makes and models
        vehicle_makes = list(VehicleMake.objects.all())
        if not vehicle_makes:
            print("No vehicle makes found. Please generate vehicle makes first.")
            return
            
        vehicle_models_map = {}
        for make in vehicle_makes:
            vehicle_models_map[make.id] = list(VehicleModel.objects.filter(make=make))
            
        # Clear existing records to avoid duplicates
        VehicleOperationCompatibility.objects.all().delete()
        print("Cleared existing VehicleOperationCompatibility records.")
        
        # Track statistics
        created_count = 0
        current_year = datetime.now().year
        
        # Loop through each operation compatibility
        for op_compat in operation_compatibilities:
            # Randomly determine how many vehicle makes will be compatible with this operation
            num_makes = random.randint(1, min(5, len(vehicle_makes)))
            selected_makes = random.sample(vehicle_makes, num_makes)
            
            for make in selected_makes:
                # Get models for this make
                models = vehicle_models_map.get(make.id, [])
                
                # Decide if we want to specify models or make the operation compatible with all models
                if models and random.choice([True, False]):
                    # Select specific models
                    num_models = random.randint(1, min(3, len(models)))
                    selected_models = random.sample(models, num_models)
                    
                    for model in selected_models:
                        # Generate year range
                        year_from = random.randint(current_year - 15, current_year - 5)
                        
                        # Decide if we want a year_to or make it compatible with all future years
                        if random.choice([True, False]):
                            year_to = random.randint(year_from, current_year)
                        else:
                            year_to = None
                            
                        # Create the compatibility record
                        VehicleOperationCompatibility.objects.create(
                            tenant_id=self.tenant_id,
                            operation_compatibility=op_compat,
                            vehicle_make=make,
                            vehicle_model=model,
                            year_from=year_from,
                            year_to=year_to
                        )
                        created_count += 1
                        
                        if created_count % 50 == 0:
                            print(f"Created {created_count} vehicle operation compatibilities...")
                else:
                    # Make the operation compatible with all models of this make
                    # Generate year range
                    year_from = random.randint(current_year - 15, current_year - 5)
                    
                    # Decide if we want a year_to or make it compatible with all future years
                    if random.choice([True, False]):
                        year_to = random.randint(year_from, current_year)
                    else:
                        year_to = None
                        
                    # Create the compatibility record
                    VehicleOperationCompatibility.objects.create(
                        tenant_id=self.tenant_id,
                        operation_compatibility=op_compat,
                        vehicle_make=make,
                        vehicle_model=None,  # No specific model, applies to all models
                        year_from=year_from,
                        year_to=year_to
                    )
                    created_count += 1
                    
                    if created_count % 50 == 0:
                        print(f"Created {created_count} vehicle operation compatibilities...")
        
        print(f"Created {created_count} vehicle operation compatibilities")
    
    def migrate_legacy_data(self):
        """Check and migrate any legacy vehicle data in OperationCompatibility."""
        print("\nChecking for legacy vehicle data to migrate...")
        
        # Find OperationCompatibility records with legacy vehicle data
        legacy_records = OperationCompatibility.objects.filter(
            vehicle_make__isnull=False  # Records that have vehicle_make are using legacy fields
        )
        
        if not legacy_records:
            print("No legacy vehicle data found that needs migration.")
            return
            
        print(f"Found {legacy_records.count()} records with legacy vehicle data.")
        
        # Track migration statistics
        migrated_count = 0
        errors = 0
        
        # Loop through legacy records and migrate them
        for record in legacy_records:
            try:
                # Call the model's migration method
                if record.migrate_legacy_vehicle_data():
                    migrated_count += 1
                    
                    if migrated_count % 10 == 0:
                        print(f"Migrated {migrated_count} records...")
            except Exception as e:
                print(f"Error migrating record {record.id}: {e}")
                errors += 1
        
        print(f"Migrated {migrated_count} records with legacy vehicle data.")
        if errors > 0:
            print(f"Encountered {errors} errors during migration.")
    
    def run(self):
        """Run all data generation steps."""
        self.generate_vehicle_operation_compatibilities()
        self.migrate_legacy_data()
        print("\nCompleted VehicleOperationCompatibility data generation")

def create_demo_vehicle_operation_compatibilities():
    """إنشاء بيانات عرض لجدول Vehicle Operation Compatibilities"""
    print("بدء إنشاء بيانات العرض لـ Vehicle Operation Compatibilities...")

    # الحصول على tenant_id من السجلات الموجودة
    try:
        # محاولة الحصول على tenant_id من سجل موجود
        op_compat = OperationCompatibility.objects.first()
        if op_compat:
            tenant_id = op_compat.tenant_id
        else:
            vehicle_make = VehicleMake.objects.first()
            if vehicle_make:
                tenant_id = vehicle_make.tenant_id
            else:
                tenant_id = "default_tenant"
                print("تحذير: لم يتم العثور على tenant_id. استخدام القيمة الافتراضية.")
    except Exception as e:
        print(f"خطأ في الحصول على tenant_id: {e}")
        tenant_id = "default_tenant"
        
    print(f"استخدام tenant_id: {tenant_id}")
    
    # الحصول على عناصر المخزون (Item) المطلوبة لإنشاء التوافقيات
    items = Item.objects.all()
    if not items.exists():
        print("خطأ: لا توجد عناصر مخزون (Items). يرجى إضافة عناصر المخزون أولاً.")
        return
    else:
        print(f"تم العثور على {items.count()} عنصر مخزون.")

    # التأكد من وجود بيانات أساسية مطلوبة
    vehicle_makes = VehicleMake.objects.all()
    if not vehicle_makes.exists():
        print("خطأ: لا توجد سجلات في جدول VehicleMake. يرجى إضافة ماركات المركبات أولاً.")
        return

    operation_types = WorkOrderType.objects.all()
    if not operation_types.exists():
        print("خطأ: لا توجد سجلات في جدول WorkOrderType. يرجى إضافة أنواع العمليات أولاً.")
        return

    maintenance_schedules = MaintenanceSchedule.objects.all()
    if not maintenance_schedules.exists():
        print("خطأ: لا توجد سجلات في جدول MaintenanceSchedule. يرجى إضافة جداول الصيانة أولاً.")
        return

    # قائمة بأوصاف عمليات مختلفة بالعربية
    operation_descriptions = [
        "تغيير زيت المحرك",
        "تغيير فلتر الزيت",
        "تغيير فلتر الهواء",
        "فحص الفرامل",
        "تغيير سائل الفرامل",
        "تغيير فلتر الوقود",
        "تغيير شمعات الإشعال (البوجيهات)",
        "ضبط محاذاة العجلات",
        "فحص وضبط الإطارات",
        "تغيير حزام التوقيت",
        "تغيير سائل التبريد",
        "فحص نظام التعليق",
        "فحص البطارية",
        "تغيير بطارية",
        "صيانة نظام تكييف الهواء",
        "فحص نظام العادم",
        "تغيير زيت ناقل الحركة",
        "فحص عام للمركبة",
        "فحص كمبيوتر السيارة",
        "تشخيص أعطال كهربائية",
        "إصلاح نظام الفرامل",
        "إصلاح نظام التعليق",
        "إصلاح نظام التوجيه",
        "إصلاح نظام التبريد",
        "إصلاح المحرك",
        "إصلاح ناقل الحركة",
        "تغيير مضخة الماء",
        "تغيير مضخة الوقود",
        "تغيير دينامو الشحن",
        "تغيير مارش التشغيل"
    ]

    # إنشاء توافقيات جديدة للعمليات
    compatibilities_created = 0
    
    with transaction.atomic():
        # إنشاء توافقيات لجميع ماركات وموديلات المركبات
        for make in vehicle_makes:
            # الحصول على جميع الموديلات لهذه الماركة
            vehicle_models = VehicleModel.objects.filter(make=make)
            
            # إذا لم تكن هناك موديلات، قم بإنشاء توافقيات للماركة فقط
            if not vehicle_models.exists():
                # إنشاء توافقيات للصيانة المجدولة
                for schedule in random.sample(list(maintenance_schedules), min(3, maintenance_schedules.count())):
                    # إنشاء سجلات توافق العمليات للجدول
                    for op_type in random.sample(list(operation_types), min(5, operation_types.count())):
                        # اختيار عنصر عشوائي من المخزون
                        item = random.choice(list(items))
                        
                        # التحقق مما إذا كان السجل موجود بالفعل
                        op_compat, created = OperationCompatibility.objects.get_or_create(
                            operation_type=op_type,
                            maintenance_schedule=schedule,
                            item=item,
                            defaults={
                                'tenant_id': tenant_id,
                                'operation_description': random.choice(operation_descriptions),
                                'duration_minutes': random.randint(30, 180),
                                'is_required': random.choice([True, False]),
                                'is_common': True
                            }
                        )
                        
                        if not created:
                            # تحديث وصف العملية إذا كان السجل موجودًا بالفعل
                            op_compat.operation_description = random.choice(operation_descriptions)
                            op_compat.save()
                        
                        # إنشاء توافق العملية للمركبة
                        vehicle_op_compat, v_created = VehicleOperationCompatibility.objects.get_or_create(
                            operation_compatibility=op_compat,
                            vehicle_make=make,
                            defaults={
                                'tenant_id': tenant_id,
                                'year_from': 2010,
                                'year_to': 2025
                            }
                        )
                        
                        if v_created:
                            compatibilities_created += 1
                
                # إنشاء توافقيات للعمليات العادية (غير المجدولة)
                for op_type in random.sample(list(operation_types), min(8, operation_types.count())):
                    # اختيار عنصر عشوائي من المخزون
                    item = random.choice(list(items))
                    
                    op_compat, created = OperationCompatibility.objects.get_or_create(
                        operation_type=op_type,
                        maintenance_schedule=None,
                        item=item,
                        defaults={
                            'tenant_id': tenant_id,
                            'operation_description': random.choice(operation_descriptions),
                            'duration_minutes': random.randint(15, 240),
                            'is_required': False,
                            'is_common': True
                        }
                    )
                    
                    if not created:
                        op_compat.operation_description = random.choice(operation_descriptions)
                        op_compat.save()
                    
                    vehicle_op_compat, v_created = VehicleOperationCompatibility.objects.get_or_create(
                        operation_compatibility=op_compat,
                        vehicle_make=make,
                        defaults={
                            'tenant_id': tenant_id,
                            'year_from': 2010,
                            'year_to': 2025
                        }
                    )
                    
                    if v_created:
                        compatibilities_created += 1
            else:
                # إنشاء توافقيات لكل موديل
                for model in vehicle_models:
                    # إنشاء توافقيات للصيانة المجدولة
                    for schedule in random.sample(list(maintenance_schedules), min(3, maintenance_schedules.count())):
                        for op_type in random.sample(list(operation_types), min(5, operation_types.count())):
                            # اختيار عنصر عشوائي من المخزون
                            item = random.choice(list(items))
                            
                            op_compat, created = OperationCompatibility.objects.get_or_create(
                                operation_type=op_type,
                                maintenance_schedule=schedule,
                                item=item,
                                defaults={
                                    'tenant_id': tenant_id,
                                    'operation_description': random.choice(operation_descriptions),
                                    'duration_minutes': random.randint(30, 180),
                                    'is_required': random.choice([True, False]),
                                    'is_common': True
                                }
                            )
                            
                            if not created:
                                op_compat.operation_description = random.choice(operation_descriptions)
                                op_compat.save()
                            
                            vehicle_op_compat, v_created = VehicleOperationCompatibility.objects.get_or_create(
                                operation_compatibility=op_compat,
                                vehicle_make=make,
                                vehicle_model=model,
                                defaults={
                                    'tenant_id': tenant_id,
                                    'year_from': model.year_introduced or 2010,
                                    'year_to': model.year_discontinued or 2025
                                }
                            )
                            
                            if v_created:
                                compatibilities_created += 1
                    
                    # إنشاء توافقيات للعمليات العادية (غير المجدولة)
                    for op_type in random.sample(list(operation_types), min(8, operation_types.count())):
                        # اختيار عنصر عشوائي من المخزون
                        item = random.choice(list(items))
                        
                        op_compat, created = OperationCompatibility.objects.get_or_create(
                            operation_type=op_type,
                            maintenance_schedule=None,
                            item=item,
                            defaults={
                                'tenant_id': tenant_id,
                                'operation_description': random.choice(operation_descriptions),
                                'duration_minutes': random.randint(15, 240),
                                'is_required': False,
                                'is_common': True
                            }
                        )
                        
                        if not created:
                            op_compat.operation_description = random.choice(operation_descriptions)
                            op_compat.save()
                        
                        vehicle_op_compat, v_created = VehicleOperationCompatibility.objects.get_or_create(
                            operation_compatibility=op_compat,
                            vehicle_make=make,
                            vehicle_model=model,
                            defaults={
                                'tenant_id': tenant_id,
                                'year_from': model.year_introduced or 2010,
                                'year_to': model.year_discontinued or 2025
                            }
                        )
                        
                        if v_created:
                            compatibilities_created += 1

    # إضافة توافقيات خاصة لسيارة Hyundai Tucson (2024) المذكورة في بيانات المستخدم
    hyundai = VehicleMake.objects.filter(name__icontains="Hyundai").first()
    tucson = None
    
    if hyundai:
        tucson = VehicleModel.objects.filter(make=hyundai, name__icontains="Tucson").first()
    
    if hyundai and tucson:
        print(f"إضافة توافقيات خاصة لسيارة {hyundai.name} {tucson.name} (2024)...")
        
        # قائمة بعمليات الصيانة الخاصة بسيارة هيونداي توسان
        tucson_operations = [
            "تغيير زيت المحرك والفلتر - توسان",
            "فحص وتنظيف فلتر الهواء - توسان",
            "فحص سائل التبريد وإضافة مانع تجمد - توسان",
            "فحص بطارية وتنظيف أقطاب - توسان", 
            "فحص نظام التكييف - توسان",
            "فحص الفرامل الأمامية والخلفية - توسان",
            "تغيير سائل الفرامل - توسان", 
            "فحص نظام التعليق والتوجيه - توسان",
            "تغيير فلتر الوقود - توسان",
            "فحص وضبط محاذاة العجلات - توسان",
            "فحص حالة الإطارات وضغط الهواء - توسان",
            "فحص مانع الاهتزاز - توسان",
            "فحص نظام العادم - توسان",
            "تغيير شمعات الإشعال - توسان"
        ]
        
        # إضافة عمليات صيانة خاصة بالتوسان
        for op_description in tucson_operations:
            for op_type in random.sample(list(operation_types), 1):  # اختيار نوع عملية عشوائي لكل عملية
                # اختيار عنصر عشوائي من المخزون
                item = random.choice(list(items))
                
                op_compat = OperationCompatibility.objects.create(
                    tenant_id=tenant_id,
                    operation_type=op_type,
                    item=item,
                    maintenance_schedule=random.choice(list(maintenance_schedules) + [None, None]),  # احتمال أكبر لعدم ارتباط بجدول صيانة
                    operation_description=op_description,
                    duration_minutes=random.randint(30, 180),
                    is_required=random.choice([True, False]),
                    is_common=True
                )
                
                # إضافة توافق المركبة
                VehicleOperationCompatibility.objects.create(
                    tenant_id=tenant_id,
                    operation_compatibility=op_compat,
                    vehicle_make=hyundai,
                    vehicle_model=tucson,
                    year_from=2020,
                    year_to=2024
                )
                
                compatibilities_created += 1

    print(f"تم إنشاء {compatibilities_created} سجل توافق جديد للعمليات")
    return compatibilities_created

def main():
    """Main entry point."""
    # Use the legacy generator or the new demo data generator
    use_demo_data = True  # Set to True to use the new demo data generator
    
    if use_demo_data:
        create_demo_vehicle_operation_compatibilities()
    else:
        generator = VehicleOperationCompatibilityGenerator()
        generator.run()

if __name__ == "__main__":
    main()