from feature_flags.models import ModuleFlag, TenantModuleFlag, FeatureFlag, TenantFeatureFlag


def is_module_active(module_name, tenant_id=None):
    """
    Check if a module is active for the system or a specific tenant.
    
    Args:
        module_name (str): The name of the module to check
        tenant_id (UUID, optional): The tenant ID to check for. If None, check system-wide setting.
        
    Returns:
        bool: True if the module is active, False otherwise
    """
    try:
        module_flag = ModuleFlag.objects.get(name=module_name)
        
        if not tenant_id:
            return module_flag.is_active
            
        # Check for tenant override
        tenant_flag = TenantModuleFlag.objects.filter(
            module_flag=module_flag,
            tenant_id=tenant_id
        ).first()
        
        if tenant_flag:
            return tenant_flag.is_active
            
        # Fall back to system default
        return module_flag.is_active
    except ModuleFlag.DoesNotExist:
        # If the flag doesn't exist, default to False
        return False


def is_feature_active(feature_name, tenant_id=None):
    """
    Check if a feature is active for the system or a specific tenant.
    Also checks if the parent module is active.
    
    Args:
        feature_name (str): The name of the feature to check
        tenant_id (UUID, optional): The tenant ID to check for. If None, check system-wide setting.
        
    Returns:
        bool: True if the feature is active, False otherwise
    """
    try:
        feature_flag = FeatureFlag.objects.get(name=feature_name)
        
        # First check if the parent module is active
        if not is_module_active(feature_flag.module.name, tenant_id):
            return False
            
        if not tenant_id:
            return feature_flag.is_active
            
        # Check for tenant override
        tenant_flag = TenantFeatureFlag.objects.filter(
            feature_flag=feature_flag,
            tenant_id=tenant_id
        ).first()
        
        if tenant_flag:
            return tenant_flag.is_active
            
        # Fall back to system default
        return feature_flag.is_active
    except FeatureFlag.DoesNotExist:
        # If the flag doesn't exist, default to False
        return False 