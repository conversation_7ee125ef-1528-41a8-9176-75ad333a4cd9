"""
Utility functions for user roles app.
"""
import logging
from django.db.models import QuerySet, Q

logger = logging.getLogger(__name__)

def get_primary_role(user):
    """
    Get the primary role for a user.
    
    Args:
        user: User object
    
    Returns:
        UserRole object or None if user has no roles
    """
    if not user or not hasattr(user, 'user_roles'):
        return None
        
    # Get active user roles
    user_roles = user.user_roles.filter(is_active=True)
    if not user_roles.exists():
        return None
        
    # First try to get the role marked as primary
    primary_role = next((r for r in user_roles if r.is_primary), None)
    
    # If no primary role is found, use the first active role
    if not primary_role:
        primary_role = user_roles.first()
        
    return primary_role

def apply_role_based_filters(queryset, user, entity_field_path=None, use_q_objects=False):
    """
    Apply role-based filters to a queryset.
    
    Args:
        queryset: Base queryset to filter
        user: User object to get roles from
        entity_field_path: Field path to filter on (e.g., 'service_center', 'service_center__company')
        use_q_objects: Whether to use Q objects (useful for OR conditions) instead of direct filtering
        
    Returns:
        Filtered queryset
    """
    # Superusers see everything
    if user.is_superuser:
        return queryset
        
    # Get the primary role
    primary_role = get_primary_role(user)
    if not primary_role:
        logger.warning(f"User {user.username} has no active roles")
        return queryset.none()
        
    # If no entity field path is provided, return unfiltered queryset
    if not entity_field_path:
        return queryset
        
    # Create filters based on role scope
    filters = Q()
    
    # Service Center scope
    if primary_role.service_center:
        if entity_field_path == 'service_center':
            filters |= Q(service_center_id=primary_role.service_center.id)
        else:
            filters |= Q(**{f"{entity_field_path}_id": primary_role.service_center.id})
    
    # Company scope
    elif primary_role.company:
        if 'company' in entity_field_path:
            # Direct company field
            field_name = entity_field_path.split('__')[-1]
            filters |= Q(**{f"{field_name}_id": primary_role.company.id})
        elif 'service_center' in entity_field_path:
            # Filter service centers in this company
            if entity_field_path == 'service_center':
                filters |= Q(service_center__company_id=primary_role.company.id)
            else:
                filters |= Q(**{f"{entity_field_path}__company_id": primary_role.company.id})
    
    # Franchise scope
    elif primary_role.franchise:
        if 'franchise' in entity_field_path:
            # Direct franchise field
            field_name = entity_field_path.split('__')[-1]
            filters |= Q(**{f"{field_name}_id": primary_role.franchise.id})
        elif 'company' in entity_field_path:
            # Filter companies in this franchise
            if entity_field_path == 'company':
                filters |= Q(company__franchise_id=primary_role.franchise.id)
            else:
                filters |= Q(**{f"{entity_field_path}__franchise_id": primary_role.franchise.id})
        elif 'service_center' in entity_field_path:
            # Filter service centers in companies in this franchise
            if entity_field_path == 'service_center':
                filters |= Q(service_center__company__franchise_id=primary_role.franchise.id)
            else:
                filters |= Q(**{f"{entity_field_path}__company__franchise_id": primary_role.franchise.id})
    
    # If we have filters and are using Q objects, apply them
    if filters and use_q_objects:
        return queryset.filter(filters)
    
    # Otherwise, apply direct filters based on role scope
    if primary_role.service_center and entity_field_path:
        if entity_field_path == 'service_center':
            return queryset.filter(service_center_id=primary_role.service_center.id)
        else:
            return queryset.filter(**{f"{entity_field_path}_id": primary_role.service_center.id})
    elif primary_role.company and 'service_center' in entity_field_path:
        if entity_field_path == 'service_center':
            return queryset.filter(service_center__company_id=primary_role.company.id)
        else:
            return queryset.filter(**{f"{entity_field_path}__company_id": primary_role.company.id})
    elif primary_role.franchise and 'service_center' in entity_field_path:
        if entity_field_path == 'service_center':
            return queryset.filter(service_center__company__franchise_id=primary_role.franchise.id)
        else:
            return queryset.filter(**{f"{entity_field_path}__company__franchise_id": primary_role.franchise.id})
    
    # If no applicable filters were found but we have a role, return empty queryset
    # This is a safety measure to prevent accidental data leakage
    logger.warning(f"User {user.username} has role {primary_role} but no filter could be applied for {entity_field_path}")
    return queryset.none() 