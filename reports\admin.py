from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from reports.models import Report, ReportExecution, Dashboard, DashboardWidget


class ReportExecutionInline(admin.TabularInline):
    model = ReportExecution
    extra = 0
    readonly_fields = ('created_at', 'updated_at', 'status', 'start_time', 'end_time', 'duration')
    fields = ('status', 'start_time', 'end_time', 'duration', 'result_file', 'created_at')
    max_num = 5
    can_delete = False
    
    def duration(self, obj):
        if obj.duration is not None:
            return f"{obj.duration:.2f} seconds"
        return "-"
    duration.short_description = _("Duration")


class DashboardWidgetInline(admin.TabularInline):
    model = DashboardWidget
    extra = 0
    fields = ('title', 'widget_type', 'report', 'position')


@admin.register(Report)
class ReportAdmin(admin.ModelAdmin):
    list_display = ('name', 'report_type', 'is_scheduled', 'tenant_id', 'created_at')
    list_filter = ('report_type', 'is_scheduled')
    search_fields = ('name', 'description')
    readonly_fields = ('created_at', 'updated_at')
    inlines = [ReportExecutionInline]
    fieldsets = (
        (None, {
            'fields': ('tenant_id', 'name', 'description', 'report_type')
        }),
        (_('Query and Parameters'), {
            'fields': ('query', 'parameters')
        }),
        (_('Scheduling'), {
            'fields': ('is_scheduled', 'schedule'),
            'classes': ('collapse',)
        }),
        (_('Metadata'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(ReportExecution)
class ReportExecutionAdmin(admin.ModelAdmin):
    list_display = ('report', 'status', 'start_time', 'end_time', 'duration_display', 'tenant_id')
    list_filter = ('status', 'report__report_type')
    search_fields = ('report__name', 'error_message')
    readonly_fields = ('created_at', 'updated_at', 'duration_display')
    raw_id_fields = ('report',)
    fieldsets = (
        (None, {
            'fields': ('tenant_id', 'report', 'status')
        }),
        (_('Timing'), {
            'fields': ('start_time', 'end_time', 'duration_display')
        }),
        (_('Results'), {
            'fields': ('parameters', 'result_file', 'error_message')
        }),
        (_('Metadata'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def duration_display(self, obj):
        if obj.duration is not None:
            return f"{obj.duration:.2f} seconds"
        return "-"
    duration_display.short_description = _("Duration")


@admin.register(Dashboard)
class DashboardAdmin(admin.ModelAdmin):
    list_display = ('name', 'is_default', 'tenant_id', 'created_at')
    list_filter = ('is_default',)
    search_fields = ('name', 'description')
    readonly_fields = ('created_at', 'updated_at')
    inlines = [DashboardWidgetInline]
    fieldsets = (
        (None, {
            'fields': ('tenant_id', 'name', 'description', 'is_default')
        }),
        (_('Layout'), {
            'fields': ('layout',)
        }),
        (_('Metadata'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(DashboardWidget)
class DashboardWidgetAdmin(admin.ModelAdmin):
    list_display = ('title', 'widget_type', 'dashboard', 'report', 'position', 'tenant_id')
    list_filter = ('widget_type', 'dashboard')
    search_fields = ('title', 'dashboard__name', 'report__name')
    readonly_fields = ('created_at', 'updated_at')
    raw_id_fields = ('dashboard', 'report')
    fieldsets = (
        (None, {
            'fields': ('tenant_id', 'dashboard', 'title', 'widget_type', 'position')
        }),
        (_('Data Source'), {
            'fields': ('report', 'config')
        }),
        (_('Metadata'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
