from django.apps import AppConfig
from django.utils.translation import gettext_lazy as _


class InventoryConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'inventory'
    verbose_name = _("Inventory")
    
    def ready(self):
        """
        Initialize module-specific tasks when app is ready
        """
        try:
            # Import and register signals
            import inventory.signals
        except ImportError:
            pass
