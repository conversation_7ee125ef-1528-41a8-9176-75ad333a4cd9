/* 
 * App.css - Main stylesheet for the After-Sales Franchise Management System 
 * This file would normally be generated from the Tailwind input file using the Tailwind CLI
 */

/* Base Tailwind classes - Commented out because they cause 404 errors */
/* These should be processed by a build tool like the Tailwind CLI */
/* @import 'tailwindcss/base';
@import 'tailwindcss/components'; 
@import 'tailwindcss/utilities'; */

/* Flowbite styles */
@import 'https://cdnjs.cloudflare.com/ajax/libs/flowbite/1.8.1/flowbite.min.css';

/* Arabic Font */
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap');

/* Login page background */
.login-page {
  background-image: url('/static/images/login-bg.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  min-height: 100vh;
}

/* Fallback background gradient if the image doesn't load */
.login-page::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #1e5799 0%, #207cca 35%, #2989d8 50%, #7db9e8 100%);
  z-index: -1;
}

/* RTL overrides for Arabic */
html[dir="rtl"] {
  font-family: 'Tajawal', sans-serif;
}

html[dir="rtl"] .rtl\:space-x-reverse > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
}

html[dir="rtl"] .rtl\:mr-auto {
  margin-right: auto;
}

html[dir="rtl"] .rtl\:ml-auto {
  margin-left: auto;
}

/* For RTL text alignment */
html[dir="rtl"] .text-left {
  text-align: right;
}

html[dir="rtl"] .text-right {
  text-align: left;
}

/* Text input for RTL languages */
html[dir="rtl"] input,
html[dir="rtl"] textarea {
  text-align: right;
}

/* Logo placeholder for login page */
svg.logo-placeholder {
  width: 80px;
  height: 80px;
  color: #3b82f6;
}

/* RTL Support */
html[dir="rtl"] .me-3 {
  margin-right: 0;
  margin-left: 0.75rem;
}

html[dir="rtl"] .ms-4 {
  margin-left: 0;
  margin-right: 1rem;
}

html[dir="rtl"] .space-x-8 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
}

/* Custom Styles */
.transition {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}

.duration-200 {
  transition-duration: 200ms;
}

.ease-out {
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}

.hover\:-translate-y-0\.5:hover {
  --tw-translate-y: -0.125rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:shadow-lg:hover {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

/* Alert Styles */
.success {
  background-color: #dbeafe;
  color: #1e40af;
}

.error {
  background-color: #fee2e2;
  color: #b91c1c;
}

.warning {
  background-color: #fffbeb;
  color: #92400e;
}

.info {
  background-color: #dbeafe;
  color: #1e40af;
} 