# Generated by Django 4.2.20 on 2025-05-15 10:49

from django.db import migrations
import uuid


def add_vehicle_makes_and_models(apps, schema_editor):
    """
    Add common vehicle makes and models available in the Egyptian market
    """
    VehicleMake = apps.get_model('setup', 'VehicleMake')
    VehicleModel = apps.get_model('setup', 'VehicleModel')
    tenant_id = 1  # Default tenant ID for single-tenant setup

    # Dictionary of makes and their models with some metadata
    # Based on popular brands and models in the Egyptian market
    egypt_vehicle_data = {
        'Toyota': {
            'country': 'Japan',
            'models': [
                {'name': 'Corolla', 'class': 'Sedan', 'year_introduced': 1966},
                {'name': 'Yaris', 'class': 'Hatchback', 'year_introduced': 1999},
                {'name': 'Camry', 'class': 'Sedan', 'year_introduced': 1982},
                {'name': 'Fortuner', 'class': 'SUV', 'year_introduced': 2005},
                {'name': 'Land Cruiser', 'class': 'SUV', 'year_introduced': 1951},
                {'name': 'RAV4', 'class': 'Crossover', 'year_introduced': 1994},
                {'name': 'Hilux', 'class': 'Pickup', 'year_introduced': 1968},
            ]
        },
        'Hyundai': {
            'country': 'South Korea',
            'models': [
                {'name': 'Elantra', 'class': 'Sedan', 'year_introduced': 1990},
                {'name': 'Accent', 'class': 'Sedan', 'year_introduced': 1994},
                {'name': 'Tucson', 'class': 'SUV', 'year_introduced': 2004},
                {'name': 'Creta', 'class': 'Crossover', 'year_introduced': 2015},
                {'name': 'i10', 'class': 'Hatchback', 'year_introduced': 2007},
                {'name': 'i20', 'class': 'Hatchback', 'year_introduced': 2008},
            ]
        },
        'Kia': {
            'country': 'South Korea',
            'models': [
                {'name': 'Cerato', 'class': 'Sedan', 'year_introduced': 2003},
                {'name': 'Picanto', 'class': 'Hatchback', 'year_introduced': 2004},
                {'name': 'Sportage', 'class': 'SUV', 'year_introduced': 1993},
                {'name': 'Sorento', 'class': 'SUV', 'year_introduced': 2002},
                {'name': 'Rio', 'class': 'Sedan', 'year_introduced': 2000},
            ]
        },
        'Chevrolet': {
            'country': 'United States',
            'models': [
                {'name': 'Optra', 'class': 'Sedan', 'year_introduced': 2002},
                {'name': 'Lanos', 'class': 'Sedan', 'year_introduced': 1997},
                {'name': 'Aveo', 'class': 'Sedan', 'year_introduced': 2002},
                {'name': 'Cruze', 'class': 'Sedan', 'year_introduced': 2008},
                {'name': 'Captiva', 'class': 'SUV', 'year_introduced': 2006},
            ]
        },
        'Nissan': {
            'country': 'Japan',
            'models': [
                {'name': 'Sunny', 'class': 'Sedan', 'year_introduced': 1966},
                {'name': 'Sentra', 'class': 'Sedan', 'year_introduced': 1982},
                {'name': 'Qashqai', 'class': 'Crossover', 'year_introduced': 2006},
                {'name': 'X-Trail', 'class': 'SUV', 'year_introduced': 2000},
                {'name': 'Juke', 'class': 'Crossover', 'year_introduced': 2010},
            ]
        },
        'Mitsubishi': {
            'country': 'Japan',
            'models': [
                {'name': 'Lancer', 'class': 'Sedan', 'year_introduced': 1973},
                {'name': 'Pajero', 'class': 'SUV', 'year_introduced': 1982},
                {'name': 'Eclipse Cross', 'class': 'Crossover', 'year_introduced': 2017},
                {'name': 'Xpander', 'class': 'MPV', 'year_introduced': 2017},
                {'name': 'Outlander', 'class': 'SUV', 'year_introduced': 2001},
            ]
        },
        'BMW': {
            'country': 'Germany',
            'models': [
                {'name': '320i', 'class': 'Sedan', 'year_introduced': 1975},
                {'name': '520i', 'class': 'Sedan', 'year_introduced': 1972},
                {'name': 'X3', 'class': 'SUV', 'year_introduced': 2003},
                {'name': 'X5', 'class': 'SUV', 'year_introduced': 1999},
                {'name': 'X6', 'class': 'SUV', 'year_introduced': 2008},
            ]
        },
        'Mercedes-Benz': {
            'country': 'Germany',
            'models': [
                {'name': 'C-Class', 'class': 'Sedan', 'year_introduced': 1993},
                {'name': 'E-Class', 'class': 'Sedan', 'year_introduced': 1953},
                {'name': 'S-Class', 'class': 'Sedan', 'year_introduced': 1972},
                {'name': 'GLC', 'class': 'SUV', 'year_introduced': 2015},
                {'name': 'GLE', 'class': 'SUV', 'year_introduced': 2015},
            ]
        },
        'Renault': {
            'country': 'France',
            'models': [
                {'name': 'Logan', 'class': 'Sedan', 'year_introduced': 2004},
                {'name': 'Sandero', 'class': 'Hatchback', 'year_introduced': 2007},
                {'name': 'Duster', 'class': 'SUV', 'year_introduced': 2010},
                {'name': 'Kadjar', 'class': 'Crossover', 'year_introduced': 2015},
                {'name': 'Megane', 'class': 'Sedan', 'year_introduced': 1995},
            ]
        },
        'Peugeot': {
            'country': 'France',
            'models': [
                {'name': '301', 'class': 'Sedan', 'year_introduced': 2012},
                {'name': '3008', 'class': 'Crossover', 'year_introduced': 2009},
                {'name': '5008', 'class': 'SUV', 'year_introduced': 2009},
                {'name': '208', 'class': 'Hatchback', 'year_introduced': 2012},
                {'name': '508', 'class': 'Sedan', 'year_introduced': 2010},
            ]
        },
        'BYD': {
            'country': 'China',
            'models': [
                {'name': 'F3', 'class': 'Sedan', 'year_introduced': 2005},
                {'name': 'Han', 'class': 'Sedan', 'year_introduced': 2020},
                {'name': 'Tang', 'class': 'SUV', 'year_introduced': 2015},
                {'name': 'Yuan', 'class': 'Crossover', 'year_introduced': 2016},
                {'name': 'Song', 'class': 'SUV', 'year_introduced': 2015},
            ]
        },
        'Geely': {
            'country': 'China',
            'models': [
                {'name': 'Emgrand', 'class': 'Sedan', 'year_introduced': 2009},
                {'name': 'Coolray', 'class': 'Crossover', 'year_introduced': 2018},
                {'name': 'Azkarra', 'class': 'SUV', 'year_introduced': 2019},
                {'name': 'Okavango', 'class': 'SUV', 'year_introduced': 2020},
            ]
        },
        'Chery': {
            'country': 'China',
            'models': [
                {'name': 'Tiggo', 'class': 'SUV', 'year_introduced': 2005},
                {'name': 'Arrizo', 'class': 'Sedan', 'year_introduced': 2013},
                {'name': 'Tiggo 7', 'class': 'SUV', 'year_introduced': 2016},
                {'name': 'Tiggo 8', 'class': 'SUV', 'year_introduced': 2018},
            ]
        },
        'MG': {
            'country': 'United Kingdom/China',
            'models': [
                {'name': 'ZS', 'class': 'Crossover', 'year_introduced': 2017},
                {'name': 'HS', 'class': 'SUV', 'year_introduced': 2018},
                {'name': '5', 'class': 'Sedan', 'year_introduced': 2011},
                {'name': '6', 'class': 'Sedan', 'year_introduced': 2010},
                {'name': 'RX5', 'class': 'SUV', 'year_introduced': 2018},
            ]
        },
        'Fiat': {
            'country': 'Italy',
            'models': [
                {'name': 'Tipo', 'class': 'Sedan', 'year_introduced': 1988},
                {'name': '500', 'class': 'Hatchback', 'year_introduced': 2007},
                {'name': 'Punto', 'class': 'Hatchback', 'year_introduced': 1993},
                {'name': 'Doblo', 'class': 'MPV', 'year_introduced': 2000},
            ]
        }
    }

    # Create makes and models
    for make_name, make_data in egypt_vehicle_data.items():
        # Generate a deterministic UUID based on the make name
        make_id = uuid.uuid5(uuid.NAMESPACE_DNS, f"make.{make_name}")
        
        # Check if make already exists
        make = None
        try:
            make = VehicleMake.objects.get(name=make_name)
            print(f"Make {make_name} already exists. Updating it instead.")
            # Update the make with new values
            make.country_of_origin = make_data['country']
            make.is_active = True
            make.description = f"{make_name} vehicles available in the Egyptian market"
            make.save()
        except VehicleMake.DoesNotExist:
            # Create the make
            make = VehicleMake.objects.create(
                id=make_id,
                tenant_id=tenant_id,
                name=make_name,
                country_of_origin=make_data['country'],
                is_active=True,
                description=f"{make_name} vehicles available in the Egyptian market"
            )
        
        # Create the models for this make
        for model_data in make_data['models']:
            model_name = model_data['name']
            # Generate a deterministic UUID based on the make name and model name
            model_id = uuid.uuid5(uuid.NAMESPACE_DNS, f"model.{make_name}.{model_name}")
            
            # Check if model already exists
            try:
                model = VehicleModel.objects.get(make=make, name=model_name)
                print(f"Model {make_name} {model_name} already exists. Updating it instead.")
                # Update the model with new values
                model.vehicle_class = model_data.get('class', '')
                model.year_introduced = model_data.get('year_introduced')
                model.year_discontinued = model_data.get('year_discontinued')
                model.is_active = True
                model.description = f"{model_name} model by {make_name}"
                model.save()
            except VehicleModel.DoesNotExist:
                # Create new model
                VehicleModel.objects.create(
                    id=model_id,
                    tenant_id=tenant_id,
                    make=make,
                    name=model_name,
                    vehicle_class=model_data.get('class', ''),
                    year_introduced=model_data.get('year_introduced'),
                    year_discontinued=model_data.get('year_discontinued'),
                    is_active=True,
                    description=f"{model_name} model by {make_name}"
                )


def remove_vehicle_makes_and_models(apps, schema_editor):
    """
    This function is not actually removing the data since we're checking for
    existing records in the forward function. It's kept here as a placeholder.
    """
    pass


class Migration(migrations.Migration):

    dependencies = [
        ("setup", "0011_auto_20250515_1014"),
    ]

    operations = [
        migrations.RunPython(add_vehicle_makes_and_models, remove_vehicle_makes_and_models),
    ]
