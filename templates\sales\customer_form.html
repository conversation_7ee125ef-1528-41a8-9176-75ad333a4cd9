{% extends "dashboard_base.html" %}
{% load i18n %}
{% load static %}

{% block title %}
    {% if object %}
        {% trans "تعديل عميل" %}
    {% else %}
        {% trans "عميل جديد" %}
    {% endif %}
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="mb-6 flex flex-wrap items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-800">
                {% if object %}
                    {% trans "تعديل عميل" %}
                {% else %}
                    {% trans "عميل جديد" %}
                {% endif %}
            </h1>
            <p class="text-gray-600">
                {% if object %}
                    {{ object.name }}
                {% else %}
                    {% trans "إضافة عميل جديد" %}
                {% endif %}
            </p>
        </div>
        <div class="mt-4 md:mt-0">
            <a href="{% if object %}{% url 'sales:customer_detail' object.id %}{% else %}{% url 'sales:customer_list' %}{% endif %}" class="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-lg inline-flex items-center">
                <i class="fas fa-arrow-{% if LANGUAGE_CODE == 'ar' %}right{% else %}left{% endif %} {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i>
                {% trans "العودة" %}
            </a>
        </div>
    </div>
    
    <!-- Form Section -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-800">{% trans "معلومات العميل" %}</h2>
        </div>
        <div class="p-6">
            <form method="post" class="space-y-6">
                {% csrf_token %}
                
                <!-- Display form errors if any -->
                {% if form.errors %}
                    <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
                        <p class="font-bold">{% trans "يرجى تصحيح الأخطاء التالية:" %}</p>
                        <ul class="list-disc {% if LANGUAGE_CODE == 'ar' %}mr-5{% else %}ml-5{% endif %} mt-2">
                            {% for field in form %}
                                {% for error in field.errors %}
                                    <li>{{ field.label }}: {{ error }}</li>
                                {% endfor %}
                            {% endfor %}
                            {% for error in form.non_field_errors %}
                                <li>{{ error }}</li>
                            {% endfor %}
                        </ul>
                    </div>
                {% endif %}
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Name -->
                    <div>
                        <label for="{{ form.name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                            {% trans "الاسم" %} <span class="text-red-500">*</span>
                        </label>
                        {{ form.name }}
                        {% if form.name.help_text %}
                            <p class="mt-1 text-xs text-gray-500">{{ form.name.help_text }}</p>
                        {% endif %}
                    </div>
                    
                    <!-- Email -->
                    <div>
                        <label for="{{ form.email.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                            {% trans "البريد الإلكتروني" %}
                        </label>
                        {{ form.email }}
                        {% if form.email.help_text %}
                            <p class="mt-1 text-xs text-gray-500">{{ form.email.help_text }}</p>
                        {% endif %}
                    </div>
                    
                    <!-- Phone -->
                    <div>
                        <label for="{{ form.phone.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                            {% trans "رقم الهاتف" %}
                        </label>
                        {{ form.phone }}
                        {% if form.phone.help_text %}
                            <p class="mt-1 text-xs text-gray-500">{{ form.phone.help_text }}</p>
                        {% endif %}
                    </div>
                </div>
                
                <!-- Address -->
                <div>
                    <label for="{{ form.address.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        {% trans "العنوان" %}
                    </label>
                    {{ form.address }}
                    {% if form.address.help_text %}
                        <p class="mt-1 text-xs text-gray-500">{{ form.address.help_text }}</p>
                    {% endif %}
                </div>
                
                <!-- Notes -->
                <div>
                    <label for="{{ form.notes.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        {% trans "ملاحظات" %}
                    </label>
                    {{ form.notes }}
                    {% if form.notes.help_text %}
                        <p class="mt-1 text-xs text-gray-500">{{ form.notes.help_text }}</p>
                    {% endif %}
                </div>
                
                <div class="pt-5 border-t border-gray-200 flex justify-end">
                    <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white py-2 px-6 rounded-lg">
                        {% if object %}
                            {% trans "حفظ التغييرات" %}
                        {% else %}
                            {% trans "إنشاء عميل" %}
                        {% endif %}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %} 