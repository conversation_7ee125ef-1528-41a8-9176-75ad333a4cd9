import logging
from django.db.models.signals import post_save, post_delete, m2m_changed
from django.dispatch import receiver
from django.contrib.auth.models import User, Group, Permission
from .models import Role, UserRole, ModulePermission

logger = logging.getLogger(__name__)

@receiver(post_save, sender=ModulePermission)
def module_permission_saved(sender, instance, created, **kwargs):
    """Log when a module permission is saved and update permissions"""
    if created:
        logger.info(f"New module permission created: {instance.role.name} - {instance.get_module_display()} - {instance.get_action_display()}")
    else:
        logger.info(f"Module permission updated: {instance.role.name} - {instance.get_module_display()} - {instance.get_action_display()}")

@receiver(post_save, sender=User)
def user_saved(sender, instance, created, **kwargs):
    """Create default roles for new users if needed"""
    if created:
        logger.info(f"New user created: {instance.username}")
        # Add default roles here if needed
    else:
        logger.info(f"User updated: {instance.username}")

@receiver(post_save, sender=Role)
def role_saved(sender, instance, created, **kwargs):
    """When a role is saved, ensure that the linked Django group exists"""
    if created:
        logger.info(f"New role created: {instance.name}")
    else:
        logger.info(f"Role updated: {instance.name}")
        
    # Make sure all users with this role are in the corresponding group
    user_roles = UserRole.objects.filter(role=instance, is_active=True)
    
    for user_role in user_roles:
        if instance.group not in user_role.user.groups.all():
            user_role.user.groups.add(instance.group)
            logger.info(f"Added user {user_role.user.username} to group {instance.group.name}")

@receiver(post_save, sender=UserRole)
def user_role_saved(sender, instance, created, **kwargs):
    """When a user role is saved, ensure the user has the right permissions"""
    if created:
        scope_info = ""
        if instance.franchise:
            scope_info = f" for franchise {instance.franchise.name}"
        elif instance.company:
            scope_info = f" for company {instance.company.name}"
        elif instance.service_center:
            scope_info = f" for service center {instance.service_center.name}"
        
        logger.info(f"New user role created: {instance.user.username} as {instance.role.name}{scope_info}")
    else:
        logger.info(f"User role updated: {instance.user.username} as {instance.role.name}")
    
    # Make sure user is in the right group
    if instance.is_active and instance.role.group not in instance.user.groups.all():
        instance.user.groups.add(instance.role.group)

@receiver(post_delete, sender=UserRole)
def user_role_deleted(sender, instance, **kwargs):
    """When a user role is deleted, check if the user should be removed from the group"""
    logger.info(f"User role deleted: {instance.user.username} as {instance.role.name}")
    
    # Check if the user has any other roles with the same group
    if not UserRole.objects.filter(user=instance.user, role__group=instance.role.group).exists():
        # If not, remove them from the group
        instance.user.groups.remove(instance.role.group)
        logger.info(f"Removed user {instance.user.username} from group {instance.role.group.name}")

@receiver(m2m_changed, sender=Group.permissions.through)
def group_permissions_changed(sender, instance, action, pk_set, **kwargs):
    """When a group's permissions change, log it"""
    if action == 'post_add':
        permissions = Permission.objects.filter(pk__in=pk_set)
        for permission in permissions:
            logger.info(f"Added permission {permission.codename} to group {instance.name}")
    elif action == 'post_remove':
        permissions = Permission.objects.filter(pk__in=pk_set)
        for permission in permissions:
            logger.info(f"Removed permission {permission.codename} from group {instance.name}") 