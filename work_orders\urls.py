from django.urls import path
from . import views
from .api_spare_parts import api_get_spare_parts
from . import ajax_views

app_name = 'work_orders'

urlpatterns = [
    # Work Order URLs
    path('', views.WorkOrderListView.as_view(), name='work_order_list'),
    path('create/', views.WorkOrderCreateView.as_view(), name='work_order_create'),
    path('<uuid:pk>/', views.WorkOrderDetailView.as_view(), name='work_order_detail'),
    path('<uuid:pk>/update/', views.WorkOrderUpdateView.as_view(), name='work_order_update'),
    
    # Vehicle Service History
    path('vehicle/<uuid:vehicle_id>/service-history/', views.VehicleServiceHistoryView.as_view(), name='vehicle_service_history'),
    
    # HTMX Endpoints
    path('htmx/search-customers/', views.htmx_search_customers, name='htmx_search_customers'),
    path('htmx/select-customer/', views.htmx_select_customer, name='htmx_select_customer'),
    path('htmx/search-vehicles/', views.htmx_search_vehicles, name='htmx_search_vehicles'),
    path('htmx/select-vehicle/', views.htmx_select_vehicle, name='htmx_select_vehicle'),
    
    # Maintenance Schedule URLs
    path('<uuid:work_order_id>/select-schedule/', views.MaintenanceScheduleSelectionView.as_view(), name='select_maintenance_schedule'),
    path('<uuid:work_order_id>/apply-schedule/<uuid:schedule_id>/', views.apply_maintenance_schedule, name='apply_maintenance_schedule'),
    path('api/maintenance-schedules/', views.api_get_maintenance_schedules, name='api_maintenance_schedules'),
    
    # Bill of Materials URLs
    path('bom/', views.BillOfMaterialsListView.as_view(), name='bom_list'),
    path('bom/<uuid:pk>/', views.BillOfMaterialsDetailView.as_view(), name='bom_detail'),
    
    # Work Order Materials
    path('<uuid:work_order_id>/materials/add/', views.WorkOrderMaterialCreateView.as_view(), name='add_material'),
    path('api/filter-items/', views.api_filter_items, name='api_filter_items'),
    
    # Operations API - Old implementations
    path('api/operations-for-vehicle/', views.api_get_operations_for_vehicle, name='api_operations_for_vehicle'),
    path('api/operation-descriptions/', views.api_get_operation_descriptions, name='api_operation_descriptions'),
    
    # New Optimized AJAX endpoints
    path('ajax/search-customers/', ajax_views.ajax_search_customers, name='ajax_search_customers'),
    path('ajax/search-vehicles/', ajax_views.ajax_search_vehicles, name='ajax_search_vehicles'),
    path('ajax/vehicle-makes/', ajax_views.ajax_get_vehicle_makes, name='ajax_vehicle_makes'),
    path('ajax/vehicle-models/', ajax_views.ajax_get_vehicle_models, name='ajax_vehicle_models'),
    path('ajax/operations-for-vehicle/', ajax_views.ajax_get_operations_for_vehicle, name='ajax_operations_for_vehicle'),
    path('ajax/spare-parts-for-operation/', ajax_views.ajax_spare_parts_for_operation, name='ajax_spare_parts_for_operation'),
    path('ajax/create-customer/', ajax_views.ajax_create_customer, name='ajax_create_customer'),
    path('ajax/create-vehicle/', ajax_views.ajax_create_vehicle, name='ajax_create_vehicle'),
    path('ajax/add-material/', ajax_views.ajax_add_material_to_work_order, name='ajax_add_material'),
    
    # Legacy API endpoints - kept for backward compatibility
    path('api/search-customers/', views.api_search_customers, name='api_search_customers'),
    path('api/create-customer/', views.api_create_customer, name='api_create_customer'),
    path('api/search-vehicles/', views.api_search_vehicles, name='api_search_vehicles'),
    path('api/create-vehicle/', views.api_create_vehicle, name='api_create_vehicle'),
    path('api/vehicle-makes/', views.api_get_vehicle_makes, name='api_vehicle_makes'),
    path('api/vehicle-models/', views.api_get_vehicle_models, name='api_vehicle_models'),
    
    # Spare Parts
    path('api/search-spare-parts/', api_get_spare_parts, name='api_spare_parts'),
    path('spare-parts-search/', views.SparePartsSearchView.as_view(), name='spare_parts_search'),

    # Other API endpoints
    path('api/get-maintenance-schedules/', views.api_get_maintenance_schedules, name='api_get_maintenance_schedules'),
    path('api/get-operations-for-vehicle/', views.api_get_operations_for_vehicle, name='api_get_operations_for_vehicle'),
    path('api/get-operation-descriptions/', views.api_get_operation_descriptions, name='api_get_operation_descriptions'),
    path('api/get-spare-parts/', api_get_spare_parts, name='api_get_spare_parts'),
    path('api/spare-parts-for-operation/', views.api_spare_parts_for_operation, name='api_spare_parts_for_operation'),
    # Re-enable with a temporary placeholder function
    path('api/request-part-transfer/', views.api_request_part_transfer_placeholder, name='api_request_part_transfer'),
    path('api/add-material/', views.api_add_material_placeholder, name='api_add_material'),
] 