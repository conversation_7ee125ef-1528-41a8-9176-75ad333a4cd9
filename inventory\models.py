from django.db import models
from django.utils.translation import gettext_lazy as _
from core.models import TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel
from core.querysets import BaseQuerySet
import waffle
import os
import uuid
from django.core.exceptions import ValidationError
from django.urls import reverse
from setup.models import VehicleMake, VehicleModel
from django.utils import timezone


class ItemClassification(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """
    Classification system for inventory items to enable better reporting and filtering
    """
    name = models.CharField(_("Classification Name"), max_length=100)
    code = models.CharField(_("Classification Code"), max_length=50)
    description = models.TextField(_("Description"), blank=True)
    parent = models.ForeignKey(
        'self', 
        on_delete=models.CASCADE, 
        null=True, blank=True,
        related_name='children',
        verbose_name=_("Parent Classification")
    )
    level = models.PositiveSmallIntegerField(_("Level"), default=0, help_text=_("Hierarchical level (0=top level)"))
    is_active = models.BooleanField(_("Is Active"), default=True)
    icon = models.CharField(_("Icon"), max_length=50, blank=True, help_text=_("Icon name for UI display"))
    color = models.CharField(_("Color"), max_length=20, blank=True, help_text=_("Color code for UI display"))
    # Additional configurable attributes
    attributes = models.JSONField(_("Custom Attributes"), default=dict, blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Item Classification")
        verbose_name_plural = _("Item Classifications")
        unique_together = [['tenant_id', 'code']]
        ordering = ['level', 'name']
        
    def __str__(self):
        return self.name
    
    def save(self, *args, **kwargs):
        # Set the level based on parent
        if self.parent:
            self.level = self.parent.level + 1
        else:
            self.level = 0
        super().save(*args, **kwargs)
    
    def get_full_path(self):
        """Return full classification path including ancestors"""
        path = []
        current = self
        while current:
            path.insert(0, current.name)
            current = current.parent
        return " > ".join(path)
    
    def get_children_recursive(self):
        """Get all children recursively"""
        all_children = list(self.children.all())
        for child in self.children.all():
            all_children.extend(child.get_children_recursive())
        return all_children


class ItemQuerySet(BaseQuerySet):
    """
    Custom queryset for Item model with tenant awareness
    """
    
    def compatible_with_vehicle(self, make, model=None, year=None):
        """
        Filter items that are compatible with the specified vehicle parameters
        """
        qs = self.filter(vehicle_compatibilities__make__iexact=make)
        
        if model:
            qs = qs.filter(vehicle_compatibilities__model__iexact=model)
            
        if year and isinstance(year, int):
            qs = qs.filter(
                models.Q(vehicle_compatibilities__year_from__lte=year) & 
                (models.Q(vehicle_compatibilities__year_to__isnull=True) | models.Q(vehicle_compatibilities__year_to__gte=year))
            )
            
        return qs.distinct()
        
    def compatible_with_operation(self, operation_type_id):
        """
        Filter items that are compatible with the specified operation type
        """
        return self.filter(operation_compatibilities__operation_type_id=operation_type_id).distinct()
        
    def by_classification(self, classification_id):
        """
        Filter items by classification (including child classifications)
        """
        try:
            classification = ItemClassification.objects.get(pk=classification_id)
            # Get all child classifications
            child_ids = [child.id for child in classification.get_children_recursive()]
            # Add current classification
            classification_ids = [classification.id] + child_ids
            return self.filter(classification_id__in=classification_ids)
        except ItemClassification.DoesNotExist:
            return self.none()


class UnitOfMeasurement(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """
    Units of measurement for inventory items
    """
    name = models.CharField(_("Name"), max_length=50)
    symbol = models.CharField(_("Symbol"), max_length=10)
    description = models.TextField(_("Description"), blank=True)
    is_base_unit = models.BooleanField(_("Is Base Unit"), default=False, 
                                      help_text=_("If checked, this unit will be used as a reference for conversions"))
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Unit of Measurement")
        verbose_name_plural = _("Units of Measurement")
        unique_together = [['tenant_id', 'name'], ['tenant_id', 'symbol']]
        
    def __str__(self):
        return f"{self.name} ({self.symbol})"


class UnitConversion(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """
    Conversion rules between units of measurement
    """
    from_unit = models.ForeignKey(UnitOfMeasurement, related_name='conversions_from', 
                                 on_delete=models.CASCADE, verbose_name=_("From Unit"))
    to_unit = models.ForeignKey(UnitOfMeasurement, related_name='conversions_to', 
                               on_delete=models.CASCADE, verbose_name=_("To Unit"))
    conversion_factor = models.DecimalField(_("Conversion Factor"), max_digits=20, decimal_places=10,
                                          help_text=_("Multiply quantity in 'from_unit' by this factor to get quantity in 'to_unit'"))
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Unit Conversion")
        verbose_name_plural = _("Unit Conversions")
        unique_together = [['tenant_id', 'from_unit', 'to_unit']]
        
    def __str__(self):
        return f"{self.from_unit.symbol} → {self.to_unit.symbol} (×{self.conversion_factor})"
    
    def clean(self):
        """
        Validate that from_unit and to_unit are different and belong to the same tenant
        """
        if self.from_unit == self.to_unit:
            raise ValidationError(_("From unit and to unit must be different"))
        
        if self.from_unit.tenant_id != self.to_unit.tenant_id:
            raise ValidationError(_("Both units must belong to the same tenant"))
        
        super().clean()
    
    def convert(self, quantity):
        """
        Convert a quantity from from_unit to to_unit
        """
        return quantity * self.conversion_factor


class Item(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """
    Core inventory item model with dynamic attributes via JSONField
    """
    sku = models.CharField(_("SKU"), max_length=100, db_index=True)
    name = models.CharField(_("Name"), max_length=255)
    description = models.TextField(_("Description"), blank=True)
    quantity = models.DecimalField(_("Quantity"), max_digits=10, decimal_places=2, default=0)
    unit_of_measurement = models.ForeignKey(UnitOfMeasurement, related_name='items', 
                                          on_delete=models.PROTECT, verbose_name=_("Unit of Measurement"),
                                          null=True, blank=True)
    unit_price = models.DecimalField(_("Unit Price"), max_digits=10, decimal_places=2, default=0)
    min_stock_level = models.DecimalField(_("Minimum Stock Level"), max_digits=10, decimal_places=2, default=0)
    attributes = models.JSONField(_("Attributes"), default=dict, blank=True)
    
    # Optional categories
    ITEM_CATEGORIES = (
        ('part', _('Part')), 
        ('consumable', _('Consumable')),
        ('tool', _('Tool')),
        ('equipment', _('Equipment')),
        ('material', _('Material')),
        ('finished_good', _('Finished Good')),
    )
    category = models.CharField(_("Category"), max_length=50, choices=ITEM_CATEGORIES, blank=True)
    item_type = models.CharField(_("Item Type"), max_length=100, blank=True, help_text=_("Specific type within category"))
    
    # Add classification relationship
    classification = models.ForeignKey(
        ItemClassification,
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name='items',
        verbose_name=_("Classification")
    )
    
    objects = ItemQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Item")
        verbose_name_plural = _("Items")
        unique_together = [['tenant_id', 'sku']]
        
    def __str__(self):
        return f"{self.name} ({self.sku})"
    
    @property
    def current_stock(self):
        """
        Returns current stock level (alias for quantity)
        """
        return self.quantity
        
    @property
    def is_low_stock(self):
        """
        Check if item is below minimum stock level
        """
        return self.quantity < self.min_stock_level

    def get_quantity_in_unit(self, target_unit):
        """
        Convert item quantity to specified unit
        
        Args:
            target_unit: UnitOfMeasurement instance or unit ID
            
        Returns:
            Decimal: Converted quantity
            
        Raises:
            ValueError: If no conversion path exists between units
        """
        if not self.unit_of_measurement:
            raise ValueError(_("Item does not have a unit of measurement defined"))
            
        # If target_unit is the same as item's unit, return quantity as is
        if (isinstance(target_unit, UnitOfMeasurement) and self.unit_of_measurement == target_unit) or \
           (isinstance(target_unit, (str, uuid.UUID)) and str(self.unit_of_measurement.id) == str(target_unit)):
            return self.quantity
        
        # Get target unit instance if an ID was provided
        if not isinstance(target_unit, UnitOfMeasurement):
            target_unit = UnitOfMeasurement.objects.get(pk=target_unit)
            
        # Look for direct conversion
        try:
            conversion = UnitConversion.objects.get(
                tenant_id=self.tenant_id,
                from_unit=self.unit_of_measurement,
                to_unit=target_unit
            )
            return conversion.convert(self.quantity)
        except UnitConversion.DoesNotExist:
            # Try inverse conversion
            try:
                conversion = UnitConversion.objects.get(
                    tenant_id=self.tenant_id,
                    from_unit=target_unit,
                    to_unit=self.unit_of_measurement
                )
                # Inverse factor for reverse conversion
                return self.quantity / conversion.conversion_factor
            except UnitConversion.DoesNotExist:
                # Could implement more complex conversion path finding here
                raise ValueError(_("No conversion path exists between {} and {}").format(
                    self.unit_of_measurement.symbol, target_unit.symbol))


def get_document_upload_path(instance, filename):
    """
    Generate unique file path for document uploads
    Format: inventory/documents/{tenant_id}/{item_id}/{uuid}_{filename}
    """
    unique_filename = f"{uuid.uuid4()}_{filename}"
    return os.path.join(
        'inventory', 
        'documents', 
        str(instance.tenant_id),
        str(instance.item.id), 
        unique_filename
    )


class ItemDocument(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """
    Document attachments for inventory items (manuals, specifications, certifications, etc.)
    """
    DOCUMENT_TYPES = (
        ('manual', _('User Manual')),
        ('spec', _('Technical Specification')),
        ('cert', _('Certification')),
        ('warranty', _('Warranty Information')),
        ('image', _('Product Image')),
        ('other', _('Other')),
    )
    
    item = models.ForeignKey(Item, related_name='documents', on_delete=models.CASCADE, 
                           verbose_name=_("Item"))
    title = models.CharField(_("Title"), max_length=255)
    document_type = models.CharField(_("Document Type"), max_length=20, choices=DOCUMENT_TYPES, 
                                   default='other')
    file = models.FileField(_("File"), upload_to=get_document_upload_path)
    description = models.TextField(_("Description"), blank=True)
    is_public = models.BooleanField(_("Public"), default=False, 
                                  help_text=_("If checked, this document will be visible to all users"))
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Item Document")
        verbose_name_plural = _("Item Documents")
        ordering = ['-created_at']
        
    def __str__(self):
        return f"{self.title} - {self.item.name}"
    
    def filename(self):
        """
        Return just the filename portion of the file path
        """
        if not self.file or not self.file.name:
            return "No file"
        return os.path.basename(self.file.name)
    
    def file_extension(self):
        """
        Return the file extension (lowercase)
        """
        if not self.file or not self.file.name:
            return ""
        name, extension = os.path.splitext(self.file.name)
        return extension.lower()[1:] if extension else ""
    
    def is_image(self):
        """
        Check if the document is an image based on its extension
        """
        if not self.file:
            return False
        return self.file_extension() in ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg']

    def delete(self, *args, **kwargs):
        """
        Delete the file from storage when the document is deleted
        """
        if self.file:
            # Delete the file from storage
            storage = self.file.storage
            if storage.exists(self.file.name):
                storage.delete(self.file.name)
                
        super().delete(*args, **kwargs)


class MovementType(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """
    Dynamic configuration of movement types (in/out/transfer/etc.)
    """
    code = models.CharField(_("Code"), max_length=50, db_index=True, help_text=_("Unique code for this movement type"))
    name = models.CharField(_("Name"), max_length=100)
    description = models.TextField(_("Description"), blank=True)
    is_inbound = models.BooleanField(_("Is Inbound"), help_text=_("Does this movement type increase stock?"))
    is_outbound = models.BooleanField(_("Is Outbound"), help_text=_("Does this movement type decrease stock?"))
    icon = models.CharField(_("Icon"), max_length=50, blank=True, help_text=_("Icon name for UI display"))
    color = models.CharField(_("Color"), max_length=20, blank=True, help_text=_("Color code for UI display"))
    is_active = models.BooleanField(_("Is Active"), default=True)
    requires_reference = models.BooleanField(_("Requires Reference"), default=False, 
                                          help_text=_("Is a reference document required?"))
    requires_approval = models.BooleanField(_("Requires Approval"), default=False,
                                         help_text=_("Does this type of movement require approval?"))
    sequence = models.PositiveIntegerField(_("Sequence"), default=10, help_text=_("Display order in UI"))
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Movement Type")
        verbose_name_plural = _("Movement Types")
        unique_together = [['tenant_id', 'code']]
        ordering = ['sequence', 'name']
        
    def __str__(self):
        return self.name
        
    def save(self, *args, **kwargs):
        # If both inbound and outbound are True, this is a transfer
        # Make sure at least one of them is True
        if not (self.is_inbound or self.is_outbound):
            raise ValidationError(_("Movement type must be either inbound, outbound, or both"))
        super().save(*args, **kwargs)

    
class Movement(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """
    Stock movement ledger for inventory items
    """
    # Legacy movement types kept for backward compatibility
    LEGACY_MOVEMENT_TYPES = (
        ('purchase', _('Purchase')),
        ('sale', _('Sale')),
        ('adjustment', _('Adjustment')),
        ('transfer', _('Transfer')),
        ('return', _('Return')),
    )
    
    item = models.ForeignKey(Item, related_name='movements', on_delete=models.CASCADE)
    quantity = models.DecimalField(_("Quantity"), max_digits=10, decimal_places=2)
    unit_of_measurement = models.ForeignKey(UnitOfMeasurement, related_name='movements',
                                          on_delete=models.PROTECT, verbose_name=_("Unit of Measurement"),
                                          null=True, blank=True)
    # New field referencing the dynamic movement type
    movement_type_ref = models.ForeignKey(MovementType, related_name='movements',
                                        on_delete=models.PROTECT, verbose_name=_("Movement Type"),
                                        null=True, blank=True)
    # Legacy field for backward compatibility
    movement_type = models.CharField(_("Legacy Movement Type"), max_length=20, 
                                   choices=LEGACY_MOVEMENT_TYPES, null=True, blank=True)
    reference = models.CharField(_("Reference"), max_length=100, blank=True)
    notes = models.TextField(_("Notes"), blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Movement")
        verbose_name_plural = _("Movements")
        ordering = ['-created_at']
        
    def __str__(self):
        movement_name = self.get_movement_name()
        return f"{movement_name}: {self.quantity} x {self.item.name}"
    
    def get_movement_name(self):
        """Get the display name of the movement type"""
        if self.movement_type_ref:
            return self.movement_type_ref.name
        # Fallback to legacy type
        return self.get_movement_type_display() if self.movement_type else _("Unknown")
    
    def is_inbound(self):
        """Determine if this is an inbound movement"""
        if self.movement_type_ref:
            return self.movement_type_ref.is_inbound
        # Legacy logic
        return self.movement_type in ['purchase', 'return', 'adjustment'] and self.quantity > 0
    
    def is_outbound(self):
        """Determine if this is an outbound movement"""
        if self.movement_type_ref:
            return self.movement_type_ref.is_outbound
        # Legacy logic
        return self.movement_type in ['sale', 'adjustment'] and self.quantity < 0
    
    def clean(self):
        """
        Validate that at least one movement type field is populated
        """
        if not self.movement_type and not self.movement_type_ref:
            raise ValidationError(_("Either movement_type or movement_type_ref must be specified"))
        super().clean()
    
    def save(self, *args, **kwargs):
        """
        Override save method to update item quantity
        """
        # Update item quantity
        item = self.item
        if not self.id:  # Only update quantity on new movement creation
            if self.is_inbound():
                item.quantity += self.quantity
            elif self.is_outbound():
                item.quantity -= abs(self.quantity)
            item.save(update_fields=['quantity', 'updated_at'])
        
        super().save(*args, **kwargs)
    
    def delete(self, *args, **kwargs):
        """
        Override delete method to revert item quantity
        """
        # Revert item quantity
        item = self.item
        if self.is_inbound():
            item.quantity -= self.quantity
        elif self.is_outbound():
            item.quantity += abs(self.quantity)
        item.save(update_fields=['quantity', 'updated_at'])
        
        super().delete(*args, **kwargs)


class VehicleCompatibility(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """
    Vehicle compatibility information for inventory items
    """
    item = models.ForeignKey(
        Item, 
        on_delete=models.CASCADE,
        related_name="vehicle_compatibilities",
        verbose_name=_("Item")
    )
    make = models.CharField(_("Vehicle Make"), max_length=100, db_index=True)
    model = models.CharField(_("Vehicle Model"), max_length=100, db_index=True)
    year_from = models.PositiveIntegerField(_("Year From"), db_index=True)
    year_to = models.PositiveIntegerField(_("Year To"), null=True, blank=True, db_index=True,
                                         help_text=_("Leave blank if still compatible with current models"))
    variant = models.CharField(_("Variant/Trim"), max_length=100, blank=True, 
                              help_text=_("Specific variant or trim level if applicable"))
    engine = models.CharField(_("Engine"), max_length=100, blank=True,
                             help_text=_("Engine type or code if applicable"))
    notes = models.TextField(_("Notes"), blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Vehicle Compatibility")
        verbose_name_plural = _("Vehicle Compatibilities")
        ordering = ['make', 'model', 'year_from']
        unique_together = [['tenant_id', 'item', 'make', 'model', 'year_from', 'variant', 'engine']]
        
    def __str__(self):
        year_range = f"{self.year_from}-{self.year_to}" if self.year_to else f"{self.year_from}+"
        return f"{self.make} {self.model} ({year_range})"
    
    def clean(self):
        """
        Validate year range
        """
        if self.year_to and self.year_from > self.year_to:
            raise ValidationError(_("Year from must be less than or equal to year to"))
        
        super().clean()


class VehicleOperationCompatibility(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """
    Links operation compatibilities to specific vehicle makes/models with year ranges
    """
    operation_compatibility = models.ForeignKey(
        'OperationCompatibility',
        on_delete=models.CASCADE,
        related_name='vehicle_compatibilities',
        verbose_name=_("Operation Compatibility")
    )
    vehicle_make = models.ForeignKey(
        VehicleMake,
        on_delete=models.CASCADE,
        related_name='operation_compatibilities',
        verbose_name=_("Vehicle Make")
    )
    vehicle_model = models.ForeignKey(
        VehicleModel,
        on_delete=models.CASCADE, 
        null=True, blank=True,
        related_name='operation_compatibilities',
        verbose_name=_("Vehicle Model")
    )
    year_from = models.PositiveIntegerField(
        _("Year From"),
        null=True, blank=True,
        help_text=_("Start year of compatible vehicle models")
    )
    year_to = models.PositiveIntegerField(
        _("Year To"),
        null=True, blank=True,
        help_text=_("End year of compatible vehicle models (leave blank for current)")
    )
    duration_minutes = models.PositiveIntegerField(
        _("Duration (minutes)"),
        null=True, blank=True,
        help_text=_("Estimated duration of this operation for this specific vehicle. Overrides the default duration.")
    )
    
    class Meta:
        verbose_name = _("Vehicle Operation Compatibility")
        verbose_name_plural = _("Vehicle Operation Compatibilities")
        unique_together = [
            ('operation_compatibility', 'vehicle_make', 'vehicle_model', 'year_from', 'year_to'),
        ]
        
    def __str__(self):
        vehicle_info = f"{self.vehicle_make.name}"
        if self.vehicle_model:
            vehicle_info += f" {self.vehicle_model.name}"
        if self.year_from:
            vehicle_info += f" ({self.year_from}"
            if self.year_to:
                vehicle_info += f"-{self.year_to}"
            vehicle_info += ")"
        return f"{self.operation_compatibility} - {vehicle_info}"

    def clean(self):
        """Validate year range"""
        if self.year_to and self.year_from and self.year_from > self.year_to:
            raise ValidationError(_("Year from must be less than or equal to year to"))
            
    def duration_minutes_display(self):
        """Return a human-readable duration"""
        if self.duration_minutes:
            hours = self.duration_minutes // 60
            minutes = self.duration_minutes % 60
            if hours and minutes:
                return f"{hours}h {minutes}m"
            elif hours:
                return f"{hours}h"
            else:
                return f"{minutes}m"
        # If no specific duration set, return the parent operation compatibility duration
        elif self.operation_compatibility and self.operation_compatibility.duration_minutes:
            return self.operation_compatibility.duration_minutes_display()
        return "-"
    duration_minutes_display.short_description = _("Duration")


class OperationCompatibility(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """
    Links inventory items to compatible work order operations/types
    """
    item = models.ForeignKey(
        Item, 
        on_delete=models.CASCADE,
        related_name="operation_compatibilities",
        verbose_name=_("Item")
    )
    operation_type = models.ForeignKey(
        'work_orders.WorkOrderType',  # Use string reference to avoid circular import
        on_delete=models.CASCADE,
        related_name="compatible_items",
        verbose_name=_("Operation Type")
    )
    is_required = models.BooleanField(_("Required"), default=False,
                                    help_text=_("Is this item required for this operation type?"))
    is_common = models.BooleanField(_("Common"), default=True,
                                   help_text=_("Is this item commonly used for this operation type?"))
    maintenance_schedule = models.ForeignKey(
        'work_orders.MaintenanceSchedule',
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name="operation_compatibilities",
        verbose_name=_("Maintenance Schedule")
    )
    
    # Vehicle compatibility is now handled through the related VehicleOperationCompatibility model
    # These fields are kept for backward compatibility but will be phased out
    vehicle_make = models.ForeignKey(
        VehicleMake,
        on_delete=models.CASCADE,
        null=True, blank=True,
        related_name='operation_compatibilities_make',
        verbose_name=_("Legacy Vehicle Make")
    )
    vehicle_model = models.ForeignKey(
        VehicleModel,
        on_delete=models.CASCADE,
        null=True, blank=True,
        related_name='operation_compatibilities_model',
        verbose_name=_("Legacy Vehicle Model")
    )
    year_from = models.PositiveIntegerField(
        _("Legacy Year From"),
        null=True, blank=True,
        help_text=_("Legacy field - use vehicle compatibilities instead")
    )
    year_to = models.PositiveIntegerField(
        _("Legacy Year To"),
        null=True, blank=True,
        help_text=_("Legacy field - use vehicle compatibilities instead")
    )
    
    # New fields for operation description and duration
    operation_description = models.CharField(
        _("Operation Description"),
        max_length=255,
        choices=[
            ('oil_change', _('Oil Change')),
            ('brake_service', _('Brake Service')),
            ('transmission_service', _('Transmission Service')),
            ('coolant_flush', _('Coolant Flush')),
            ('air_filter', _('Air Filter Replacement')),
            ('fuel_filter', _('Fuel Filter Replacement')),
            ('spark_plugs', _('Spark Plugs Replacement')),
            ('timing_belt', _('Timing Belt Replacement')),
            ('battery_replacement', _('Battery Replacement')),
            ('tire_rotation', _('Tire Rotation')),
            ('wheel_alignment', _('Wheel Alignment')),
            ('suspension_repair', _('Suspension Repair')),
            ('exhaust_repair', _('Exhaust System Repair')),
            ('ac_service', _('A/C Service')),
            ('diagnostics', _('Diagnostics')),
            ('other', _('Other')),
        ],
        blank=True,
        null=True
    )
    duration_minutes = models.PositiveIntegerField(
        _("Duration (minutes)"),
        null=True, blank=True,
        help_text=_("Estimated duration of this operation in minutes")
    )
    
    class Meta:
        verbose_name = _("Operation Compatibility")
        verbose_name_plural = _("Operation Compatibilities")
        unique_together = [
            ('item', 'operation_type', 'maintenance_schedule'),
        ]
        ordering = ['operation_type__name', 'item__name']
        
    def __str__(self):
        base_info = f"{self.item} - {self.operation_type}"
        if self.vehicle_compatibilities.exists():
            # Show count of compatible vehicles
            return f"{base_info} ({self.vehicle_compatibilities.count()} vehicles)"
        # Legacy display for old records
        vehicle_info = ""
        if self.vehicle_make:
            vehicle_info += f" - {self.vehicle_make}"
        if self.vehicle_model:
            vehicle_info += f" {self.vehicle_model}"
        if self.year_from:
            vehicle_info += f" ({self.year_from}"
            if self.year_to:
                vehicle_info += f"-{self.year_to}"
            vehicle_info += ")"
            
        return f"{base_info}{vehicle_info}"
    
    def vehicle_make_display(self):
        """For legacy data"""
        return self.vehicle_make.name if self.vehicle_make else "-"
    vehicle_make_display.short_description = _("Make")
    
    def vehicle_model_display(self):
        """For legacy data"""
        return self.vehicle_model.name if self.vehicle_model else "-"
    vehicle_model_display.short_description = _("Model")
    
    def duration_minutes_display(self):
        if self.duration_minutes:
            hours = self.duration_minutes // 60
            minutes = self.duration_minutes % 60
            if hours and minutes:
                return f"{hours}h {minutes}m"
            elif hours:
                return f"{hours}h"
            else:
                return f"{minutes}m"
        return "-"
    duration_minutes_display.short_description = _("Duration")
    
    def get_compatible_vehicles(self):
        """Return a list of vehicle makes/models this operation is compatible with"""
        return self.vehicle_compatibilities.all()
        
    def migrate_legacy_vehicle_data(self):
        """Migrate legacy vehicle data to the new structure"""
        if self.vehicle_make and not self.vehicle_compatibilities.filter(
            vehicle_make=self.vehicle_make,
            vehicle_model=self.vehicle_model,
            year_from=self.year_from,
            year_to=self.year_to
        ).exists():
            VehicleOperationCompatibility.objects.create(
                operation_compatibility=self,
                vehicle_make=self.vehicle_make,
                vehicle_model=self.vehicle_model,
                year_from=self.year_from,
                year_to=self.year_to
            )
            return True
        return False
    
    def is_compatible_with_vehicle(self, vehicle):
        """
        Check if this operation compatibility applies to the given vehicle
        
        Args:
            vehicle: Vehicle instance to check compatibility against
            
        Returns:
            bool: True if compatible, False otherwise
        """
        # First check new vehicle compatibilities
        for compat in self.vehicle_compatibilities.all():
            # Check make
            if vehicle.make and vehicle.make.id != compat.vehicle_make_id:
                continue
                
            # Check model if specified
            if compat.vehicle_model and vehicle.model and vehicle.model.id != compat.vehicle_model_id:
                continue
                
            # Check year range if specified
            if compat.year_from and vehicle.year and vehicle.year < compat.year_from:
                continue
                
            if compat.year_to and vehicle.year and vehicle.year > compat.year_to:
                continue
                
            # If we passed all checks, this vehicle is compatible
            return True
            
        # If no new compatibilities, check legacy fields
        # If no vehicle restrictions, compatible with all
        if not self.vehicle_make and not self.vehicle_model and not self.year_from:
            return True
            
        # Check make
        if self.vehicle_make and vehicle.make and vehicle.make.id != self.vehicle_make.id:
            return False
            
        # Check model
        if self.vehicle_model and vehicle.model and vehicle.model.id != self.vehicle_model.id:
            return False
            
        # Check year
        if self.year_from and vehicle.year and vehicle.year < self.year_from:
            return False
            
        if self.year_to and vehicle.year and vehicle.year > self.year_to:
            return False
            
        return True
        
    @classmethod
    def get_compatible_operations(cls, vehicle, item=None):
        """
        Get operation compatibilities that match the given vehicle and optionally item
        
        Args:
            vehicle: Vehicle instance to find compatibilities for
            item: Optional Item instance to filter by
            
        Returns:
            QuerySet of compatible OperationCompatibility instances
        """
        # Base query
        query = cls.objects.all()
        
        if item:
            query = query.filter(item=item)
        
        # Find through new vehicle compatibilities
        vehicle_compat_filter = models.Q(
            vehicle_compatibilities__vehicle_make=vehicle.make
        )
        
        # If model is provided, add to filter, including records without model constraint
        if vehicle.model:
            vehicle_compat_filter &= (
                models.Q(vehicle_compatibilities__vehicle_model__isnull=True) | 
                models.Q(vehicle_compatibilities__vehicle_model=vehicle.model)
            )
            
        # If year is provided, add to filter
        if vehicle.year:
            vehicle_compat_filter &= (
                models.Q(vehicle_compatibilities__year_from__isnull=True) |
                models.Q(vehicle_compatibilities__year_from__lte=vehicle.year)
            )
            vehicle_compat_filter &= (
                models.Q(vehicle_compatibilities__year_to__isnull=True) |
                models.Q(vehicle_compatibilities__year_to__gte=vehicle.year)
            )
        
        # Legacy filter
        legacy_filter = models.Q()
        if vehicle.make:
            legacy_filter &= (
                models.Q(vehicle_make__isnull=True) | 
                models.Q(vehicle_make=vehicle.make)
            )
            
        if vehicle.model:
            legacy_filter &= (
                models.Q(vehicle_model__isnull=True) | 
                models.Q(vehicle_model=vehicle.model)
            )
            
        if vehicle.year:
            legacy_filter &= (
                models.Q(year_from__isnull=True) |
                models.Q(year_from__lte=vehicle.year)
            )
            legacy_filter &= (
                models.Q(year_to__isnull=True) |
                models.Q(year_to__gte=vehicle.year)
            )
            
        # Combine new and legacy filters
        query = query.filter(vehicle_compat_filter | legacy_filter)
            
        return query.distinct()


class VehicleModelPart(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """
    Maps specific vehicle models/years to their compatible spare parts
    """
    make = models.CharField(_("Vehicle Make"), max_length=100, db_index=True)
    model = models.CharField(_("Vehicle Model"), max_length=100, db_index=True)
    year_from = models.PositiveIntegerField(_("Year From"), db_index=True)
    year_to = models.PositiveIntegerField(_("Year To"), null=True, blank=True, db_index=True)
    
    # Part/Item information
    item = models.ForeignKey(
        Item,
        on_delete=models.CASCADE,
        related_name="vehicle_models",
        verbose_name=_("Part/Item")
    )
    
    # Engine details
    engine_type = models.CharField(_("Engine Type"), max_length=100, blank=True)
    engine_displacement = models.CharField(_("Engine Displacement"), max_length=50, blank=True)
    transmission_type = models.CharField(_("Transmission Type"), max_length=50, blank=True)
    
    # Part details
    is_oem = models.BooleanField(_("Is OEM Part"), default=False,
                                help_text=_("Is this an Original Equipment Manufacturer part?"))
    fits_position = models.CharField(_("Position"), max_length=50, blank=True,
                                   help_text=_("Position on vehicle (e.g., front, rear, left, right)"))
    
    # Maintenance schedule link
    maintenance_schedule = models.ForeignKey(
        'work_orders.MaintenanceSchedule', 
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name="vehicle_parts",
        verbose_name=_("Maintenance Schedule")
    )
    
    notes = models.TextField(_("Notes"), blank=True)
    attributes = models.JSONField(_("Custom Attributes"), default=dict, blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Vehicle Model Part")
        verbose_name_plural = _("Vehicle Model Parts")
        ordering = ['make', 'model', 'year_from']
        unique_together = [['tenant_id', 'make', 'model', 'year_from', 'item', 'engine_type']]
        
    def __str__(self):
        year_range = f"{self.year_from}"
        if self.year_to:
            year_range += f"-{self.year_to}"
        return f"{self.item.name} - {self.make} {self.model} ({year_range})"
        
    def clean(self):
        """Validate year range"""
        if self.year_to and self.year_from > self.year_to:
            raise ValidationError(_("Year from must be less than or equal to year to"))
            
    @property
    def year_range_display(self):
        """Return a formatted year range string"""
        if self.year_to:
            return f"{self.year_from}-{self.year_to}"
        return f"{self.year_from}+"


class OperationPricing(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """
    Pricing model for operations on different vehicle makes/models
    """
    operation_type = models.ForeignKey(
        'work_orders.WorkOrderType',
        on_delete=models.CASCADE,
        related_name="operation_prices",
        verbose_name=_("Operation Type")
    )
    vehicle_make = models.ForeignKey(
        VehicleMake,
        on_delete=models.CASCADE,
        related_name="operation_prices",
        verbose_name=_("Vehicle Make")
    )
    vehicle_model = models.ForeignKey(
        VehicleModel,
        on_delete=models.CASCADE,
        null=True, blank=True, 
        related_name="operation_prices",
        verbose_name=_("Vehicle Model")
    )
    # Location-specific pricing
    franchise = models.ForeignKey(
        'setup.Franchise',
        on_delete=models.CASCADE,
        null=True, blank=True,
        related_name="operation_prices",
        verbose_name=_("Franchise")
    )
    company = models.ForeignKey(
        'setup.Company',
        on_delete=models.CASCADE,
        null=True, blank=True,
        related_name="operation_prices",
        verbose_name=_("Company")
    )
    service_center = models.ForeignKey(
        'setup.ServiceCenter',
        on_delete=models.CASCADE,
        null=True, blank=True,
        related_name="operation_prices",
        verbose_name=_("Service Center")
    )
    # Time range pricing
    year_from = models.PositiveIntegerField(
        _("Year From"),
        null=True, blank=True,
        help_text=_("Start year for this pricing")
    )
    year_to = models.PositiveIntegerField(
        _("Year To"),
        null=True, blank=True,
        help_text=_("End year for this pricing (leave blank for current)")
    )
    base_price = models.DecimalField(
        _("Base Price"), 
        max_digits=15, 
        decimal_places=2,
        help_text=_("Base price for this operation")
    )
    labor_hours = models.DecimalField(
        _("Labor Hours"),
        max_digits=8,
        decimal_places=2,
        default=0,
        help_text=_("Estimated labor hours required")
    )
    labor_rate = models.DecimalField(
        _("Labor Rate"),
        max_digits=15,
        decimal_places=2,
        default=0,
        help_text=_("Hourly labor rate")
    )
    is_active = models.BooleanField(_("Is Active"), default=True)
    notes = models.TextField(_("Notes"), blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Operation Pricing")
        verbose_name_plural = _("Operation Pricings")
        unique_together = [
            ['tenant_id', 'operation_type', 'vehicle_make', 'vehicle_model', 'year_from', 'year_to', 
             'franchise', 'company', 'service_center']
        ]
        ordering = ['operation_type__name', 'vehicle_make__name', 'vehicle_model__name']
        
    def __str__(self):
        model_str = f" - {self.vehicle_model}" if self.vehicle_model else ""
        year_str = f" ({self.year_from}"
        if self.year_to:
            year_str += f"-{self.year_to}"
        else:
            year_str += "-present"
        year_str += ")"
        
        return f"{self.operation_type.name} - {self.vehicle_make}{model_str}{year_str}"
    
    @property
    def total_price(self):
        """
        Calculate total price including labor
        """
        labor_cost = self.labor_hours * self.labor_rate
        return self.base_price + labor_cost
    
    def clean(self):
        """
        Validate year ranges and location hierarchy
        """
        if self.year_from and self.year_to and self.year_from > self.year_to:
            raise ValidationError(_("Year from cannot be greater than year to"))
            
        # Validate location hierarchy (can't have service center without company, etc.)
        if self.service_center and not self.company:
            self.company = self.service_center.company
            
        if self.company and not self.franchise and self.company.franchise:
            self.franchise = self.company.franchise
            
        # Can't specify multiple levels in hierarchy that don't match
        if self.service_center and self.company and self.service_center.company != self.company:
            raise ValidationError(_("Service center must belong to the specified company"))
            
        if self.company and self.franchise and self.company.franchise != self.franchise:
            raise ValidationError(_("Company must belong to the specified franchise"))
            
        super().clean()
        

class PartPricing(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """
    Pricing model for parts with operation and vehicle specific pricing
    """
    item = models.ForeignKey(
        Item,
        on_delete=models.CASCADE,
        related_name="part_prices",
        verbose_name=_("Part/Item")
    )
    # Location-specific pricing
    franchise = models.ForeignKey(
        'setup.Franchise',
        on_delete=models.CASCADE,
        null=True, blank=True,
        related_name="part_prices",
        verbose_name=_("Franchise")
    )
    company = models.ForeignKey(
        'setup.Company',
        on_delete=models.CASCADE,
        null=True, blank=True,
        related_name="part_prices",
        verbose_name=_("Company")
    )
    service_center = models.ForeignKey(
        'setup.ServiceCenter',
        on_delete=models.CASCADE,
        null=True, blank=True,
        related_name="part_prices",
        verbose_name=_("Service Center")
    )
    operation_type = models.ForeignKey(
        'work_orders.WorkOrderType',
        on_delete=models.CASCADE,
        related_name="part_prices",
        verbose_name=_("Operation Type"),
        null=True, blank=True,
        help_text=_("Optional - specific operation type pricing")
    )
    vehicle_make = models.ForeignKey(
        VehicleMake,
        on_delete=models.CASCADE,
        related_name="part_prices",
        verbose_name=_("Vehicle Make"),
        null=True, blank=True,
        help_text=_("Optional - specific vehicle make pricing")
    )
    vehicle_model = models.ForeignKey(
        VehicleModel,
        on_delete=models.CASCADE,
        null=True, blank=True, 
        related_name="part_prices",
        verbose_name=_("Vehicle Model"),
        help_text=_("Optional - specific vehicle model pricing")
    )
    price = models.DecimalField(
        _("Price"), 
        max_digits=15, 
        decimal_places=2
    )
    is_special_pricing = models.BooleanField(
        _("Special Pricing"), 
        default=False,
        help_text=_("Indicates if this is a special price (discount/promotion)")
    )
    valid_from = models.DateField(
        _("Valid From"),
        null=True, blank=True,
        help_text=_("Start date for this pricing")
    )
    valid_to = models.DateField(
        _("Valid To"),
        null=True, blank=True,
        help_text=_("End date for this pricing")
    )
    is_active = models.BooleanField(_("Is Active"), default=True)
    notes = models.TextField(_("Notes"), blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Part Pricing")
        verbose_name_plural = _("Part Pricings")
        ordering = ['item__name', '-valid_from']
        
    def __str__(self):
        operation_str = f" - {self.operation_type.name}" if self.operation_type else ""
        make_str = f" - {self.vehicle_make.name}" if self.vehicle_make else ""
        model_str = f" - {self.vehicle_model.name}" if self.vehicle_model else ""
        location_str = ""
        
        if self.service_center:
            location_str = f" @ {self.service_center.name}"
        elif self.company:
            location_str = f" @ {self.company.name}"
        elif self.franchise:
            location_str = f" @ {self.franchise.name}"
        
        return f"{self.item.name}{operation_str}{make_str}{model_str}{location_str}: {self.price}"
    
    def clean(self):
        """
        Validate date ranges and location hierarchy
        """
        if self.valid_from and self.valid_to and self.valid_from > self.valid_to:
            raise ValidationError(_("Valid from date cannot be after valid to date"))
            
        # Validate location hierarchy (can't have service center without company, etc.)
        if self.service_center and not self.company:
            self.company = self.service_center.company
            
        if self.company and not self.franchise and self.company.franchise:
            self.franchise = self.company.franchise
            
        # Can't specify multiple levels in hierarchy that don't match
        if self.service_center and self.company and self.service_center.company != self.company:
            raise ValidationError(_("Service center must belong to the specified company"))
            
        if self.company and self.franchise and self.company.franchise != self.franchise:
            raise ValidationError(_("Company must belong to the specified franchise"))
            
        super().clean()
    
    @classmethod
    def get_price_for_operation(cls, item, operation_type=None, vehicle=None, service_center=None, company=None, franchise=None):
        """
        Get the appropriate price for an item based on operation, vehicle, and location
        
        Searches in order of specificity:
        1. Most specific: Exact match for item + operation + vehicle + service center
        2. Company level: Exact match for item + operation + vehicle + company
        3. Franchise level: Exact match for item + operation + vehicle + franchise
        4. Location agnostic: Exact match for item + operation + vehicle
        5. Fallback to previous search logic if no location-specific pricing found
        
        Args:
            item: The Item object
            operation_type: Optional WorkOrderType object
            vehicle: Optional Vehicle object
            service_center: Optional ServiceCenter object
            company: Optional Company object
            franchise: Optional Franchise object
            
        Returns:
            Decimal: The appropriate price
        """
        # Start with most specific query
        queryset = cls.objects.filter(item=item, is_active=True)
        
        # Add date filtering
        today = timezone.now().date()
        date_filter = (
            (models.Q(valid_from__isnull=True) | models.Q(valid_from__lte=today)) &
            (models.Q(valid_to__isnull=True) | models.Q(valid_to__gte=today))
        )
        queryset = queryset.filter(date_filter)
        
        # Try most specific match by location first if provided
        if service_center:
            company = service_center.company
            franchise = company.franchise if company else None

        # Build location filters from most specific to least specific
        if service_center and vehicle and operation_type:
            # Most specific: service center + operation + vehicle
            price = queryset.filter(
                service_center=service_center,
                operation_type=operation_type,
                vehicle_make=vehicle.make,
                vehicle_model=vehicle.model
            ).first()
            if price:
                return price.price
                
        if company and vehicle and operation_type:
            # Company level: company + operation + vehicle
            price = queryset.filter(
                company=company,
                service_center__isnull=True,
                operation_type=operation_type,
                vehicle_make=vehicle.make,
                vehicle_model=vehicle.model
            ).first()
            if price:
                return price.price
                
        if franchise and vehicle and operation_type:
            # Franchise level: franchise + operation + vehicle
            price = queryset.filter(
                franchise=franchise,
                company__isnull=True,
                service_center__isnull=True,
                operation_type=operation_type,
                vehicle_make=vehicle.make,
                vehicle_model=vehicle.model
            ).first()
            if price:
                return price.price
        
        # Try location-specific but less specific on other dimensions
        if service_center:
            # Just service center and item
            price = queryset.filter(
                service_center=service_center,
                operation_type__isnull=True,
                vehicle_make__isnull=True
            ).first()
            if price:
                return price.price
                
        if company:
            # Just company and item
            price = queryset.filter(
                company=company,
                service_center__isnull=True,
                operation_type__isnull=True,
                vehicle_make__isnull=True
            ).first()
            if price:
                return price.price
                
        if franchise:
            # Just franchise and item
            price = queryset.filter(
                franchise=franchise,
                company__isnull=True,
                service_center__isnull=True,
                operation_type__isnull=True,
                vehicle_make__isnull=True
            ).first()
            if price:
                return price.price
                
        # Fall back to the existing search logic if no location-specific pricing found
        # Try most specific match first
        if operation_type and vehicle:
            # Try exact operation + make + model
            price = queryset.filter(
                operation_type=operation_type,
                vehicle_make=vehicle.make,
                vehicle_model=vehicle.model
            ).first()
            if price:
                return price.price
                
            # Try operation + make (any model)
            price = queryset.filter(
                operation_type=operation_type,
                vehicle_make=vehicle.make,
                vehicle_model__isnull=True
            ).first()
            if price:
                return price.price
                
        # ... [rest of the existing method remains unchanged] ...
        
        # If no special price found, return item's unit price
        return item.unit_price
