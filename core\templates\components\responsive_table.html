{% load i18n %}

{# 
  Responsive table component using Flowbite
  
  Parameters:
  - headers: List of table headers
  - rows: List of rows, each containing a list of cells
  - id: Optional table ID for JavaScript targeting
  - classes: Optional additional classes for the table
  - responsive_breakpoint: Optional breakpoint for responsive behavior (default: md)
  - actions: Optional dictionary with action buttons configuration
  - empty_message: Optional message to display when there are no rows
#}

<div class="relative overflow-x-auto shadow-md sm:rounded-lg">
  {% if rows %}
    <table id="{{ id|default:'responsive-table' }}" class="w-full text-sm text-left text-gray-500 dark:text-gray-400 {{ classes|default:'' }}">
      <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
        <tr>
          {% for header in headers %}
            <th scope="col" class="px-6 py-3">
              {{ header }}
            </th>
          {% endfor %}
          {% if actions %}
            <th scope="col" class="px-6 py-3">
              <span class="sr-only">{% trans "Actions" %}</span>
            </th>
          {% endif %}
        </tr>
      </thead>
      <tbody>
        {% for row in rows %}
          <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
            {% for cell in row.cells %}
              {% if forloop.first %}
                <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                  {{ cell }}
                </th>
              {% else %}
                <td class="px-6 py-4 {% if cell.highlight %}font-medium {% if cell.highlight_color %}text-{{ cell.highlight_color }}-500{% else %}text-blue-500{% endif %}{% endif %}">
                  {% if cell.type == 'status' %}
                    <span class="px-2 py-1 text-xs font-medium rounded-full {% if cell.status_color %}bg-{{ cell.status_color }}-100 text-{{ cell.status_color }}-800{% else %}bg-blue-100 text-blue-800{% endif %}">
                      {{ cell.value }}
                    </span>
                  {% elif cell.type == 'badge' %}
                    <span class="bg-{{ cell.badge_color|default:'blue' }}-100 text-{{ cell.badge_color|default:'blue' }}-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-{{ cell.badge_color|default:'blue' }}-900 dark:text-{{ cell.badge_color|default:'blue' }}-300">
                      {{ cell.value }}
                    </span>
                  {% elif cell.type == 'datetime' %}
                    <span title="{{ cell.value|date:'r' }}">{{ cell.value|date:"SHORT_DATE_FORMAT" }}</span>
                  {% elif cell.type == 'boolean' %}
                    {% if cell.value %}
                      <svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                      </svg>
                    {% else %}
                      <svg class="w-5 h-5 text-red-500" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                      </svg>
                    {% endif %}
                  {% else %}
                    {{ cell.value|default:cell }}
                  {% endif %}
                </td>
              {% endif %}
            {% endfor %}
            {% if actions %}
              <td class="px-6 py-4 text-right">
                <div class="flex items-center space-x-2 justify-end">
                  {% for action in row.actions %}
                    {% if action.type == 'link' %}
                      <a href="{{ action.url }}" class="font-medium text-{{ action.color|default:'blue' }}-600 dark:text-{{ action.color|default:'blue' }}-500 hover:underline">
                        {% if action.icon %}
                          <span class="{{ action.icon }} mr-1"></span>
                        {% endif %}
                        {{ action.label }}
                      </a>
                    {% elif action.type == 'button' %}
                      <button 
                        type="button" 
                        class="font-medium text-{{ action.color|default:'blue' }}-600 dark:text-{{ action.color|default:'blue' }}-500 hover:underline"
                        {% if action.id %}id="{{ action.id }}"{% endif %}
                        {% if action.data %}
                          {% for key, value in action.data.items %}
                            data-{{ key }}="{{ value }}"
                          {% endfor %}
                        {% endif %}
                        {% if action.click %}onclick="{{ action.click }}"{% endif %}
                      >
                        {% if action.icon %}
                          <span class="{{ action.icon }} mr-1"></span>
                        {% endif %}
                        {{ action.label }}
                      </button>
                    {% elif action.type == 'dropdown' %}
                      <div class="relative inline-block text-left">
                        <button type="button" 
                                class="inline-flex items-center text-gray-500 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 font-medium rounded-lg text-sm px-3 py-1.5 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700"
                                id="dropdown-{{ row.id }}"
                                data-dropdown-toggle="dropdown-menu-{{ row.id }}">
                          {% trans "Actions" %}
                          <svg class="w-3 h-3 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                          </svg>
                        </button>
                        <div class="hidden absolute right-0 z-10 w-44 bg-white rounded divide-y divide-gray-100 shadow dark:bg-gray-700 dark:divide-gray-600" id="dropdown-menu-{{ row.id }}">
                          <ul class="py-1 text-sm text-gray-700 dark:text-gray-200" aria-labelledby="dropdown-{{ row.id }}">
                            {% for item in action.items %}
                              <li>
                                <a href="{{ item.url }}" class="block py-2 px-4 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                  {{ item.label }}
                                </a>
                              </li>
                            {% endfor %}
                          </ul>
                        </div>
                      </div>
                    {% endif %}
                  {% endfor %}
                </div>
              </td>
            {% endif %}
          </tr>
        {% endfor %}
      </tbody>
    </table>
  {% else %}
    <div class="flex items-center justify-center p-6 text-gray-500 dark:text-gray-400">
      <p>{{ empty_message|default:_("No items found.") }}</p>
    </div>
  {% endif %}
</div> 