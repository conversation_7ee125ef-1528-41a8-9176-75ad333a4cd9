#!/usr/bin/env python
"""
Check user role counts and display information about created user roles.
"""
import os
import sys
import django

# Add the parent directory to sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

from django.contrib.auth.models import User
from user_roles.models import Role, UserRole, ModulePermission
from setup.models import ServiceCenter, Franchise, Company

def check_user_roles():
    """Display information about user roles in the database."""
    total_roles = UserRole.objects.count()
    system_admin_roles = UserRole.objects.filter(role__code='system_admin').count()
    service_center_roles = UserRole.objects.filter(service_center__isnull=False).count()
    franchise_roles = UserRole.objects.filter(franchise__isnull=False).count()
    company_roles = UserRole.objects.filter(company__isnull=False).count()
    
    active_roles = UserRole.objects.filter(is_active=True).count()
    inactive_roles = UserRole.objects.filter(is_active=False).count()
    primary_roles = UserRole.objects.filter(is_primary=True).count()
    
    print("===== User Roles in Database =====")
    print(f"Total user roles: {total_roles}")
    print(f"System admin roles: {system_admin_roles}")
    print(f"Service center roles: {service_center_roles}")
    print(f"Franchise roles: {franchise_roles}")
    print(f"Company roles: {company_roles}")
    print(f"Active roles: {active_roles}")
    print(f"Inactive roles: {inactive_roles}")
    print(f"Primary roles: {primary_roles}")
    
    # Get tenant breakdowns
    print("\n===== Roles by Tenant =====")
    tenant_ids = UserRole.objects.values_list('tenant_id', flat=True).distinct()
    for tenant_id in tenant_ids:
        roles_count = UserRole.objects.filter(tenant_id=tenant_id).count()
        print(f"Tenant {tenant_id}: {roles_count} roles")
    
    # Get role type breakdowns
    print("\n===== Roles by Type =====")
    role_codes = Role.objects.values_list('code', flat=True)
    for code in role_codes:
        count = UserRole.objects.filter(role__code=code).count()
        if count > 0:
            print(f"Role '{code}': {count} assignments")
    
    # Get user breakdowns
    print("\n===== Roles per User =====")
    users = User.objects.all()
    for user in users:
        roles_count = UserRole.objects.filter(user=user).count()
        print(f"User '{user.username}': {roles_count} roles")
    
    return total_roles

if __name__ == "__main__":
    check_user_roles() 