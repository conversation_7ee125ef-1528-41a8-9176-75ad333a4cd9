# Generated by Django 4.2.20 on 2025-05-07 10:01

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('inventory', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='PurchaseOrder',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('order_number', models.CharField(max_length=50, unique=True, verbose_name='PO Number')),
                ('order_date', models.DateField(verbose_name='Order Date')),
                ('expected_delivery_date', models.DateField(blank=True, null=True, verbose_name='Expected Delivery Date')),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('sent', 'Sent'), ('confirmed', 'Confirmed'), ('received', 'Received'), ('cancelled', 'Cancelled')], default='draft', max_length=20, verbose_name='Status')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('total_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Total Amount')),
            ],
            options={
                'verbose_name': 'Purchase Order',
                'verbose_name_plural': 'Purchase Orders',
                'ordering': ['-order_date'],
            },
        ),
        migrations.CreateModel(
            name='PurchaseOrderItem',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('quantity', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Quantity')),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Unit Price')),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='purchase_items', to='inventory.item', verbose_name='Item')),
                ('purchase_order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='purchases.purchaseorder', verbose_name='Purchase Order')),
            ],
            options={
                'verbose_name': 'Purchase Order Item',
                'verbose_name_plural': 'Purchase Order Items',
                'unique_together': {('purchase_order', 'item')},
            },
        ),
        migrations.CreateModel(
            name='PurchaseReceipt',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('receipt_number', models.CharField(max_length=50, unique=True, verbose_name='Receipt Number')),
                ('receipt_date', models.DateField(verbose_name='Receipt Date')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('purchase_order', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='receipts', to='purchases.purchaseorder', verbose_name='Purchase Order')),
            ],
            options={
                'verbose_name': 'Purchase Receipt',
                'verbose_name_plural': 'Purchase Receipts',
                'ordering': ['-receipt_date'],
            },
        ),
        migrations.CreateModel(
            name='Supplier',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('name', models.CharField(max_length=255, verbose_name='Name')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='Email')),
                ('phone', models.CharField(blank=True, max_length=50, verbose_name='Phone')),
                ('address', models.TextField(blank=True, verbose_name='Address')),
                ('is_active', models.BooleanField(default=True, verbose_name='Active')),
            ],
            options={
                'verbose_name': 'Supplier',
                'verbose_name_plural': 'Suppliers',
            },
        ),
        migrations.CreateModel(
            name='PurchaseReceiptItem',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('quantity', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Quantity Received')),
                ('purchase_order_item', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='receipts', to='purchases.purchaseorderitem', verbose_name='Purchase Order Item')),
                ('purchase_receipt', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='purchases.purchasereceipt', verbose_name='Purchase Receipt')),
            ],
            options={
                'verbose_name': 'Purchase Receipt Item',
                'verbose_name_plural': 'Purchase Receipt Items',
            },
        ),
        migrations.AddField(
            model_name='purchaseorder',
            name='supplier',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='purchase_orders', to='purchases.supplier', verbose_name='Supplier'),
        ),
    ]
