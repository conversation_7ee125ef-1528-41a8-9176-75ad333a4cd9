@echo off
echo ===============================================
echo ID-Preserving SQLite to PostgreSQL Migration
echo ===============================================
echo.

REM Check if virtual environment is activated
python -c "import sys; print('Virtual environment:', hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix))" 2>nul
if errorlevel 1 (
    echo ERROR: Python is not available or virtual environment is not activated
    echo Please activate your virtual environment first:
    echo   env\Scripts\activate
    pause
    exit /b 1
)

REM Check if Django is available
python -c "import django" 2>nul
if errorlevel 1 (
    echo ERROR: Django is not installed in the current environment
    echo Please install requirements: pip install -r requirements.txt
    pause
    exit /b 1
)

echo Current directory: %CD%
echo.

REM Check if db.sqlite3 exists
if not exist "db.sqlite3" (
    echo ERROR: SQLite database file 'db.sqlite3' not found in current directory
    echo Please make sure you're running this from the Django project root
    pause
    exit /b 1
)

echo Found SQLite database: db.sqlite3
echo File size: 
for %%I in (db.sqlite3) do echo   %%~zI bytes
echo.

echo IMPORTANT: This script will:
echo  1. Clear ALL existing data in PostgreSQL
echo  2. Preserve original primary keys from SQLite
echo  3. Disable foreign key constraints during migration
echo  4. Migrate all data while maintaining relationships
echo.

REM Prompt for dry run first
set /p choice="Do you want to run a DRY RUN first to see what would be migrated? (Y/N): "
if /i "%choice%"=="Y" (
    echo.
    echo ========================================
    echo Running DRY RUN - No data will be moved
    echo ========================================
    python migrate_data_fixed_ids.py --dry-run --sqlite-path "db.sqlite3"
    echo.
    echo DRY RUN completed. Review the output above.
    echo.
    set /p choice2="Do you want to proceed with the actual migration? (Y/N): "
    if /i not "%choice2%"=="Y" (
        echo Migration cancelled by user.
        pause
        exit /b 0
    )
)

echo.
echo ==========================================
echo Starting ACTUAL migration with ID preservation
echo ==========================================
echo.

REM Run the actual migration
python migrate_data_fixed_ids.py --sqlite-path "db.sqlite3"

echo.
echo ==========================================
echo Migration process completed!
echo ==========================================

if errorlevel 1 (
    echo.
    echo ERROR: Migration failed. Please check the error messages above.
    echo Common solutions:
    echo  1. Ensure PostgreSQL database is accessible
    echo  2. Check database connection settings in settings.py  
    echo  3. Verify Django migrations are up to date
    echo  4. Check that all required tables exist in PostgreSQL
) else (
    echo.
    echo SUCCESS: Data migration completed successfully!
    echo.
    echo NEXT STEPS:
    echo  1. Test your Django application with the migrated data
    echo  2. Verify that foreign key relationships are preserved
    echo  3. Check that record IDs match between related tables
    echo  4. Create a backup of your PostgreSQL database
    echo  5. Update any hardcoded IDs in your application if needed
)

echo.
pause 