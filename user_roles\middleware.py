import logging
from django.conf import settings
from django.shortcuts import redirect
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.urls import resolve, reverse
from django.http import HttpResponseForbidden
from functools import wraps

logger = logging.getLogger(__name__)


def role_required(required_permission):
    """
    Decorator for views that checks if a user has the required permission (module flag).
    Assumes the user is already authenticated.
    """
    def decorator(view_func):
        @wraps(view_func)
        def _wrapped_view(request, *args, **kwargs):
            # Superusers have all permissions
            if request.user.is_superuser:
                return view_func(request, *args, **kwargs)

            # Get the user's primary role
            primary_role = None
            if hasattr(request.user, 'user_roles'):
                user_roles = request.user.user_roles.filter(is_active=True)
                for user_role_instance in user_roles:
                    if user_role_instance.is_primary:
                        primary_role = user_role_instance.role
                        break
                if not primary_role and user_roles.exists():
                    primary_role = user_roles.first().role
            
            if primary_role:
                if hasattr(primary_role, required_permission) and getattr(primary_role, required_permission):
                    return view_func(request, *args, **kwargs)
                else:
                    logger.warning(
                        f"User {request.user.username} with role {primary_role.name} "
                        f"attempted to access view requiring permission '{required_permission}' without it."
                    )
                    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                        return HttpResponseForbidden(_("You don't have permission to perform this action."))
                    messages.warning(request, _("You don't have permission to access this page."))
                    return redirect(request.META.get('HTTP_REFERER', '/')) # Redirect to referrer or home
            else:
                logger.warning(
                    f"User {request.user.username} without a role attempted to access view "
                    f"requiring permission '{required_permission}'."
                )
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return HttpResponseForbidden(_("You need a role to access this page."))
                messages.warning(request, _("You need to be assigned a role to access this page."))
                return redirect('/')

        return _wrapped_view
    return decorator


class RoleBasedAccessMiddleware:
    """
    Middleware to enforce role-based access control for URLs
    """
    def __init__(self, get_response):
        self.get_response = get_response
        # Map URL namespaces to required module access flags
        self.module_map = {
            'setup': 'can_access_setup',
            'work_orders': 'can_access_work_orders',
            'inventory': 'can_access_inventory',
            'warehouse': 'can_access_warehouse',
            'sales': 'can_access_sales',
            'purchases': 'can_access_purchases',
            'reports': 'can_access_reports',
            'app_settings': 'can_access_settings',
            'franchise_setup': 'can_access_setup', # Franchise setup also uses 'can_access_setup'
        }
        
        # Public URLs that don't require authentication
        self.public_urls = [
            reverse('login'),
            reverse('logout'),
            '/static/',
            '/media/',
            '/favicon.ico',
            '/i18n/setlang/',
        ]
        
        # Admin URLs that bypass this middleware
        self.admin_urls = [
            '/dzJAMvwB/',  # Admin URL
        ]

    def __call__(self, request):
        # Skip middleware check for public and admin URLs
        if any(request.path.startswith(url) for url in self.public_urls + self.admin_urls):
            return self.get_response(request)
        
        # Skip for unauthenticated users (let Django's auth middleware handle this)
        if not request.user.is_authenticated:
            return self.get_response(request)
        
        # Get the URL namespace
        try:
            resolved = resolve(request.path)
            namespace = resolved.namespace
        except:
            namespace = None
        
        # If namespace is in our module map, check if the user has the required role
        if namespace in self.module_map:
            module_flag = self.module_map[namespace]
            
            # Get the user's primary role
            primary_role_obj = None # Renamed to avoid conflict with role model
            if hasattr(request.user, 'user_roles'):
                user_roles = request.user.user_roles.filter(is_active=True)
                for user_role_instance in user_roles: # Iterate over UserRole instances
                    if user_role_instance.is_primary:
                        primary_role_obj = user_role_instance.role # Access the related Role instance
                        break
                    
                if not primary_role_obj and user_roles.exists():
                    primary_role_obj = user_roles.first().role
            
            # If the user has a primary role, check if they can access this module
            if primary_role_obj:
                if not getattr(primary_role_obj, module_flag, False):
                    logger.warning(f"User {request.user.username} with role {primary_role_obj.name} attempted to access {namespace} without permission")
                    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                        return HttpResponseForbidden(_("You don't have permission to access this module"))
                    
                    messages.warning(request, _("You don't have permission to access the {module} module").format(module=namespace))
                    return redirect('/')
            
            # If the user doesn't have a primary role, check if they're a superuser
            elif not request.user.is_superuser:
                logger.warning(f"User {request.user.username} without a role attempted to access {namespace}")
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return HttpResponseForbidden(_("You don't have permission to access this module"))
                
                messages.warning(request, _("You need to be assigned a role to access the {module} module").format(module=namespace))
                return redirect('/')
        
        response = self.get_response(request)
        return response 