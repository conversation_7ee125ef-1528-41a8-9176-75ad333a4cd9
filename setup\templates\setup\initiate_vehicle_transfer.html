{% extends "base.html" %}
{% load i18n static %}

{% block title %}{{ title }}{% endblock %}

{% block extrahead %}
<style>
    .vehicle-list {
        margin-bottom: 20px;
    }
    .vehicle-item {
        padding: 10px;
        border-bottom: 1px solid #eee;
    }
    .vehicle-item:last-child {
        border-bottom: none;
    }
    .vehicle-details {
        display: flex;
        flex-wrap: wrap;
    }
    .vehicle-detail {
        margin-right: 20px;
        margin-bottom: 5px;
    }
    .vehicle-detail strong {
        margin-right: 5px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <h1>{{ title }}</h1>
    
    {% if not vehicles %}
        <div class="alert alert-warning">
            {% trans "No vehicles selected for transfer. Please select vehicles from the vehicle list." %}
        </div>
        <p>
            <a href="{% url 'admin:setup_vehicle_changelist' %}" class="btn btn-primary">
                {% trans "Go to Vehicle List" %}
            </a>
        </p>
    {% else %}
        <h3>{% trans "Selected Vehicles" %}</h3>
        <div class="vehicle-list card">
            {% for vehicle in vehicles %}
                <div class="vehicle-item">
                    <h5>{{ vehicle.make }} {{ vehicle.model }} ({{ vehicle.year }})</h5>
                    <div class="vehicle-details">
                        <div class="vehicle-detail">
                            <strong>{% trans "License Plate:" %}</strong>
                            {{ vehicle.license_plate }}
                        </div>
                        <div class="vehicle-detail">
                            <strong>{% trans "VIN:" %}</strong>
                            {{ vehicle.vin|default:"-" }}
                        </div>
                        <div class="vehicle-detail">
                            <strong>{% trans "Current Owner:" %}</strong>
                            {{ vehicle.owner|default:"No owner" }}
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
        
        <form method="post" class="card p-4">
            {% csrf_token %}
            
            <div class="alert alert-info">
                {% trans "Complete the form below to initiate the ownership transfer for the selected vehicles." %}
            </div>
            
            {% if form.errors %}
                <div class="alert alert-danger">
                    {% trans "Please correct the errors below." %}
                </div>
            {% endif %}
            
            <div class="form-group mb-3">
                <label for="{{ form.new_owner.id_for_label }}">
                    {{ form.new_owner.label }}
                    {% if form.new_owner.field.required %}*{% endif %}
                </label>
                {{ form.new_owner }}
                {% if form.new_owner.help_text %}
                    <small class="form-text text-muted">{{ form.new_owner.help_text }}</small>
                {% endif %}
                {% if form.new_owner.errors %}
                    <div class="invalid-feedback d-block">{{ form.new_owner.errors }}</div>
                {% endif %}
            </div>
            
            <div class="form-group mb-3">
                <label for="{{ form.transfer_date.id_for_label }}">
                    {{ form.transfer_date.label }}
                    {% if form.transfer_date.field.required %}*{% endif %}
                </label>
                {{ form.transfer_date }}
                {% if form.transfer_date.help_text %}
                    <small class="form-text text-muted">{{ form.transfer_date.help_text }}</small>
                {% endif %}
                {% if form.transfer_date.errors %}
                    <div class="invalid-feedback d-block">{{ form.transfer_date.errors }}</div>
                {% endif %}
            </div>
            
            <div class="form-group mb-3">
                <label for="{{ form.sale_price.id_for_label }}">
                    {{ form.sale_price.label }}
                    {% if form.sale_price.field.required %}*{% endif %}
                </label>
                {{ form.sale_price }}
                {% if form.sale_price.help_text %}
                    <small class="form-text text-muted">{{ form.sale_price.help_text }}</small>
                {% endif %}
                {% if form.sale_price.errors %}
                    <div class="invalid-feedback d-block">{{ form.sale_price.errors }}</div>
                {% endif %}
            </div>
            
            <div class="form-group mb-3">
                <label for="{{ form.odometer_reading.id_for_label }}">
                    {{ form.odometer_reading.label }}
                    {% if form.odometer_reading.field.required %}*{% endif %}
                </label>
                {{ form.odometer_reading }}
                {% if form.odometer_reading.help_text %}
                    <small class="form-text text-muted">{{ form.odometer_reading.help_text }}</small>
                {% endif %}
                {% if form.odometer_reading.errors %}
                    <div class="invalid-feedback d-block">{{ form.odometer_reading.errors }}</div>
                {% endif %}
            </div>
            
            <div class="form-group mb-3">
                <label for="{{ form.notes.id_for_label }}">
                    {{ form.notes.label }}
                    {% if form.notes.field.required %}*{% endif %}
                </label>
                {{ form.notes }}
                {% if form.notes.help_text %}
                    <small class="form-text text-muted">{{ form.notes.help_text }}</small>
                {% endif %}
                {% if form.notes.errors %}
                    <div class="invalid-feedback d-block">{{ form.notes.errors }}</div>
                {% endif %}
            </div>
            
            <div class="form-group mt-4">
                <button type="submit" class="btn btn-primary">
                    {% trans "Initiate Transfer" %}
                </button>
                <a href="{% url 'admin:setup_vehicle_changelist' %}" class="btn btn-secondary">
                    {% trans "Cancel" %}
                </a>
            </div>
        </form>
    {% endif %}
</div>
{% endblock %} 