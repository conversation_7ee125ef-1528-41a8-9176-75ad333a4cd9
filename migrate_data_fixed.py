#!/usr/bin/env python3
"""
Improved Data Migration Script: SQLite to PostgreSQL
==================================================

This script migrates all data from SQLite database to PostgreSQL database
with proper handling of table relationships, foreign keys, and timezone issues.

Usage:
    python migrate_data_fixed.py [--dry-run] [--sqlite-path path/to/db.sqlite3]
"""

import os
import sys
import django
from pathlib import Path
import logging

# Add the project root to Python path
BASE_DIR = Path(__file__).resolve().parent
sys.path.insert(0, str(BASE_DIR))

# Set up Django with proper timezone handling
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
os.environ.setdefault('TZ', 'Africa/Cairo')  # Set timezone before Django setup
django.setup()

from django.db import connections, transaction
from django.apps import apps
from django.conf import settings
from django.core.management import call_command
from django.db.models import ForeignKey, OneToOneField, ManyToManyField
from django.db.models.fields.related import RelatedField
import argparse
from collections import defaultdict, deque

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(message)s')
logger = logging.getLogger(__name__)


class ImprovedDataMigrator:
    def __init__(self, sqlite_path='db.sqlite3', dry_run=False):
        self.sqlite_path = sqlite_path
        self.dry_run = dry_run
        self.exclude_apps = [
            'contenttypes', 'auth', 'sessions', 'admin', 'messages',
            'django_otp', 'waffle', 'drf_api_logger', 'authtoken'
        ]
        # Models that should be migrated first (no dependencies)
        self.priority_models = [
            'setup.Company', 'setup.Franchise', 'setup.ServiceLevel',
            'setup.ServiceCenterType', 'setup.VehicleMake', 'setup.VehicleModel',
            'user_roles.Role', 'feature_flags.ModuleFlag', 'feature_flags.FeatureFlag',
            'inventory.ItemClassification', 'inventory.UnitOfMeasurement',
            'inventory.MovementType', 'inventory.InventoryValuationMethod',
            'warehouse.LocationType', 'billing.CustomerClassification',
            'billing.DiscountType', 'billing.PaymentMethod', 'billing.WarrantyType',
            'billing.InsuranceCompany', 'notifications.NotificationType',
            'work_orders.WorkOrderType', 'purchases.Supplier'
        ]
        
    def migrate(self):
        """Main migration method with improved error handling"""
        if not os.path.exists(self.sqlite_path):
            logger.error(f"❌ SQLite file not found: {self.sqlite_path}")
            return False
            
        logger.info(f"🚀 Starting migration from {self.sqlite_path} to PostgreSQL")
        
        if self.dry_run:
            logger.info("⚠️  DRY RUN MODE - No data will be actually migrated")
        
        try:
            # Setup database connections with proper timezone handling
            self._setup_connections()
            
            # Get all models to migrate
            models_to_migrate = self._get_models_to_migrate()
            sorted_models = self._sort_models_by_dependencies_advanced(models_to_migrate)
            
            total_records = 0
            migrated_records = 0
            failed_models = []
            
            logger.info(f"📋 Found {len(sorted_models)} models to migrate")
            
            # Migrate each model
            for model in sorted_models:
                try:
                    count = self._migrate_model_safe(model)
                    if count is not None:
                        total_records += count
                        if not self.dry_run:
                            migrated_records += count
                    else:
                        failed_models.append(model.__name__)
                except Exception as e:
                    logger.error(f"❌ Critical error migrating {model.__name__}: {e}")
                    failed_models.append(model.__name__)
                    continue
            
            # Report results
            if failed_models:
                logger.warning(f"⚠️  Failed to migrate {len(failed_models)} models: {', '.join(failed_models)}")
            
            if self.dry_run:
                logger.info(f"✅ DRY RUN COMPLETE: Would migrate {total_records} total records")
            else:
                logger.info(f"✅ Migration completed: {migrated_records}/{total_records} records migrated")
                
            return len(failed_models) == 0  # Success if no failures
            
        except Exception as e:
            logger.error(f"❌ Migration failed: {e}")
            import traceback
            traceback.print_exc()
            return False
        finally:
            self._cleanup_connections()
    
    def _setup_connections(self):
        """Setup database connections with proper timezone handling"""
        # Ensure timezone is set correctly
        import time
        os.environ['TZ'] = 'Africa/Cairo'
        time.tzset() if hasattr(time, 'tzset') else None
        
        # Setup SQLite connection with all required Django database settings
        sqlite_config = {
            'ENGINE': 'django.db.backends.sqlite3',
            'NAME': self.sqlite_path,
            'TIME_ZONE': 'Africa/Cairo',  # Add TIME_ZONE to match main settings
            'OPTIONS': {
                'timeout': 30,
            },
            'USER': '',
            'PASSWORD': '',
            'HOST': '',
            'PORT': '',
        }
        settings.DATABASES['sqlite'] = sqlite_config
        connections.databases['sqlite'] = sqlite_config
        
        # Test connections
        try:
            sqlite_conn = connections['sqlite']
            with sqlite_conn.cursor() as cursor:
                cursor.execute("SELECT 1")
            logger.info("✅ SQLite connection established")
        except Exception as e:
            raise Exception(f"Failed to connect to SQLite: {e}")
            
        try:
            pg_conn = connections['default']
            with pg_conn.cursor() as cursor:
                cursor.execute("SELECT 1")
            logger.info("✅ PostgreSQL connection established")
        except Exception as e:
            raise Exception(f"Failed to connect to PostgreSQL: {e}")
    
    def _cleanup_connections(self):
        """Clean up database connections"""
        if 'sqlite' in settings.DATABASES:
            del settings.DATABASES['sqlite']
        if 'sqlite' in connections.databases:
            del connections.databases['sqlite']
        connections.close_all()
    
    def _get_models_to_migrate(self):
        """Get all Django models that should be migrated"""
        models = []
        for app_config in apps.get_app_configs():
            if app_config.label not in self.exclude_apps:
                for model in app_config.get_models():
                    # Skip abstract models and proxy models
                    if not model._meta.abstract and not model._meta.proxy:
                        models.append(model)
        return models
    
    def _sort_models_by_dependencies_advanced(self, models):
        """Advanced dependency-aware sorting of models"""
        # Create model lookup
        model_lookup = {f"{model._meta.app_label}.{model.__name__}": model for model in models}
        
        # Build dependency graph
        dependencies = defaultdict(set)
        reverse_dependencies = defaultdict(set)
        
        for model in models:
            model_key = f"{model._meta.app_label}.{model.__name__}"
            
            for field in model._meta.get_fields():
                if isinstance(field, (ForeignKey, OneToOneField)):
                    related_model = field.related_model
                    if related_model and related_model in models:
                        related_key = f"{related_model._meta.app_label}.{related_model.__name__}"
                        if related_key != model_key:  # Avoid self-references
                            dependencies[model_key].add(related_key)
                            reverse_dependencies[related_key].add(model_key)
        
        # Topological sort with priority handling
        sorted_models = []
        remaining_models = set(model_lookup.keys())
        
        # First, add priority models that have no dependencies
        for priority_model in self.priority_models:
            if priority_model in remaining_models and not dependencies[priority_model]:
                sorted_models.append(model_lookup[priority_model])
                remaining_models.remove(priority_model)
                self._remove_from_dependencies(priority_model, dependencies, reverse_dependencies)
        
        # Then continue with regular topological sort
        max_iterations = len(remaining_models) * 2
        iteration = 0
        
        while remaining_models and iteration < max_iterations:
            iteration += 1
            models_added = []
            
            for model_key in list(remaining_models):
                if not dependencies[model_key]:  # No unresolved dependencies
                    models_added.append(model_key)
            
            if models_added:
                for model_key in models_added:
                    sorted_models.append(model_lookup[model_key])
                    remaining_models.remove(model_key)
                    self._remove_from_dependencies(model_key, dependencies, reverse_dependencies)
            else:
                # Break circular dependencies by adding a model with minimal dependencies
                if remaining_models:
                    min_deps_model = min(remaining_models, key=lambda x: len(dependencies[x]))
                    logger.warning(f"⚠️  Breaking circular dependency for {min_deps_model}")
                    sorted_models.append(model_lookup[min_deps_model])
                    remaining_models.remove(min_deps_model)
                    self._remove_from_dependencies(min_deps_model, dependencies, reverse_dependencies)
        
        logger.info(f"📊 Model migration order determined for {len(sorted_models)} models")
        return sorted_models
    
    def _remove_from_dependencies(self, model_key, dependencies, reverse_dependencies):
        """Remove a model from dependency tracking"""
        # Remove this model from all dependency lists
        for dependent in reverse_dependencies[model_key]:
            dependencies[dependent].discard(model_key)
        
        # Clear its own dependencies
        dependencies[model_key].clear()
        reverse_dependencies[model_key].clear()
    
    def _migrate_model_safe(self, model):
        """Migrate a single model's data with enhanced error handling"""
        model_name = f"{model._meta.app_label}.{model.__name__}"
        table_name = model._meta.db_table
        
        try:
            # Check if table exists in SQLite
            with connections['sqlite'].cursor() as cursor:
                cursor.execute(
                    "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
                    [table_name]
                )
                if not cursor.fetchone():
                    logger.info(f"📋 {model_name}: Table not found in SQLite - skipping")
                    return 0
            
            # Get record count from SQLite
            try:
                sqlite_queryset = model.objects.using('sqlite').all()
                record_count = sqlite_queryset.count()
            except Exception as e:
                logger.warning(f"⚠️  Could not count records for {model_name}: {e}")
                return None
            
            if record_count == 0:
                logger.info(f"📋 {model_name}: 0 records - skipping")
                return 0
            
            logger.info(f"🔄 Migrating {model_name}: {record_count} records...")
            
            if self.dry_run:
                logger.info(f"   Would migrate {record_count} records from {table_name}")
                return record_count
            
            # Disable foreign key checks temporarily if possible
            with connections['default'].cursor() as cursor:
                cursor.execute("SET session_replication_role = replica;")
            
            try:
                # Clear existing data in PostgreSQL
                model.objects.using('default').all().delete()
                
                # Migrate data in smaller batches for better memory management
                batch_size = 500
                migrated = 0
                
                for i in range(0, record_count, batch_size):
                    try:
                        batch = list(sqlite_queryset[i:i + batch_size])
                        postgres_objects = []
                        
                        for sqlite_obj in batch:
                            new_obj = model()
                            
                            # Copy all field values except primary key
                            for field in model._meta.fields:
                                if not field.primary_key:
                                    try:
                                        value = getattr(sqlite_obj, field.name)
                                        setattr(new_obj, field.name, value)
                                    except Exception as e:
                                        logger.warning(f"⚠️  Could not copy field {field.name}: {e}")
                                        continue
                            
                            postgres_objects.append(new_obj)
                        
                        if postgres_objects:
                            model.objects.using('default').bulk_create(
                                postgres_objects, 
                                batch_size=batch_size,
                                ignore_conflicts=True
                            )
                            migrated += len(postgres_objects)
                            
                    except Exception as e:
                        logger.warning(f"⚠️  Error in batch {i//batch_size + 1}: {e}")
                        continue
                
                logger.info(f"✅ Migrated {migrated} records from {model_name}")
                return migrated
                
            finally:
                # Re-enable foreign key checks
                with connections['default'].cursor() as cursor:
                    cursor.execute("SET session_replication_role = DEFAULT;")
                    
        except Exception as e:
            logger.error(f"❌ Error migrating {model_name}: {e}")
            return None


def main():
    parser = argparse.ArgumentParser(description='Migrate data from SQLite to PostgreSQL (Improved)')
    parser.add_argument(
        '--sqlite-path',
        default='db.sqlite3',
        help='Path to SQLite database file (default: db.sqlite3)'
    )
    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='Show what would be migrated without actually doing it'
    )
    
    args = parser.parse_args()
    
    print("🔄 Improved Data Migration: SQLite → PostgreSQL")
    print("=" * 55)
    
    # Check PostgreSQL configuration
    default_db = settings.DATABASES.get('default', {})
    if default_db.get('ENGINE') != 'django.db.backends.postgresql':
        logger.error("❌ PostgreSQL is not configured as the default database")
        logger.error("   Please update your settings.py DATABASES configuration")
        sys.exit(1)
    
    # Run Django migrations on PostgreSQL first
    if not args.dry_run:
        logger.info("🔄 Running Django migrations on PostgreSQL...")
        try:
            call_command('migrate', verbosity=0)
            logger.info("✅ Django migrations completed")
        except Exception as e:
            logger.error(f"❌ Failed to run Django migrations: {e}")
            sys.exit(1)
    
    # Create migrator and run migration
    migrator = ImprovedDataMigrator(
        sqlite_path=args.sqlite_path,
        dry_run=args.dry_run
    )
    
    success = migrator.migrate()
    
    if success:
        logger.info("\n🎉 Migration process completed successfully!")
        if not args.dry_run:
            logger.info("💡 Post-migration steps:")
            logger.info("   1. Test your application with the migrated data")
            logger.info("   2. Verify critical business data")
            logger.info("   3. Backup your PostgreSQL database")
            logger.info("   4. Update any application configurations if needed")
    else:
        logger.error("\n❌ Migration process completed with errors!")
        logger.error("   Please review the error messages above and retry")
        sys.exit(1)


if __name__ == '__main__':
    main() 