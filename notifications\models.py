from django.db import models
from django.utils.translation import gettext_lazy as _
from core.models import TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel
from core.querysets import BaseQuerySet


class Notification(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """
    Notification model for tracking all system and user notifications
    """
    LEVEL_CHOICES = (
        ('info', _('Information')),
        ('success', _('Success')),
        ('warning', _('Warning')),
        ('error', _('Error')),
    )
    
    title = models.CharField(_("Title"), max_length=255)
    message = models.TextField(_("Message"))
    level = models.Char<PERSON>ield(_("Level"), max_length=20, choices=LEVEL_CHOICES, default='info')
    is_read = models.BooleanField(_("Read"), default=False)
    read_at = models.DateTimeField(_("Read At"), null=True, blank=True)
    object_type = models.Char<PERSON><PERSON>(_("Object Type"), max_length=100, blank=True)
    object_id = models.CharField(_("Object ID"), max_length=100, blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Notification")
        verbose_name_plural = _("Notifications")
        ordering = ['-created_at']
        
    def __str__(self):
        return f"{self.title} ({self.get_level_display()})"
    
    def mark_as_read(self):
        """
        Mark notification as read and set read_at timestamp
        """
        from django.utils import timezone
        
        if not self.is_read:
            self.is_read = True
            self.read_at = timezone.now()
            self.save(update_fields=['is_read', 'read_at', 'updated_at'])
            
    @property
    def has_object_reference(self):
        """
        Check if notification references an object
        """
        return bool(self.object_type and self.object_id)


class WebhookEndpoint(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """
    Configuration for webhook endpoints to receive notifications about events
    """
    name = models.CharField(_("Name"), max_length=100)
    url = models.URLField(_("URL"), max_length=500)
    description = models.TextField(_("Description"), blank=True)
    is_active = models.BooleanField(_("Active"), default=True)
    secret_key = models.CharField(_("Secret Key"), max_length=255, blank=True, 
                                help_text=_("Used for webhook signature validation"))
    
    # Event subscriptions
    subscribe_item_created = models.BooleanField(_("Item Created"), default=True)
    subscribe_item_updated = models.BooleanField(_("Item Updated"), default=True)
    subscribe_stock_low = models.BooleanField(_("Stock Low"), default=True)
    subscribe_movement_created = models.BooleanField(_("Movement Created"), default=True)
    
    class Meta:
        verbose_name = _("Webhook Endpoint")
        verbose_name_plural = _("Webhook Endpoints")
    
    def __str__(self):
        return self.name


class WebhookDelivery(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """
    Record of webhook delivery attempts
    """
    endpoint = models.ForeignKey(WebhookEndpoint, on_delete=models.CASCADE, 
                              related_name='deliveries', verbose_name=_("Endpoint"))
    event_type = models.CharField(_("Event Type"), max_length=100)
    payload = models.JSONField(_("Payload"))
    response_status = models.IntegerField(_("Response Status"), null=True, blank=True)
    response_body = models.TextField(_("Response Body"), blank=True)
    is_success = models.BooleanField(_("Success"), default=False)
    attempts = models.IntegerField(_("Attempts"), default=0)
    
    class Meta:
        verbose_name = _("Webhook Delivery")
        verbose_name_plural = _("Webhook Deliveries")
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.event_type} - {self.endpoint.name} - {self.created_at}"
