from django.db import models
from django.utils.translation import gettext_lazy as _
from core.models import TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel
from core.querysets import BaseQuerySet
from inventory.models import Item


class Customer(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """
    Customer model for sales
    """
    name = models.CharField(_("Name"), max_length=255)
    email = models.EmailField(_("Email"), blank=True)
    phone = models.CharField(_("Phone"), max_length=50, blank=True)
    address = models.TextField(_("Address"), blank=True)
    is_active = models.BooleanField(_("Active"), default=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Customer")
        verbose_name_plural = _("Customers")
        
    def __str__(self):
        return self.name


class SalesOrder(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """
    Sales order model
    """
    STATUS_CHOICES = (
        ('draft', _('Draft')),
        ('confirmed', _('Confirmed')),
        ('shipped', _('Shipped')),
        ('delivered', _('Delivered')),
        ('cancelled', _('Cancelled')),
        ('returned', _('Returned')),
    )
    
    order_number = models.CharField(_("Order Number"), max_length=50, unique=True)
    customer = models.ForeignKey(
        Customer, 
        on_delete=models.PROTECT, 
        related_name='sales_orders',
        verbose_name=_("Customer")
    )
    order_date = models.DateField(_("Order Date"))
    status = models.CharField(_("Status"), max_length=20, choices=STATUS_CHOICES, default='draft')
    shipping_address = models.TextField(_("Shipping Address"), blank=True)
    notes = models.TextField(_("Notes"), blank=True)
    total_amount = models.DecimalField(_("Total Amount"), max_digits=10, decimal_places=2, default=0)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Sales Order")
        verbose_name_plural = _("Sales Orders")
        ordering = ['-order_date']
        
    def __str__(self):
        return f"{self.order_number} - {self.customer.name}"
    
    def update_total_amount(self):
        """
        Update the total amount based on order items
        """
        self.total_amount = sum(item.total_price for item in self.items.all())
        self.save(update_fields=['total_amount', 'updated_at'])


class SalesOrderItem(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """
    Sales order item model
    """
    sales_order = models.ForeignKey(
        SalesOrder, 
        on_delete=models.CASCADE, 
        related_name='items',
        verbose_name=_("Sales Order")
    )
    item = models.ForeignKey(
        Item, 
        on_delete=models.PROTECT, 
        related_name='sales_items',
        verbose_name=_("Item")
    )
    quantity = models.DecimalField(_("Quantity"), max_digits=10, decimal_places=2)
    unit_price = models.DecimalField(_("Unit Price"), max_digits=10, decimal_places=2)
    discount = models.DecimalField(_("Discount"), max_digits=10, decimal_places=2, default=0)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Sales Order Item")
        verbose_name_plural = _("Sales Order Items")
        unique_together = [['sales_order', 'item']]
        
    def __str__(self):
        return f"{self.quantity} x {self.item.name} in {self.sales_order.order_number}"
    
    @property
    def total_price(self):
        """
        Calculate total price for this line item
        """
        return (self.quantity * self.unit_price) - self.discount
        
    def save(self, *args, **kwargs):
        """
        Override save method to update sales order total
        """
        super().save(*args, **kwargs)
        self.sales_order.update_total_amount()


class SalesReturn(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """
    Sales return model
    """
    return_number = models.CharField(_("Return Number"), max_length=50, unique=True)
    sales_order = models.ForeignKey(
        SalesOrder, 
        on_delete=models.PROTECT, 
        related_name='returns',
        verbose_name=_("Sales Order")
    )
    return_date = models.DateField(_("Return Date"))
    reason = models.TextField(_("Reason"))
    notes = models.TextField(_("Notes"), blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Sales Return")
        verbose_name_plural = _("Sales Returns")
        ordering = ['-return_date']
        
    def __str__(self):
        return f"{self.return_number} - {self.sales_order.order_number}"


class SalesReturnItem(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """
    Sales return item model
    """
    sales_return = models.ForeignKey(
        SalesReturn, 
        on_delete=models.CASCADE, 
        related_name='items',
        verbose_name=_("Sales Return")
    )
    sales_order_item = models.ForeignKey(
        SalesOrderItem, 
        on_delete=models.PROTECT, 
        related_name='returns',
        verbose_name=_("Sales Order Item")
    )
    quantity = models.DecimalField(_("Quantity"), max_digits=10, decimal_places=2)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Sales Return Item")
        verbose_name_plural = _("Sales Return Items")
        
    def __str__(self):
        return f"{self.quantity} x {self.sales_order_item.item.name} in {self.sales_return.return_number}"
