import os
import sys
import django
import random
import uuid
from datetime import datetime, timedelta

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

# Import Django models
from django.db import transaction, IntegrityError
from django.utils import timezone

# Import setup models
from setup.models import Customer, Vehicle, ServiceCenter

# Egyptian vehicle data
EGYPTIAN_MAKES_MODELS = [
    {"make": "تويوتا", "models": ["كورولا", "لاند كروزر", "كامري", "هايلكس", "فورتشنر", "راف 4", "يارس"]},
    {"make": "هيونداي", "models": ["النترا", "اكسنت", "توسان", "كريتا", "سوناتا", "فيرنا"]},
    {"make": "نيسان", "models": ["صني", "سنترا", "قشقاي", "جوك", "اكس تريل", "باترول"]},
    {"make": "شيفروليه", "models": ["أفيو", "أوبترا", "لانوس", "كروز", "كابتيفا"]},
    {"make": "كيا", "models": ["سيراتو", "سبورتاج", "بيكانتو", "سورينتو", "ريو"]},
    {"make": "ميتسوبيشي", "models": ["لانسر", "أوتلاندر", "باجيرو", "إكليبس"]},
    {"make": "بي ام دبليو", "models": ["الفئة الثالثة", "الفئة الخامسة", "الفئة السابعة", "X5", "X3"]},
    {"make": "مرسيدس", "models": ["C200", "E180", "GLC", "A Class", "GLE", "S Class"]},
    {"make": "أودي", "models": ["A4", "A6", "Q5", "Q7", "A3"]},
    {"make": "فولكس فاجن", "models": ["باسات", "جيتا", "تيجوان", "جولف"]},
    {"make": "بيجو", "models": ["301", "3008", "508", "208", "2008"]},
    {"make": "رينو", "models": ["لوجان", "داستر", "كادجار", "ميجان", "فلوانس"]},
    {"make": "فيات", "models": ["تيبو", "500", "دوبلو", "لينيا"]},
    {"make": "جيلي", "models": ["إمجراند", "باندينو", "GC6", "X7"]},
    {"make": "BYD", "models": ["F3", "L3", "S6", "F0"]},
    {"make": "DFSK", "models": ["Glory 330", "Glory 580", "C35", "C37"]},
    {"make": "شيري", "models": ["تيجو", "اريزو", "إنفي", "E5"]}
]

COLORS = ["أبيض", "أسود", "فضي", "رمادي", "أحمر", "أزرق", "بني", "ذهبي", "أخضر", "برتقالي"]

class VehicleGenerator:
    def __init__(self):
        self.tenant_id = str(uuid.uuid4())
        print(f"Tenant ID: {self.tenant_id}")
        print("Vehicle generator initialized")
    
    def generate_vehicles(self, count=100):
        print(f"\nGenerating {count} vehicles...")
        
        # Get customers and service centers
        customers = list(Customer.objects.all())
        if not customers:
            print("No customers found. Please generate customers first.")
            return
            
        service_centers = list(ServiceCenter.objects.all())
        if not service_centers:
            print("No service centers found. Please generate service centers first.")
            return
            
        vehicles_created = 0
        current_year = datetime.now().year
        
        for i in range(1, count + 1):
            # Select random make and model
            make_model = random.choice(EGYPTIAN_MAKES_MODELS)
            make = make_model["make"]
            model = random.choice(make_model["models"])
            
            # Generate random vehicle data
            year = random.randint(current_year - 15, current_year)
            color = random.choice(COLORS)
            
            # Generate VIN and license plate
            vin = f"EGT{random.randint(100000, 999999)}{random.randint(1000, 9999)}"
            license_format = random.choice(["3 letters, 4 numbers", "3 numbers, 3 letters", "numbers only"])
            
            if license_format == "3 letters, 4 numbers":
                letters = "".join(random.choice("أبتثجحخدذرزسشصضطظعغفقكلمنهوي") for _ in range(3))
                numbers = str(random.randint(1000, 9999))
                license_plate = f"{letters} {numbers}"
            elif license_format == "3 numbers, 3 letters":
                numbers = str(random.randint(100, 999))
                letters = "".join(random.choice("أبتثجحخدذرزسشصضطظعغفقكلمنهوي") for _ in range(3))
                license_plate = f"{numbers} {letters}"
            else:
                license_plate = str(random.randint(10000, 99999))
            
            # Select random owner and service center
            owner = random.choice(customers)
            service_center = random.choice(service_centers)
            
            # Generate dates
            purchase_date = datetime(year, random.randint(1, 12), random.randint(1, 28)).date()
            warranty_years = random.randint(3, 7)
            warranty_end_date = datetime(year + warranty_years, 
                                        purchase_date.month, 
                                        min(purchase_date.day, 28)).date()
            
            # Create vehicle
            try:
                vehicle = Vehicle.objects.create(
                    make=make,
                    model=model,
                    year=year,
                    vin=vin,
                    license_plate=license_plate,
                    color=color,
                    owner=owner,  # Correctly associate with owner object
                    service_center=service_center,
                    purchase_date=purchase_date,
                    warranty_end_date=warranty_end_date,
                    notes=f"سيارة {make} {model} موديل {year}",
                    tenant_id=self.tenant_id
                )
                vehicles_created += 1
                
                if vehicles_created % 10 == 0:
                    print(f"Created {vehicles_created} vehicles...")
                    
            except Exception as e:
                print(f"Error creating vehicle {i}: {e}")
        
        print(f"Created {vehicles_created} vehicles")
        return vehicles_created > 0

    @transaction.atomic
    def run(self):
        print("Starting vehicle generation...")
        
        # Generate vehicles
        vehicles_created = self.generate_vehicles(100)
        
        print("\nVehicle generation complete!")

if __name__ == "__main__":
    generator = VehicleGenerator()
    generator.run() 