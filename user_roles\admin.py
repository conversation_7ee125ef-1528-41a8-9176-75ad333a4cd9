from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from django.db.models import Q
from django import forms
from .models import Role, UserRole, ModulePermission
from setup.models import ServiceCenter, Company

class ModulePermissionInline(admin.TabularInline):
    model = ModulePermission
    extra = 1
    fields = ('module', 'action')

@admin.register(Role)
class RoleAdmin(admin.ModelAdmin):
    list_display = ('name', 'role_type', 'code', 'is_active', 'display_module_access')
    list_filter = ('role_type', 'is_active', 'can_access_setup', 'can_access_work_orders', 
                   'can_access_inventory', 'can_access_warehouse', 'can_access_sales',
                   'can_access_purchases', 'can_access_reports', 'can_access_settings')
    search_fields = ('name', 'code', 'description')
    inlines = [ModulePermissionInline]
    fieldsets = (
        (None, {
            'fields': ('name', 'code', 'role_type', 'description', 'is_active'),
        }),
        (_('Module Access'), {
            'fields': (
                'can_access_setup', 'can_access_work_orders', 'can_access_inventory',
                'can_access_warehouse', 'can_access_sales', 'can_access_purchases',
                'can_access_reports', 'can_access_settings'
            ),
        }),
        (_('Group Association'), {
            'fields': ('group',),
            'classes': ('collapse',),
        }),
    )
    readonly_fields = ('group',)
    
    def display_module_access(self, obj):
        """Show which modules this role can access"""
        modules = []
        if obj.can_access_setup:
            modules.append(str(_('Setup')))
        if obj.can_access_work_orders:
            modules.append(str(_('Work Orders')))
        if obj.can_access_inventory:
            modules.append(str(_('Inventory')))
        if obj.can_access_warehouse:
            modules.append(str(_('Warehouse')))
        if obj.can_access_sales:
            modules.append(str(_('Sales')))
        if obj.can_access_purchases:
            modules.append(str(_('Purchases')))
        if obj.can_access_reports:
            modules.append(str(_('Reports')))
        if obj.can_access_settings:
            modules.append(str(_('Settings')))
        
        if modules:
            return ', '.join(modules)
        return str(_('None'))
    
    display_module_access.short_description = _('Module Access')

# Custom ModelForm for UserRole
class UserRoleForm(forms.ModelForm):
    tenant_id = forms.UUIDField(
        label=_("Tenant ID"),
        help_text=_("UUID of the tenant associated with this role."),
        required=True,
        widget=forms.TextInput(attrs={'placeholder': 'Will be auto-populated on save'})
    )
    
    class Meta:
        model = UserRole
        fields = ('user', 'role', 'franchise', 'company', 'service_center', 'is_primary', 'is_active', 'tenant_id')
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        instance = kwargs.get('instance')
        
        # If this is an existing instance, populate the tenant_id field
        if instance and hasattr(instance, 'tenant_id'):
            self.fields['tenant_id'].initial = instance.tenant_id
        
        # Make tenant_id field optional on form - we'll handle it in save
        self.fields['tenant_id'].required = False
        
    def clean(self):
        """Validate and set tenant_id based on service_center or company"""
        cleaned_data = super().clean()
        
        service_center = cleaned_data.get('service_center')
        company = cleaned_data.get('company')
        tenant_id = cleaned_data.get('tenant_id')
        
        # If tenant_id is not provided, try to get it from related objects
        if not tenant_id:
            if service_center and hasattr(service_center, 'tenant_id'):
                cleaned_data['tenant_id'] = service_center.tenant_id
            elif company and hasattr(company, 'tenant_id'):
                cleaned_data['tenant_id'] = company.tenant_id
            # If we still don't have a tenant_id, validation will fail in the model save
        
        return cleaned_data

@admin.register(UserRole)
class UserRoleAdmin(admin.ModelAdmin):
    form = UserRoleForm
    list_display = ('user', 'role', 'display_scope', 'is_primary', 'is_active')
    list_filter = ('is_active', 'is_primary', 'role', 'role__role_type')
    search_fields = ('user__username', 'user__first_name', 'user__last_name', 
                     'role__name', 'franchise__name', 'company__name', 'service_center__name')
    fieldsets = (
        (None, {
            'fields': ('user', 'role', 'is_primary', 'is_active'),
        }),
        (_('Role Scope'), {
            'fields': ('franchise', 'company', 'service_center'),
            'description': _('Set the scope based on the role type. Only one should be set.'),
        }),
        (_('Tenant Information'), {
            'fields': ('tenant_id',),
            'description': _('UUID of the tenant associated with this role. Will be auto-populated from service center or company.'),
        }),
    )
    
    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        
        # Add dynamic help text and validation
        if obj and obj.role:
            role_type = obj.role.role_type
            
            # Set help text based on role type
            if role_type == 'system_admin':
                form.base_fields['franchise'].help_text = _('System administrators do not need a scope')
                form.base_fields['company'].help_text = _('System administrators do not need a scope')
                form.base_fields['service_center'].help_text = _('System administrators do not need a scope')
            elif role_type == 'franchise_admin':
                form.base_fields['franchise'].help_text = _('Required for Franchise Administrators')
                form.base_fields['company'].help_text = _('Leave blank for Franchise Administrators')
                form.base_fields['service_center'].help_text = _('Leave blank for Franchise Administrators')
            elif role_type == 'company_admin':
                form.base_fields['franchise'].help_text = _('Leave blank for Company Administrators')
                form.base_fields['company'].help_text = _('Required for Company Administrators')
                form.base_fields['service_center'].help_text = _('Leave blank for Company Administrators')
            else:
                form.base_fields['franchise'].help_text = _('Leave blank for Service Center roles')
                form.base_fields['company'].help_text = _('Leave blank for Service Center roles')
                form.base_fields['service_center'].help_text = _('Required for Service Center roles')
        
        return form
    
    def save_model(self, request, obj, form, change):
        """
        Ensure tenant_id is properly set before saving the model
        """
        # Debug information
        print(f"Saving UserRole for user: {obj.user.username}, role: {obj.role.name}")
        print(f"Scope - Franchise: {obj.franchise}, Company: {obj.company}, Service Center: {obj.service_center}")
        
        # Double-check tenant_id in case form.clean() was bypassed
        if not obj.tenant_id:
            if obj.service_center and hasattr(obj.service_center, 'tenant_id'):
                obj.tenant_id = obj.service_center.tenant_id
                print(f"Taking tenant_id from service center: {obj.tenant_id}")
            elif obj.company and hasattr(obj.company, 'tenant_id'):
                obj.tenant_id = obj.company.tenant_id 
                print(f"Taking tenant_id from company: {obj.tenant_id}")
        
        print(f"Final tenant_id: {obj.tenant_id}")
        super().save_model(request, obj, form, change)
    
    def display_scope(self, obj):
        """Display the scope of this user role"""
        if obj.franchise:
            return f"{obj.franchise.name} (Franchise)"
        elif obj.company:
            return f"{obj.company.name} (Company)"
        elif obj.service_center:
            return f"{obj.service_center.name} (Service Center)"
        else:
            return _("System-wide")
    
    display_scope.short_description = _('Scope')

@admin.register(ModulePermission)
class ModulePermissionAdmin(admin.ModelAdmin):
    list_display = ('role', 'module', 'action')
    list_filter = ('module', 'action', 'role')
    search_fields = ('role__name',)
