import os
import sys
import django
import random
from datetime import datetime, timedelta
from django.db import transaction
from faker import Faker

# Add the parent directory to sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

# Import necessary models
from setup.models import ServiceCenter, Vehicle, Customer
from work_orders.models import (
    WorkOrder, WorkOrderType, WorkOrderOperation, WorkOrderMaterial,
    BillOfMaterials, BOMItem, MaintenanceSchedule, ScheduleOperation
)
from inventory.models import Item, ItemClassification
from django.contrib.auth.models import User
from django.utils import timezone

# Initialize Faker
fake = Faker('ar_EG')  # Using Egyptian Arabic locale

class WorkOrderGenerator:
    """Generate demo work orders data for the Aftersails system."""
    
    def __init__(self):
        self.tenants = self._get_tenant_ids()
        self.service_centers = {}
        self.vehicles = {}
        self.customers = {}
        self.work_order_types = {}
        self.inventory_items = {}
        self.users = {}
        
        for tenant_id in self.tenants:
            self.service_centers[tenant_id] = list(ServiceCenter.objects.filter(tenant_id=tenant_id))
            self.vehicles[tenant_id] = list(Vehicle.objects.filter(tenant_id=tenant_id))
            self.customers[tenant_id] = list(Customer.objects.filter(tenant_id=tenant_id))
            self.work_order_types[tenant_id] = list(WorkOrderType.objects.filter(tenant_id=tenant_id))
            self.inventory_items[tenant_id] = list(Item.objects.filter(tenant_id=tenant_id))
            # Get users (not tenant-specific in Django's User model)
            self.users = list(User.objects.all())
        
        print(f"Initialized WorkOrderGenerator with {len(self.tenants)} tenants")
        for tenant_id in self.tenants:
            print(f"Tenant {tenant_id}: {len(self.service_centers.get(tenant_id, []))} service centers, "
                  f"{len(self.vehicles.get(tenant_id, []))} vehicles, "
                  f"{len(self.work_order_types.get(tenant_id, []))} work order types")
    
    def _get_tenant_ids(self):
        """Get unique tenant IDs from ServiceCenter."""
        return ServiceCenter.objects.values_list('tenant_id', flat=True).distinct()
    
    def _generate_work_order_number(self, tenant_id, service_center):
        """Generate a unique work order number."""
        prefix = service_center.code if service_center.code else "SC"
        date_str = datetime.now().strftime("%y%m%d")
        # Get count of existing work orders for this service center
        existing_count = WorkOrder.objects.filter(
            tenant_id=tenant_id,
            service_center=service_center
        ).count()
        return f"{prefix}-{date_str}-{existing_count + 1:04d}"
    
    def generate_maintenance_schedules(self, count=15):
        """Generate maintenance schedule templates."""
        print(f"Generating {count} maintenance schedules...")
        
        maintenance_schedules = []
        
        for tenant_id in self.tenants:
            # Only generate for tenants with items and work order types
            if (not self.inventory_items.get(tenant_id) or 
                not self.work_order_types.get(tenant_id)):
                continue
                
            # Get vehicle makes and models from vehicles for this tenant
            vehicle_makes = set()
            vehicle_models = {}
            
            for vehicle in self.vehicles.get(tenant_id, []):
                make = vehicle.make
                model = vehicle.model
                vehicle_makes.add(make)
                if make not in vehicle_models:
                    vehicle_models[make] = set()
                vehicle_models[make].add(model)
            
            # Convert sets to lists for random selection
            vehicle_makes = list(vehicle_makes)
            
            # Arabic maintenance schedule names
            schedule_names_ar = [
                "الصيانة الدورية 5,000 كم",
                "الصيانة الدورية 10,000 كم",
                "الصيانة الدورية 20,000 كم", 
                "الصيانة الدورية 40,000 كم",
                "الصيانة الدورية 60,000 كم",
                "الصيانة الدورية 80,000 كم",
                "الصيانة الدورية 100,000 كم",
                "صيانة نصف سنوية",
                "صيانة سنوية",
                "فحص ما قبل السفر",
                "الصيانة الشتوية",
                "الصيانة الصيفية",
                "صيانة الفرامل",
                "صيانة نظام التعليق",
                "صيانة نظام التبريد"
            ]
            
            # Create maintenance schedules
            schedules_per_tenant = count // len(self.tenants) + 1
            for i in range(min(schedules_per_tenant, len(schedule_names_ar))):
                if not vehicle_makes:
                    continue
                    
                make = random.choice(vehicle_makes)
                models = list(vehicle_models.get(make, ["العامة"]))
                model = random.choice(models) if models else ""
                
                interval_type = random.choice(['mileage', 'time', 'both'])
                
                schedule = MaintenanceSchedule.objects.create(
                    tenant_id=tenant_id,
                    name=schedule_names_ar[i],
                    description=f"جدول الصيانة ل{make} {model}",
                    interval_type=interval_type,
                    mileage_interval=random.choice([5000, 10000, 20000, 40000, 60000]) if interval_type in ['mileage', 'both'] else None,
                    time_interval_months=random.choice([3, 6, 12]) if interval_type in ['time', 'both'] else None,
                    vehicle_make=make,
                    vehicle_model=model,
                    year_from=random.randint(2010, 2019),
                    year_to=random.randint(2020, 2023),
                    is_active=True
                )
                maintenance_schedules.append(schedule)
                
                # Create operations for this schedule
                self._create_schedule_operations(tenant_id, schedule)
        
        print(f"Created {len(maintenance_schedules)} maintenance schedules")
        return maintenance_schedules
    
    def _create_schedule_operations(self, tenant_id, schedule):
        """Create operations for a maintenance schedule."""
        # Arabic operation names
        operation_names_ar = [
            "تغيير زيت المحرك",
            "تغيير فلتر الزيت",
            "تغيير فلتر الهواء",
            "فحص وضبط الإطارات",
            "فحص نظام الفرامل",
            "فحص البطارية",
            "فحص سائل التبريد",
            "فحص المساحات",
            "فحص الإضاءة",
            "فحص مستوى الزيوت",
            "فحص نظام التعليق",
            "تغيير شمعات الإشعال",
            "فحص سير المحرك",
            "تغيير زيت ناقل الحركة",
            "ضبط زوايا العجلات"
        ]
        
        if not self.work_order_types.get(tenant_id):
            return
            
        # Determine how many operations to create for this schedule
        num_operations = random.randint(3, 8)
        
        # Shuffle operations to pick random ones
        operations_to_use = operation_names_ar.copy()
        random.shuffle(operations_to_use)
        operations_to_use = operations_to_use[:num_operations]
        
        # Create operations
        for i, operation_name in enumerate(operations_to_use):
            operation_type = random.choice(self.work_order_types.get(tenant_id))
            
            operation = ScheduleOperation.objects.create(
                tenant_id=tenant_id,
                maintenance_schedule=schedule,
                name=operation_name,
                description=f"عملية {operation_name} ل{schedule.vehicle_make}",
                duration_minutes=random.randint(15, 120),
                sequence=(i+1) * 10,
                is_required=random.random() > 0.2,  # 80% chance of being required
                operation_type=operation_type
            )
            
            # Add parts to this operation
            self._add_parts_to_schedule_operation(tenant_id, operation)
    
    def _add_parts_to_schedule_operation(self, tenant_id, operation):
        """Add parts (items) to a schedule operation."""
        if not self.inventory_items.get(tenant_id):
            return
            
        # Randomly decide how many parts to add
        num_parts = random.randint(0, 3)
        
        # Get a random selection of items
        items = random.sample(
            self.inventory_items.get(tenant_id), 
            min(num_parts, len(self.inventory_items.get(tenant_id)))
        )
        
        for item in items:
            from work_orders.models import OperationPart
            
            OperationPart.objects.create(
                tenant_id=tenant_id,
                schedule_operation=operation,
                item=item,
                quantity=random.randint(1, 5),
                is_required=random.random() > 0.3,  # 70% chance of being required
                notes=fake.sentence() if random.random() > 0.7 else ""
            )
    
    def generate_bills_of_materials(self, count=10):
        """Generate bills of materials for assembly-type work orders."""
        print(f"Generating {count} bills of materials...")
        
        boms = []
        for tenant_id in self.tenants:
            # Skip if no items are available
            if not self.inventory_items.get(tenant_id):
                continue
                
            # Get finished items (products that can be assembled)
            # Filter to items classified as "assembled" or "manufactured" if possible
            assembled_items = []
            try:
                assembled_classification = ItemClassification.objects.filter(
                    tenant_id=tenant_id, 
                    name__icontains='assembled'
                ).first()
                
                if assembled_classification:
                    assembled_items = list(Item.objects.filter(
                        tenant_id=tenant_id,
                        classification=assembled_classification
                    ))
                else:
                    # Fallback to random items
                    assembled_items = self.inventory_items.get(tenant_id)
            except:
                # Fallback to all items
                assembled_items = self.inventory_items.get(tenant_id)
            
            # If still no items found, continue to next tenant
            if not assembled_items:
                continue
                
            # Create BOMs for this tenant
            boms_per_tenant = count // len(self.tenants) + 1
            
            for i in range(boms_per_tenant):
                finished_item = random.choice(assembled_items)
                
                bom = BillOfMaterials.objects.create(
                    tenant_id=tenant_id,
                    name=f"{finished_item.name} BOM",
                    description=f"مكونات تجميع {finished_item.name}",
                    finished_item=finished_item,
                    version=f"1.{random.randint(0, 9)}",
                    is_active=True,
                    notes=fake.paragraph() if random.random() > 0.7 else ""
                )
                boms.append(bom)
                
                # Add components to this BOM
                self._add_components_to_bom(tenant_id, bom)
        
        print(f"Created {len(boms)} bills of materials")
        return boms
    
    def _add_components_to_bom(self, tenant_id, bom):
        """Add component items to a bill of materials."""
        if not self.inventory_items.get(tenant_id):
            return
            
        # Get available items, excluding the finished item itself
        available_items = [
            item for item in self.inventory_items.get(tenant_id)
            if item != bom.finished_item
        ]
        
        if not available_items:
            return
            
        # Determine how many components to add
        num_components = random.randint(3, 10)
        
        # Get a random selection of items for components
        component_items = random.sample(
            available_items,
            min(num_components, len(available_items))
        )
        
        for i, item in enumerate(component_items):
            BOMItem.objects.create(
                tenant_id=tenant_id,
                bom=bom,
                item=item,
                quantity=random.uniform(1, 10),
                unit_of_measure=item.unit_of_measure,
                is_optional=random.random() > 0.8,  # 20% chance of being optional
                sequence=(i+1) * 10,
                notes=""
            )
    
    def generate_work_orders(self, count=100):
        """Generate work orders with operations and materials."""
        print(f"Generating {count} work orders...")
        
        maintenance_schedules = list(MaintenanceSchedule.objects.all())
        bills_of_materials = list(BillOfMaterials.objects.all())
        
        work_orders = []
        
        # Status distribution for more realistic data
        status_weights = {
            'draft': 15,
            'planned': 15,
            'in_progress': 25,
            'on_hold': 5,
            'completed': 35,
            'cancelled': 5
        }
        
        # Priority distribution
        priority_weights = {
            'low': 20,
            'medium': 50,
            'high': 25,
            'critical': 5
        }
        
        for tenant_id in self.tenants:
            # Skip tenants without necessary related data
            if (not self.service_centers.get(tenant_id) or
                not self.vehicles.get(tenant_id) or
                not self.work_order_types.get(tenant_id)):
                continue
                
            # Get available entities for this tenant
            service_centers = self.service_centers.get(tenant_id)
            vehicles = self.vehicles.get(tenant_id)
            customers = self.customers.get(tenant_id)
            work_order_types = self.work_order_types.get(tenant_id)
            
            # Get tenant-specific schedules and BOMs
            tenant_schedules = [s for s in maintenance_schedules if s.tenant_id == tenant_id]
            tenant_boms = [b for b in bills_of_materials if b.tenant_id == tenant_id]
            
            # Create work orders for this tenant
            work_orders_per_tenant = count // len(self.tenants) + 1
            
            for i in range(work_orders_per_tenant):
                # Pick random entities
                service_center = random.choice(service_centers)
                vehicle = random.choice(vehicles) if vehicles else None
                customer = random.choice(customers) if customers else None
                work_order_type = random.choice(work_order_types)
                
                # Generate dates
                # Most work orders should be recent (last 60 days)
                days_ago = int(abs(random.gauss(0, 20)))  # Normal distribution centered at 0
                days_ago = min(days_ago, 60)  # Cap at 60 days ago
                
                order_date = timezone.now() - timedelta(days=days_ago)
                
                # Determine if this is a scheduled maintenance or custom work order
                is_scheduled = random.random() < 0.4  # 40% chance of scheduled maintenance
                
                # Select schedule if this is a scheduled maintenance
                maintenance_schedule = None
                if is_scheduled and tenant_schedules:
                    maintenance_schedule = random.choice(tenant_schedules)
                
                # Select BOM if this is an assembly-type work order
                bill_of_materials = None
                if work_order_type.name.lower().find('assembly') >= 0 and tenant_boms:
                    bill_of_materials = random.choice(tenant_boms)
                
                # Randomly select status with weighting
                status = random.choices(
                    list(status_weights.keys()),
                    weights=list(status_weights.values()),
                    k=1
                )[0]
                
                # Randomly select priority with weighting
                priority = random.choices(
                    list(priority_weights.keys()),
                    weights=list(priority_weights.values()),
                    k=1
                )[0]
                
                # Create dates based on status
                planned_start_date = order_date + timedelta(days=random.randint(1, 5))
                planned_end_date = planned_start_date + timedelta(days=random.randint(1, 3))
                
                actual_start_date = None
                actual_end_date = None
                
                if status in ['in_progress', 'on_hold', 'completed']:
                    # Work has started
                    actual_start_date = planned_start_date + timedelta(
                        hours=random.randint(-12, 24)  # Can start a bit earlier or later than planned
                    )
                    
                    if status == 'completed':
                        # Work is done
                        actual_end_date = actual_start_date + timedelta(
                            hours=random.randint(1, 72)  # 1 hour to 3 days to complete
                        )
                
                # Create the work order
                work_order = WorkOrder.objects.create(
                    tenant_id=tenant_id,
                    work_order_number=self._generate_work_order_number(tenant_id, service_center),
                    work_order_type=work_order_type,
                    bill_of_materials=bill_of_materials,
                    description=fake.paragraph(),
                    priority=priority,
                    status=status,
                    operation_category='scheduled' if is_scheduled else 'custom',
                    maintenance_schedule=maintenance_schedule,
                    
                    # Service center and vehicle info
                    service_center=service_center,
                    vehicle=vehicle,
                    current_odometer=random.randint(5000, 150000) if vehicle else None,
                    fuel_level=random.randint(10, 100) if vehicle else None,
                    
                    # Dates
                    planned_start_date=planned_start_date,
                    planned_end_date=planned_end_date,
                    actual_start_date=actual_start_date,
                    actual_end_date=actual_end_date,
                    
                    # Customer info
                    customer=customer,
                    customer_name=customer.name if customer else fake.name(),
                    customer_phone=customer.phone if customer else fake.phone_number(),
                    customer_email=customer.email if customer else fake.email(),
                    
                    # Financial info
                    estimated_cost=random.randint(500, 5000),
                    actual_cost=random.randint(450, 6000) if status == 'completed' else None,
                    
                    notes=fake.paragraph() if random.random() > 0.7 else ""
                )
                
                work_orders.append(work_order)
                
                # Add operations to this work order
                if is_scheduled and maintenance_schedule:
                    # Use operations from the schedule
                    self._create_operations_from_schedule(tenant_id, work_order, maintenance_schedule)
                else:
                    # Create custom operations
                    self._create_custom_operations(tenant_id, work_order)
                
                # Add materials to this work order
                self._add_materials_to_work_order(tenant_id, work_order)
        
        print(f"Created {len(work_orders)} work orders")
        return work_orders
    
    def _create_operations_from_schedule(self, tenant_id, work_order, schedule):
        """Create work order operations from a maintenance schedule."""
        schedule_operations = schedule.operations.all()
        
        for i, schedule_op in enumerate(schedule_operations):
            # Create work order operation
            wo_operation = WorkOrderOperation.objects.create(
                tenant_id=tenant_id,
                work_order=work_order,
                sequence=schedule_op.sequence,
                name=schedule_op.name,
                description=schedule_op.description,
                duration_minutes=schedule_op.duration_minutes,
                is_completed=work_order.status == 'completed',
                completed_at=work_order.actual_end_date if work_order.status == 'completed' else None,
                notes=fake.sentence() if random.random() > 0.7 else ""
            )
            
            # Add materials from schedule operation parts
            for part in schedule_op.parts.all():
                WorkOrderMaterial.objects.create(
                    tenant_id=tenant_id,
                    work_order=work_order,
                    item=part.item,
                    quantity=part.quantity,
                    unit_of_measure=part.item.unit_of_measure,
                    is_consumed=work_order.status == 'completed',
                    notes=part.notes
                )
    
    def _create_custom_operations(self, tenant_id, work_order):
        """Create custom operations for a work order."""
        # Arabic operation names
        operation_names_ar = [
            "فحص العطل",
            "تفكيك القطع المعطوبة",
            "استبدال القطع",
            "تجميع المكونات",
            "اختبار الإصلاح",
            "فحص نهائي",
            "تنظيف وتسليم",
            "إعادة ضبط",
            "تحميل البرمجيات",
            "معايرة الحساسات",
            "لحام وإصلاح",
            "فك وتركيب",
            "تشحيم",
            "تنظيف",
            "ضبط"
        ]
        
        # Determine how many operations to create
        num_operations = random.randint(2, 6)
        
        # Create operations
        for i in range(num_operations):
            operation_name = random.choice(operation_names_ar)
            
            is_completed = False
            completed_at = None
            
            if work_order.status == 'completed':
                # All operations completed
                is_completed = True
                completed_at = work_order.actual_end_date
            elif work_order.status == 'in_progress':
                # Some operations may be completed
                is_completed = i < random.randint(0, num_operations)
                if is_completed and work_order.actual_start_date:
                    # Completed sometime between actual start and now
                    time_since_start = timezone.now() - work_order.actual_start_date
                    completed_at = work_order.actual_start_date + timedelta(
                        seconds=random.randint(0, int(time_since_start.total_seconds()))
                    )
            
            WorkOrderOperation.objects.create(
                tenant_id=tenant_id,
                work_order=work_order,
                sequence=(i+1) * 10,
                name=operation_name,
                description=f"عملية {operation_name} ل{work_order.vehicle.make if work_order.vehicle else 'المركبة'}",
                duration_minutes=random.randint(15, 120),
                is_completed=is_completed,
                completed_at=completed_at,
                notes=fake.sentence() if random.random() > 0.7 else ""
            )
    
    def _add_materials_to_work_order(self, tenant_id, work_order):
        """Add materials to a work order."""
        if not self.inventory_items.get(tenant_id):
            return
            
        # If this is a BOM-based work order, use BOM items
        if work_order.bill_of_materials:
            for bom_item in work_order.bill_of_materials.items.all():
                WorkOrderMaterial.objects.create(
                    tenant_id=tenant_id,
                    work_order=work_order,
                    item=bom_item.item,
                    quantity=bom_item.quantity,
                    unit_of_measure=bom_item.unit_of_measure,
                    is_consumed=work_order.status == 'completed',
                    notes=bom_item.notes
                )
            return
                
        # For other work orders, add random materials
        # Determine how many materials to add
        num_materials = random.randint(1, 5)
        
        # Get a random selection of items
        materials = random.sample(
            self.inventory_items.get(tenant_id),
            min(num_materials, len(self.inventory_items.get(tenant_id)))
        )
        
        for item in materials:
            WorkOrderMaterial.objects.create(
                tenant_id=tenant_id,
                work_order=work_order,
                item=item,
                quantity=random.randint(1, 5),
                unit_of_measure=item.unit_of_measure,
                is_consumed=work_order.status == 'completed',
                notes=fake.sentence() if random.random() > 0.7 else ""
            )
    
    @transaction.atomic
    def run(self):
        """Run the generator."""
        print("Starting Work Order Generator...")
        
        # Check if we have work order types
        if not WorkOrderType.objects.exists():
            print("❌ No work order types found. Please run add_work_order_types.py first.")
            return
        
        # Check if we have items
        if not Item.objects.exists():
            print("❌ No inventory items found. Please run add_inventory_data.py first.")
            return
        
        # Check if we have service centers
        if not ServiceCenter.objects.exists():
            print("❌ No service centers found. Please run add_service_center_data.py first.")
            return
        
        # Check if we have vehicles
        if not Vehicle.objects.exists():
            print("❌ No vehicles found. Please run add_vehicles.py first.")
            return
        
        # Generate data in the correct order
        self.generate_maintenance_schedules(15)
        self.generate_bills_of_materials(10)
        self.generate_work_orders(100)
        
        print("✅ Work orders data generation completed!")


def main():
    """Execute the work order generator."""
    generator = WorkOrderGenerator()
    generator.run()


if __name__ == "__main__":
    main() 