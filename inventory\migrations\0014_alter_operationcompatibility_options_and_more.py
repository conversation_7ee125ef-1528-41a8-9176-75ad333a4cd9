# Generated by Django 4.2.20 on 2025-05-20 11:35

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ("work_orders", "0004_workorder_customer"),
        ("setup", "0012_populate_egyptian_vehicle_data"),
        ("inventory", "0013_populate_vehicle_data"),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="operationcompatibility",
            options={
                "ordering": ["operation_type__name", "item__name"],
                "verbose_name": "Operation Compatibility",
                "verbose_name_plural": "Operation Compatibilities",
            },
        ),
        migrations.AlterUniqueTogether(
            name="operationcompatibility",
            unique_together=set(),
        ),
        migrations.AlterField(
            model_name="operationcompatibility",
            name="duration_minutes",
            field=models.PositiveIntegerField(
                blank=True,
                help_text="Estimated duration of this operation in minutes",
                null=True,
                verbose_name="Duration (minutes)",
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="operationcompatibility",
            name="is_common",
            field=models.BooleanField(
                default=True,
                help_text="Is this item commonly used for this operation type?",
                verbose_name="Common",
            ),
        ),
        migrations.AlterField(
            model_name="operationcompatibility",
            name="maintenance_schedule",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="operation_compatibilities",
                to="work_orders.maintenanceschedule",
                verbose_name="Maintenance Schedule",
            ),
        ),
        migrations.AlterField(
            model_name="operationcompatibility",
            name="operation_description",
            field=models.CharField(
                blank=True,
                choices=[
                    ("oil_change", "Oil Change"),
                    ("brake_service", "Brake Service"),
                    ("transmission_service", "Transmission Service"),
                    ("coolant_flush", "Coolant Flush"),
                    ("air_filter", "Air Filter Replacement"),
                    ("fuel_filter", "Fuel Filter Replacement"),
                    ("spark_plugs", "Spark Plugs Replacement"),
                    ("timing_belt", "Timing Belt Replacement"),
                    ("battery_replacement", "Battery Replacement"),
                    ("tire_rotation", "Tire Rotation"),
                    ("wheel_alignment", "Wheel Alignment"),
                    ("suspension_repair", "Suspension Repair"),
                    ("exhaust_repair", "Exhaust System Repair"),
                    ("ac_service", "A/C Service"),
                    ("diagnostics", "Diagnostics"),
                    ("other", "Other"),
                ],
                max_length=255,
                null=True,
                verbose_name="Operation Description",
            ),
        ),
        migrations.AlterField(
            model_name="operationcompatibility",
            name="vehicle_make",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="operation_compatibilities_make",
                to="setup.vehiclemake",
                verbose_name="Legacy Vehicle Make",
            ),
        ),
        migrations.AlterField(
            model_name="operationcompatibility",
            name="vehicle_model",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="operation_compatibilities_model",
                to="setup.vehiclemodel",
                verbose_name="Legacy Vehicle Model",
            ),
        ),
        migrations.AlterField(
            model_name="operationcompatibility",
            name="year_from",
            field=models.PositiveIntegerField(
                blank=True,
                help_text="Legacy field - use vehicle compatibilities instead",
                null=True,
                verbose_name="Legacy Year From",
            ),
        ),
        migrations.AlterField(
            model_name="operationcompatibility",
            name="year_to",
            field=models.PositiveIntegerField(
                blank=True,
                help_text="Legacy field - use vehicle compatibilities instead",
                null=True,
                verbose_name="Legacy Year To",
            ),
        ),
        migrations.AlterUniqueTogether(
            name="operationcompatibility",
            unique_together={("item", "operation_type", "maintenance_schedule")},
        ),
        migrations.RemoveField(
            model_name="operationcompatibility",
            name="default_quantity",
        ),
        migrations.RemoveField(
            model_name="operationcompatibility",
            name="engine_displacement",
        ),
        migrations.RemoveField(
            model_name="operationcompatibility",
            name="engine_type",
        ),
        migrations.RemoveField(
            model_name="operationcompatibility",
            name="notes",
        ),
        migrations.CreateModel(
            name="VehicleOperationCompatibility",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated at"),
                ),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "tenant_id",
                    models.UUIDField(db_index=True, verbose_name="Tenant ID"),
                ),
                (
                    "year_from",
                    models.PositiveIntegerField(
                        blank=True,
                        help_text="Start year of compatible vehicle models",
                        null=True,
                        verbose_name="Year From",
                    ),
                ),
                (
                    "year_to",
                    models.PositiveIntegerField(
                        blank=True,
                        help_text="End year of compatible vehicle models (leave blank for current)",
                        null=True,
                        verbose_name="Year To",
                    ),
                ),
                (
                    "operation_compatibility",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="vehicle_compatibilities",
                        to="inventory.operationcompatibility",
                        verbose_name="Operation Compatibility",
                    ),
                ),
                (
                    "vehicle_make",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="operation_compatibilities",
                        to="setup.vehiclemake",
                        verbose_name="Vehicle Make",
                    ),
                ),
                (
                    "vehicle_model",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="operation_compatibilities",
                        to="setup.vehiclemodel",
                        verbose_name="Vehicle Model",
                    ),
                ),
            ],
            options={
                "verbose_name": "Vehicle Operation Compatibility",
                "verbose_name_plural": "Vehicle Operation Compatibilities",
                "unique_together": {
                    (
                        "operation_compatibility",
                        "vehicle_make",
                        "vehicle_model",
                        "year_from",
                        "year_to",
                    )
                },
            },
        ),
    ]
