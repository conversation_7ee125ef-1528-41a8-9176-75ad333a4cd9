{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "اختيار جدول الصيانة" %}{% endblock %}

{% block extra_head %}
<style>
    .schedule-card {
        cursor: pointer;
        transition: all 0.2s;
        margin-bottom: 15px;
    }
    
    .schedule-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    
    .schedule-card.recommended {
        border-color: #28a745;
    }
    
    .recommended-badge {
        position: absolute;
        top: 10px;
        right: 10px;
    }
    
    /* RTL-specific styling */
    html[dir="rtl"] .recommended-badge {
        right: auto;
        left: 10px;
    }
    
    html[dir="rtl"] .mr-2 {
        margin-right: 0;
        margin-left: 0.5rem;
    }
    
    .metric-value {
        direction: ltr; /* Always show numbers left-to-right */
        display: inline-block;
        font-family: 'Tajawal', sans-serif;
        text-align: start;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>{% trans "اختيار جدول الصيانة" %}</h1>
                <a href="{% url 'work_orders:work_order_detail' work_order.id %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left {% if LANGUAGE_CODE == 'ar' %}ml-1{% else %}mr-1{% endif %}"></i> {% trans "العودة إلى أمر العمل" %}
                </a>
            </div>
            
            {% if vehicle %}
            <div class="alert alert-info">
                <div class="row">
                    <div class="col-md-6">
                        <h5>{% trans "معلومات المركبة" %}</h5>
                        <p>
                            <strong>{% trans "الصانع:" %}</strong> {{ vehicle.make }}<br>
                            <strong>{% trans "الطراز:" %}</strong> {{ vehicle.model }}<br>
                            <strong>{% trans "السنة:" %}</strong> {{ vehicle.year }}<br>
                            <strong>{% trans "لوحة الترخيص:" %}</strong> {{ vehicle.license_plate }}
                        </p>
                    </div>
                    <div class="col-md-6">
                        <h5>{% trans "معلومات الخدمة" %}</h5>
                        <p>
                            <strong>{% trans "عداد المسافات الحالي:" %}</strong> <span class="metric-value">{{ work_order.current_odometer|default:"N/A" }}</span> {% trans "كم" %}<br>
                            {% if last_service %}
                                <strong>{% trans "آخر صيانة:" %}</strong> {{ last_service.actual_end_date|date:"Y-m-d" }}<br>
                                <strong>{% trans "عداد المسافات السابق:" %}</strong> <span class="metric-value">{{ last_service.current_odometer }}</span> {% trans "كم" %}<br>
                                <strong>{% trans "المسافة منذ آخر صيانة:" %}</strong> <span class="metric-value">{{ mileage_since_last_service }}</span> {% trans "كم" %}
                            {% else %}
                                <strong>{% trans "آخر صيانة:" %}</strong> {% trans "لا يوجد سجل صيانة سابق" %}
                            {% endif %}
                        </p>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
    
    <div class="row mt-4">
        {% if schedules %}
            {% for schedule in schedules %}
                <div class="col-md-4">
                    <div class="card schedule-card {% if mileage_since_last_service and schedule.interval_type == 'mileage' and mileage_since_last_service >= schedule.mileage_interval %}recommended{% endif %}">
                        {% if mileage_since_last_service and schedule.interval_type == 'mileage' and mileage_since_last_service >= schedule.mileage_interval %}
                            <span class="badge badge-success recommended-badge">{% trans "موصى به" %}</span>
                        {% endif %}
                        
                        <div class="card-body">
                            <h5 class="card-title">{{ schedule.name }}</h5>
                            <p class="card-text text-muted">{{ schedule.description|truncatewords:15 }}</p>
                            
                            <div class="mb-3">
                                {% if schedule.interval_type == 'mileage' %}
                                    <span class="badge badge-primary">{% trans "كل" %} <span class="metric-value">{{ schedule.mileage_interval }}</span> {% trans "كم" %}</span>
                                {% elif schedule.interval_type == 'time' %}
                                    <span class="badge badge-info">{% trans "كل" %} <span class="metric-value">{{ schedule.time_interval_months }}</span> {% trans "شهر" %}</span>
                                {% else %}
                                    <span class="badge badge-secondary">{% trans "كل" %} <span class="metric-value">{{ schedule.mileage_interval }}</span> {% trans "كم" %} / <span class="metric-value">{{ schedule.time_interval_months }}</span> {% trans "شهر" %}</span>
                                {% endif %}
                                
                                <span class="badge badge-light"><span class="metric-value">{{ schedule.operations.count }}</span> {% trans "عملية" %}</span>
                                
                                {% if schedule.vehicle_make %}
                                    <span class="badge badge-dark">{{ schedule.vehicle_make }} {% if schedule.vehicle_model %}{{ schedule.vehicle_model }}{% endif %}</span>
                                {% endif %}
                            </div>
                            
                            <a href="{% url 'work_orders:apply_maintenance_schedule' work_order.id schedule.id %}" class="btn btn-primary">
                                <i class="fas fa-check {% if LANGUAGE_CODE == 'ar' %}ml-1{% else %}mr-1{% endif %}"></i> {% trans "تطبيق الجدول" %}
                            </a>
                        </div>
                    </div>
                </div>
            {% endfor %}
        {% else %}
            <div class="col-12">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle {% if LANGUAGE_CODE == 'ar' %}ml-1{% else %}mr-1{% endif %}"></i> {% trans "لم يتم العثور على جداول صيانة لهذه المركبة. جرب إنشاء أمر عمل مخصص بدلاً من ذلك." %}
                </div>
                <a href="{% url 'work_orders:work_order_detail' work_order.id %}" class="btn btn-primary">
                    <i class="fas fa-wrench {% if LANGUAGE_CODE == 'ar' %}ml-1{% else %}mr-1{% endif %}"></i> {% trans "متابعة مع أمر عمل مخصص" %}
                </a>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Highlight recommended schedules
        $('.schedule-card.recommended').addClass('border-success');
    });
</script>
{% endblock %} 