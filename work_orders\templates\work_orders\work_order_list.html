{% extends "dashboard_base.html" %}
{% load i18n %}
{% load static %}
{% load table_tags %}

{% block title %}{% trans "طلبات العمل" %}{% endblock %}

{% block extra_css %}
<style>
    .status-badge {
        border-radius: 0.375rem;
        padding: 0.5rem 1rem;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        transition: all 0.2s;
        cursor: pointer;
    }
    
    .status-badge:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    .status-badge .count {
        background-color: rgba(255, 255, 255, 0.2);
        border-radius: 9999px;
        padding: 0.125rem 0.5rem;
        margin-right: 0.5rem;
        font-weight: bold;
    }
    
    .status-badge i {
        margin-left: 0.5rem;
    }
    
    .action-btn {
        padding: 0.35rem;
        border-radius: 0.375rem;
        transition: all 0.2s;
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }
    
    .view-btn {
        color: #60a5fa;
        background-color: #eff6ff;
    }
    
    .view-btn:hover {
        background-color: #dbeafe;
    }
    
    .edit-btn {
        color: #34d399;
        background-color: #ecfdf5;
    }
    
    .edit-btn:hover {
        background-color: #d1fae5;
    }
    
    .table-row {
        border-bottom: 1px solid #e5e7eb;
    }
    
    .table-row:hover {
        background-color: #f9fafb;
    }
    
    .table-cell {
        padding: 1rem 0.75rem;
    }
    
    .metric-value {
        direction: ltr;
        display: inline-block;
        font-family: 'Tajawal', sans-serif;
        text-align: start;
    }
    
    /* RTL-specific adjustments */
    html[dir="rtl"] .status-badge .count {
        margin-right: 0;
        margin-left: 0.5rem;
    }
    
    html[dir="rtl"] .status-badge i {
        margin-left: 0;
        margin-right: 0.5rem;
    }
    
    /* Filter form custom styling */
    .filter-form select {
        appearance: none;
        -webkit-appearance: none; 
        -moz-appearance: none;
        background-image: none !important;
        transition: all 0.2s ease;
    }
    
    .filter-form select::-ms-expand {
        display: none;
    }
    
    .filter-form select:hover {
        border-color: #93c5fd;
        box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
    }
    
    .filter-form .relative {
        position: relative;
    }
    
    html[dir="rtl"] .filter-form select {
        padding-left: 2rem !important;
        padding-right: 0.75rem !important;
        text-align: right;
    }
    
    html[dir="rtl"] .filter-form .relative .absolute {
        right: auto !important;
        left: 0 !important;
    }
    
    html[dir="rtl"] .filter-form .chevron-icon {
        left: 0.75rem;
        right: auto;
    }
    
    .filter-active {
        background-color: #f0f9ff;
        border-color: #bae6fd;
    }
    
    .filter-form select.filter-active + div i {
        color: #3b82f6;
        animation: pulse 2s infinite;
    }
    
    @keyframes pulse {
        0% {
            transform: scale(1);
        }
        50% {
            transform: scale(1.2);
        }
        100% {
            transform: scale(1);
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto">
    {% if auto_apply_filter and not selected_franchise and not selected_company and not selected_service_center %}
    <input type="hidden" id="should-auto-apply" value="true">
    {% endif %}

    <!-- Main Card -->
    <div class="relative overflow-x-auto shadow-md sm:rounded-lg bg-white">
        <!-- Card Header with Search and Add Button -->
        <div class="flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 p-4 bg-white sm:rounded-t-lg">
            <div class="w-full md:w-1/2">
                <form class="flex items-center">
                    {% for key, value in preserved_query.items %}
                      {% if key != 'search' %}
                        <input type="hidden" name="{{ key }}" value="{{ value }}">
                      {% endif %}
                    {% endfor %}
                    <label for="simple-search" class="sr-only">Search</label>
                    <div class="relative w-full">
                        <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                            <svg aria-hidden="true" class="w-5 h-5 text-gray-500" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <input type="text" name="search" value="{{ request.GET.search|default:'' }}" id="simple-search" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full pl-10 p-2" placeholder="Search" required="">
                    </div>
                </form>
            </div>
            <div class="w-full md:w-auto flex flex-col md:flex-row space-y-2 md:space-y-0 items-stretch md:items-center justify-end md:space-x-3 flex-shrink-0">
                <a href="{% url 'work_orders:work_order_create' %}" class="flex items-center justify-center text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-4 py-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800">
                    <i class="fas fa-file-medical h-3.5 w-3.5 mr-2"></i>
                    {% trans "أضف أمر عمل جديد" %}
                </a>
            </div>
        </div>
        
        <!-- Status Filter Badges -->
        <div class="p-4 flex flex-wrap gap-3 bg-gray-50 border-b justify-end">
            <a href="{% url 'work_orders:work_order_list' %}{% if selected_franchise or selected_company or selected_service_center or request.GET.search %}?{% endif %}{% if selected_franchise %}franchise_id={{ selected_franchise.id }}{% if selected_company or selected_service_center or request.GET.search %}&{% endif %}{% endif %}{% if selected_company %}company_id={{ selected_company.id }}{% if selected_service_center or request.GET.search %}&{% endif %}{% endif %}{% if selected_service_center %}service_center_id={{ selected_service_center.id }}{% if request.GET.search %}&{% endif %}{% endif %}{% if request.GET.search %}search={{ request.GET.search }}{% endif %}"
               class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium me-2 {% if not active_status %}bg-gray-600{% else %}bg-gray-500{% endif %} hover:bg-gray-600 text-white">
                <span class="count text-white bg-white bg-opacity-20 rounded-full px-1.5 py-0.5 mr-1">{% trans "" %}{{ paginator.count|default:0 }}</span>
                {% trans "الكل" %}
            </a>
            <a href="{% url 'work_orders:work_order_list' %}?status=cancelled{% if selected_franchise %}&franchise_id={{ selected_franchise.id }}{% endif %}{% if selected_company %}&company_id={{ selected_company.id }}{% endif %}{% if selected_service_center %}&service_center_id={{ selected_service_center.id }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}"
               class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium me-2 {% if active_status == 'cancelled' %}bg-red-600{% else %}bg-red-500{% endif %} hover:bg-red-600 text-white">
                <span class="count text-white bg-white bg-opacity-20 rounded-full px-1.5 py-0.5 mr-1">{% trans "" %}{{ status_counts.cancelled|default:0 }}</span>
                {% trans "ملغي" %}
                <i class="fas fa-ban ml-1"></i>
            </a>
            
            <a href="{% url 'work_orders:work_order_list' %}?status=completed{% if selected_franchise %}&franchise_id={{ selected_franchise.id }}{% endif %}{% if selected_company %}&company_id={{ selected_company.id }}{% endif %}{% if selected_service_center %}&service_center_id={{ selected_service_center.id }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}"
               class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium me-2 {% if active_status == 'completed' %}bg-green-600{% else %}bg-green-500{% endif %} hover:bg-green-600 text-white">
                <span class="count text-white bg-white bg-opacity-20 rounded-full px-1.5 py-0.5 mr-1">{% trans "" %}{{ status_counts.completed|default:0 }}</span>
                {% trans "مكتمل" %}
                <i class="fas fa-check ml-1"></i>
            </a>
            
            <a href="{% url 'work_orders:work_order_list' %}?status=on_hold{% if selected_franchise %}&franchise_id={{ selected_franchise.id }}{% endif %}{% if selected_company %}&company_id={{ selected_company.id }}{% endif %}{% if selected_service_center %}&service_center_id={{ selected_service_center.id }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}"
               class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium me-2 {% if active_status == 'on_hold' %}bg-yellow-600{% else %}bg-yellow-500{% endif %} hover:bg-yellow-600 text-white">
                <span class="count text-white bg-white bg-opacity-20 rounded-full px-1.5 py-0.5 mr-1">{% trans "" %}{{ status_counts.on_hold|default:0 }}</span>
                {% trans "قيد الانتظار" %}
                <i class="fas fa-pause ml-1"></i>
            </a>
            
            <a href="{% url 'work_orders:work_order_list' %}?status=in_progress{% if selected_franchise %}&franchise_id={{ selected_franchise.id }}{% endif %}{% if selected_company %}&company_id={{ selected_company.id }}{% endif %}{% if selected_service_center %}&service_center_id={{ selected_service_center.id }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}"
               class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium me-2 {% if active_status == 'in_progress' %}bg-blue-600{% else %}bg-blue-500{% endif %} hover:bg-blue-600 text-white">
                <span class="count text-white bg-white bg-opacity-20 rounded-full px-1.5 py-0.5 mr-1">{% trans "" %}{{ status_counts.in_progress|default:0 }}</span>
                {% trans "تحت التشغيل" %}
                <i class="fas fa-cog ml-1"></i>
            </a>
            
            <a href="{% url 'work_orders:work_order_list' %}?status=planned{% if selected_franchise %}&franchise_id={{ selected_franchise.id }}{% endif %}{% if selected_company %}&company_id={{ selected_company.id }}{% endif %}{% if selected_service_center %}&service_center_id={{ selected_service_center.id }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}"
               class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium me-2 {% if active_status == 'planned' %}bg-indigo-600{% else %}bg-indigo-500{% endif %} hover:bg-indigo-600 text-white">
                <span class="count text-white bg-white bg-opacity-20 rounded-full px-1.5 py-0.5 mr-1">{% trans "" %}{{ status_counts.planned|default:0 }}</span>
                {% trans "استقبال" %}
                <i class="fas fa-clipboard-list ml-1"></i>
            </a>
        </div>
        
        <!-- Responsive Table using our new component -->
        {% with headers=headers rows=table_rows id="work-orders-table" empty_message=_("لم يتم العثور على أوامر عمل.") %}
            {% render_responsive_table headers rows id empty_message=empty_message %}
        {% endwith %}
        
        <!-- Pagination -->
        {% if is_paginated %}
        <div class="p-4 border-t flex justify-center">
            <nav aria-label="Page navigation example">
                <ul class="inline-flex -space-x-px text-sm rounded-lg overflow-hidden">
                    {% if page_obj.has_previous %}
                    <li>
                        <a href="?page={{ page_obj.previous_page_number }}{% if active_status %}&status={{ active_status }}{% endif %}{% if selected_franchise %}&franchise_id={{ selected_franchise.id }}{% endif %}{% if selected_company %}&company_id={{ selected_company.id }}{% endif %}{% if selected_service_center %}&service_center_id={{ selected_service_center.id }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}"
                           class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 rtl:rotate-180">
                            Previous
                        </a>
                    </li>
                    {% endif %}

                    {% for i in paginator.page_range %}
                        <li>
                            <a href="?page={{ i }}{% if active_status %}&status={{ active_status }}{% endif %}{% if selected_franchise %}&franchise_id={{ selected_franchise.id }}{% endif %}{% if selected_company %}&company_id={{ selected_company.id }}{% endif %}{% if selected_service_center %}&service_center_id={{ selected_service_center.id }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}"
                               class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 {% if page_obj.number == i %}z-10 text-blue-600 border-blue-300 bg-blue-50 hover:bg-blue-100 hover:text-blue-700{% endif %}"
                               {% if page_obj.number == i %}aria-current="page"{% endif %}>
                                <span class="metric-value">{{ i }}</span>
                            </a>
                        </li>
                    {% endfor %}

                    {% if page_obj.has_next %}
                    <li>
                        <a href="?page={{ page_obj.next_page_number }}{% if active_status %}&status={{ active_status }}{% endif %}{% if selected_franchise %}&franchise_id={{ selected_franchise.id }}{% endif %}{% if selected_company %}&company_id={{ selected_company.id }}{% endif %}{% if selected_service_center %}&service_center_id={{ selected_service_center.id }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}"
                           class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 rounded-r-lg hover:bg-gray-100 hover:text-gray-700 rtl:rotate-180">
                            Next
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add auto-apply filter functionality for this specific page
        autoApplyUserFilters();
        
        // Force reload once on first visit to ensure proper initialization
        if (!sessionStorage.getItem('work_orders_initialized')) {
            sessionStorage.setItem('work_orders_initialized', 'true');
            // Only reload if no filters are set, to apply role-based default filters
            const urlParams = new URLSearchParams(window.location.search);
            const franchise_id = urlParams.get('franchise_id');
            const company_id = urlParams.get('company_id');
            const service_center_id = urlParams.get('service_center_id');
            
            if (!franchise_id && !company_id && !service_center_id) {
                window.location.reload();
            }
        } else {
            // Clear the initialization flag after 30 minutes (1800000 ms)
            // This allows reinitialization after some time
            setTimeout(() => {
                sessionStorage.removeItem('work_orders_initialized');
            }, 1800000);
        }
    });
    
    function autoApplyUserFilters() {
        // Check if we should auto-apply filters based on user role
        const shouldAutoApplyElement = document.getElementById('should-auto-apply');
        if (!shouldAutoApplyElement) return;
        
        const franchiseSelect = document.getElementById('franchise_id');
        
        if (franchiseSelect) {
            // If user has access to only one franchise, auto-select it
            if (franchiseSelect.options.length === 2) { // First option is "All"
                franchiseSelect.selectedIndex = 1;
                
                // Get the company select and check if there's only one company
                const companySelect = document.getElementById('company_id');
                
                if (companySelect && companySelect.options.length === 2) {
                    // If there's only one company, select it
                    companySelect.selectedIndex = 1;
                    companySelect.disabled = false;
                    
                    // Check for service center
                    const serviceCenterSelect = document.getElementById('service_center_id');
                    
                    if (serviceCenterSelect && serviceCenterSelect.options.length === 2) {
                        // If there's only one service center, select it
                        serviceCenterSelect.selectedIndex = 1;
                        serviceCenterSelect.disabled = false;
                    }
                }
                
                // Submit the form to apply filters
                const filterForm = franchiseSelect.closest('form');
                if (filterForm) {
                    // Store a flag to prevent infinite reload loops
                    sessionStorage.setItem('filters_applied', 'true');
                    filterForm.submit();
                }
            }
        }
    }
</script>
{% endblock %} 