from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from django.contrib import messages
from django.utils.html import format_html
from django.urls import reverse
from django.shortcuts import redirect

from .models import (
    ServiceLevel,
    Franchise,
    Company,
    ServiceCenterType,
    ServiceCenter,
    Vehicle,
    ServiceHistory,
    Customer,
    ServiceCenterMakeModel,
    VehicleOwnershipTransfer,
    VehicleMake,
    VehicleModel
)

@admin.register(Customer)
class CustomerAdmin(admin.ModelAdmin):
    list_display = ('full_name', 'customer_type', 'phone', 'email', 'id_type', 'id_number', 'gender', 'age', 'is_active')
    list_filter = ('customer_type', 'gender', 'is_active', 'service_center')
    search_fields = ('first_name', 'second_name', 'third_name', 'last_name', 'phone', 'email', 'id_number')
    fieldsets = (
        (None, {
            'fields': ('tenant_id', 'is_active')
        }),
        ('Personal Information', {
            'fields': (('first_name', 'second_name'), ('third_name', 'last_name'), 'gender', 'date_of_birth')
        }),
        ('Identification', {
            'fields': ('id_type', 'id_number')
        }),
        ('Contact Information', {
            'fields': ('phone', 'email', 'address', 'city', 'state', 'postal_code', 'country')
        }),
        ('Customer Type', {
            'fields': ('customer_type', 'company_name', 'company_registration')
        }),
        ('Association', {
            'fields': ('service_center',)
        }),
        ('Additional Information', {
            'fields': ('notes', 'attributes')
        }),
    )
    
    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        if 'company_name' in form.base_fields:
            form.base_fields['company_name'].widget.attrs['placeholder'] = _('Only for corporate customers')
        return form

@admin.register(ServiceLevel)
class ServiceLevelAdmin(admin.ModelAdmin):
    list_display = ('name', 'priority', 'response_time_hours', 'resolution_time_hours', 'is_active')
    list_filter = ('is_active',)
    search_fields = ('name', 'description')
    ordering = ('priority',)
    fieldsets = (
        (None, {
            'fields': ('name', 'description', 'priority', 'is_active'),
        }),
        (_('Primary SLA Terms'), {
            'fields': ('response_time_hours', 'resolution_time_hours', 'support_hours'),
        }),
        (_('Extended SLA Metrics'), {
            'fields': (
                'emergency_response_time_hours', 
                'onsite_response_time_hours',
                'parts_delivery_time_hours',
            ),
        }),
        (_('Availability & Maintenance'), {
            'fields': (
                'availability_target_percent',
                'scheduled_maintenance_frequency',
                'maintenance_window_hours',
            ),
        }),
        (_('Service Desk & Support'), {
            'fields': (
                'service_desk_hours',
                'max_incidents_per_month',
            ),
        }),
        (_('Financial Terms'), {
            'fields': ('penalty_per_hour_downtime',),
        }),
        (_('Additional Information'), {
            'fields': ('attributes',),
            'classes': ('collapse',),
        }),
    )

class CompanyInline(admin.TabularInline):
    model = Company
    extra = 0
    fields = ('name', 'code', 'is_active')
    show_change_link = True

@admin.register(Franchise)
class FranchiseAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'country', 'is_active')
    list_filter = ('is_active', 'country')
    search_fields = ('name', 'code', 'address', 'city')
    inlines = [CompanyInline]
    fieldsets = (
        (None, {
            'fields': ('name', 'code', 'logo', 'is_active'),
        }),
        (_('Contact Information'), {
            'fields': ('address', 'city', 'state', 'country', 'postal_code', 'phone', 'email', 'website'),
        }),
        (_('Business Information'), {
            'fields': ('tax_id', 'registration_number', 'founding_date'),
        }),
        (_('Additional Information'), {
            'fields': ('notes', 'attributes'),
            'classes': ('collapse',),
        }),
    )

class ServiceCenterInline(admin.TabularInline):
    model = ServiceCenter
    extra = 0
    fields = ('name', 'code', 'center_type', 'city', 'is_active')
    show_change_link = True

@admin.register(Company)
class CompanyAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'franchise', 'country', 'is_active')
    list_filter = ('is_active', 'country', 'franchise')
    search_fields = ('name', 'code', 'address', 'city')
    inlines = [ServiceCenterInline]
    fieldsets = (
        (None, {
            'fields': ('franchise', 'name', 'code', 'logo', 'is_active'),
        }),
        (_('Contact Information'), {
            'fields': ('address', 'city', 'state', 'country', 'postal_code', 'phone', 'email', 'website'),
        }),
        (_('Business Information'), {
            'fields': ('tax_id', 'registration_number', 'founding_date', 'service_level'),
        }),
        (_('Additional Information'), {
            'fields': ('notes', 'attributes'),
            'classes': ('collapse',),
        }),
    )

@admin.register(ServiceCenterType)
class ServiceCenterTypeAdmin(admin.ModelAdmin):
    list_display = ('name', 'max_capacity', 'is_active')
    list_filter = ('is_active',)
    search_fields = ('name', 'description')
    fieldsets = (
        (None, {
            'fields': ('name', 'description', 'max_capacity', 'color_code', 'is_active'),
        }),
        (_('Additional Information'), {
            'fields': ('attributes',),
            'classes': ('collapse',),
        }),
    )

class VehicleInline(admin.TabularInline):
    model = Vehicle
    extra = 0
    fields = ('make', 'model', 'year', 'license_plate', 'owner')
    show_change_link = True

class ServiceHistoryInline(admin.TabularInline):
    model = ServiceHistory
    extra = 0
    fields = ('service_date', 'odometer', 'work_order_number', 'total_cost')
    show_change_link = True

class ServiceCenterMakeModelInline(admin.TabularInline):
    model = ServiceCenterMakeModel
    extra = 1
    fields = ('make', 'model', 'is_active', 'notes')

@admin.register(ServiceCenter)
class ServiceCenterAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'company', 'center_type', 'city', 'is_active')
    list_filter = ('is_active', 'center_type', 'country', 'company', 'serves_all_vehicle_makes')
    search_fields = ('name', 'code', 'address', 'city')
    inlines = [ServiceCenterMakeModelInline, VehicleInline]
    fieldsets = (
        (None, {
            'fields': ('tenant_id', 'company', 'name', 'code', 'center_type', 'size', 'is_active'),
        }),
        (_('Vehicle Service Capability'), {
            'fields': ('serves_all_vehicle_makes',),
        }),
        (_('Contact Information'), {
            'fields': ('address', 'city', 'state', 'country', 'postal_code', 'latitude', 'longitude', 'phone', 'email'),
        }),
        (_('Operational Information'), {
            'fields': ('manager', 'opening_hours', 'capacity', 'service_level'),
        }),
        (_('Warehouse Information'), {
            'fields': ('primary_warehouse',),
        }),
        (_('Additional Information'), {
            'fields': ('notes', 'attributes'),
            'classes': ('collapse',),
        }),
    )

class OwnershipTransferInline(admin.TabularInline):
    model = VehicleOwnershipTransfer
    extra = 0
    fields = ('transfer_date', 'previous_owner', 'new_owner', 'status')
    readonly_fields = ('transfer_date', 'previous_owner', 'new_owner', 'status')
    can_delete = False
    
    def has_add_permission(self, request, obj=None):
        return False

@admin.register(Vehicle)
class VehicleAdmin(admin.ModelAdmin):
    list_display = ('make', 'model', 'year', 'license_plate', 'vin', 'owner', 'service_center', 'get_transfer_link')
    list_filter = ('make', 'service_center', 'standard_make')
    search_fields = ('make', 'model', 'license_plate', 'vin')
    filter_horizontal = ('drivers',)
    inlines = [OwnershipTransferInline]
    actions = ['initiate_ownership_transfer']
    
    def get_queryset(self, request):
        qs = super().get_queryset(request)
        return qs.select_related('owner', 'service_center', 'standard_make', 'standard_model')
    
    def get_transfer_link(self, obj):
        """Generate a link to initiate transfer for this vehicle"""
        if obj.owner:
            url = f"{reverse('setup:initiate_vehicle_transfer')}?vehicle_id={obj.pk}"
            return format_html('<a href="{}" class="button">Transfer</a>', url)
        return "-"
    get_transfer_link.short_description = _("Transfer")
    
    def get_fieldsets(self, request, obj=None):
        fieldsets = [
            (None, {
                'fields': ('tenant_id', ('make', 'model', 'year'), ('license_plate', 'vin', 'color')),
            }),
            (_('Standard Make/Model Reference'), {
                'fields': ('standard_make', 'standard_model'),
                'description': _('Link to standardized vehicle makes and models for better data consistency'),
            }),
            (_('Ownership & Service'), {
                'fields': ('owner', 'service_center'),
            }),
            (_('Other Information'), {
                'fields': ('purchase_date', 'warranty_end_date', 'notes', 'attributes'),
                'classes': ('collapse',),
            }),
        ]
        return fieldsets
        
    def initiate_ownership_transfer(self, request, queryset):
        """Admin action to initiate ownership transfers for selected vehicles"""
        # Store selected vehicle IDs in session for the redirect
        vehicle_ids = [str(vehicle.pk) for vehicle in queryset]
        request.session['selected_vehicle_ids'] = vehicle_ids
        
        # Redirect to a custom view for transfer initiation
        return redirect('setup:initiate_vehicle_transfer')
    initiate_ownership_transfer.short_description = _("Initiate ownership transfer")

@admin.register(ServiceHistory)
class ServiceHistoryAdmin(admin.ModelAdmin):
    list_display = ('vehicle', 'service_date', 'service_center', 'work_order_number', 'odometer')
    list_filter = ('service_date', 'service_center')
    search_fields = ('vehicle__make', 'vehicle__model', 'vehicle__license_plate', 'work_order_number')
    date_hierarchy = 'service_date'

@admin.register(ServiceCenterMakeModel)
class ServiceCenterMakeModelAdmin(admin.ModelAdmin):
    list_display = ('service_center', 'make', 'model', 'is_active')
    list_filter = ('service_center', 'make', 'is_active')
    search_fields = ('service_center__name', 'make', 'model')
    fieldsets = (
        (None, {
            'fields': ('tenant_id', 'service_center', 'make', 'model', 'is_active'),
        }),
        (_('Additional Information'), {
            'fields': ('notes',),
        }),
    )

class VehicleModelInline(admin.TabularInline):
    model = VehicleModel
    extra = 1
    fields = ('name', 'vehicle_class', 'year_introduced', 'year_discontinued', 'is_active')

@admin.register(VehicleMake)
class VehicleMakeAdmin(admin.ModelAdmin):
    list_display = ('name', 'country_of_origin', 'is_active')
    list_filter = ('is_active', 'country_of_origin')
    search_fields = ('name', 'description')
    inlines = [VehicleModelInline]
    fieldsets = (
        (None, {
            'fields': ('tenant_id', 'name', 'description', 'logo', 'is_active'),
        }),
        (_('Origin Information'), {
            'fields': ('country_of_origin',),
        }),
        (_('Additional Information'), {
            'fields': ('attributes',),
            'classes': ('collapse',),
        }),
    )

@admin.register(VehicleModel)
class VehicleModelAdmin(admin.ModelAdmin):
    list_display = ('name', 'make', 'vehicle_class', 'year_introduced', 'year_discontinued', 'is_active')
    list_filter = ('make', 'vehicle_class', 'is_active')
    search_fields = ('name', 'make__name', 'description')
    fieldsets = (
        (None, {
            'fields': ('tenant_id', 'make', 'name', 'description', 'is_active'),
        }),
        (_('Classification'), {
            'fields': ('vehicle_class',),
        }),
        (_('Production Years'), {
            'fields': ('year_introduced', 'year_discontinued'),
        }),
        (_('Additional Information'), {
            'fields': ('attributes',),
            'classes': ('collapse',),
        }),
    )

@admin.register(VehicleOwnershipTransfer)
class VehicleOwnershipTransferAdmin(admin.ModelAdmin):
    list_display = ('vehicle', 'previous_owner', 'new_owner', 'transfer_date', 'status', 'approved_by')
    list_filter = ('status', 'transfer_date', 'approved_by')
    search_fields = ('vehicle__license_plate', 'vehicle__vin', 'previous_owner__first_name', 'previous_owner__last_name', 
                    'new_owner__first_name', 'new_owner__last_name', 'notes')
    readonly_fields = ('approved_by', 'approved_date')
    date_hierarchy = 'transfer_date'
    actions = ['approve_transfers', 'complete_transfers', 'reject_transfers', 'cancel_transfers']
    
    fieldsets = (
        (None, {
            'fields': ('tenant_id', 'vehicle', 'status'),
        }),
        (_('Ownership Details'), {
            'fields': (('previous_owner', 'new_owner'), 'transfer_date'),
        }),
        (_('Transfer Information'), {
            'fields': ('sale_price', 'odometer_reading', 'documents', 'notes'),
        }),
        (_('Approval Information'), {
            'fields': (('approved_by', 'approved_date'),),
            'classes': ('collapse',),
        }),
    )
    
    def approve_transfers(self, request, queryset):
        """Admin action to approve selected pending transfers"""
        count = 0
        for transfer in queryset.filter(status='pending'):
            try:
                transfer.approve(request.user)
                count += 1
            except Exception as e:
                self.message_user(request, f"Error approving transfer {transfer}: {str(e)}", messages.ERROR)
                
        if count > 0:
            self.message_user(request, _("{} transfers were approved.").format(count), messages.SUCCESS)
    approve_transfers.short_description = _("Approve selected transfers")
    
    def complete_transfers(self, request, queryset):
        """Admin action to complete selected approved transfers"""
        count = 0
        for transfer in queryset.filter(status='approved'):
            try:
                transfer.complete()
                count += 1
            except Exception as e:
                self.message_user(request, f"Error completing transfer {transfer}: {str(e)}", messages.ERROR)
                
        if count > 0:
            self.message_user(request, _("{} transfers were completed.").format(count), messages.SUCCESS)
    complete_transfers.short_description = _("Complete selected transfers")
    
    def reject_transfers(self, request, queryset):
        """Admin action to reject selected pending or approved transfers"""
        count = 0
        for transfer in queryset.filter(status__in=['pending', 'approved']):
            try:
                transfer.reject()
                count += 1
            except Exception as e:
                self.message_user(request, f"Error rejecting transfer {transfer}: {str(e)}", messages.ERROR)
                
        if count > 0:
            self.message_user(request, _("{} transfers were rejected.").format(count), messages.SUCCESS)
    reject_transfers.short_description = _("Reject selected transfers")
    
    def cancel_transfers(self, request, queryset):
        """Admin action to cancel selected pending or approved transfers"""
        count = 0
        for transfer in queryset.filter(status__in=['pending', 'approved']):
            try:
                transfer.cancel()
                count += 1
            except Exception as e:
                self.message_user(request, f"Error cancelling transfer {transfer}: {str(e)}", messages.ERROR)
                
        if count > 0:
            self.message_user(request, _("{} transfers were cancelled.").format(count), messages.SUCCESS)
    cancel_transfers.short_description = _("Cancel selected transfers")
    
    def get_queryset(self, request):
        qs = super().get_queryset(request)
        return qs.select_related('vehicle', 'previous_owner', 'new_owner', 'approved_by')
    
    def save_model(self, request, obj, form, change):
        """Auto-set approved_by when status changes to approved"""
        if not change:  # New transfer
            pass
        elif 'status' in form.changed_data and obj.status == 'approved' and not obj.approved_by:
            obj.approved_by = request.user
            obj.approved_date = timezone.now()
            
        super().save_model(request, obj, form, change)
