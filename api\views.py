from django.shortcuts import render
from rest_framework import viewsets, status
from rest_framework.permissions import IsAuthenticated
from waffle.decorators import waffle_flag
from rest_framework.decorators import action
from rest_framework.response import Response
from django.utils.decorators import method_decorator
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FormParser

from inventory.models import Item, Movement, ItemDocument
from reports.models import Report, ReportExecution, Dashboard, DashboardWidget
from reports.services import execute_report, get_dashboard_data
from .serializers import (
    ItemSerializer, MovementSerializer, ItemDocumentSerializer,
    ReportSerializer, ReportExecutionSerializer, 
    DashboardSerializer, DashboardWidgetSerializer
)


class WaffleFlagProtectedViewSet(viewsets.ModelViewSet):
    """Base ViewSet that requires the 'api' waffle flag to be active"""
    
    @method_decorator(waffle_flag('api', redirect_to=None))
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)


class ItemViewSet(WaffleFlagProtectedViewSet):
    """API endpoint for inventory items"""
    queryset = Item.objects.all()
    serializer_class = ItemSerializer
    permission_classes = [IsAuthenticated]
    filterset_fields = ['name', 'sku']
    search_fields = ['name', 'sku', 'description']

    @action(detail=True, methods=['get'])
    def movements(self, request, pk=None):
        """Get all movements for a specific item"""
        item = self.get_object()
        movements = Movement.objects.filter(item=item)
        serializer = MovementSerializer(movements, many=True)
        return Response(serializer.data)
        
    @action(detail=True, methods=['get'])
    def documents(self, request, pk=None):
        """Get all documents for a specific item"""
        item = self.get_object()
        
        # By default, only show public documents to regular users
        # Allow admins to see all documents
        if not request.user.is_staff:
            documents = item.documents.filter(is_public=True)
        else:
            documents = item.documents.all()
            
        serializer = ItemDocumentSerializer(documents, many=True, context={'request': request})
        return Response(serializer.data)


class MovementViewSet(WaffleFlagProtectedViewSet):
    """API endpoint for inventory movements"""
    queryset = Movement.objects.all()
    serializer_class = MovementSerializer
    permission_classes = [IsAuthenticated]
    filterset_fields = ['item', 'movement_type', 'created_at']


class ItemDocumentViewSet(WaffleFlagProtectedViewSet):
    """API endpoint for item documents"""
    queryset = ItemDocument.objects.all()
    serializer_class = ItemDocumentSerializer
    permission_classes = [IsAuthenticated]
    filterset_fields = ['item', 'document_type', 'is_public']
    parser_classes = [MultiPartParser, FormParser]
    
    def get_queryset(self):
        """
        Filter queryset based on user permissions.
        Staff users can see all documents, regular users only see public documents.
        """
        queryset = super().get_queryset()
        
        # Non-staff users can only see public documents
        if not self.request.user.is_staff:
            queryset = queryset.filter(is_public=True)
            
        return queryset
    
    def get_serializer_context(self):
        """
        Add request to serializer context to generate absolute URLs
        """
        context = super().get_serializer_context()
        context['request'] = self.request
        return context


class AdvancedReportingViewSet(WaffleFlagProtectedViewSet):
    """Base ViewSet that requires both 'api' and 'advanced_reporting' waffle flags"""
    
    @method_decorator(waffle_flag('advanced_reporting', redirect_to=None))
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)


class ReportViewSet(AdvancedReportingViewSet):
    """API endpoint for reports"""
    queryset = Report.objects.all()
    serializer_class = ReportSerializer
    permission_classes = [IsAuthenticated]
    filterset_fields = ['report_type', 'is_scheduled']
    search_fields = ['name', 'description']
    
    def get_queryset(self):
        """Filter reports by tenant"""
        return Report.objects.filter(tenant_id=self.request.tenant_id)
    
    @action(detail=True, methods=['post'])
    def execute(self, request, pk=None):
        """Execute a report with provided parameters"""
        report = self.get_object()
        
        try:
            # Get parameters from request data
            parameters = request.data.get('parameters', {})
            
            # Execute report
            execution, data = execute_report(
                report_id=report.id,
                parameters=parameters,
                tenant_id=request.tenant_id,
                user=request.user
            )
            
            # Return report data
            return Response({
                'execution_id': str(execution.id),
                'status': execution.status,
                'data': data
            })
        except Exception as e:
            return Response({
                'error': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)


class ReportExecutionViewSet(AdvancedReportingViewSet):
    """API endpoint for report executions"""
    queryset = ReportExecution.objects.all()
    serializer_class = ReportExecutionSerializer
    permission_classes = [IsAuthenticated]
    filterset_fields = ['report', 'status', 'created_at']
    
    def get_queryset(self):
        """Filter executions by tenant"""
        return ReportExecution.objects.filter(tenant_id=self.request.tenant_id)


class DashboardViewSet(AdvancedReportingViewSet):
    """API endpoint for dashboards"""
    queryset = Dashboard.objects.all()
    serializer_class = DashboardSerializer
    permission_classes = [IsAuthenticated]
    filterset_fields = ['is_default']
    search_fields = ['name', 'description']
    
    def get_queryset(self):
        """Filter dashboards by tenant"""
        return Dashboard.objects.filter(tenant_id=self.request.tenant_id)
    
    @action(detail=True, methods=['get'])
    def data(self, request, pk=None):
        """Get dashboard data with widget data"""
        dashboard = self.get_object()
        
        try:
            # Get dashboard data
            dashboard_data = get_dashboard_data(
                dashboard_id=dashboard.id,
                tenant_id=request.tenant_id
            )
            
            return Response(dashboard_data)
        except Exception as e:
            return Response({
                'error': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)


class DashboardWidgetViewSet(AdvancedReportingViewSet):
    """API endpoint for dashboard widgets"""
    queryset = DashboardWidget.objects.all()
    serializer_class = DashboardWidgetSerializer
    permission_classes = [IsAuthenticated]
    filterset_fields = ['dashboard', 'widget_type']
    
    def get_queryset(self):
        """Filter widgets by tenant through their dashboard"""
        return DashboardWidget.objects.filter(dashboard__tenant_id=self.request.tenant_id)
    
    @action(detail=True, methods=['get'])
    def data(self, request, pk=None):
        """Get widget data"""
        widget = self.get_object()
        
        try:
            # Execute the report for this widget
            if widget.report:
                params = widget.config.get('parameters', {})
                _, data = execute_report(
                    widget.report.id, 
                    parameters=params,
                    tenant_id=request.tenant_id
                )
                
                return Response({
                    'widget_id': str(widget.id),
                    'data': data
                })
            else:
                return Response({
                    'widget_id': str(widget.id),
                    'data': None,
                    'error': 'No report associated with this widget'
                }, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({
                'widget_id': str(widget.id),
                'error': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)
