from django.shortcuts import render, get_object_or_404, redirect
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy, reverse
from django.utils.translation import gettext_lazy as _
from feature_flags.middleware import requires_module, requires_feature
from inventory.models import Item, Movement, UnitOfMeasurement, UnitConversion, MovementType, OperationCompatibility
from django.db.models import Count, Sum, Avg, F, Q
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib import messages
from django.core.exceptions import ValidationError


@requires_module('inventory')
class DashboardView(LoginRequiredMixin, TemplateView):
    template_name = 'inventory/dashboard.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        tenant_id = getattr(self.request, 'tenant_id', None)
        queryset = Item.objects.all()
        
        if tenant_id:
            queryset = queryset.for_tenant(tenant_id)
        
        # Get basic stats
        context['total_items'] = queryset.count()
        context['low_stock_count'] = queryset.filter(
            quantity__lt=F('min_stock_level')
        ).count()
        
        # Recent items
        context['recent_items'] = queryset.order_by('-created_at')[:5]
        
        # Add low stock items for alerts section
        context['low_stock_items'] = queryset.filter(
            quantity__lt=F('min_stock_level')
        ).order_by('quantity')[:5]
        
        # Operation compatibilities for common operations
        compat_queryset = OperationCompatibility.objects.all()
        if tenant_id:
            compat_queryset = compat_queryset.for_tenant(tenant_id)
        
        # Filter to show common operations first, then required ones
        context['operation_compatibilities'] = compat_queryset.filter(
            Q(is_common=True) | Q(is_required=True)
        ).select_related('item', 'operation_type', 'item__unit_of_measurement').order_by('-is_common', '-is_required')[:10]
        
        # Movement stats if available
        movement_queryset = Movement.objects.all()
        if tenant_id:
            movement_queryset = movement_queryset.for_tenant(tenant_id)
        
        context['total_movements'] = movement_queryset.count()
        context['recent_movements'] = movement_queryset.order_by('-created_at')[:5]
        
        # Units of measurement stats
        unit_queryset = UnitOfMeasurement.objects.all()
        if tenant_id:
            unit_queryset = unit_queryset.for_tenant(tenant_id)
        
        context['total_units'] = unit_queryset.count()
        context['recent_units'] = unit_queryset.order_by('-created_at')[:5]
        
        # Add URLs for the template
        context['inventory_item_list_url'] = reverse('inventory:item_list')
        context['inventory_low_stock_url'] = reverse('inventory:item_list') + '?low_stock=1'
        context['inventory_movement_list_url'] = reverse('inventory:movement_list')
        context['inventory_item_create_url'] = reverse('inventory:item_create')
        context['inventory_movement_create_url'] = reverse('inventory:movement_create')
        context['inventory_scan_barcode_url'] = reverse('inventory:scan_barcode')
        
        context['title'] = _('Inventory Dashboard')
        return context


@requires_module('inventory')
class ItemListView(ListView):
    model = Item
    template_name = 'inventory/item_list.html'
    context_object_name = 'items'
    
    def get_queryset(self):
        """
        Filter by tenant ID if available
        """
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            return Item.objects.for_tenant(tenant_id)
        return Item.objects.all()
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get available units for reference
        tenant_id = getattr(self.request, 'tenant_id', None)
        units_queryset = UnitOfMeasurement.objects.all()
        
        if tenant_id:
            units_queryset = units_queryset.for_tenant(tenant_id)
            
        context['units'] = units_queryset
        return context


@requires_module('inventory')
class ItemDetailView(DetailView):
    model = Item
    template_name = 'inventory/item_detail.html'
    context_object_name = 'item'
    
    def get_object(self, queryset=None):
        """
        Get object filtered by tenant ID if available
        """
        if queryset is None:
            queryset = self.get_queryset()
            
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            queryset = queryset.for_tenant(tenant_id)
            
        return get_object_or_404(queryset, pk=self.kwargs['pk'])
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get available units for conversion
        tenant_id = getattr(self.request, 'tenant_id', None)
        units_queryset = UnitOfMeasurement.objects.all()
        
        if tenant_id:
            units_queryset = units_queryset.for_tenant(tenant_id)
            
        context['units'] = units_queryset
        
        # Get item quantity in different units
        item = self.get_object()
        if item.unit_of_measurement:
            converted_quantities = []
            
            for unit in units_queryset:
                if unit != item.unit_of_measurement:
                    try:
                        quantity = item.get_quantity_in_unit(unit)
                        converted_quantities.append({
                            'unit': unit,
                            'quantity': quantity
                        })
                    except ValueError:
                        # No conversion path exists
                        pass
                        
            context['converted_quantities'] = converted_quantities
            
        return context


@requires_module('inventory')
class ItemCreateView(CreateView):
    model = Item
    template_name = 'inventory/item_form.html'
    fields = ['sku', 'name', 'description', 'quantity', 'unit_of_measurement', 'unit_price', 'min_stock_level', 'attributes']
    success_url = reverse_lazy('inventory:item_list')
    
    def get_form(self, form_class=None):
        form = super().get_form(form_class)
        tenant_id = getattr(self.request, 'tenant_id', None)
        
        # Filter units by tenant
        if tenant_id and 'unit_of_measurement' in form.fields:
            form.fields['unit_of_measurement'].queryset = UnitOfMeasurement.objects.for_tenant(tenant_id)
            
        return form
    
    def form_valid(self, form):
        """
        Set tenant ID if available
        """
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            form.instance.tenant_id = tenant_id
        return super().form_valid(form)


@requires_module('inventory')
@requires_feature('inventory_advanced')
class MovementListView(ListView):
    """
    This view requires both the inventory module and the
    inventory_advanced feature to be active
    """
    model = Movement
    template_name = 'inventory/movement_list.html'
    context_object_name = 'movements'
    
    def get_queryset(self):
        """
        Filter by tenant ID if available
        """
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            return Movement.objects.for_tenant(tenant_id)
        return Movement.objects.all()
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get available units for reference
        tenant_id = getattr(self.request, 'tenant_id', None)
        units_queryset = UnitOfMeasurement.objects.all()
        
        if tenant_id:
            units_queryset = units_queryset.for_tenant(tenant_id)
            
        context['units'] = units_queryset
        return context


@requires_module('inventory')
@requires_feature('inventory_advanced')
class MovementCreateView(CreateView):
    model = Movement
    template_name = 'inventory/movement_form.html'
    fields = ['item', 'quantity', 'unit_of_measurement', 'reference', 'notes']
    success_url = reverse_lazy('inventory:movement_list')
    
    def get_form(self, form_class=None):
        form = super().get_form(form_class)
        tenant_id = getattr(self.request, 'tenant_id', None)
        
        # Filter units and items by tenant
        if tenant_id:
            if 'item' in form.fields:
                form.fields['item'].queryset = Item.objects.for_tenant(tenant_id)
                
            if 'unit_of_measurement' in form.fields:
                form.fields['unit_of_measurement'].queryset = UnitOfMeasurement.objects.for_tenant(tenant_id)
                
        return form
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        tenant_id = getattr(self.request, 'tenant_id', None)
        
        # Get movement types for the tenant
        movement_types = MovementType.objects.filter(is_active=True)
        if tenant_id:
            movement_types = movement_types.for_tenant(tenant_id)
            
        # Split movement types by category
        context['movement_types_inbound'] = movement_types.filter(is_inbound=True, is_outbound=False)
        context['movement_types_outbound'] = movement_types.filter(is_inbound=False, is_outbound=True)
        context['movement_types_transfer'] = movement_types.filter(is_inbound=True, is_outbound=True)
        
        return context
    
    def form_valid(self, form):
        """
        Set tenant ID if available and validate movement type
        """
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            form.instance.tenant_id = tenant_id
        
        # Get movement type data from request
        movement_type_ref_id = self.request.POST.get('movement_type_ref')
        legacy_movement_type = self.request.POST.get('movement_type')
        
        # Validate that at least one movement type is set
        if not movement_type_ref_id and not legacy_movement_type:
            form.add_error(None, _("Please select a movement type"))
            return self.form_invalid(form)
        
        # Set the appropriate movement type
        if movement_type_ref_id:
            try:
                movement_type = MovementType.objects.get(pk=movement_type_ref_id)
                if tenant_id and movement_type.tenant_id != tenant_id:
                    form.add_error(None, _("Invalid movement type selected"))
                    return self.form_invalid(form)
                form.instance.movement_type_ref = movement_type
            except MovementType.DoesNotExist:
                form.add_error(None, _("Selected movement type does not exist"))
                return self.form_invalid(form)
        else:
            # Use legacy movement type
            form.instance.movement_type = legacy_movement_type
        
        return super().form_valid(form)


@requires_module('inventory')
@requires_feature('barcode_scanning')
def scan_barcode(request):
    """
    This view requires both the inventory module and the
    barcode_scanning feature to be active
    """
    if request.method == 'POST':
        barcode = request.POST.get('barcode')
        tenant_id = getattr(request, 'tenant_id', None)
        
        try:
            if tenant_id:
                item = Item.objects.get_for_tenant(tenant_id, attributes__barcode=barcode)
            else:
                item = Item.objects.get(attributes__barcode=barcode)
                
            return redirect('inventory:item_detail', pk=item.pk)
        except Item.DoesNotExist:
            # Handle not found
            return render(request, 'inventory/scan_barcode.html', {
                'error': _('Barcode not found'),
                'barcode': barcode
            })
            
    return render(request, 'inventory/scan_barcode.html')


@requires_module('inventory')
class UnitOfMeasurementListView(ListView):
    model = UnitOfMeasurement
    template_name = 'inventory/unit_list.html'
    context_object_name = 'units'
    
    def get_queryset(self):
        """
        Filter by tenant ID if available
        """
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            return UnitOfMeasurement.objects.for_tenant(tenant_id)
        return UnitOfMeasurement.objects.all()


@requires_module('inventory')
class UnitOfMeasurementDetailView(DetailView):
    model = UnitOfMeasurement
    template_name = 'inventory/unit_detail.html'
    context_object_name = 'unit'
    
    def get_object(self, queryset=None):
        """
        Get object filtered by tenant ID if available
        """
        if queryset is None:
            queryset = self.get_queryset()
            
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            queryset = queryset.for_tenant(tenant_id)
            
        return get_object_or_404(queryset, pk=self.kwargs['pk'])
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        unit = self.get_object()
        
        # Get conversions from this unit
        context['conversions_from'] = unit.conversions_from.all()
        
        # Get conversions to this unit
        context['conversions_to'] = unit.conversions_to.all()
        
        # Get items using this unit
        context['items'] = unit.items.all()
        
        return context


@requires_module('inventory')
class UnitOfMeasurementCreateView(CreateView):
    model = UnitOfMeasurement
    template_name = 'inventory/unit_form.html'
    fields = ['name', 'symbol', 'description', 'is_base_unit']
    success_url = reverse_lazy('inventory:unit_list')
    
    def form_valid(self, form):
        """
        Set tenant ID if available
        """
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            form.instance.tenant_id = tenant_id
        return super().form_valid(form)


@requires_module('inventory')
class UnitConversionListView(ListView):
    model = UnitConversion
    template_name = 'inventory/conversion_list.html'
    context_object_name = 'conversions'
    
    def get_queryset(self):
        """
        Filter by tenant ID if available
        """
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            return UnitConversion.objects.for_tenant(tenant_id)
        return UnitConversion.objects.all()


@requires_module('inventory')
class UnitConversionCreateView(CreateView):
    model = UnitConversion
    template_name = 'inventory/conversion_form.html'
    fields = ['from_unit', 'to_unit', 'conversion_factor']
    success_url = reverse_lazy('inventory:conversion_list')
    
    def get_form(self, form_class=None):
        form = super().get_form(form_class)
        tenant_id = getattr(self.request, 'tenant_id', None)
        
        # Filter units by tenant
        if tenant_id:
            form.fields['from_unit'].queryset = UnitOfMeasurement.objects.for_tenant(tenant_id)
            form.fields['to_unit'].queryset = UnitOfMeasurement.objects.for_tenant(tenant_id)
            
        return form
    
    def form_valid(self, form):
        """
        Set tenant ID if available and validate conversion
        """
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            form.instance.tenant_id = tenant_id
            
        # Validate that from_unit and to_unit are different
        from_unit = form.cleaned_data.get('from_unit')
        to_unit = form.cleaned_data.get('to_unit')
        
        if from_unit == to_unit:
            messages.error(self.request, _("From unit and to unit must be different"))
            return self.form_invalid(form)
            
        return super().form_valid(form)


@requires_module('inventory')
@requires_feature('inventory_advanced')
class MovementTypeListView(ListView):
    """
    View for listing and managing movement types
    """
    model = MovementType
    template_name = 'inventory/movement_type_list.html'
    context_object_name = 'movement_types'
    
    def get_queryset(self):
        """
        Filter by tenant ID if available
        """
        tenant_id = getattr(self.request, 'tenant_id', None)
        queryset = MovementType.objects.all()
        
        if tenant_id:
            queryset = queryset.for_tenant(tenant_id)
            
        # Sort by sequence and name
        return queryset.order_by('sequence', 'name')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['inbound_types'] = self.get_queryset().filter(is_inbound=True, is_outbound=False)
        context['outbound_types'] = self.get_queryset().filter(is_inbound=False, is_outbound=True)
        context['transfer_types'] = self.get_queryset().filter(is_inbound=True, is_outbound=True)
        return context


@requires_module('inventory')
@requires_feature('inventory_advanced')
class MovementTypeCreateView(CreateView):
    """
    View for creating a new movement type
    """
    model = MovementType
    template_name = 'inventory/movement_type_form.html'
    fields = ['code', 'name', 'description', 'is_inbound', 'is_outbound', 
              'icon', 'color', 'requires_reference', 'requires_approval', 
              'sequence', 'is_active']
    success_url = reverse_lazy('inventory:movement_type_list')
    
    def form_valid(self, form):
        """
        Set tenant ID if available
        """
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            form.instance.tenant_id = tenant_id
        return super().form_valid(form)


@requires_module('inventory')
@requires_feature('inventory_advanced')
class MovementTypeUpdateView(UpdateView):
    """
    View for updating an existing movement type
    """
    model = MovementType
    template_name = 'inventory/movement_type_form.html'
    fields = ['name', 'description', 'is_inbound', 'is_outbound', 
              'icon', 'color', 'requires_reference', 'requires_approval', 
              'sequence', 'is_active']
    success_url = reverse_lazy('inventory:movement_type_list')
    
    def get_object(self, queryset=None):
        """
        Get object filtered by tenant ID if available
        """
        if queryset is None:
            queryset = self.get_queryset()
            
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            queryset = queryset.for_tenant(tenant_id)
            
        return get_object_or_404(queryset, pk=self.kwargs['pk'])
