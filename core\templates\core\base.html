{% load static %}
{% load i18n %}
{% load waffle_tags %}
<!DOCTYPE html>
<html lang="ar" dir="rtl" class="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{% trans "After-Sales Franchise Management System" %}{% endblock %}</title>
    <link rel="stylesheet" href="{% static 'css/app.css' %}">  {# This will be the output of Tailwind build #}
    <!-- Fallback CSS if local file not found -->
    <script>
        (function() {
            var cssLink = document.querySelector("link[href='{% static 'css/app.css' %}']");
            cssLink.addEventListener('error', function() {
                // Load Tailwind from CDN
                var tailwind = document.createElement('link');
                tailwind.rel = 'stylesheet';
                tailwind.href = 'https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css';
                document.head.appendChild(tailwind);
                
                // Load Flowbite from CDN
                var flowbite = document.createElement('link');
                flowbite.rel = 'stylesheet';
                flowbite.href = 'https://cdnjs.cloudflare.com/ajax/libs/flowbite/1.8.1/flowbite.min.css';
                document.head.appendChild(flowbite);
            });
        })();
    </script>
    {% block extra_css %}{% endblock %}
</head>
<body class="bg-gray-100 dark:bg-gray-900 text-gray-900 dark:text-gray-100 font-sans {% block body_class %}{% endblock %}">

    {% block header %}
    {% if request.resolver_match.url_name != 'login' %}
    <nav class="bg-white border-gray-200 px-4 lg:px-6 py-2.5 dark:bg-gray-800 fixed w-full z-20 top-0 start-0 border-b dark:border-gray-600">
        <div class="container flex flex-wrap justify-between items-center mx-auto">
            <a href="{% url 'core:language_demo' %}" class="flex items-center">
                {# <img src="{% static 'images/logo.png' %}" class="mr-3 h-6 sm:h-9" alt="Logo" /> #}
                <svg class="w-8 h-8 mr-2 text-blue-600 dark:text-blue-500" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path></svg>
                <span class="self-center text-xl font-semibold whitespace-nowrap dark:text-white">{% trans "After-Sales System" %}</span>
            </a>
            <div class="flex items-center lg:order-2">
                {% if user.is_authenticated %}
                    <button data-dropdown-toggle="user-dropdown-menu" type="button" class="flex mx-3 text-sm bg-gray-800 rounded-full md:mr-0 focus:ring-4 focus:ring-gray-300 dark:focus:ring-gray-600" id="user-menu-button" aria-expanded="false">
                        <span class="sr-only">Open user menu</span>
                        {# <img class="w-8 h-8 rounded-full" src="{% static 'images/avatar.png' %}" alt="user photo"> #}
                        <svg class="w-8 h-8 rounded-full text-gray-400 bg-gray-700" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path></svg>
                    </button>
                    <!-- User Dropdown menu -->
                    <div class="hidden z-50 my-4 w-56 text-base list-none bg-white rounded divide-y divide-gray-100 shadow dark:bg-gray-700 dark:divide-gray-600" id="user-dropdown-menu">
                        <div class="py-3 px-4">
                            <span class="block text-sm font-semibold text-gray-900 dark:text-white">{{ user.get_full_name|default:user.username }}</span>
                            <span class="block text-sm text-gray-500 truncate dark:text-gray-400">{{ user.email|default:"" }}</span>
                        </div>
                        <ul class="py-1 text-gray-500 dark:text-gray-400" aria-labelledby="user-menu-button">
                            <li><a href="#" class="block py-2 px-4 text-sm hover:bg-gray-100 dark:hover:bg-gray-600 dark:text-gray-400 dark:hover:text-white">{% trans "Profile" %}</a></li>
                            <li><a href="{% url 'admin:index' %}" class="block py-2 px-4 text-sm hover:bg-gray-100 dark:hover:bg-gray-600 dark:text-gray-400 dark:hover:text-white">{% trans "Admin Dashboard" %}</a></li>
                        </ul>
                        <ul class="py-1 text-gray-500 dark:text-gray-400" aria-labelledby="dropdownDefaultsButton">
                            <li><a href="{% url 'admin:logout' %}" class="block py-2 px-4 text-sm hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">{% trans "Sign out" %}</a></li>
                        </ul>
                    </div>
                {% else %}
                    <a href="{% url 'admin:login' %}" class="text-gray-800 dark:text-white hover:bg-gray-50 focus:ring-4 focus:ring-gray-300 font-medium rounded-lg text-sm px-4 lg:px-5 py-2 lg:py-2.5 mr-2 dark:hover:bg-gray-700 focus:outline-none dark:focus:ring-gray-800 transition duration-200 ease-out hover:-translate-y-0.5 hover:shadow-lg">{% trans "Log in" %}</a>
                {% endif %}
                <button data-collapse-toggle="mobile-menu-2" type="button" class="inline-flex items-center p-2 ml-1 text-sm text-gray-500 rounded-lg lg:hidden hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200 dark:text-gray-400 dark:hover:bg-gray-700 dark:focus:ring-gray-600" aria-controls="mobile-menu-2" aria-expanded="false">
                    <span class="sr-only">Open main menu</span>
                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path></svg>
                    <svg class="hidden w-6 h-6" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>
                </button>
            </div>
            <div class="hidden justify-between items-center w-full lg:flex lg:w-auto lg:order-1" id="mobile-menu-2">
                <ul class="flex flex-col mt-4 font-medium lg:flex-row lg:space-x-8 lg:mt-0 rtl:space-x-reverse">
                    <li>
                        <a href="{% url 'core:language_demo' %}" class="block py-2 pr-4 pl-3 text-gray-700 rounded bg-blue-700 lg:bg-transparent lg:text-blue-700 lg:p-0 dark:text-white" aria-current="page">{% trans "Home" %}</a>
                    </li>
                     {% if user.is_authenticated %}
                        {% flag "inventory_module" %}
                        <li>
                            <button id="inventoryDropdownNavbarLink" data-dropdown-toggle="inventoryDropdownNavbar" class="flex justify-between items-center py-2 pr-4 pl-3 w-full font-medium text-gray-700 border-b border-gray-100 hover:bg-gray-50 lg:hover:bg-transparent lg:border-0 lg:hover:text-blue-700 lg:p-0 lg:w-auto dark:text-gray-400 dark:hover:text-white dark:focus:text-white dark:border-gray-700 dark:hover:bg-gray-700 lg:dark:hover:bg-transparent">{% trans "Inventory" %}
                                <svg class="ml-1 w-4 h-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>
                            </button>
                            <!-- Inventory Dropdown menu -->
                            <div id="inventoryDropdownNavbar" class="hidden z-10 w-44 font-normal bg-white rounded divide-y divide-gray-100 shadow dark:bg-gray-700 dark:divide-gray-600">
                                <ul class="py-1 text-sm text-gray-700 dark:text-gray-400" aria-labelledby="inventoryDropdownNavbarLink">
                                    <li><a href="#" class="block py-2 px-4 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">{% trans "Items" %}</a></li>
                                    <li><a href="#" class="block py-2 px-4 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">{% trans "Stock Movements" %}</a></li>
                                </ul>
                            </div>
                        </li>
                        {% endflag %}
                        {% flag "reports_module" %}
                        <li>
                             <button id="reportsDropdownNavbarLink" data-dropdown-toggle="reportsDropdownNavbar" class="flex justify-between items-center py-2 pr-4 pl-3 w-full font-medium text-gray-700 border-b border-gray-100 hover:bg-gray-50 lg:hover:bg-transparent lg:border-0 lg:hover:text-blue-700 lg:p-0 lg:w-auto dark:text-gray-400 dark:hover:text-white dark:focus:text-white dark:border-gray-700 dark:hover:bg-gray-700 lg:dark:hover:bg-transparent">{% trans "Reports" %}
                                <svg class="ml-1 w-4 h-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>
                            </button>
                            <!-- Reports Dropdown menu -->
                            <div id="reportsDropdownNavbar" class="hidden z-10 w-44 font-normal bg-white rounded divide-y divide-gray-100 shadow dark:bg-gray-700 dark:divide-gray-600">
                                <ul class="py-1 text-sm text-gray-700 dark:text-gray-400" aria-labelledby="reportsDropdownNavbarLink">
                                    <li><a href="#" class="block py-2 px-4 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">{% trans "Sales Report" %}</a></li>
                                    <li><a href="#" class="block py-2 px-4 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">{% trans "Inventory Dashboard" %}</a></li>
                                </ul>
                            </div>
                        </li>
                        {% endflag %}
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>
    {% if user.is_authenticated %}
    <!-- Entity Filter Section -->
    <div class="bg-blue-50 border-b border-blue-200 px-4 py-2 fixed w-full z-10 top-16">
      <div class="container mx-auto">
        <div class="flex flex-wrap items-center justify-between">
          <div class="flex items-center">
            <i class="fas fa-filter text-blue-500 mr-2"></i>
            <span class="text-sm text-blue-800 font-medium">
              {% if selected_franchise or selected_company or selected_service_center %}
                {% trans "تصفية النتائج:" %}
                {% if selected_franchise %}
                  <span class="bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full text-xs mx-1">{{ selected_franchise.name }}</span>
                {% endif %}
                {% if selected_company %}
                  <span class="bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full text-xs mx-1">{{ selected_company.name }}</span>
                {% endif %}
                {% if selected_service_center %}
                  <span class="bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full text-xs mx-1">{{ selected_service_center.name }}</span>
                {% endif %}
              {% else %}
                {% trans "تصفية حسب:" %} {% trans "عرض الكل" %}
              {% endif %}
            </span>
          </div>
          
          <button id="toggle-filter" class="text-blue-600 hover:text-blue-800 text-xs flex items-center"
                  data-text-close="{% trans "إغلاق" %}" 
                  data-text-open="{% trans "تغيير التصفية" %}">
            <i class="fas fa-sliders-h mr-1"></i> {% trans "تغيير التصفية" %}
          </button>
        </div>
        
        <!-- Collapsible Filter Form -->
        <div id="filter-form-container" class="mt-3 pt-3 border-t border-blue-200 hidden">
          <form method="get" class="flex flex-wrap items-end gap-2">
            {% for key, value in preserved_query.items %}
              {% if key != 'franchise_id' and key != 'company_id' and key != 'service_center_id' %}
                <input type="hidden" name="{{ key }}" value="{{ value }}">
              {% endif %}
            {% endfor %}
            
            <div class="w-full flex flex-wrap items-center gap-3 mb-1">
              <div class="w-full sm:w-auto flex-1 min-w-[180px]">
                <label for="franchise_id" class="block text-xs text-gray-700 mb-1 font-medium">{% trans "الامتياز" %}</label>
                <div class="relative">
                  <select id="franchise_id" name="franchise_id" class="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 text-sm py-1.5 px-3 w-full appearance-none">
                    <option value="">{% trans "الكل" %}</option>
                    {% for franchise in franchises %}
                      <option value="{{ franchise.id }}" {% if selected_franchise.id == franchise.id %}selected{% endif %}>{{ franchise.name }}</option>
                    {% endfor %}
                  </select>
                  <div class="absolute inset-y-0 {% if LANGUAGE_CODE == 'ar' %}left-0 pl-3{% else %}right-0 pr-3{% endif %} flex items-center pointer-events-none">
                    <i class="fas fa-chevron-down text-gray-400 text-xs"></i>
                  </div>
                </div>
              </div>
              
              <div class="w-full sm:w-auto flex-1 min-w-[180px]">
                <label for="company_id" class="block text-xs text-gray-700 mb-1 font-medium">{% trans "الشركة" %}</label>
                <div class="relative">
                  <select id="company_id" name="company_id" class="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 text-sm py-1.5 px-3 w-full appearance-none" {% if not selected_franchise %}disabled{% endif %}>
                    <option value="">{% trans "الكل" %}</option>
                    {% for company in companies %}
                      <option value="{{ company.id }}" {% if selected_company.id == company.id %}selected{% endif %}>{{ company.name }}</option>
                    {% endfor %}
                  </select>
                  <div class="absolute inset-y-0 {% if LANGUAGE_CODE == 'ar' %}left-0 pl-3{% else %}right-0 pr-3{% endif %} flex items-center pointer-events-none">
                    <i class="fas fa-chevron-down text-gray-400 text-xs"></i>
                  </div>
                </div>
              </div>
              
              <div class="w-full sm:w-auto flex-1 min-w-[180px]">
                <label for="service_center_id" class="block text-xs text-gray-700 mb-1 font-medium">{% trans "مركز الخدمة" %}</label>
                <div class="relative">
                  <select id="service_center_id" name="service_center_id" class="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 text-sm py-1.5 px-3 w-full appearance-none" {% if not selected_company %}disabled{% endif %}>
                    <option value="">{% trans "الكل" %}</option>
                    {% for service_center in service_centers %}
                      <option value="{{ service_center.id }}" {% if selected_service_center.id == service_center.id %}selected{% endif %}>{{ service_center.name }}</option>
                    {% endfor %}
                  </select>
                  <div class="absolute inset-y-0 {% if LANGUAGE_CODE == 'ar' %}left-0 pl-3{% else %}right-0 pr-3{% endif %} flex items-center pointer-events-none">
                    <i class="fas fa-chevron-down text-gray-400 text-xs"></i>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="w-full flex justify-end gap-2 mt-3">
              <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white rounded-md py-1.5 px-4 text-sm flex items-center">
                <i class="fas fa-search mr-1"></i> {% trans "تصفية" %}
              </button>
              <a href="{{ request.path }}" class="bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-md py-1.5 px-4 text-sm flex items-center">
                <i class="fas fa-times mr-1"></i> {% trans "إعادة ضبط" %}
              </a>
            </div>
          </form>
        </div>
      </div>
    </div>
    {% endif %}
    {% endif %}
    {% endblock %}

    <main class="{% if request.resolver_match.url_name != 'login' %}{% if user.is_authenticated %}pt-32{% else %}pt-20{% endif %}{% endif %} container mx-auto px-4 min-h-screen">
        {% comment %}
        Messages block removed to disable all notifications including logout messages
        {% endcomment %}
        
        {% if messages %}
        <div id="django-messages" class="hidden">
            {% for message in messages %}
            <div 
                data-message="{{ message }}" 
                data-tags="{{ message.tags }}"
                data-level="{{ message.level_tag }}">
            </div>
            {% endfor %}
        </div>
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const messagesContainer = document.getElementById('django-messages');
                if (messagesContainer) {
                    const messageElements = messagesContainer.querySelectorAll('div[data-message]');
                    
                    messageElements.forEach(function(element) {
                        const message = element.getAttribute('data-message');
                        const tags = element.getAttribute('data-tags');
                        
                        // Map Django message tags to our notification types
                        let type = 'info';
                        if (tags.includes('success')) type = 'success';
                        if (tags.includes('error')) type = 'error';
                        if (tags.includes('warning')) type = 'warning';
                        
                        // If our notification function exists, use it
                        if (typeof window.notify === 'function') {
                            window.notify(message, type);
                        } 
                        // Otherwise dispatch event for it to be caught elsewhere
                        else {
                            window.dispatchEvent(new CustomEvent('showNotification', {
                                detail: { message, type, duration: 5000 }
                            }));
                        }
                    });
                }
            });
        </script>
        {% endif %}

        {% block content %}{% endblock %}
    </main>

    {% if request.resolver_match.url_name != 'login' %}
    <footer class="p-4 bg-white md:p-8 lg:p-10 dark:bg-gray-800">
        <div class="mx-auto max-w-screen-xl text-center">
            <a href="#" class="flex justify-center items-center text-2xl font-semibold text-gray-900 dark:text-white">
                <svg class="mr-2 h-8" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path></svg>
                {% trans "After-Sales System" %}    
            </a>
            <p class="my-6 text-gray-500 dark:text-gray-400">{% trans "A modern, modular system for after-sales franchise management." %}</p>
            <ul class="flex flex-wrap justify-center items-center mb-6 text-gray-900 dark:text-white">
                <li><a href="#" class="mr-4 hover:underline md:mr-6 ">{% trans "About" %}</a></li>
                <li><a href="#" class="mr-4 hover:underline md:mr-6">{% trans "Modules" %}</a></li>
                <li><a href="#" class="mr-4 hover:underline md:mr-6 ">{% trans "Contact" %}</a></li>
            </ul>
            <span class="text-sm text-gray-500 sm:text-center dark:text-gray-400">© {% now "Y" %} <a href="#" class="hover:underline">{% trans "After-Sales System" %}™</a>. {% trans "All Rights Reserved." %}</span>
        </div>
    </footer>
    {% endif %}

    <script src="{% static 'js/flowbite.min.js' %}"></script>
    <!-- Fallback script if local file not found -->
    <script>
        (function() {
            document.addEventListener('DOMContentLoaded', function() {
                setTimeout(function() {
                    if (!window.Flowbite) {
                        var script = document.createElement('script');
                        script.src = 'https://cdnjs.cloudflare.com/ajax/libs/flowbite/1.8.1/flowbite.min.js';
                        document.body.appendChild(script);
                    }
                }, 500);
            });
        })();
    </script>
    {# Or from CDN: <script src="https://cdnjs.cloudflare.com/ajax/libs/flowbite/1.8.1/flowbite.min.js"></script> #}
    {% block extra_js %}{% endblock %}
    
    <!-- Global entity filter JavaScript -->
    {% if user.is_authenticated %}
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize the entity filter toggle
            initEntityFilter();
        });
        
        function initEntityFilter() {
            const toggleFilterBtn = document.getElementById('toggle-filter');
            const filterFormContainer = document.getElementById('filter-form-container');
            
            if (toggleFilterBtn && filterFormContainer) {
                // Toggle filter form visibility
                toggleFilterBtn.addEventListener('click', function() {
                    filterFormContainer.classList.toggle('hidden');
                    
                    if (!filterFormContainer.classList.contains('hidden')) {
                        toggleFilterBtn.innerHTML = '<i class="fas fa-times mr-1"></i> ' + toggleFilterBtn.dataset.textClose;
                    } else {
                        toggleFilterBtn.innerHTML = '<i class="fas fa-sliders-h mr-1"></i> ' + toggleFilterBtn.dataset.textOpen;
                    }
                });
                
                // Handle cascading dropdowns
                const franchiseSelect = document.getElementById('franchise_id');
                const companySelect = document.getElementById('company_id');
                const serviceCenterSelect = document.getElementById('service_center_id');
                
                if (franchiseSelect && companySelect) {
                    franchiseSelect.addEventListener('change', function() {
                        // Reset dependent filters
                        if (companySelect) companySelect.value = '';
                        if (serviceCenterSelect) serviceCenterSelect.value = '';
                        
                        // Enable/disable company select based on franchise selection
                        if (companySelect) {
                            companySelect.disabled = !this.value;
                        }
                        
                        // Auto-submit when franchise changes
                        if (this.value) {
                            this.form.submit();
                        }
                    });
                }
                
                if (companySelect && serviceCenterSelect) {
                    companySelect.addEventListener('change', function() {
                        // Reset service center selection
                        if (serviceCenterSelect) serviceCenterSelect.value = '';
                        
                        // Enable/disable service center select based on company selection
                        if (serviceCenterSelect) {
                            serviceCenterSelect.disabled = !this.value;
                        }
                        
                        // Auto-submit when company changes
                        if (this.value) {
                            this.form.submit();
                        }
                    });
                }
            }
        }
    </script>
    {% endif %}
</body>
</html> 