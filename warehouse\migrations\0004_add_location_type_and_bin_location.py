# Generated by Django 4.2.20 on 2025-05-08 18:51

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('warehouse', '0003_itemlocation_max_stock_itemlocation_min_stock_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='LocationType',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('tenant_id', models.UUIDField(blank=True, db_index=True, null=True, verbose_name='Tenant ID')),
                ('name', models.CharField(max_length=100, verbose_name='Name')),
                ('code', models.CharField(max_length=50, verbose_name='Code')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('icon', models.CharField(blank=True, help_text='Icon name for UI display', max_length=50, verbose_name='Icon')),
                ('color', models.CharField(blank=True, help_text='Color code for UI display', max_length=20, verbose_name='Color')),
                ('is_active', models.BooleanField(default=True, verbose_name='Active')),
                ('requires_bin_locations', models.BooleanField(default=False, help_text='If true, this location type must have bin locations defined', verbose_name='Requires Bin Locations')),
                ('is_storage', models.BooleanField(default=True, help_text='If true, this location can store inventory', verbose_name='Is Storage Location')),
                ('is_receiving', models.BooleanField(default=False, help_text='If true, this location can receive inventory from vendors', verbose_name='Is Receiving Location')),
                ('is_shipping', models.BooleanField(default=False, help_text='If true, this location can ship inventory to customers', verbose_name='Is Shipping Location')),
                ('is_service', models.BooleanField(default=False, help_text='If true, this location is associated with a service center', verbose_name='Is Service Location')),
            ],
            options={
                'verbose_name': 'Location Type',
                'verbose_name_plural': 'Location Types',
                'ordering': ['name'],
                'unique_together': {('tenant_id', 'code')},
            },
        ),
        migrations.AddField(
            model_name='location',
            name='location_type',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='locations', to='warehouse.locationtype', verbose_name='Location Type'),
        ),
        migrations.AddField(
            model_name='location',
            name='parent',
            field=models.ForeignKey(blank=True, help_text='If this is a sub-location (e.g., a shelf within a warehouse)', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='warehouse.location', verbose_name='Parent Location'),
        ),
        migrations.CreateModel(
            name='BinLocation',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('tenant_id', models.UUIDField(blank=True, db_index=True, null=True, verbose_name='Tenant ID')),
                ('name', models.CharField(max_length=100, verbose_name='Name')),
                ('code', models.CharField(max_length=50, verbose_name='Code')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('is_active', models.BooleanField(default=True, verbose_name='Active')),
                ('aisle', models.CharField(blank=True, help_text='Aisle identifier', max_length=20, verbose_name='Aisle')),
                ('rack', models.CharField(blank=True, help_text='Rack identifier', max_length=20, verbose_name='Rack')),
                ('shelf', models.CharField(blank=True, help_text='Shelf identifier', max_length=20, verbose_name='Shelf')),
                ('position', models.CharField(blank=True, help_text='Position identifier', max_length=20, verbose_name='Position')),
                ('barcode', models.CharField(blank=True, max_length=100, verbose_name='Barcode')),
                ('location', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='bins', to='warehouse.location', verbose_name='Location')),
            ],
            options={
                'verbose_name': 'Bin Location',
                'verbose_name_plural': 'Bin Locations',
                'ordering': ['location', 'aisle', 'rack', 'shelf', 'position'],
                'unique_together': {('tenant_id', 'location', 'code')},
            },
        ),
        migrations.AddField(
            model_name='itemlocation',
            name='bin_location',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='items', to='warehouse.binlocation', verbose_name='Bin Location'),
        ),
    ]
