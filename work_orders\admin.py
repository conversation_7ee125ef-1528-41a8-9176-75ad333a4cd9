from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from .models import (
    WorkOrder, WorkOrderType, WorkOrderOperation, WorkOrderMaterial,
    BillOfMaterials, BOMItem, MaintenanceSchedule, ScheduleOperation, OperationPart
    )

class WorkOrderOperationInline(admin.TabularInline):
    model = WorkOrderOperation
    extra = 1

class WorkOrderMaterialInline(admin.TabularInline):
    model = WorkOrderMaterial
    extra = 1

@admin.register(WorkOrder)
class WorkOrderAdmin(admin.ModelAdmin):
    list_display = ('work_order_number', 'customer_display', 'work_order_type', 'operation_category', 'status', 'priority', 'planned_start_date')
    list_filter = ('status', 'priority', 'operation_category', 'work_order_type', 'service_center')
    search_fields = ('work_order_number', 'description', 'customer_name', 'customer__first_name', 'customer__last_name', 'vehicle__license_plate')
    readonly_fields = ('created_at', 'updated_at')
    inlines = [WorkOrderOperationInline, WorkOrderMaterialInline]
    fieldsets = (
        (None, {
            'fields': ('tenant_id', 'work_order_number', 'description', 'work_order_type', 'operation_category', 'maintenance_schedule')
        }),
        (_('Status and Priority'), {
            'fields': ('status', 'priority')
        }),
        (_('Customer Information'), {
            'fields': ('customer', ('customer_name', 'customer_phone'), 'customer_email', 'warranty_status')
        }),
        (_('Vehicle Information'), {
            'fields': ('service_center', 'vehicle', 'current_odometer', 'fuel_level', 'service_item_serial')
        }),
        (_('Scheduling'), {
            'fields': ('planned_start_date', 'planned_end_date', 'actual_start_date', 'actual_end_date')
        }),
        (_('Financial'), {
            'fields': ('estimated_cost', 'actual_cost')
        }),
        (_('Other'), {
            'fields': ('bill_of_materials', 'notes', 'attributes')
        }),
        (_('Metadata'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def customer_display(self, obj):
        """Display the customer name, prioritizing the Customer model if available"""
        if obj.customer:
            return obj.customer.full_name
        return obj.customer_name
    customer_display.short_description = _("Customer")
    
    def save_model(self, request, obj, form, change):
        """Auto-assign customer from vehicle if not set"""
        super().save_model(request, obj, form, change)
        if not change:  # Only for new objects
            obj.auto_assign_customer_from_vehicle()
            if obj.customer and not obj.customer_name:
                obj.customer_name = obj.customer.full_name
                obj.save()

@admin.register(WorkOrderType)
class WorkOrderTypeAdmin(admin.ModelAdmin):
    list_display = ('name', 'is_active')
    search_fields = ('name', 'description')
    list_filter = ('is_active',)

class ScheduleOperationInline(admin.TabularInline):
    model = ScheduleOperation
    extra = 1

@admin.register(MaintenanceSchedule)
class MaintenanceScheduleAdmin(admin.ModelAdmin):
    list_display = ('name', 'interval_type', 'mileage_interval', 'time_interval_months', 'vehicle_make', 'vehicle_model', 'is_active')
    list_filter = ('interval_type', 'is_active', 'vehicle_make')
    search_fields = ('name', 'description', 'vehicle_make', 'vehicle_model')
    inlines = [ScheduleOperationInline]
    fieldsets = (
        (None, {
            'fields': ('tenant_id', 'name', 'description', 'is_active')
        }),
        (_('Interval Settings'), {
            'fields': ('interval_type', 'mileage_interval', 'time_interval_months')
        }),
        (_('Vehicle Compatibility'), {
            'fields': ('vehicle_make', 'vehicle_model', 'year_from', 'year_to')
        }),
    )

class OperationPartInline(admin.TabularInline):
    model = OperationPart
    extra = 1
    
@admin.register(ScheduleOperation)
class ScheduleOperationAdmin(admin.ModelAdmin):
    list_display = ('name', 'maintenance_schedule', 'duration_minutes', 'sequence', 'is_required')
    list_filter = ('maintenance_schedule', 'is_required')
    search_fields = ('name', 'description', 'maintenance_schedule__name')
    inlines = [OperationPartInline]

class BOMItemInline(admin.TabularInline):
    model = BOMItem
    extra = 1

@admin.register(BillOfMaterials)
class BillOfMaterialsAdmin(admin.ModelAdmin):
    list_display = ('name', 'version', 'finished_item', 'is_active')
    list_filter = ('is_active',)
    search_fields = ('name', 'description', 'finished_item__name')
    inlines = [BOMItemInline]

@admin.register(WorkOrderOperation)
class WorkOrderOperationAdmin(admin.ModelAdmin):
    list_display = ('name', 'work_order', 'sequence', 'duration_minutes', 'is_completed')
    list_filter = ('is_completed',)
    search_fields = ('name', 'description', 'work_order__work_order_number')

@admin.register(WorkOrderMaterial)
class WorkOrderMaterialAdmin(admin.ModelAdmin):
    list_display = ('item', 'work_order', 'quantity', 'unit_of_measure', 'is_consumed')
    list_filter = ('is_consumed',)
    search_fields = ('item__name', 'item__sku', 'work_order__work_order_number', 'notes')
