{% extends "dashboard_base.html" %}
{% load i18n %}
{% load core_tags %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <h1 class="mt-4">{{ title }}</h1>
    
    <!-- Vehicle Information Card -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-car me-1"></i>
            {% trans "Vehicle Information" %}
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <h5>{% trans "Make & Model" %}</h5>
                    <p>{{ vehicle.make }} {{ vehicle.model }} ({{ vehicle.year }})</p>
                </div>
                <div class="col-md-3">
                    <h5>{% trans "License Plate" %}</h5>
                    <p>{{ vehicle.license_plate }}</p>
                </div>
                <div class="col-md-3">
                    <h5>{% trans "VIN" %}</h5>
                    <p>{{ vehicle.vin|default:"-" }}</p>
                </div>
                <div class="col-md-3">
                    <h5>{% trans "Owner" %}</h5>
                    <p>{{ vehicle.owner|default:"-" }}</p>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-md-12">
                    <a href="{% url 'setup:vehicle_detail' vehicle.id %}" class="btn btn-outline-primary">
                        <i class="fas fa-info-circle"></i> {% trans "View Full Vehicle Details" %}
                    </a>
                    <a href="{% url 'work_orders:work_order_create' %}?vehicle_id={{ vehicle.id }}" class="btn btn-success">
                        <i class="fas fa-plus"></i> {% trans "Create New Work Order" %}
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Service History Stats -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card bg-primary text-white mb-4">
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <div class="small">{% trans "Total Service Records" %}</div>
                            <div class="fs-4">{{ total_records }}</div>
                        </div>
                        <i class="fas fa-history fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-success text-white mb-4">
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <div class="small">{% trans "Work Orders" %}</div>
                            <div class="fs-4">{{ work_orders_count }}</div>
                        </div>
                        <i class="fas fa-clipboard-list fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-info text-white mb-4">
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <div class="small">{% trans "Service History Records" %}</div>
                            <div class="fs-4">{{ service_history_count }}</div>
                        </div>
                        <i class="fas fa-tools fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-secondary text-white mb-4">
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <div class="small">{% trans "Last Service Date" %}</div>
                            <div class="fs-4">
                                {% if service_records %}
                                    {{ service_records.0.service_date|date:"Y-m-d" }}
                                {% else %}
                                    -
                                {% endif %}
                            </div>
                        </div>
                        <i class="fas fa-calendar-check fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Service History Timeline -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-history me-1"></i>
            {% trans "Service History Timeline" %}
        </div>
        <div class="card-body">
            {% if service_records %}
                <div class="timeline-container">
                    {% for record in service_records %}
                        <div class="timeline-item">
                            <div class="timeline-item-marker">
                                {% if record.record_type == 'work_order' %}
                                    <div class="timeline-item-marker-indicator bg-success">
                                        <i class="fas fa-clipboard-list"></i>
                                    </div>
                                {% else %}
                                    <div class="timeline-item-marker-indicator bg-info">
                                        <i class="fas fa-tools"></i>
                                    </div>
                                {% endif %}
                                <div class="timeline-item-marker-date">{{ record.service_date|date:"Y-m-d" }}</div>
                            </div>
                            <div class="timeline-item-content">
                                <div class="card">
                                    <div class="card-header">
                                        {% if record.record_type == 'work_order' %}
                                            <span class="badge bg-success">{% trans "Work Order" %}</span>
                                        {% else %}
                                            <span class="badge bg-info">{% trans "Service History" %}</span>
                                        {% endif %}
                                        
                                        <strong>{{ record.work_order_number }}</strong>
                                        {% if record.record_type == 'work_order' %}
                                            <a href="{% url 'work_orders:work_order_detail' record.id %}" class="float-end">
                                                <i class="fas fa-external-link-alt"></i>
                                            </a>
                                        {% endif %}
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-4">
                                                <h6>{% trans "Service Center" %}</h6>
                                                <p>{{ record.service_center.name|default:"-" }}</p>
                                            </div>
                                            <div class="col-md-4">
                                                <h6>{% trans "Odometer" %}</h6>
                                                <p>{{ record.odometer|default:"-" }} km</p>
                                            </div>
                                            <div class="col-md-4">
                                                <h6>{% trans "Total Cost" %}</h6>
                                                <p>{{ record.total_cost|default:"-" }}</p>
                                            </div>
                                        </div>
                                        
                                        <h6>{% trans "Description" %}</h6>
                                        <p>{{ record.description }}</p>
                                        
                                        <!-- Services Performed -->
                                        {% if record.services_performed %}
                                            <h6>{% trans "Services Performed" %}</h6>
                                            <ul class="list-group mb-3">
                                                {% for service in record.services_performed %}
                                                    <li class="list-group-item">
                                                        <strong>{{ service.name }}</strong>
                                                        {% if service.description %}
                                                            <p class="small mb-0">{{ service.description }}</p>
                                                        {% endif %}
                                                    </li>
                                                {% endfor %}
                                            </ul>
                                        {% endif %}
                                        
                                        <!-- Parts Used -->
                                        {% if record.parts_used %}
                                            <h6>{% trans "Parts Used" %}</h6>
                                            <ul class="list-group">
                                                {% for part in record.parts_used %}
                                                    <li class="list-group-item">
                                                        <strong>{{ part.name }}</strong>
                                                        {% if part.quantity %}
                                                            <span class="badge bg-secondary">
                                                                {{ part.quantity }} {{ part.unit|default:"" }}
                                                            </span>
                                                        {% endif %}
                                                    </li>
                                                {% endfor %}
                                            </ul>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="alert alert-info">
                    {% trans "No service history records found for this vehicle." %}
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .timeline-container {
        position: relative;
        padding: 1rem;
        margin: 0 auto;
    }
    
    .timeline-container::before {
        content: '';
        position: absolute;
        height: 100%;
        width: 2px;
        background-color: #e3e6ec;
        left: 1.5rem;
        top: 0;
    }
    
    .timeline-item {
        position: relative;
        display: flex;
        margin-bottom: 2rem;
    }
    
    .timeline-item:last-child {
        margin-bottom: 0;
    }
    
    .timeline-item-marker {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-right: 1rem;
    }
    
    .timeline-item-marker-date {
        font-size: 0.8rem;
        margin-top: 0.5rem;
        white-space: nowrap;
        width: 5rem;
        text-align: center;
    }
    
    .timeline-item-marker-indicator {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 3rem;
        width: 3rem;
        background-color: #fff;
        border-radius: 100%;
        color: #fff;
        z-index: 10;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }
    
    .timeline-item-content {
        flex: 1;
    }
</style>
{% endblock %} 