"""
Service functions for the warehouse app.
"""
import logging
from decimal import Decimal

logger = logging.getLogger(__name__)

def check_part_availability(item_id, service_center_id=None):
    """
    Check availability of a part across warehouses.
    Optionally filter by service center's local warehouse.
    
    Args:
        item_id: ID of the item to check
        service_center_id: Optional ID of the service center to prioritize
        
    Returns:
        Dictionary with availability information
    """
    from inventory.models import Item
    from warehouse.models import Warehouse, Location, ItemLocation
    from setup.models import ServiceCenter
    from work_orders.utils import safe_uuid
    
    # Validate IDs
    item_id = safe_uuid(item_id)
    service_center_id = safe_uuid(service_center_id) if service_center_id else None
    
    if not item_id:
        return {
            'success': False,
            'error': 'Invalid item ID',
            'item_id': item_id
        }
    
    try:
        # Get the item
        item = Item.objects.get(id=item_id)
        
        # Get all warehouses
        warehouses_query = Warehouse.objects.all()
        
        # If service center provided, identify its primary warehouse
        primary_warehouse_id = None
        if service_center_id:
            try:
                service_center = ServiceCenter.objects.select_related('primary_warehouse').get(id=service_center_id)
                if service_center.primary_warehouse:
                    primary_warehouse_id = service_center.primary_warehouse.id
                    # We'll mark this warehouse as local below
            except ServiceCenter.DoesNotExist:
                logger.warning(f"Service center with ID {service_center_id} not found")
        
        # Get all warehouses and their stock for this item
        warehouse_data = []
        total_available = 0
        
        for warehouse in warehouses_query:
            # Get all locations in this warehouse
            locations = Location.objects.filter(warehouse=warehouse)
            
            # Get all ItemLocation records for this item in this warehouse
            item_locations = ItemLocation.objects.filter(
                item=item,
                location__in=locations,
                quantity__gt=0
            )
            
            # Calculate total quantity in this warehouse
            warehouse_qty = sum(il.quantity for il in item_locations)
            
            # If we have stock in this warehouse, add it to the results
            if warehouse_qty > 0:
                warehouse_data.append({
                    'warehouse_id': str(warehouse.id),
                    'warehouse_name': warehouse.name,
                    'quantity': float(warehouse_qty),
                    'is_local': warehouse.id == primary_warehouse_id if primary_warehouse_id else False,
                    'is_central': warehouse.is_central if hasattr(warehouse, 'is_central') else False,
                    'locations': [
                        {
                            'location_id': str(il.location.id),
                            'location_name': il.location.name,
                            'quantity': float(il.quantity)
                        }
                        for il in item_locations
                    ]
                })
                
                total_available += warehouse_qty
        
        # Sort warehouses by local first, then central, then others
        sorted_warehouse_data = sorted(
            warehouse_data,
            key=lambda x: (not x['is_local'], not x['is_central'], x['warehouse_name'])
        )
        
        return {
            'success': True,
            'item_id': str(item.id),
            'item_name': item.name,
            'item_sku': getattr(item, 'sku', None),
            'total_available': float(total_available),
            'warehouse_availability': sorted_warehouse_data,
            'is_available_locally': any(w['is_local'] for w in warehouse_data),
            'needs_transfer': total_available > 0 and not any(w['is_local'] for w in warehouse_data)
        }
    
    except Item.DoesNotExist:
        logger.error(f"Item with ID {item_id} not found")
        return {
            'success': False,
            'error': 'Item not found',
            'item_id': item_id
        }
    
    except Exception as e:
        logger.exception(f"Error checking part availability: {str(e)}")
        return {
            'success': False,
            'error': str(e),
            'item_id': item_id
        }

def request_stock_transfer(item_id, quantity, source_warehouse_id, destination_warehouse_id, work_order_id=None, note=None):
    """
    Request a stock transfer between warehouses.
    
    Args:
        item_id: ID of the item to transfer
        quantity: Quantity to transfer
        source_warehouse_id: ID of the source warehouse
        destination_warehouse_id: ID of the destination warehouse
        work_order_id: Optional ID of the work order requesting the transfer
        note: Optional note to include in the transfer request
        
    Returns:
        Dictionary with transfer request information
    """
    from inventory.models import Item, Movement, MovementType
    from warehouse.models import Warehouse, StockTransfer, StockTransferStatus
    from work_orders.utils import safe_uuid
    
    # Validate IDs
    item_id = safe_uuid(item_id)
    source_warehouse_id = safe_uuid(source_warehouse_id)
    destination_warehouse_id = safe_uuid(destination_warehouse_id)
    work_order_id = safe_uuid(work_order_id) if work_order_id else None
    
    if not item_id or not source_warehouse_id or not destination_warehouse_id:
        return {
            'success': False,
            'error': 'Invalid item or warehouse ID'
        }
    
    if source_warehouse_id == destination_warehouse_id:
        return {
            'success': False,
            'error': 'Source and destination warehouses cannot be the same'
        }
    
    try:
        # Convert quantity to Decimal
        if not isinstance(quantity, Decimal):
            try:
                quantity = Decimal(str(quantity))
            except (ValueError, TypeError):
                return {'success': False, 'error': f"Invalid quantity: {quantity}"}
        
        # Get the item and warehouses
        item = Item.objects.get(id=item_id)
        source_warehouse = Warehouse.objects.get(id=source_warehouse_id)
        destination_warehouse = Warehouse.objects.get(id=destination_warehouse_id)
        
        # Check availability in source warehouse
        availability = check_part_availability(item_id)
        if not availability['success']:
            return availability
        
        # Find source warehouse availability
        source_availability = next(
            (w for w in availability['warehouse_availability'] if w['warehouse_id'] == str(source_warehouse_id)),
            None
        )
        
        if not source_availability or source_availability['quantity'] < quantity:
            return {
                'success': False,
                'error': f"Insufficient stock in source warehouse. Available: {source_availability['quantity'] if source_availability else 0}, Requested: {quantity}",
                'item_id': str(item.id),
                'source_warehouse_id': str(source_warehouse_id)
            }
        
        # Create a stock transfer request
        tenant_id = item.tenant_id
        
        transfer = StockTransfer.objects.create(
            tenant_id=tenant_id,
            item=item,
            quantity=quantity,
            source_warehouse=source_warehouse,
            destination_warehouse=destination_warehouse,
            status=StockTransferStatus.REQUESTED,
            reference_type='work_order' if work_order_id else None,
            reference_id=str(work_order_id) if work_order_id else None,
            notes=note or f"Transfer for Work Order #{work_order_id}" if work_order_id else "Manual transfer request"
        )
        
        logger.info(
            f"Created stock transfer request {transfer.id} for item {item.id} ({item.name}) "
            f"from warehouse {source_warehouse.id} ({source_warehouse.name}) "
            f"to warehouse {destination_warehouse.id} ({destination_warehouse.name}). "
            f"Quantity: {quantity}"
        )
        
        return {
            'success': True,
            'transfer_id': str(transfer.id),
            'item_id': str(item.id),
            'item_name': item.name,
            'quantity': float(quantity),
            'source_warehouse_id': str(source_warehouse_id),
            'source_warehouse_name': source_warehouse.name,
            'destination_warehouse_id': str(destination_warehouse_id),
            'destination_warehouse_name': destination_warehouse.name,
            'status': 'requested',
            'message': f"Transfer request created successfully"
        }
    
    except Item.DoesNotExist:
        logger.error(f"Item with ID {item_id} not found")
        return {'success': False, 'error': 'Item not found'}
    
    except Warehouse.DoesNotExist:
        logger.error(f"Warehouse not found")
        return {'success': False, 'error': 'Warehouse not found'}
    
    except Exception as e:
        logger.exception(f"Error requesting stock transfer: {str(e)}")
        return {'success': False, 'error': str(e)} 