from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from sales.models import Customer, SalesOrder, SalesOrderItem, SalesReturn, SalesReturnItem


class SalesOrderItemInline(admin.TabularInline):
    model = SalesOrderItem
    extra = 0
    raw_id_fields = ('item',)


class SalesReturnItemInline(admin.TabularInline):
    model = SalesReturnItem
    extra = 0
    raw_id_fields = ('sales_order_item',)


@admin.register(Customer)
class CustomerAdmin(admin.ModelAdmin):
    list_display = ('name', 'email', 'phone', 'is_active', 'tenant_id')
    list_filter = ('is_active',)
    search_fields = ('name', 'email', 'phone', 'address')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        (None, {
            'fields': ('tenant_id', 'name', 'email', 'phone', 'is_active')
        }),
        (_('Address'), {
            'fields': ('address',)
        }),
        (_('Metadata'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(SalesOrder)
class SalesOrderAdmin(admin.ModelAdmin):
    list_display = ('order_number', 'customer', 'order_date', 'status', 'total_amount', 'tenant_id')
    list_filter = ('status', 'order_date')
    search_fields = ('order_number', 'customer__name', 'notes')
    readonly_fields = ('created_at', 'updated_at', 'total_amount')
    raw_id_fields = ('customer',)
    inlines = [SalesOrderItemInline]
    fieldsets = (
        (None, {
            'fields': ('tenant_id', 'order_number', 'customer', 'order_date', 'status')
        }),
        (_('Shipping'), {
            'fields': ('shipping_address',)
        }),
        (_('Financial'), {
            'fields': ('total_amount',)
        }),
        (_('Additional Information'), {
            'fields': ('notes',)
        }),
        (_('Metadata'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(SalesOrderItem)
class SalesOrderItemAdmin(admin.ModelAdmin):
    list_display = ('sales_order', 'item', 'quantity', 'unit_price', 'discount', 'total_price', 'tenant_id')
    list_filter = ('sales_order__status',)
    search_fields = ('sales_order__order_number', 'item__name', 'item__sku')
    readonly_fields = ('created_at', 'updated_at', 'total_price')
    raw_id_fields = ('sales_order', 'item')
    fieldsets = (
        (None, {
            'fields': ('tenant_id', 'sales_order', 'item', 'quantity')
        }),
        (_('Pricing'), {
            'fields': ('unit_price', 'discount', 'total_price')
        }),
        (_('Metadata'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def total_price(self, obj):
        return obj.total_price
    total_price.short_description = _("Total Price")


@admin.register(SalesReturn)
class SalesReturnAdmin(admin.ModelAdmin):
    list_display = ('return_number', 'sales_order', 'return_date', 'tenant_id')
    list_filter = ('return_date',)
    search_fields = ('return_number', 'sales_order__order_number', 'reason', 'notes')
    readonly_fields = ('created_at', 'updated_at')
    raw_id_fields = ('sales_order',)
    inlines = [SalesReturnItemInline]
    fieldsets = (
        (None, {
            'fields': ('tenant_id', 'return_number', 'sales_order', 'return_date')
        }),
        (_('Additional Information'), {
            'fields': ('reason', 'notes')
        }),
        (_('Metadata'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(SalesReturnItem)
class SalesReturnItemAdmin(admin.ModelAdmin):
    list_display = ('sales_return', 'sales_order_item', 'quantity', 'tenant_id')
    search_fields = ('sales_return__return_number', 'sales_order_item__item__name')
    readonly_fields = ('created_at', 'updated_at')
    raw_id_fields = ('sales_return', 'sales_order_item')
    fieldsets = (
        (None, {
            'fields': ('tenant_id', 'sales_return', 'sales_order_item', 'quantity')
        }),
        (_('Metadata'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
