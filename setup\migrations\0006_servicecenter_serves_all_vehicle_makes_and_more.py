# Generated by Django 4.2.20 on 2025-05-08 15:56

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('setup', '0005_servicecenter_size'),
    ]

    operations = [
        migrations.AddField(
            model_name='servicecenter',
            name='serves_all_vehicle_makes',
            field=models.BooleanField(default=True, help_text='If true, this center can service all makes and models. If false, only specific makes defined in ServiceCenterMakeModel.', verbose_name='Serves All Vehicle Makes'),
        ),
        migrations.CreateModel(
            name='ServiceCenterMakeModel',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('make', models.CharField(max_length=100, verbose_name='Make')),
                ('model', models.CharField(blank=True, help_text='If left blank, all models for this make are supported', max_length=100, verbose_name='Model')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('service_center', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='supported_makes_models', to='setup.servicecenter', verbose_name='Service Center')),
            ],
            options={
                'verbose_name': 'Service Center Make & Model',
                'verbose_name_plural': 'Service Center Makes & Models',
                'unique_together': {('tenant_id', 'service_center', 'make', 'model')},
            },
        ),
    ]
