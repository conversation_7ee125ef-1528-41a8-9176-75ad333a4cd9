from django.urls import path, include
from rest_framework import routers
from rest_framework.authtoken.views import obtain_auth_token

from project.settings import DEBUG
from .views import (
    ItemViewSet, MovementViewSet, ItemDocumentViewSet,
    ReportViewSet, ReportExecutionViewSet, 
    DashboardViewSet, DashboardWidgetViewSet
)
from work_orders.api_spare_parts import api_get_spare_parts

router = routers.DefaultRouter()
router.register(r'items', ItemViewSet)
router.register(r'movements', MovementViewSet)
router.register(r'documents', ItemDocumentViewSet)
router.register(r'reports', ReportViewSet)
router.register(r'report-executions', ReportExecutionViewSet)
router.register(r'dashboards', DashboardViewSet)
router.register(r'dashboard-widgets', DashboardWidgetViewSet)

urlpatterns = [
    path('v1/', include(router.urls)),
    path('token/', obtain_auth_token, name='api_token_auth'),
    path('work-orders/spare-parts/', api_get_spare_parts, name='api_get_spare_parts'),
]

if DEBUG:
    urlpatterns += [path('api-auth/', include('rest_framework.urls'))]