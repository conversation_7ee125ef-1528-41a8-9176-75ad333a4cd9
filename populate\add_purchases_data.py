import os
import sys
import django
import random
from datetime import datetime, timedelta
from django.db import transaction
from faker import Faker

# Add the parent directory to sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

# Import necessary models
from purchases.models import (
    Supplier, PurchaseOrder, PurchaseOrderItem, 
    PurchaseReceipt, PurchaseReceiptItem
)
from warehouse.models import Location
from inventory.models import Item
from django.utils import timezone

# Initialize Faker
fake = Faker('ar_EG')  # Using Egyptian Arabic locale

class PurchasesGenerator:
    """Generate purchases data for the Aftersails system."""
    
    def __init__(self):
        self.tenants = self._get_tenant_ids()
        self.inventory_items = {}
        self.warehouse_locations = {}
        
        for tenant_id in self.tenants:
            self.inventory_items[tenant_id] = list(Item.objects.filter(tenant_id=tenant_id))
            # Get receiving locations for purchases
            self.warehouse_locations[tenant_id] = list(Location.objects.filter(
                tenant_id=tenant_id,
                is_receiving=True
            ))
            
        print(f"Initialized PurchasesGenerator with {len(self.tenants)} tenants")
        for tenant_id in self.tenants:
            print(f"Tenant {tenant_id}: {len(self.inventory_items.get(tenant_id, []))} items, "
                  f"{len(self.warehouse_locations.get(tenant_id, []))} receiving locations")
    
    def _get_tenant_ids(self):
        """Get unique tenant IDs from existing records."""
        tenant_ids = Item.objects.values_list('tenant_id', flat=True).distinct()
        return tenant_ids
    
    def generate_suppliers(self, count=20):
        """Generate supplier records."""
        print(f"Generating {count} suppliers...")
        
        created_suppliers = []
        
        # Company types in Arabic
        company_types = [
            "شركة", "مؤسسة", "مصنع", "معرض", "مركز", "مجموعة"
        ]
        
        # Business types in Arabic
        business_types = [
            "للسيارات", "لقطع الغيار", "للإطارات", "للزيوت", "للبطاريات", 
            "للإكسسوارات", "للصيانة", "للإلكترونيات", "للاستيراد والتصدير",
            "للخدمات الفنية"
        ]
        
        for tenant_id in self.tenants:
            # Generate suppliers for this tenant
            suppliers_per_tenant = count // len(self.tenants) + 1
            
            for i in range(suppliers_per_tenant):
                # Generate a company name
                company_type = random.choice(company_types)
                business_type = random.choice(business_types)
                
                if random.random() > 0.5:
                    # Use person name in company
                    name = f"{company_type} {fake.first_name()} {business_type}"
                else:
                    # Use a made-up name
                    syllables = ["تك", "سيرف", "أوتو", "ماك", "ترانس", "فيوتر", "موب", "كار"]
                    company_name = "".join(random.sample(syllables, random.randint(1, 3)))
                    name = f"{company_type} {company_name} {business_type}"
                
                # Create the supplier
                supplier = Supplier.objects.create(
                    tenant_id=tenant_id,
                    name=name,
                    email=fake.company_email(),
                    phone=fake.phone_number(),
                    address=fake.address(),
                    is_active=random.random() > 0.1  # 90% active
                )
                created_suppliers.append(supplier)
        
        print(f"Created {len(created_suppliers)} suppliers")
        return created_suppliers
    
    def generate_purchase_orders(self, count=50):
        """Generate purchase orders with items."""
        print(f"Generating {count} purchase orders...")
        
        created_orders = []
        
        # Get all suppliers by tenant
        suppliers_by_tenant = {}
        for tenant_id in self.tenants:
            suppliers_by_tenant[tenant_id] = list(Supplier.objects.filter(
                tenant_id=tenant_id,
                is_active=True
            ))
        
        # Status distribution for more realistic data
        status_weights = {
            'draft': 10,
            'sent': 15,
            'confirmed': 25,
            'received': 45,
            'cancelled': 5
        }
        
        for tenant_id in self.tenants:
            # Skip if no suppliers or items
            if (not suppliers_by_tenant.get(tenant_id) or 
                not self.inventory_items.get(tenant_id)):
                print(f"Missing suppliers or items for tenant {tenant_id}, skipping purchase orders")
                continue
            
            # Generate purchase orders for this tenant
            orders_per_tenant = count // len(self.tenants) + 1
            
            for i in range(orders_per_tenant):
                # Generate dates - most orders in last 90 days
                days_ago = int(abs(random.gauss(30, 20)))  # Normal distribution centered at 30 days ago
                days_ago = min(days_ago, 90)  # Cap at 90 days ago
                
                order_date = timezone.now().date() - timedelta(days=days_ago)
                expected_delivery_date = order_date + timedelta(days=random.randint(7, 30))
                
                # Select a supplier
                supplier = random.choice(suppliers_by_tenant[tenant_id])
                
                # Generate PO number
                po_number = f"PO-{tenant_id}-{order_date.strftime('%y%m%d')}-{i+1:04d}"
                
                # Select status based on weights
                status = random.choices(
                    list(status_weights.keys()),
                    weights=list(status_weights.values()),
                    k=1
                )[0]
                
                # Create the purchase order
                purchase_order = PurchaseOrder.objects.create(
                    tenant_id=tenant_id,
                    order_number=po_number,
                    supplier=supplier,
                    order_date=order_date,
                    expected_delivery_date=expected_delivery_date,
                    status=status,
                    notes=fake.paragraph() if random.random() > 0.7 else "",
                    total_amount=0  # Will be updated after adding items
                )
                created_orders.append(purchase_order)
                
                # Add items to the purchase order
                self._add_items_to_purchase_order(tenant_id, purchase_order)
                
                # For received orders, create a receipt
                if status == 'received':
                    self._create_purchase_receipt(tenant_id, purchase_order)
        
        print(f"Created {len(created_orders)} purchase orders")
        return created_orders
    
    def _add_items_to_purchase_order(self, tenant_id, purchase_order):
        """Add items to a purchase order."""
        # Determine how many different items to add
        num_items = random.randint(3, 10)
        
        # Get available items for this tenant
        available_items = self.inventory_items.get(tenant_id, [])
        
        if not available_items:
            return
        
        # Select a random subset of items
        selected_items = random.sample(
            available_items,
            min(num_items, len(available_items))
        )
        
        for item in selected_items:
            # Generate random quantity and price
            quantity = random.randint(1, 50)
            unit_price = round(random.uniform(10, 1000), 2)
            
            # Create the purchase order item
            PurchaseOrderItem.objects.create(
                tenant_id=tenant_id,
                purchase_order=purchase_order,
                item=item,
                quantity=quantity,
                unit_price=unit_price
            )
        
        # Update the total amount
        purchase_order.update_total_amount()
    
    def _create_purchase_receipt(self, tenant_id, purchase_order):
        """Create a purchase receipt for a completed purchase order."""
        # Generate a receipt number
        receipt_number = f"REC-{purchase_order.order_number[3:]}"
        
        # Generate receipt date (after order date, before or on current date)
        min_days = 1
        max_days = (timezone.now().date() - purchase_order.order_date).days
        days_after_order = random.randint(min_days, max(min_days, max_days))
        receipt_date = purchase_order.order_date + timedelta(days=days_after_order)
        
        # Create the receipt
        receipt = PurchaseReceipt.objects.create(
            tenant_id=tenant_id,
            receipt_number=receipt_number,
            purchase_order=purchase_order,
            receipt_date=receipt_date,
            notes=fake.paragraph() if random.random() > 0.7 else ""
        )
        
        # Add items to the receipt
        for po_item in purchase_order.items.all():
            # Usually receive the full order, but sometimes less
            if random.random() > 0.2:  # 80% receive full order
                received_qty = po_item.quantity
            else:
                # Receive partial order
                received_qty = po_item.quantity * random.uniform(0.7, 0.95)
                received_qty = round(received_qty, 2)
            
            # Create the receipt item
            PurchaseReceiptItem.objects.create(
                tenant_id=tenant_id,
                purchase_receipt=receipt,
                purchase_order_item=po_item,
                quantity=received_qty
            )
        
        return receipt
    
    @transaction.atomic
    def run(self):
        """Run the purchases data generator."""
        print("Starting Purchases Data Generator...")
        
        # Check if we have inventory items
        if not Item.objects.exists():
            print("❌ No inventory items found. Please run add_inventory_data.py first.")
            return
        
        # Generate data in the correct order
        self.generate_suppliers(20)
        self.generate_purchase_orders(50)
        
        print("✅ Purchases data generation completed!")


def main():
    """Execute the purchases data generator."""
    generator = PurchasesGenerator()
    generator.run()


if __name__ == "__main__":
    main() 