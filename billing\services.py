from decimal import Decimal
from django.utils import timezone
from django.db.models import Q
import json
import logging

from .models import Invoice, InvoiceItem
from .models_rules import PromotionRule, RuleCondition, RuleEffect, RuleLog

logger = logging.getLogger(__name__)

class RuleEngine:
    """
    Engine for processing and applying dynamic billing rules
    """
    
    def __init__(self, tenant_id):
        self.tenant_id = tenant_id
    
    def get_applicable_rules(self, context):
        """
        Find all rules that might apply to the current context
        
        Args:
            context: Dictionary with context info (customer, vehicle, items, etc)
        
        Returns:
            QuerySet of applicable PromotionRule objects
        """
        now = timezone.now()
        
        # Start with active rules for this tenant within date range
        rules = PromotionRule.objects.filter(
            tenant_id=self.tenant_id,
            is_active=True,
            start_date__lte=now
        ).filter(
            Q(end_date__isnull=True) | Q(end_date__gte=now)
        ).filter(
            Q(max_usages__isnull=True) | Q(current_usages__lt=models.F('max_usages'))
        ).order_by('-priority')
        
        return rules
    
    def evaluate_rule_conditions(self, rule, context):
        """
        Check if a rule's conditions match the current context
        
        Args:
            rule: PromotionRule object
            context: Dictionary with context info
        
        Returns:
            boolean: True if conditions match, False otherwise
        """
        conditions = rule.conditions.all()
        if not conditions:
            return True  # Rule with no conditions applies to everything
        
        # Group conditions by group_id
        condition_groups = {}
        for condition in conditions:
            group_id = condition.group_id or 'default'
            if group_id not in condition_groups:
                condition_groups[group_id] = []
            condition_groups[group_id].append(condition)
        
        # Each condition group is joined with AND
        # Within each group, conditions are joined with AND unless is_or_condition is True
        for group_id, group_conditions in condition_groups.items():
            group_result = False
            
            for i, condition in enumerate(group_conditions):
                condition_result = self._evaluate_single_condition(condition, context)
                
                if i == 0:
                    group_result = condition_result
                else:
                    if condition.is_or_condition:
                        group_result = group_result or condition_result
                    else:
                        group_result = group_result and condition_result
            
            if not group_result:
                return False  # If any group fails, the whole rule fails
        
        return True  # All condition groups passed
    
    def _evaluate_single_condition(self, condition, context):
        """
        Evaluate a single condition against the current context
        
        Args:
            condition: RuleCondition object
            context: Dictionary with context info
        
        Returns:
            boolean: True if condition matches, False otherwise
        """
        condition_type = condition.condition_type
        operator = condition.operator
        value = condition.get_value_as_type()
        value2 = condition.value2
        
        # Extract the target value from context based on condition type
        target_value = self._get_target_value(condition_type, context)
        if target_value is None:
            return False
        
        # Apply the operator
        return self._apply_operator(operator, target_value, value, value2)
    
    def _get_target_value(self, condition_type, context):
        """
        Extract the relevant value from context based on condition type
        """
        # Customer conditions
        if condition_type == 'customer_id':
            return context.get('customer', {}).get('id')
        elif condition_type == 'customer_group':
            return context.get('customer', {}).get('group')
        elif condition_type == 'customer_status':
            return context.get('customer', {}).get('status')
        elif condition_type == 'customer_since':
            return context.get('customer', {}).get('created_at')
        elif condition_type == 'customer_age':
            return context.get('customer', {}).get('age')
        elif condition_type == 'customer_gender':
            return context.get('customer', {}).get('gender')
        elif condition_type == 'customer_location':
            return context.get('customer', {}).get('location')
        
        # Vehicle conditions
        elif condition_type == 'vehicle_id':
            return context.get('vehicle', {}).get('id')
        elif condition_type == 'vehicle_make':
            return context.get('vehicle', {}).get('make')
        elif condition_type == 'vehicle_model':
            return context.get('vehicle', {}).get('model')
        elif condition_type == 'vehicle_year':
            return context.get('vehicle', {}).get('year')
        elif condition_type == 'vehicle_type':
            return context.get('vehicle', {}).get('type')
        elif condition_type == 'vehicle_age':
            return context.get('vehicle', {}).get('age')
        elif condition_type == 'vehicle_mileage':
            return context.get('vehicle', {}).get('mileage')
        
        # Order conditions
        elif condition_type == 'order_total':
            return context.get('order', {}).get('total')
        elif condition_type == 'order_items':
            return len(context.get('order', {}).get('items', []))
        elif condition_type == 'order_date':
            return context.get('order', {}).get('date')
        elif condition_type == 'service_center':
            return context.get('service_center', {}).get('id')
        
        # Time conditions
        elif condition_type == 'day_of_week':
            now = timezone.now()
            return now.strftime('%A').lower()
        elif condition_type == 'month':
            now = timezone.now()
            return now.strftime('%B').lower()
        elif condition_type == 'time_of_day':
            now = timezone.now()
            return now.strftime('%H:%M')
        
        # Service/Part conditions
        elif condition_type == 'service_id':
            services = [item.get('service_id') for item in context.get('order', {}).get('items', []) 
                      if item.get('type') == 'service']
            return services
        elif condition_type == 'part_id':
            parts = [item.get('part_id') for item in context.get('order', {}).get('items', []) 
                   if item.get('type') == 'part']
            return parts
        
        # If not found
        return None
    
    def _apply_operator(self, operator, target_value, condition_value, condition_value2=None):
        """
        Apply the operator to compare target_value and condition_value
        """
        # Handle list target values (like for parts)
        if isinstance(target_value, list):
            if operator == 'contains':
                return condition_value in target_value
            elif operator == 'not_contains':
                return condition_value not in target_value
            elif operator == 'eq':
                return condition_value == target_value
            elif operator == 'in':
                return any(val in condition_value for val in target_value)
            elif operator == 'not_in':
                return not any(val in condition_value for val in target_value)
            return False
        
        # Standard operators for scalar values
        if operator == 'eq':
            return target_value == condition_value
        elif operator == 'neq':
            return target_value != condition_value
        elif operator == 'gt':
            return target_value > condition_value
        elif operator == 'gte':
            return target_value >= condition_value
        elif operator == 'lt':
            return target_value < condition_value
        elif operator == 'lte':
            return target_value <= condition_value
        elif operator == 'contains':
            return str(condition_value) in str(target_value)
        elif operator == 'not_contains':
            return str(condition_value) not in str(target_value)
        elif operator == 'starts_with':
            return str(target_value).startswith(str(condition_value))
        elif operator == 'ends_with':
            return str(target_value).endswith(str(condition_value))
        elif operator == 'in':
            return target_value in condition_value
        elif operator == 'not_in':
            return target_value not in condition_value
        elif operator == 'between':
            if condition_value2:
                return condition_value <= target_value <= condition_value2
            return False
        elif operator == 'not_between':
            if condition_value2:
                return not (condition_value <= target_value <= condition_value2)
            return False
        
        return False
    
    def apply_rules_to_invoice(self, invoice, context=None):
        """
        Apply all applicable rules to an invoice
        
        Args:
            invoice: Invoice object
            context: Optional context dict (will be built if not provided)
            
        Returns:
            dict: Summary of applied rules and effects
        """
        if context is None:
            context = self._build_context(invoice)
        
        applied_rules = []
        rules = self.get_applicable_rules(context)
        
        for rule in rules:
            if self.evaluate_rule_conditions(rule, context):
                rule_applied = self._apply_rule_effects(rule, invoice, context)
                if rule_applied:
                    applied_rules.append(rule)
                    rule.increment_usage()
        
        # After all rules applied, recalculate invoice totals
        invoice.calculate_totals()
        invoice.save()
        
        return {
            'invoice': invoice,
            'applied_rules': applied_rules,
            'rule_count': len(applied_rules)
        }
    
    def _build_context(self, invoice):
        """
        Build a context dictionary from an invoice
        """
        context = {
            'customer': self._get_customer_context(invoice.customer),
            'vehicle': self._get_vehicle_context(invoice.work_order.vehicle),
            'service_center': {
                'id': invoice.service_center.id,
                'name': invoice.service_center.name
            },
            'order': {
                'id': invoice.id,
                'date': invoice.invoice_date,
                'total': invoice.subtotal,
                'items': self._get_items_context(invoice)
            }
        }
        return context
    
    def _get_customer_context(self, customer):
        """Extract customer data for context"""
        context = {
            'id': customer.id,
            'full_name': customer.full_name,
            'email': customer.email,
            'phone': customer.phone,
            'created_at': customer.created_at
        }
        
        # Try to get customer preferences
        try:
            preferences = customer.preferences
            context['status'] = preferences.status
            context['payment_terms'] = preferences.payment_terms
            context['default_discount'] = preferences.default_discount_percentage
        except:
            pass
            
        return context
    
    def _get_vehicle_context(self, vehicle):
        """Extract vehicle data for context"""
        if not vehicle:
            return {}
            
        today = timezone.now().date()
        manufacture_date = vehicle.manufacture_date or today
        age = (today.year - manufacture_date.year)
        
        return {
            'id': vehicle.id,
            'make': vehicle.make,
            'model': vehicle.model, 
            'year': vehicle.year,
            'mileage': vehicle.last_odometer_reading,
            'age': age,
            'vin': vehicle.vin,
            'license_plate': vehicle.license_plate
        }
    
    def _get_items_context(self, invoice):
        """Extract item data for context"""
        items = []
        for item in invoice.items.all():
            items.append({
                'id': item.id,
                'type': item.item_type,
                'description': item.description,
                'quantity': item.quantity,
                'unit_price': item.unit_price,
                'total': item.line_total,
                'part_id': item.part_id,
                'work_order_material_id': str(item.work_order_material_id) if item.work_order_material_id else None,
                'work_order_operation_id': str(item.work_order_operation_id) if item.work_order_operation_id else None
            })
        return items
    
    def _apply_rule_effects(self, rule, invoice, context):
        """
        Apply a rule's effects to an invoice
        
        Args:
            rule: PromotionRule object
            invoice: Invoice object
            context: Context dictionary
            
        Returns:
            boolean: True if any effects were applied
        """
        effects = rule.effects.all()
        if not effects:
            return False
            
        applied_effects = []
        items_modified = []
        
        for effect in effects:
            result = self._apply_single_effect(effect, invoice, context)
            if result['applied']:
                applied_effects.append(effect)
                items_modified.extend(result.get('items_modified', []))
                
                # Log the rule application
                self._log_rule_application(rule, effect, invoice, result)
        
        return len(applied_effects) > 0
    
    def _apply_single_effect(self, effect, invoice, context):
        """
        Apply a single rule effect to an invoice
        
        Args:
            effect: RuleEffect object
            invoice: Invoice object
            context: Context dictionary
            
        Returns:
            dict: Results of applying the effect
        """
        effect_type = effect.effect_type
        effect_value = effect.effect_value
        items_modified = []
        
        # Global discount effects
        if effect_type == 'order_discount_percentage':
            discount_amount = (invoice.subtotal * effect_value) / Decimal('100.0')
            if effect.max_amount:
                discount_amount = min(discount_amount, effect.max_amount)
                
            invoice.discount_amount = discount_amount
            
            return {
                'applied': True,
                'effect_type': effect_type,
                'amount': discount_amount,
                'description': effect.description or f"Order discount {effect_value}%"
            }
            
        elif effect_type == 'order_discount_fixed':
            discount_amount = min(effect_value, invoice.subtotal)
            invoice.discount_amount = discount_amount
            
            return {
                'applied': True,
                'effect_type': effect_type,
                'amount': discount_amount,
                'description': effect.description or f"Order discount {discount_amount}"
            }
        
        # Item-level discounts
        elif effect_type in ('item_discount_percentage', 'item_discount_fixed'):
            applicable_items = self._get_applicable_items(invoice, effect)
            
            for item in applicable_items:
                if effect_type == 'item_discount_percentage':
                    item_discount = (item.unit_price * effect_value) / Decimal('100.0')
                    item.discount_percentage = effect_value
                else:
                    item_discount = min(effect_value, item.unit_price)
                    
                item.discount_amount = item_discount * item.quantity
                item.save()
                items_modified.append(str(item.id))
            
            return {
                'applied': len(items_modified) > 0,
                'effect_type': effect_type,
                'items_modified': items_modified,
                'description': effect.description or f"Item discount applied to {len(items_modified)} items"
            }
        
        # Special price effects
        elif effect_type == 'set_special_price':
            applicable_items = self._get_applicable_items(invoice, effect)
            
            for item in applicable_items:
                original_price = item.unit_price
                item.unit_price = effect_value
                item.save()
                items_modified.append(str(item.id))
            
            return {
                'applied': len(items_modified) > 0,
                'effect_type': effect_type,
                'items_modified': items_modified,
                'description': effect.description or f"Special price applied to {len(items_modified)} items"
            }
        
        # Warranty/Insurance effects
        elif effect_type == 'set_warranty_coverage':
            applicable_items = self._get_applicable_items(invoice, effect)
            
            for item in applicable_items:
                item.is_covered_by_warranty = True
                item.coverage_percentage = effect_value
                item.save()
                items_modified.append(str(item.id))
            
            return {
                'applied': len(items_modified) > 0,
                'effect_type': effect_type,
                'items_modified': items_modified,
                'description': effect.description or f"Warranty coverage {effect_value}% applied to {len(items_modified)} items" 
            }
        
        elif effect_type == 'set_insurance_coverage':
            applicable_items = self._get_applicable_items(invoice, effect)
            
            for item in applicable_items:
                item.is_covered_by_insurance = True
                item.coverage_percentage = effect_value
                item.save()
                items_modified.append(str(item.id))
            
            return {
                'applied': len(items_modified) > 0,
                'effect_type': effect_type,
                'items_modified': items_modified,
                'description': effect.description or f"Insurance coverage {effect_value}% applied to {len(items_modified)} items"
            }
        
        # Buy X Get Y
        elif effect_type == 'buy_x_get_y':
            if not effect.effect_data:
                return {'applied': False}
                
            data = effect.effect_data
            buy_item_id = data.get('buy_item_id')
            get_item_id = data.get('get_item_id')
            buy_quantity = data.get('buy_quantity', 1)
            get_quantity = data.get('get_quantity', 1)
            
            # Find eligible buy items
            buy_items = invoice.items.filter(part_id=buy_item_id)
            if not buy_items.exists():
                return {'applied': False}
                
            # Calculate how many free items to add
            total_buy_qty = sum(item.quantity for item in buy_items)
            free_sets = int(total_buy_qty / buy_quantity)
            
            if free_sets > 0:
                # Apply discount to existing matching items or create new free item
                get_items = invoice.items.filter(part_id=get_item_id)
                if get_items.exists():
                    get_item = get_items.first()
                    get_item.discount_percentage = 100
                    get_item.save()
                    items_modified.append(str(get_item.id))
                else:
                    # We might need to create a new free item
                    # Logic would depend on how your system works
                    pass
                    
                return {
                    'applied': True,
                    'effect_type': effect_type,
                    'items_modified': items_modified,
                    'description': effect.description or f"Buy {buy_quantity} get {get_quantity} free"
                }
        
        # If we didn't apply anything
        return {'applied': False}
    
    def _get_applicable_items(self, invoice, effect):
        """
        Get the invoice items that this effect applies to
        """
        apply_to = effect.apply_to
        items = invoice.items.all()
        
        # Filter by type
        if apply_to == 'parts_only':
            items = items.filter(item_type='part')
        elif apply_to == 'labor_only':
            items = items.filter(item_type='labor')
        elif apply_to == 'specific_items':
            item_filter = effect.item_filter or {}
            item_ids = item_filter.get('item_ids', [])
            if item_ids:
                items = items.filter(id__in=item_ids)
        elif apply_to == 'specific_categories':
            item_filter = effect.item_filter or {}
            categories = item_filter.get('categories', [])
            if categories:
                # This would need to be customized based on how you track categories
                pass
        
        # Limit the number of items if specified
        if effect.max_items and effect.max_items > 0:
            items = items[:effect.max_items]
            
        return items
    
    def _log_rule_application(self, rule, effect, invoice, result):
        """
        Log that a rule was applied
        """
        customer = invoice.customer
        vehicle = invoice.work_order.vehicle if invoice.work_order else None
        
        log = RuleLog(
            tenant_id=self.tenant_id,
            rule=rule,
            invoice=invoice,
            work_order=invoice.work_order,
            customer=customer,
            vehicle=vehicle,
            effect_type=effect.effect_type,
            amount=result.get('amount', 0),
            required_approval=rule.requires_approval,
            applied_items=result.get('items_modified', []),
            rule_data={
                'rule_name': rule.name,
                'rule_type': rule.rule_type,
                'effect_description': result.get('description', '')
            }
        )
        log.save()
        
        return log 