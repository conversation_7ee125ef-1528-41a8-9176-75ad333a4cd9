"""
Core utility functions used across the project.
"""
import logging
from django.db.models import Model

logger = logging.getLogger(__name__)

def get_tenant_id(request, model_class=None):
    """
    Get tenant ID from request with consistent fallbacks.
    
    Args:
        request: The HTTP request object
        model_class: Optional model class to check for tenant_id
        
    Returns:
        tenant_id (UUID string) or None if not found
    """
    # First check request attribute (most direct)
    tenant_id = getattr(request, 'tenant_id', None)
    
    # Then check session
    if not tenant_id and hasattr(request, 'session'):
        tenant_id = request.session.get('tenant_id')
    
    # Finally check a model instance if needed
    if not tenant_id and model_class and issubclass(model_class, Model):
        try:
            obj = model_class.objects.first()
            if obj and hasattr(obj, 'tenant_id'):
                tenant_id = obj.tenant_id
        except Exception as e:
            logger.warning(f"Error getting tenant_id from model {model_class.__name__}: {str(e)}")
    
    if not tenant_id:
        logger.warning(f"Could not determine tenant_id from request or model")
        
    return tenant_id

def add_tenant_id_to_kwargs(request, kwargs, model_class=None):
    """
    Add tenant_id to kwargs if not present.
    Useful for filtering querysets in views.
    
    Args:
        request: The HTTP request object
        kwargs: Dictionary of keyword arguments
        model_class: Optional model class to check for tenant_id
        
    Returns:
        Updated kwargs dictionary with tenant_id
    """
    if 'tenant_id' not in kwargs:
        tenant_id = get_tenant_id(request, model_class)
        if tenant_id:
            kwargs['tenant_id'] = tenant_id
    
    return kwargs

def get_language_from_request(request):
    """
    Get the active language from request.
    
    Args:
        request: The HTTP request object
        
    Returns:
        Language code (str)
    """
    from django.utils import translation
    
    # Check if request has a language attribute
    if hasattr(request, 'LANGUAGE_CODE'):
        return request.LANGUAGE_CODE
    
    # Try to get from session
    if hasattr(request, 'session'):
        lang_code = request.session.get('django_language')
        if lang_code:
            return lang_code
    
    # Default to active language
    return translation.get_language() or 'ar'  # Default to Arabic if not set 