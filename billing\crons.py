import logging
from django.db import transaction
from core.models.tenant import Tenant
from .services_classification import CustomerClassificationService

logger = logging.getLogger(__name__)


def evaluate_all_customers_classifications():
    """
    Scheduled task to evaluate all customers across all tenants for possible classification changes
    """
    tenants = Tenant.objects.filter(is_active=True)
    results = {
        'tenants_processed': 0,
        'total_customers': 0,
        'customers_changed': 0,
        'errors': 0
    }
    
    for tenant in tenants:
        try:
            with transaction.atomic():
                service = CustomerClassificationService(tenant.id)
                tenant_results = service.evaluate_all_customers()
                
                results['tenants_processed'] += 1
                results['total_customers'] += tenant_results['total']
                results['customers_changed'] += tenant_results['changed']
                
                # Log significant changes
                if tenant_results['changed'] > 0:
                    logger.info(f"Tenant {tenant.name}: {tenant_results['changed']} customer classifications changed")
        
        except Exception as e:
            results['errors'] += 1
            logger.error(f"Error processing tenant {tenant.id}: {str(e)}")
    
    return results


def check_for_classification_downgrades():
    """
    Scheduled task to check for customers who should be downgraded
    """
    tenants = Tenant.objects.filter(is_active=True)
    results = {
        'tenants_processed': 0,
        'total_customers': 0,
        'customers_downgraded': 0,
        'errors': 0
    }
    
    for tenant in tenants:
        try:
            with transaction.atomic():
                service = CustomerClassificationService(tenant.id)
                tenant_results = service.check_for_downgrades()
                
                results['tenants_processed'] += 1
                results['total_customers'] += tenant_results['total']
                results['customers_downgraded'] += tenant_results['downgraded']
                
                # Log significant changes
                if tenant_results['downgraded'] > 0:
                    logger.info(f"Tenant {tenant.name}: {tenant_results['downgraded']} customer classifications downgraded")
        
        except Exception as e:
            results['errors'] += 1
            logger.error(f"Error processing tenant {tenant.id}: {str(e)}")
    
    return results 