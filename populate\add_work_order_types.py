import os
import sys
import django
import random
import uuid

# Add the parent directory to the Python path so imports work correctly
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

# Import Django models
from django.db import transaction
from django.utils import timezone

try:
    from work_orders.models import WorkOrderType
    from setup.models import ServiceCenter
except ImportError as e:
    print(f"Could not import models: {e}")
    print("Please check app structure and model names.")
    sys.exit(1)


class WorkOrderTypeGenerator:
    """Generate WorkOrderType records for the work_orders app"""
    
    def __init__(self):
        print("WorkOrderType Generator initialized")
        
    def run(self):
        """Run the work order type generator"""
        print("Starting work order type generation...")
        
        try:
            with transaction.atomic():
                self.generate_work_order_types()
        except Exception as e:
            print(f"Error generating work order types: {e}")
        
        print("\nWork order type generation complete!")
    
    def generate_work_order_types(self):
        """Generate WorkOrderType records"""
        # Delete existing records
        WorkOrderType.objects.all().delete()
        print("Cleared existing WorkOrderType records")
        
        # Get tenant ID from service centers (for consistency)
        tenant_id = None
        service_center = ServiceCenter.objects.first()
        if service_center:
            tenant_id = service_center.tenant_id
            print(f"Using tenant_id: {tenant_id}")
        else:
            print("No service centers found. Using default UUID for tenant_id.")
            tenant_id = uuid.uuid4()
        
        # Common work order types in Egyptian vehicle service context
        work_order_types = [
            {
                "name": "تغيير زيت",
                "name_en": "Oil Change",
                "code": "OIL_CHANGE",
                "description": "تغيير زيت المحرك وفلتر الزيت",
                "description_en": "Change engine oil and oil filter",
                "color": "#FF9500"
            },
            {
                "name": "صيانة دورية",
                "name_en": "Periodic Maintenance",
                "code": "PERIODIC",
                "description": "فحص وصيانة دورية للمركبة وفقًا لتوصيات الشركة المصنعة",
                "description_en": "Check and perform routine maintenance according to manufacturer recommendations",
                "color": "#34C759"
            },
            {
                "name": "فحص فرامل",
                "name_en": "Brake Inspection",
                "code": "BRAKE_INSPECT",
                "description": "فحص نظام الفرامل وتغيير قطع الغيار إذا لزم الأمر",
                "description_en": "Inspect brake system and replace parts if necessary",
                "color": "#FF3B30"
            },
            {
                "name": "تغيير فرامل",
                "name_en": "Brake Replacement",
                "code": "BRAKE_REPLACE",
                "description": "تغيير وسائد الفرامل أو أقراص الفرامل",
                "description_en": "Replace brake pads or brake discs",
                "color": "#FF3B30"
            },
            {
                "name": "ضبط محاذاة العجلات",
                "name_en": "Wheel Alignment",
                "code": "ALIGNMENT",
                "description": "ضبط محاذاة العجلات لتحسين الأداء وإطالة عمر الإطارات",
                "description_en": "Adjust wheel alignment to improve performance and tire life",
                "color": "#5856D6"
            },
            {
                "name": "تغيير إطارات",
                "name_en": "Tire Change",
                "code": "TIRE_CHANGE",
                "description": "تغيير الإطارات وموازنة العجلات",
                "description_en": "Change tires and balance wheels",
                "color": "#5856D6"
            },
            {
                "name": "صيانة تكييف",
                "name_en": "AC Service",
                "code": "AC_SERVICE",
                "description": "فحص وصيانة نظام التكييف وإعادة شحن غاز التبريد",
                "description_en": "Inspect and service AC system and recharge refrigerant",
                "color": "#007AFF"
            },
            {
                "name": "فحص كهرباء",
                "name_en": "Electrical Check",
                "code": "ELECTRICAL",
                "description": "فحص وإصلاح المشاكل الكهربائية",
                "description_en": "Check and repair electrical issues",
                "color": "#FFCC00"
            },
            {
                "name": "تشخيص أعطال",
                "name_en": "Diagnostics",
                "code": "DIAGNOSTICS",
                "description": "تشخيص أعطال المركبة باستخدام أجهزة الفحص الإلكترونية",
                "description_en": "Diagnose vehicle issues using electronic testing equipment",
                "color": "#8E8E93"
            },
            {
                "name": "فحص قبل السفر",
                "name_en": "Pre-trip Inspection",
                "code": "PRE_TRIP",
                "description": "فحص شامل للمركبة قبل رحلات السفر الطويلة",
                "description_en": "Comprehensive vehicle check before long trips",
                "color": "#4CD964"
            },
            {
                "name": "تغيير سير التايمنج",
                "name_en": "Timing Belt Replacement",
                "code": "TIMING_BELT",
                "description": "تغيير سير التايمنج والبكرات المرتبطة به",
                "description_en": "Replace timing belt and associated pulleys",
                "color": "#FF2D55"
            },
            {
                "name": "إصلاح المحرك",
                "name_en": "Engine Repair",
                "code": "ENGINE_REPAIR",
                "description": "إصلاح أو تجديد المحرك",
                "description_en": "Engine repair or rebuilding",
                "color": "#FF9500"
            },
            {
                "name": "إصلاح ناقل الحركة",
                "name_en": "Transmission Repair",
                "code": "TRANSMISSION",
                "description": "إصلاح أو تجديد ناقل الحركة",
                "description_en": "Transmission repair or rebuilding",
                "color": "#AF52DE"
            },
            {
                "name": "تغيير البطارية",
                "name_en": "Battery Replacement",
                "code": "BATTERY",
                "description": "تغيير بطارية المركبة",
                "description_en": "Replace vehicle battery",
                "color": "#FFCC00"
            },
            {
                "name": "تنظيف وتعقيم",
                "name_en": "Cleaning and Sanitizing",
                "code": "CLEANING",
                "description": "تنظيف وتعقيم المركبة من الداخل والخارج",
                "description_en": "Clean and sanitize vehicle interior and exterior",
                "color": "#5AC8FA"
            }
        ]
        
        created_count = 0
        preset_ids = {}
        
        # Create WorkOrderType records
        for work_type in work_order_types:
            # Generate a UUID for this work order type
            type_id = uuid.uuid4()
            preset_ids[work_type["code"]] = type_id
            
            # Create the work order type
            WorkOrderType.objects.create(
                id=type_id,
                tenant_id=tenant_id,
                name=work_type["name"],
                description=work_type["description"],
                color_code=work_type["color"],
                is_active=True,
                attributes={"name_en": work_type["name_en"], "description_en": work_type["description_en"], "code": work_type["code"]}
            )
            created_count += 1
        
        print(f"Successfully created {created_count} work order types")
        
        # Save the preset IDs for use in other scripts (especially for operation compatibilities)
        print("\nPreset Work Order Type IDs:")
        for code, type_id in preset_ids.items():
            print(f"{code}: {type_id}")
        
        return created_count, preset_ids


if __name__ == "__main__":
    generator = WorkOrderTypeGenerator()
    generator.run() 