import os
import sys
import django
import random
from decimal import Decimal
from datetime import datetime, timedelta
from django.db import transaction
from django.utils import timezone
from faker import Faker

# Add the parent directory to sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

# Import necessary models
from setup.models import ServiceCenter, Customer, Vehicle
from billing.models import (
    CustomerPreference, InsuranceCompany, InsurancePolicy, WarrantyType, 
    VehicleWarranty, DiscountType, PaymentMethod, Invoice, InvoiceItem
)

# Initialize Faker
fake = Faker('ar_EG')  # Using Egyptian Arabic locale for Egyptian market specifics

class BillingDataGenerator:
    """Generate billing data for the Aftersails system."""
    
    def __init__(self):
        # Use a specific tenant_id from vehicles that we know exists
        specific_tenant_id = "35a0932c-dd2c-4c5a-9b95-a67bf2857913"  # From vehicles we checked
        
        try:
            # Try to verify tenant ID exists
            vehicles = Vehicle.objects.filter(tenant_id=specific_tenant_id)
            if vehicles.exists():
                tenant_id = specific_tenant_id
                print(f"Using specific tenant ID from vehicles: {tenant_id}")
            else:
                # Fallback to any available tenant
                tenant_id = self._find_any_tenant_id()
        except:
            # Fallback if there's an error
            tenant_id = self._find_any_tenant_id()
            
        self.tenant_id = tenant_id
        print(f"Using tenant ID: {self.tenant_id}")
    
    def _find_any_tenant_id(self):
        """Find any tenant ID from existing data"""
        try:
            # Try customers first
            customer = Customer.objects.first()
            if customer:
                return customer.tenant_id
                
            # Then try service centers
            service_center = ServiceCenter.objects.first()
            if service_center:
                return service_center.tenant_id
                
            # Then vehicles
            vehicle = Vehicle.objects.first()
            if vehicle:
                return vehicle.tenant_id
        except:
            pass
            
        # If no tenant_id found, use a default one
        return "default_tenant"
    
    @transaction.atomic
    def generate_billing_data(self):
        """Generate billing-related data."""
        print("Generating Billing data...")
        
        # Get existing customers and service centers
        customers = list(Customer.objects.filter(tenant_id=self.tenant_id))
        service_centers = list(ServiceCenter.objects.filter(tenant_id=self.tenant_id))
        
        if not customers or not service_centers:
            print("No customers or service centers found. Make sure to run generate_setup_data.py first.")
            return
        
        # Create insurance companies
        insurance_companies = self._create_insurance_companies()
        
        # Create warranty types
        warranty_types = self._create_warranty_types()
        
        # Create discount types
        discount_types = self._create_discount_types()
        
        # Create payment methods
        payment_methods = self._create_payment_methods()
        
        # Create customer preferences
        self._create_customer_preferences(customers, payment_methods)
        
        # Create insurance policies
        self._create_insurance_policies(customers, insurance_companies)
        
        # Create vehicle warranties
        self._create_vehicle_warranties(customers, warranty_types)
        
        # Create invoices
        self._create_invoices(customers, service_centers, payment_methods, discount_types)
    
    def _create_insurance_companies(self):
        """Create insurance company records."""
        companies_data = [
            {
                'name': 'شركة مصر للتأمين',
                'code': 'MISR-INS',
                'contact_person': 'أحمد محمود',
                'phone': '0223456789',
                'email': '<EMAIL>'
            },
            {
                'name': 'الشركة الأهلية للتأمين',
                'code': 'AHLIA-INS',
                'contact_person': 'محمد السيد',
                'phone': '0223456790',
                'email': '<EMAIL>'
            },
            {
                'name': 'شركة التأمين العربية',
                'code': 'ARAB-INS',
                'contact_person': 'مصطفى كمال',
                'phone': '0223456791',
                'email': '<EMAIL>'
            }
        ]
        
        companies = []
        for data in companies_data:
            try:
                company, created = InsuranceCompany.objects.get_or_create(
                    tenant_id=self.tenant_id,
                    name=data['name'],
                    defaults={
                        'code': data['code'],
                        'contact_person': data['contact_person'],
                        'phone': data['phone'],
                        'email': data['email'],
                        'address': fake.address(),
                        'contract_number': f"INS-{fake.random_number(digits=6)}",
                        'contract_start_date': fake.date_between(start_date='-2y', end_date='-1y'),
                        'contract_end_date': fake.date_between(start_date='+1y', end_date='+3y'),
                        'approval_required': random.choice([True, False]),
                        'standard_approval_time_hours': random.choice([24, 48, 72]),
                        'is_active': True
                    }
                )
                
                companies.append(company)
                if created:
                    print(f"Created Insurance Company: {company.name}")
                else:
                    print(f"Using existing Insurance Company: {company.name}")
            except Exception as e:
                print(f"Error creating insurance company {data['name']}: {e}")
        
        return companies
    
    def _create_warranty_types(self):
        """Create warranty type records."""
        warranty_data = [
            {
                'name': 'ضمان المصنع',
                'description': 'الضمان الأساسي المقدم من المصنع',
                'parts_covered': 'كافة الأجزاء',
                'labor_covered': True,
                'default_duration_months': 36
            },
            {
                'name': 'ضمان ممتد',
                'description': 'ضمان إضافي بعد انتهاء ضمان المصنع',
                'parts_covered': 'المحرك وناقل الحركة',
                'labor_covered': True,
                'default_duration_months': 24
            },
            {
                'name': 'ضمان قطع الغيار',
                'description': 'ضمان على قطع الغيار فقط',
                'parts_covered': 'قطع الغيار المستبدلة',
                'labor_covered': False,
                'default_duration_months': 12
            },
            {
                'name': 'ضمان الصيانة',
                'description': 'ضمان على أعمال الصيانة المقدمة',
                'parts_covered': 'الأجزاء المصانة',
                'labor_covered': True,
                'default_duration_months': 6
            }
        ]
        
        warranties = []
        for data in warranty_data:
            try:
                warranty, created = WarrantyType.objects.get_or_create(
                    tenant_id=self.tenant_id,
                    name=data['name'],
                    defaults={
                        'description': data['description'],
                        'parts_covered': data['parts_covered'],
                        'labor_covered': data['labor_covered'],
                        'default_duration_months': data['default_duration_months'],
                        'default_mileage_limit': random.randint(50000, 150000) if random.choice([True, False]) else None,
                        'is_active': True
                    }
                )
                
                warranties.append(warranty)
                if created:
                    print(f"Created Warranty Type: {warranty.name}")
                else:
                    print(f"Using existing Warranty Type: {warranty.name}")
            except Exception as e:
                print(f"Error creating warranty type {data['name']}: {e}")
        
        return warranties
    
    def _create_discount_types(self):
        """Create discount type records."""
        discount_data = [
            {
                'name': 'خصم العملاء الدائمين',
                'description': 'خصم للعملاء الذين لديهم تاريخ طويل معنا',
                'discount_method': 'percentage',
                'percentage': Decimal('10.00')
            },
            {
                'name': 'خصم الصيانة الشاملة',
                'description': 'خصم عند إجراء صيانة شاملة',
                'discount_method': 'percentage',
                'percentage': Decimal('15.00')
            },
            {
                'name': 'خصم العروض الموسمية',
                'description': 'خصم خلال المواسم والعروض الخاصة',
                'discount_method': 'percentage',
                'percentage': Decimal('20.00')
            },
            {
                'name': 'خصم عيد الميلاد',
                'description': 'خصم في عيد ميلاد العميل',
                'discount_method': 'percentage',
                'percentage': Decimal('5.00')
            }
        ]
        
        discounts = []
        for data in discount_data:
            try:
                now = timezone.now().date()
                discount, created = DiscountType.objects.get_or_create(
                    tenant_id=self.tenant_id,
                    name=data['name'],
                    defaults={
                        'description': data['description'],
                        'discount_method': data['discount_method'],
                        'percentage': data['percentage'],
                        'fixed_amount': Decimal('0.00'),
                        'valid_from': now - timedelta(days=30),
                        'valid_to': now + timedelta(days=365),
                        'is_active': True,
                        'apply_to_parts': True,
                        'apply_to_labor': True
                    }
                )
                
                discounts.append(discount)
                if created:
                    print(f"Created Discount Type: {discount.name}")
                else:
                    print(f"Using existing Discount Type: {discount.name}")
            except Exception as e:
                print(f"Error creating discount type {data['name']}: {e}")
        
        return discounts
    
    def _create_payment_methods(self):
        """Create payment method records."""
        payment_data = [
            {
                'name': 'نقدي',
                'payment_type': 'cash',
                'processing_fee_percentage': Decimal('0.00')
            },
            {
                'name': 'بطاقة ائتمان',
                'payment_type': 'credit_card',
                'processing_fee_percentage': Decimal('2.50')
            },
            {
                'name': 'تحويل بنكي',
                'payment_type': 'bank_transfer',
                'processing_fee_percentage': Decimal('1.00')
            },
            {
                'name': 'محفظة إلكترونية',
                'payment_type': 'e_wallet',
                'processing_fee_percentage': Decimal('1.50')
            },
            {
                'name': 'فوري',
                'payment_type': 'e_wallet',
                'processing_fee_percentage': Decimal('1.25')
            }
        ]
        
        payment_methods = []
        for data in payment_data:
            try:
                method, created = PaymentMethod.objects.get_or_create(
                    tenant_id=self.tenant_id,
                    name=data['name'],
                    defaults={
                        'payment_type': data['payment_type'],
                        'description': f"طريقة دفع عن طريق {data['name']}",
                        'processing_fee_percentage': data['processing_fee_percentage'],
                        'processing_fee_fixed': Decimal('0.00'),
                        'is_active': True,
                        'requires_approval': data['payment_type'] in ['bank_transfer', 'check', 'insurance']
                    }
                )
                
                payment_methods.append(method)
                if created:
                    print(f"Created Payment Method: {method.name}")
                else:
                    print(f"Using existing Payment Method: {method.name}")
            except Exception as e:
                print(f"Error creating payment method {data['name']}: {e}")
        
        return payment_methods
    
    def _create_customer_preferences(self, customers, payment_methods):
        """Create customer preference records."""
        customer_preferences = []
        
        for customer in random.sample(customers, min(35, len(customers))):
            try:
                preference, created = CustomerPreference.objects.get_or_create(
                    tenant_id=self.tenant_id,
                    customer=customer,
                    defaults={
                        'status': random.choice(['regular', 'vip', 'gold', 'platinum']),
                        'payment_terms': random.choice(['cash', 'credit_30', 'credit_60', 'credit_90']),
                        'credit_limit': Decimal(str(random.randint(5000, 50000))),
                        'default_discount_percentage': Decimal(str(random.randint(0, 10))),
                        'send_sms_notifications': random.choice([True, False]),
                        'send_email_notifications': random.choice([True, False]),
                        'special_instructions': fake.paragraph(nb_sentences=2) if random.choice([True, False]) else ''
                    }
                )
                
                customer_preferences.append(preference)
                if created:
                    print(f"Created Customer Preference for {customer}")
                else:
                    print(f"Using existing Customer Preference for {customer}")
            except Exception as e:
                print(f"Error creating customer preference for {customer}: {e}")
        
        print(f"Created {len(customer_preferences)} Customer Preferences")
        return customer_preferences
    
    def _create_insurance_policies(self, customers, insurance_companies):
        """Create insurance policy records for vehicles."""
        policies = []
        
        # Get vehicles owned by customers
        vehicles = list(Vehicle.objects.filter(tenant_id=self.tenant_id))
        if not vehicles:
            print("No vehicles found. Cannot create insurance policies.")
            return []
        
        # Only create policies for some vehicles
        sample_size = min(20, len(vehicles))
        selected_vehicles = random.sample(vehicles, sample_size)
        
        for vehicle in selected_vehicles:
            try:
                # Try to get the actual owner
                customer = None
                if vehicle.owner:
                    customer = vehicle.owner
                else:
                    # Assign a random customer
                    customer = random.choice(customers)
                
                # Select a random insurance company
                insurance_company = random.choice(insurance_companies)
                
                # Create start and end dates
                start_date = fake.date_between(start_date='-2y', end_date='today')
                end_date = start_date + timedelta(days=random.randint(180, 365 * 3))  # 6 months to 3 years
                
                policy, created = InsurancePolicy.objects.get_or_create(
                    tenant_id=self.tenant_id,
                    vehicle=vehicle,
                    customer=customer,
                    defaults={
                        'insurance_company': insurance_company,
                        'policy_number': f"POL-{fake.random_number(digits=8)}",
                        'policy_type': random.choice(['comprehensive', 'third_party', 'partial']),
                        'start_date': start_date,
                        'end_date': end_date,
                        'coverage_amount': Decimal(str(random.randint(50000, 500000))),
                        'deductible': Decimal(str(random.randint(1000, 10000))),
                        'is_active': True,
                        'notes': fake.sentence() if random.choice([True, False]) else ''
                    }
                )
                
                policies.append(policy)
                if created:
                    print(f"Created Insurance Policy for vehicle {vehicle}")
                else:
                    print(f"Using existing Insurance Policy for vehicle {vehicle}")
            except Exception as e:
                print(f"Error creating insurance policy for vehicle {vehicle}: {e}")
        
        print(f"Created {len(policies)} Insurance Policies")
        return policies
    
    def _create_vehicle_warranties(self, customers, warranty_types):
        """Create vehicle warranty records."""
        warranties = []
        
        # Get vehicles owned by customers
        vehicles = list(Vehicle.objects.filter(tenant_id=self.tenant_id))
        if not vehicles:
            print("No vehicles found. Cannot create vehicle warranties.")
            return []
        
        # Only create warranties for some vehicles
        sample_size = min(30, len(vehicles))
        selected_vehicles = random.sample(vehicles, sample_size)
        
        for vehicle in selected_vehicles:
            try:
                # Select a random warranty type
                warranty_type = random.choice(warranty_types)
                
                # Create start and end dates
                start_date = fake.date_between(start_date='-3y', end_date='-6m')
                end_date = start_date + timedelta(days=warranty_type.default_duration_months * 30)
                
                # Generate mileage limit based on warranty type's default
                mileage_limit = warranty_type.default_mileage_limit or random.randint(50000, 150000)
                
                # Determine provider
                provider = random.choice(['manufacturer', 'dealer', 'third_party', 'extended'])
                provider_name = {
                    'manufacturer': vehicle.make,
                    'dealer': f"{vehicle.make} مركز خدمة",
                    'third_party': random.choice(['شركة الضمان الشامل', 'مصر للضمانات', 'الأهلية للضمان']),
                    'extended': random.choice(['الضمان الممتد العالمي', 'ضمان بلس', 'الحماية الكاملة'])
                }[provider]
                
                warranty, created = VehicleWarranty.objects.get_or_create(
                    tenant_id=self.tenant_id,
                    vehicle=vehicle,
                    warranty_type=warranty_type,
                    defaults={
                        'warranty_number': f"WAR-{fake.random_number(digits=8)}",
                        'provider': provider,
                        'provider_name': provider_name,
                        'start_date': start_date,
                        'end_date': end_date,
                        'mileage_limit': mileage_limit,
                        'is_active': True,
                        'notes': fake.sentence() if random.choice([True, False]) else ''
                    }
                )
                
                warranties.append(warranty)
                if created:
                    print(f"Created Vehicle Warranty for {vehicle}")
                else:
                    print(f"Using existing Vehicle Warranty for {vehicle}")
            except Exception as e:
                print(f"Error creating vehicle warranty for vehicle {vehicle}: {e}")
        
        print(f"Created {len(warranties)} Vehicle Warranties")
        return warranties
    
    def _create_invoices(self, customers, service_centers, payment_methods, discount_types):
        """Create invoice records."""
        invoices = []
        
        # Check if we can access WorkOrder
        try:
            from work_orders.models import WorkOrder
            work_orders_available = True
            work_orders = list(WorkOrder.objects.filter(tenant_id=self.tenant_id))
            if not work_orders:
                print("No work orders found. Cannot create invoices.")
                work_orders_available = False
        except ImportError:
            print("WorkOrder model not available. Will create invoices without work_order reference.")
            work_orders_available = False
        
        # Create between 10-20 invoices or fewer if limited by constraints
        num_invoices = min(20, len(customers))
        selected_customers = random.sample(customers, num_invoices)
        
        for customer in selected_customers:
            try:
                # Select a random service center
                service_center = random.choice(service_centers)
                
                # Get a work order for this customer if available
                work_order = None
                if work_orders_available:
                    customer_work_orders = [wo for wo in work_orders if wo.customer_id == customer.id]
                    if customer_work_orders:
                        work_order = random.choice(customer_work_orders)
                
                # If we need a work_order and don't have one, skip this invoice
                if not work_order and work_orders_available:
                    continue
                
                # Create dates
                invoice_date = fake.date_between(start_date='-1y', end_date='today')
                due_date = invoice_date + timedelta(days=random.choice([15, 30, 45, 60]))
                
                # Generate invoice number
                invoice_number = f"INV-{fake.random_number(digits=6)}"
                
                # Apply discount sometimes
                apply_discount = random.random() < 0.3  # 30% chance of discount
                discount_type = random.choice(discount_types) if discount_types and apply_discount else None
                discount_amount = Decimal(str(random.randint(50, 500))) if apply_discount else Decimal('0')
                
                # Generate random tax rate
                tax_percentage = Decimal(str(random.choice([0, 5, 10, 14, 16])))
                
                # Create the invoice
                invoice_data = {
                    'tenant_id': self.tenant_id,
                    'customer': customer,
                    'service_center': service_center,
                    'invoice_number': invoice_number,
                    'invoice_date': invoice_date,
                    'due_date': due_date,
                    'status': random.choice(['draft', 'issued', 'paid', 'overdue']),
                    'discount_type': discount_type,
                    'discount_amount': discount_amount,
                    'tax_method': random.choice(['inclusive', 'exclusive']),
                    'tax_percentage': tax_percentage,
                    'notes': fake.paragraph(nb_sentences=2) if random.choice([True, False]) else '',
                    'terms_and_conditions': "الشروط والأحكام القياسية للفواتير" if random.choice([True, False]) else '',
                }
                
                if work_order:
                    invoice_data['work_order'] = work_order
                
                # Try to find existing invoice or create new one
                if work_order:
                    invoice, created = Invoice.objects.get_or_create(
                        tenant_id=self.tenant_id,
                        work_order=work_order,
                        defaults=invoice_data
                    )
                else:
                    invoice, created = Invoice.objects.get_or_create(
                        tenant_id=self.tenant_id,
                        invoice_number=invoice_number,
                        defaults=invoice_data
                    )
                
                if created:
                    # Create invoice items
                    self._create_invoice_items(invoice)
                    
                    # Calculate totals
                    invoice.calculate_totals()
                    invoice.save()
                    
                    invoices.append(invoice)
                    print(f"Created Invoice: {invoice.invoice_number} for {customer}")
                else:
                    print(f"Using existing Invoice: {invoice.invoice_number}")
            except Exception as e:
                print(f"Error creating invoice: {e}")
        
        print(f"Created {len(invoices)} Invoices")
        return invoices
    
    def _create_invoice_items(self, invoice):
        """Create line items for an invoice."""
        num_items = random.randint(1, 5)
        
        # Different item types and descriptions
        item_types = ['part', 'labor', 'service', 'fee']
        descriptions = {
            'part': [
                'فلتر زيت', 'فلتر هواء', 'زيت محرك', 'شمعات إشعال', 'بطارية', 
                'طقم فرامل أمامي', 'طقم فرامل خلفي', 'مساعدين أمامي', 'مساعدين خلفي', 'بلي عجل'
            ],
            'labor': [
                'أجور تغيير زيت', 'أجور صيانة دورية', 'أجور إصلاح فرامل', 'أجور ضبط زوايا',
                'أجور فحص كمبيوتر', 'أجور إصلاح كهرباء', 'أجور تغيير قطع غيار'
            ],
            'service': [
                'غسيل سيارة', 'تلميع', 'صيانة دورية كاملة', 'فحص شامل', 'ضبط محرك', 'غسيل ماتور'
            ],
            'fee': [
                'رسوم إدارية', 'رسوم فحص', 'رسوم كشف', 'رسوم توصيل'
            ]
        }
        
        for _ in range(num_items):
            # Select random item type and description
            item_type = random.choice(item_types)
            description = random.choice(descriptions[item_type])
            
            # Generate pricing
            quantity = Decimal(str(random.randint(1, 5))) if item_type == 'part' else Decimal('1')
            unit_price = Decimal(str(random.randint(50, 2000)))
            
            # Apply discounts sometimes
            discount_percentage = Decimal(str(random.randint(0, 20))) if random.random() < 0.3 else Decimal('0')
            
            # Tax rate may match invoice or be item-specific
            tax_percentage = invoice.tax_percentage if random.random() < 0.7 else Decimal(str(random.choice([0, 5, 14])))
            
            # Create item
            InvoiceItem.objects.create(
                tenant_id=invoice.tenant_id,
                invoice=invoice,
                item_type=item_type,
                description=description,
                quantity=quantity,
                unit_price=unit_price,
                discount_percentage=discount_percentage,
                tax_percentage=tax_percentage,
                is_covered_by_insurance=random.random() < 0.1,  # 10% chance
                is_covered_by_warranty=random.random() < 0.1,  # 10% chance
            )
    
    def run(self):
        """Run all data generation steps."""
        self.generate_billing_data()
        print("\nCompleted Billing data generation")

def main():
    """Main entry point."""
    generator = BillingDataGenerator()
    generator.run()

if __name__ == "__main__":
    main() 