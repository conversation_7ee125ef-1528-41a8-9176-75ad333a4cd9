"""
Service functions for the inventory app.
"""
import logging
from decimal import Decimal

logger = logging.getLogger(__name__)

def consume_inventory_item(item_id, quantity, warehouse_id, work_order_id=None, note=None):
    """
    Handle consumption of inventory items, updating stock levels and creating movements.
    
    Args:
        item_id: ID of the item to consume
        quantity: Quantity to consume
        warehouse_id: ID of the warehouse where the item is stored
        work_order_id: Optional ID of the work order consuming the item
        note: Optional note to include in the movement record
        
    Returns:
        Tuple of (success, message)
    """
    from inventory.models import Item, Movement
    from warehouse.models import Warehouse, ItemLocation
    from work_orders.utils import safe_uuid
    
    # Validate IDs
    item_id = safe_uuid(item_id)
    warehouse_id = safe_uuid(warehouse_id)
    work_order_id = safe_uuid(work_order_id) if work_order_id else None
    
    if not item_id or not warehouse_id:
        return False, "Invalid item or warehouse ID"
    
    try:
        # Get the item and warehouse
        item = Item.objects.get(id=item_id)
        warehouse = Warehouse.objects.get(id=warehouse_id)
        
        # Convert quantity to Decimal
        if not isinstance(quantity, Decimal):
            try:
                quantity = Decimal(str(quantity))
            except (ValueError, TypeError):
                return False, f"Invalid quantity: {quantity}"
        
        # Find available stock in this warehouse
        locations = warehouse.locations.all()
        item_locations = ItemLocation.objects.filter(
            item=item,
            location__in=locations,
            quantity__gt=0
        ).order_by('location__priority')
        
        total_available = sum(il.quantity for il in item_locations)
        
        # Check if we have enough stock
        if total_available < quantity:
            logger.warning(
                f"Insufficient stock for item {item.id} ({item.name}) in warehouse {warehouse.id} "
                f"({warehouse.name}). Available: {total_available}, Requested: {quantity}"
            )
            return False, f"Insufficient stock. Available: {total_available}, Requested: {quantity}"
        
        # Create inventory movement record
        tenant_id = item.tenant_id
        
        movement = Movement.objects.create(
            tenant_id=tenant_id,
            item=item,
            quantity=-quantity,  # Negative for consumption
            warehouse=warehouse,
            reference_type="work_order",
            reference_id=str(work_order_id) if work_order_id else None,
            notes=note or f"Consumed for Work Order #{work_order_id}" if work_order_id else "Manual consumption"
        )
        
        logger.info(
            f"Created inventory movement {movement.id} for item {item.id} ({item.name}) "
            f"in warehouse {warehouse.id} ({warehouse.name}). Quantity: -{quantity}"
        )
        
        # Update stock levels in locations
        remaining = quantity
        for il in item_locations:
            if remaining <= 0:
                break
                
            if il.quantity >= remaining:
                # We have enough in this location
                il.quantity -= remaining
                il.save()
                
                logger.info(
                    f"Updated location {il.location.id} ({il.location.name}) for item {item.id}. "
                    f"New quantity: {il.quantity}"
                )
                
                remaining = 0
            else:
                # Use all from this location and continue
                remaining -= il.quantity
                
                logger.info(
                    f"Emptied location {il.location.id} ({il.location.name}) for item {item.id}. "
                    f"Used {il.quantity}, remaining to allocate: {remaining}"
                )
                
                il.quantity = 0
                il.save()
        
        # Update item level quantities if needed
        try:
            # Refresh total stock count on the item
            from django.db.models import Sum
            total_stock = ItemLocation.objects.filter(item=item).aggregate(Sum('quantity'))['quantity__sum'] or 0
            
            # Update the item's stock level if tracking at item level
            if hasattr(item, 'stock_level'):
                item.stock_level = total_stock
                item.save(update_fields=['stock_level'])
                
                logger.info(f"Updated item {item.id} stock level to {total_stock}")
        except Exception as e:
            logger.error(f"Error updating item stock level: {str(e)}")
            # Don't fail the entire operation if this fails
        
        return True, f"Successfully consumed {quantity} units of {item.name}"
    
    except Item.DoesNotExist:
        logger.error(f"Item with ID {item_id} not found")
        return False, f"Item not found"
    
    except Warehouse.DoesNotExist:
        logger.error(f"Warehouse with ID {warehouse_id} not found")
        return False, f"Warehouse not found"
    
    except Exception as e:
        logger.exception(f"Error consuming inventory: {str(e)}")
        return False, f"Error consuming inventory: {str(e)}"

def get_item_availability(item_id):
    """
    Get availability information for an item across all warehouses.
    
    Args:
        item_id: ID of the item to check
        
    Returns:
        Dictionary with availability information
    """
    from inventory.models import Item
    from warehouse.models import Warehouse, ItemLocation
    from work_orders.utils import safe_uuid
    
    # Validate item_id
    item_id = safe_uuid(item_id)
    if not item_id:
        return {'success': False, 'error': 'Invalid item ID'}
    
    try:
        # Get the item
        item = Item.objects.get(id=item_id)
        
        # Get all warehouses with stock of this item
        warehouses = Warehouse.objects.filter(
            locations__itemlocation__item=item,
            locations__itemlocation__quantity__gt=0
        ).distinct()
        
        # Get availability by warehouse
        availability = []
        total_quantity = 0
        
        for warehouse in warehouses:
            locations = warehouse.locations.all()
            item_locations = ItemLocation.objects.filter(
                item=item,
                location__in=locations,
                quantity__gt=0
            )
            
            warehouse_quantity = sum(il.quantity for il in item_locations)
            if warehouse_quantity > 0:
                availability.append({
                    'warehouse_id': str(warehouse.id),
                    'warehouse_name': warehouse.name,
                    'quantity': float(warehouse_quantity),
                    'is_central': warehouse.is_central if hasattr(warehouse, 'is_central') else False,
                    'locations': [
                        {
                            'location_id': str(il.location.id),
                            'location_name': il.location.name,
                            'quantity': float(il.quantity)
                        }
                        for il in item_locations
                    ]
                })
                
                total_quantity += warehouse_quantity
        
        return {
            'success': True,
            'item': {
                'id': str(item.id),
                'name': item.name,
                'sku': item.sku,
                'unit_price': float(item.unit_price) if item.unit_price else None
            },
            'total_quantity': float(total_quantity),
            'availability': availability,
            'is_available': total_quantity > 0
        }
    
    except Item.DoesNotExist:
        logger.error(f"Item with ID {item_id} not found")
        return {'success': False, 'error': 'Item not found'}
    
    except Exception as e:
        logger.exception(f"Error getting item availability: {str(e)}")
        return {'success': False, 'error': str(e)} 