{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Movement Types" %}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>{% trans "Movement Types" %}</h1>
        <a href="{% url 'inventory:movement_type_create' %}" class="btn btn-primary">
            <i class="fas fa-plus"></i> {% trans "Add New Type" %}
        </a>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-success text-white">
            <h5 class="mb-0"><i class="fas fa-arrow-down"></i> {% trans "Inbound Types" %}</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>{% trans "Name" %}</th>
                            <th>{% trans "Code" %}</th>
                            <th>{% trans "Description" %}</th>
                            <th>{% trans "Requires Reference" %}</th>
                            <th>{% trans "Status" %}</th>
                            <th>{% trans "Actions" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for type in inbound_types %}
                        <tr>
                            <td>
                                {% if type.icon %}<i class="fas fa-{{ type.icon }}" style="color: {{ type.color }}"></i>{% endif %}
                                {{ type.name }}
                            </td>
                            <td><code>{{ type.code }}</code></td>
                            <td>{{ type.description|truncatechars:50 }}</td>
                            <td>{% if type.requires_reference %}{% trans "Yes" %}{% else %}{% trans "No" %}{% endif %}</td>
                            <td>
                                {% if type.is_active %}
                                <span class="badge bg-success">{% trans "Active" %}</span>
                                {% else %}
                                <span class="badge bg-secondary">{% trans "Inactive" %}</span>
                                {% endif %}
                            </td>
                            <td>
                                <a href="{% url 'inventory:movement_type_update' type.id %}" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-edit"></i>
                                </a>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="6" class="text-center">{% trans "No inbound types defined" %}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-danger text-white">
            <h5 class="mb-0"><i class="fas fa-arrow-up"></i> {% trans "Outbound Types" %}</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>{% trans "Name" %}</th>
                            <th>{% trans "Code" %}</th>
                            <th>{% trans "Description" %}</th>
                            <th>{% trans "Requires Reference" %}</th>
                            <th>{% trans "Status" %}</th>
                            <th>{% trans "Actions" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for type in outbound_types %}
                        <tr>
                            <td>
                                {% if type.icon %}<i class="fas fa-{{ type.icon }}" style="color: {{ type.color }}"></i>{% endif %}
                                {{ type.name }}
                            </td>
                            <td><code>{{ type.code }}</code></td>
                            <td>{{ type.description|truncatechars:50 }}</td>
                            <td>{% if type.requires_reference %}{% trans "Yes" %}{% else %}{% trans "No" %}{% endif %}</td>
                            <td>
                                {% if type.is_active %}
                                <span class="badge bg-success">{% trans "Active" %}</span>
                                {% else %}
                                <span class="badge bg-secondary">{% trans "Inactive" %}</span>
                                {% endif %}
                            </td>
                            <td>
                                <a href="{% url 'inventory:movement_type_update' type.id %}" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-edit"></i>
                                </a>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="6" class="text-center">{% trans "No outbound types defined" %}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-info text-white">
            <h5 class="mb-0"><i class="fas fa-exchange-alt"></i> {% trans "Transfer Types" %}</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>{% trans "Name" %}</th>
                            <th>{% trans "Code" %}</th>
                            <th>{% trans "Description" %}</th>
                            <th>{% trans "Requires Reference" %}</th>
                            <th>{% trans "Status" %}</th>
                            <th>{% trans "Actions" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for type in transfer_types %}
                        <tr>
                            <td>
                                {% if type.icon %}<i class="fas fa-{{ type.icon }}" style="color: {{ type.color }}"></i>{% endif %}
                                {{ type.name }}
                            </td>
                            <td><code>{{ type.code }}</code></td>
                            <td>{{ type.description|truncatechars:50 }}</td>
                            <td>{% if type.requires_reference %}{% trans "Yes" %}{% else %}{% trans "No" %}{% endif %}</td>
                            <td>
                                {% if type.is_active %}
                                <span class="badge bg-success">{% trans "Active" %}</span>
                                {% else %}
                                <span class="badge bg-secondary">{% trans "Inactive" %}</span>
                                {% endif %}
                            </td>
                            <td>
                                <a href="{% url 'inventory:movement_type_update' type.id %}" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-edit"></i>
                                </a>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="6" class="text-center">{% trans "No transfer types defined" %}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %} 