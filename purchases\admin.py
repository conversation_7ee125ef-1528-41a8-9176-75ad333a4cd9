from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from purchases.models import (
    Supplier, PurchaseOrder, PurchaseOrderItem, 
    PurchaseReceipt, PurchaseReceiptItem
)


class PurchaseOrderItemInline(admin.TabularInline):
    model = PurchaseOrderItem
    extra = 0
    raw_id_fields = ('item',)


class PurchaseReceiptItemInline(admin.TabularInline):
    model = PurchaseReceiptItem
    extra = 0
    raw_id_fields = ('purchase_order_item',)


@admin.register(Supplier)
class SupplierAdmin(admin.ModelAdmin):
    list_display = ('name', 'email', 'phone', 'is_active', 'tenant_id')
    list_filter = ('is_active',)
    search_fields = ('name', 'email', 'phone', 'address')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        (None, {
            'fields': ('tenant_id', 'name', 'email', 'phone', 'is_active')
        }),
        (_('Address'), {
            'fields': ('address',)
        }),
        (_('Metadata'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(PurchaseOrder)
class PurchaseOrderAdmin(admin.ModelAdmin):
    list_display = ('order_number', 'supplier', 'order_date', 'expected_delivery_date', 'status', 'total_amount', 'tenant_id')
    list_filter = ('status', 'order_date')
    search_fields = ('order_number', 'supplier__name', 'notes')
    readonly_fields = ('created_at', 'updated_at', 'total_amount')
    raw_id_fields = ('supplier',)
    inlines = [PurchaseOrderItemInline]
    fieldsets = (
        (None, {
            'fields': ('tenant_id', 'order_number', 'supplier', 'order_date', 'expected_delivery_date', 'status')
        }),
        (_('Financial'), {
            'fields': ('total_amount',)
        }),
        (_('Additional Information'), {
            'fields': ('notes',)
        }),
        (_('Metadata'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(PurchaseOrderItem)
class PurchaseOrderItemAdmin(admin.ModelAdmin):
    list_display = ('purchase_order', 'item', 'quantity', 'unit_price', 'total_price', 'tenant_id')
    list_filter = ('purchase_order__status',)
    search_fields = ('purchase_order__order_number', 'item__name', 'item__sku')
    readonly_fields = ('created_at', 'updated_at', 'total_price')
    raw_id_fields = ('purchase_order', 'item')
    fieldsets = (
        (None, {
            'fields': ('tenant_id', 'purchase_order', 'item', 'quantity')
        }),
        (_('Pricing'), {
            'fields': ('unit_price', 'total_price')
        }),
        (_('Metadata'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def total_price(self, obj):
        return obj.total_price
    total_price.short_description = _("Total Price")


@admin.register(PurchaseReceipt)
class PurchaseReceiptAdmin(admin.ModelAdmin):
    list_display = ('receipt_number', 'purchase_order', 'receipt_date', 'tenant_id')
    list_filter = ('receipt_date',)
    search_fields = ('receipt_number', 'purchase_order__order_number', 'notes')
    readonly_fields = ('created_at', 'updated_at')
    raw_id_fields = ('purchase_order',)
    inlines = [PurchaseReceiptItemInline]
    fieldsets = (
        (None, {
            'fields': ('tenant_id', 'receipt_number', 'purchase_order', 'receipt_date')
        }),
        (_('Additional Information'), {
            'fields': ('notes',)
        }),
        (_('Metadata'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(PurchaseReceiptItem)
class PurchaseReceiptItemAdmin(admin.ModelAdmin):
    list_display = ('purchase_receipt', 'purchase_order_item', 'quantity', 'tenant_id')
    search_fields = ('purchase_receipt__receipt_number', 'purchase_order_item__item__name')
    readonly_fields = ('created_at', 'updated_at')
    raw_id_fields = ('purchase_receipt', 'purchase_order_item')
    fieldsets = (
        (None, {
            'fields': ('tenant_id', 'purchase_receipt', 'purchase_order_item', 'quantity')
        }),
        (_('Metadata'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
