# Generated by Django 4.2.20 on 2025-05-08 15:48

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('user_roles', '0001_initial'),
    ]

    operations = [
        migrations.AlterField(
            model_name='role',
            name='role_type',
            field=models.CharField(choices=[('system_admin', 'System Administrator'), ('franchise_admin', 'Franchise Administrator'), ('company_admin', 'Company Administrator'), ('service_center_manager', 'Service Center Manager'), ('service_advisor', 'Service Advisor'), ('technician', 'Technician'), ('inventory_manager', 'Inventory Manager'), ('parts_clerk', 'Parts Clerk'), ('accountant', 'Accountant'), ('receptionist', 'Receptionist'), ('customer_service', 'Customer Service'), ('readonly', 'Read Only User'), ('warranty_admin', 'Warranty Administrator'), ('finance_manager', 'Finance Manager'), ('marketing_manager', 'Marketing Manager'), ('quality_inspector', 'Quality Control Inspector'), ('fleet_manager', 'Fleet Account Manager'), ('regional_manager', 'Regional Manager'), ('cashier', 'Cashier/Billing Specialist')], max_length=50, verbose_name='Role Type'),
        ),
    ]
