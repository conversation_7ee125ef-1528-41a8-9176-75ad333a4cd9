#!/usr/bin/env python3
"""
Data Migration Script: SQLite to PostgreSQL
==========================================

This script migrates all data from SQLite database to PostgreSQL database
using Django's ORM. It handles foreign key dependencies and maintains
data integrity.

Usage:
    python migrate_data.py [--dry-run] [--sqlite-path path/to/db.sqlite3]
"""

import os
import sys
import django
from pathlib import Path

# Add the project root to Python path
BASE_DIR = Path(__file__).resolve().parent
sys.path.insert(0, str(BASE_DIR))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

from django.db import connections, transaction
from django.apps import apps
from django.conf import settings
from django.core.management import call_command
from django.db.models import ForeignKey, ManyToManyField
import sqlite3
import argparse


class DataMigrator:
    def __init__(self, sqlite_path='db.sqlite3', dry_run=False):
        self.sqlite_path = sqlite_path
        self.dry_run = dry_run
        self.exclude_apps = [
            'contenttypes', 'auth', 'sessions', 'admin', 'messages',
            'django_otp', 'waffle', 'drf_api_logger'
        ]
        
    def migrate(self):
        """Main migration method"""
        if not os.path.exists(self.sqlite_path):
            print(f"❌ SQLite file not found: {self.sqlite_path}")
            return False
            
        print(f"🚀 Starting migration from {self.sqlite_path} to PostgreSQL")
        
        if self.dry_run:
            print("⚠️  DRY RUN MODE - No data will be actually migrated")
        
        try:
            # Setup temporary SQLite connection in Django
            self._setup_sqlite_connection()
            
            # Get all models to migrate
            models_to_migrate = self._get_models_to_migrate()
            sorted_models = self._sort_models_by_dependencies(models_to_migrate)
            
            total_records = 0
            migrated_records = 0
            
            print(f"📋 Found {len(sorted_models)} models to migrate")
            
            # Migrate each model
            for model in sorted_models:
                count = self._migrate_model(model)
                if count is not None:
                    total_records += count
                    if not self.dry_run:
                        migrated_records += count
            
            if self.dry_run:
                print(f"✅ DRY RUN COMPLETE: Would migrate {total_records} total records")
            else:
                print(f"✅ Migration completed: {migrated_records}/{total_records} records migrated")
                
            return True
            
        except Exception as e:
            print(f"❌ Migration failed: {e}")
            import traceback
            traceback.print_exc()
            return False
        finally:
            self._cleanup_sqlite_connection()
    
    def _setup_sqlite_connection(self):
        """Setup temporary SQLite database connection"""
        sqlite_config = {
            'ENGINE': 'django.db.backends.sqlite3',
            'NAME': self.sqlite_path,
        }
        settings.DATABASES['sqlite'] = sqlite_config
        connections.databases['sqlite'] = sqlite_config
    
    def _cleanup_sqlite_connection(self):
        """Clean up temporary SQLite connection"""
        if 'sqlite' in settings.DATABASES:
            del settings.DATABASES['sqlite']
        if 'sqlite' in connections.databases:
            del connections.databases['sqlite']
    
    def _get_models_to_migrate(self):
        """Get all Django models that should be migrated"""
        models = []
        for app_config in apps.get_app_configs():
            if app_config.label not in self.exclude_apps:
                for model in app_config.get_models():
                    models.append(model)
        return models
    
    def _sort_models_by_dependencies(self, models):
        """Sort models by their foreign key dependencies"""
        sorted_models = []
        remaining_models = models.copy()
        max_iterations = len(models) * 2  # Prevent infinite loops
        iteration = 0
        
        while remaining_models and iteration < max_iterations:
            iteration += 1
            models_added_this_round = []
            
            for model in remaining_models[:]:  # Create a copy to iterate over
                has_unresolved_deps = False
                
                # Check foreign key dependencies
                for field in model._meta.get_fields():
                    if isinstance(field, ForeignKey):
                        related_model = field.related_model
                        if (related_model in remaining_models and 
                            related_model != model and 
                            related_model not in models_added_this_round):
                            has_unresolved_deps = True
                            break
                
                if not has_unresolved_deps:
                    models_added_this_round.append(model)
                    remaining_models.remove(model)
            
            if models_added_this_round:
                sorted_models.extend(models_added_this_round)
            elif remaining_models:
                # Break circular dependencies by adding the first remaining model
                model = remaining_models.pop(0)
                sorted_models.append(model)
                print(f"⚠️  Breaking circular dependency for {model.__name__}")
        
        return sorted_models
    
    def _migrate_model(self, model):
        """Migrate a single model's data"""
        model_name = f"{model._meta.app_label}.{model.__name__}"
        table_name = model._meta.db_table
        
        try:
            # Get count from SQLite
            sqlite_queryset = model.objects.using('sqlite').all()
            record_count = sqlite_queryset.count()
            
            if record_count == 0:
                print(f"📋 {model_name}: 0 records - skipping")
                return 0
            
            print(f"🔄 Migrating {model_name}: {record_count} records...")
            
            if self.dry_run:
                print(f"   Would migrate {record_count} records from {table_name}")
                return record_count
            
            # Clear existing data in PostgreSQL
            model.objects.using('default').all().delete()
            
            # Migrate data in batches
            batch_size = 1000
            migrated = 0
            
            for i in range(0, record_count, batch_size):
                batch = list(sqlite_queryset[i:i + batch_size])
                
                # Create new instances for PostgreSQL
                postgres_objects = []
                for sqlite_obj in batch:
                    # Create a new instance
                    new_obj = model()
                    
                    # Copy all field values
                    for field in model._meta.fields:
                        if not field.primary_key:  # Skip primary key, let PostgreSQL auto-generate
                            value = getattr(sqlite_obj, field.name)
                            setattr(new_obj, field.name, value)
                    
                    postgres_objects.append(new_obj)
                
                # Bulk create in PostgreSQL
                if postgres_objects:
                    model.objects.using('default').bulk_create(postgres_objects, batch_size=batch_size)
                    migrated += len(postgres_objects)
            
            print(f"✅ Migrated {migrated} records from {model_name}")
            return migrated
            
        except Exception as e:
            print(f"❌ Error migrating {model_name}: {e}")
            return None


def main():
    parser = argparse.ArgumentParser(description='Migrate data from SQLite to PostgreSQL')
    parser.add_argument(
        '--sqlite-path',
        default='db.sqlite3',
        help='Path to SQLite database file (default: db.sqlite3)'
    )
    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='Show what would be migrated without actually doing it'
    )
    
    args = parser.parse_args()
    
    print("🔄 Data Migration: SQLite → PostgreSQL")
    print("=" * 50)
    
    # Check if PostgreSQL is configured
    default_db = settings.DATABASES.get('default', {})
    if default_db.get('ENGINE') != 'django.db.backends.postgresql':
        print("❌ PostgreSQL is not configured as the default database")
        print("   Please update your settings.py DATABASES configuration")
        sys.exit(1)
    
    # Run migrations on PostgreSQL first
    if not args.dry_run:
        print("🔄 Running Django migrations on PostgreSQL...")
        call_command('migrate', verbosity=0)
        print("✅ Django migrations completed")
    
    # Create migrator and run migration
    migrator = DataMigrator(
        sqlite_path=args.sqlite_path,
        dry_run=args.dry_run
    )
    
    success = migrator.migrate()
    
    if success:
        print("\n🎉 Migration process completed successfully!")
        if not args.dry_run:
            print("💡 Don't forget to:")
            print("   1. Test your application with the migrated data")
            print("   2. Update any sequences or auto-increment values if needed")
            print("   3. Backup your PostgreSQL database")
    else:
        print("\n❌ Migration process failed!")
        sys.exit(1)


if __name__ == '__main__':
    main() 