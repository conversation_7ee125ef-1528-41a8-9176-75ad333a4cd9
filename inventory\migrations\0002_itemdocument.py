# Generated by Django 4.2.20 on 2025-05-07 11:22

from django.db import migrations, models
import django.db.models.deletion
import inventory.models
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='ItemDocument',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('title', models.CharField(max_length=255, verbose_name='Title')),
                ('document_type', models.CharField(choices=[('manual', 'User Manual'), ('spec', 'Technical Specification'), ('cert', 'Certification'), ('warranty', 'Warranty Information'), ('image', 'Product Image'), ('other', 'Other')], default='other', max_length=20, verbose_name='Document Type')),
                ('file', models.FileField(upload_to=inventory.models.get_document_upload_path, verbose_name='File')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('is_public', models.BooleanField(default=False, help_text='If checked, this document will be visible to all users', verbose_name='Public')),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='documents', to='inventory.item', verbose_name='Item')),
            ],
            options={
                'verbose_name': 'Item Document',
                'verbose_name_plural': 'Item Documents',
                'ordering': ['-created_at'],
            },
        ),
    ]
