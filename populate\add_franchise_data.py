import os
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

# Now import Django models after setup
from django.db import transaction

# Import models needed for this script
from website.models import Franchise

print("Starting franchise data generation...")

@transaction.atomic
def generate_franchise_data():
    # Create main franchise
    main_franchise, created = Franchise.objects.get_or_create(
        name="أفتر سيلز للصيانة",
        defaults={
            "address": "شارع التحرير، القاهرة",
            "phone": "+20 ************",
            "email": "<EMAIL>",
            "website": "www.aftersales-eg.com",
            "active": True
        }
    )
    
    # Create additional franchises
    franchises = [
        {
            "name": "الدلتا للصيانة",
            "address": "شارع الجلاء، المنصورة",
            "phone": "+20 ************",
            "email": "<EMAIL>"
        },
        {
            "name": "الإسكندرية أوتو سيرفيس",
            "address": "طريق الكورنيش، الإسكندرية",
            "phone": "+20 ************",
            "email": "<EMAIL>"
        },
        {
            "name": "الصعيد للخدمات الفنية",
            "address": "شارع أسيوط الرئيسي، أسيوط",
            "phone": "+20 ************",
            "email": "<EMAIL>"
        }
    ]
    
    for franchise_data in franchises:
        Franchise.objects.get_or_create(
            name=franchise_data["name"],
            defaults={
                "address": franchise_data["address"],
                "phone": franchise_data["phone"],
                "email": franchise_data["email"],
                "active": True
            }
        )
        
    print(f"Created {len(franchises) + 1} franchises")

if __name__ == "__main__":
    generate_franchise_data()
    print("Franchise data generation complete!") 