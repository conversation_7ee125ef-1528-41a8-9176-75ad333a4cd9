import os
import sys
import django
import random
from datetime import datetime, timedelta
from django.db import transaction
from faker import Faker

# Add the parent directory to sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

# Import necessary models
from sales.models import (
    Customer, SalesOrder, SalesOrderItem, 
    SalesReturn, SalesReturnItem
)
from setup.models import Vehicle
from inventory.models import Item
from django.utils import timezone

# Initialize Faker
fake = Faker('ar_EG')  # Using Egyptian Arabic locale

class SalesGenerator:
    """Generate sales data for the Aftersails system."""
    
    def __init__(self):
        self.tenants = self._get_tenant_ids()
        self.inventory_items = {}
        self.vehicles = {}
        
        for tenant_id in self.tenants:
            self.inventory_items[tenant_id] = list(Item.objects.filter(tenant_id=tenant_id))
            self.vehicles[tenant_id] = list(Vehicle.objects.filter(tenant_id=tenant_id))
            
        print(f"Initialized SalesGenerator with {len(self.tenants)} tenants")
        for tenant_id in self.tenants:
            print(f"Tenant {tenant_id}: {len(self.inventory_items.get(tenant_id, []))} items, "
                  f"{len(self.vehicles.get(tenant_id, []))} vehicles")
    
    def _get_tenant_ids(self):
        """Get unique tenant IDs from existing records."""
        tenant_ids = Item.objects.values_list('tenant_id', flat=True).distinct()
        return tenant_ids
    
    def generate_customers(self, count=50):
        """Generate customer records."""
        print(f"Generating {count} customers...")
        
        created_customers = []
        
        for tenant_id in self.tenants:
            # Generate customers for this tenant
            customers_per_tenant = count // len(self.tenants) + 1
            
            for i in range(customers_per_tenant):
                # Create regular individual customers
                if random.random() > 0.2:  # 80% individual customers
                    customer = Customer.objects.create(
                        tenant_id=tenant_id,
                        name=fake.name(),
                        email=fake.email(),
                        phone=fake.phone_number(),
                        address=fake.address(),
                        is_active=random.random() > 0.05  # 95% active
                    )
                else:
                    # Create business customers
                    business_types = ["شركة", "مؤسسة", "معرض", "مصنع", "ورشة", "مركز"]
                    business_type = random.choice(business_types)
                    business_name = fake.company()
                    
                    customer = Customer.objects.create(
                        tenant_id=tenant_id,
                        name=f"{business_type} {business_name}",
                        email=fake.company_email(),
                        phone=fake.phone_number(),
                        address=fake.address(),
                        is_active=random.random() > 0.05  # 95% active
                    )
                
                created_customers.append(customer)
        
        print(f"Created {len(created_customers)} customers")
        return created_customers
    
    def generate_sales_orders(self, count=100):
        """Generate sales orders with items."""
        print(f"Generating {count} sales orders...")
        
        created_orders = []
        
        # Get all customers by tenant
        customers_by_tenant = {}
        for tenant_id in self.tenants:
            customers_by_tenant[tenant_id] = list(Customer.objects.filter(
                tenant_id=tenant_id,
                is_active=True
            ))
        
        # Status distribution for more realistic data
        status_weights = {
            'draft': 5,
            'confirmed': 15,
            'shipped': 25,
            'delivered': 50,
            'cancelled': 3,
            'returned': 2
        }
        
        for tenant_id in self.tenants:
            # Skip if no customers or items
            if (not customers_by_tenant.get(tenant_id) or 
                not self.inventory_items.get(tenant_id)):
                print(f"Missing customers or items for tenant {tenant_id}, skipping sales orders")
                continue
            
            # Generate sales orders for this tenant
            orders_per_tenant = count // len(self.tenants) + 1
            
            for i in range(orders_per_tenant):
                # Generate dates - most orders in last 60 days
                days_ago = int(abs(random.gauss(15, 15)))  # Normal distribution centered at 15 days ago
                days_ago = min(days_ago, 60)  # Cap at 60 days ago
                
                order_date = timezone.now().date() - timedelta(days=days_ago)
                
                # Select a customer
                customer = random.choice(customers_by_tenant[tenant_id])
                
                # Generate order number
                order_number = f"SO-{tenant_id}-{order_date.strftime('%y%m%d')}-{i+1:04d}"
                
                # Select status based on weights
                status = random.choices(
                    list(status_weights.keys()),
                    weights=list(status_weights.values()),
                    k=1
                )[0]
                
                # Create the sales order
                sales_order = SalesOrder.objects.create(
                    tenant_id=tenant_id,
                    order_number=order_number,
                    customer=customer,
                    order_date=order_date,
                    status=status,
                    shipping_address=customer.address,
                    notes=fake.paragraph() if random.random() > 0.7 else "",
                    total_amount=0  # Will be updated after adding items
                )
                created_orders.append(sales_order)
                
                # Add items to the sales order
                self._add_items_to_sales_order(tenant_id, sales_order)
                
                # For returned orders, create a return
                if status == 'returned':
                    self._create_sales_return(tenant_id, sales_order)
        
        print(f"Created {len(created_orders)} sales orders")
        return created_orders
    
    def _add_items_to_sales_order(self, tenant_id, sales_order):
        """Add items to a sales order."""
        # Determine how many different items to add
        num_items = random.randint(1, 5)
        
        # Get available items for this tenant
        available_items = self.inventory_items.get(tenant_id, [])
        
        if not available_items:
            return
        
        # Select a random subset of items
        selected_items = random.sample(
            available_items,
            min(num_items, len(available_items))
        )
        
        for item in selected_items:
            # Generate random quantity and price
            quantity = random.randint(1, 10)
            unit_price = round(random.uniform(20, 2000), 2)
            
            # Some orders might have discounts
            discount = 0
            if random.random() > 0.7:  # 30% chance of discount
                discount = round(unit_price * quantity * random.uniform(0.05, 0.2), 2)  # 5-20% discount
            
            # Create the sales order item
            SalesOrderItem.objects.create(
                tenant_id=tenant_id,
                sales_order=sales_order,
                item=item,
                quantity=quantity,
                unit_price=unit_price,
                discount=discount
            )
        
        # Update the total amount
        sales_order.update_total_amount()
    
    def _create_sales_return(self, tenant_id, sales_order):
        """Create a sales return for an order."""
        # Generate a return number
        return_number = f"RET-{sales_order.order_number[3:]}"
        
        # Generate return date (after order date, before or on current date)
        min_days = 1
        max_days = (timezone.now().date() - sales_order.order_date).days
        days_after_order = random.randint(min_days, max(min_days, max_days))
        return_date = sales_order.order_date + timedelta(days=days_after_order)
        
        # Generate reason for return
        return_reasons = [
            "المنتج معيب",
            "المنتج غير مطابق للمواصفات",
            "خطأ في الطلب",
            "تأخر التسليم",
            "المنتج تالف",
            "القطعة غير متوافقة مع السيارة",
            "العميل غير راضٍ عن المنتج",
            "تم العثور على بديل أفضل",
            "المنتج لا يعمل بشكل صحيح",
            "سعر أفضل في مكان آخر"
        ]
        
        # Create the return
        sales_return = SalesReturn.objects.create(
            tenant_id=tenant_id,
            return_number=return_number,
            sales_order=sales_order,
            return_date=return_date,
            reason=random.choice(return_reasons),
            notes=fake.paragraph() if random.random() > 0.5 else ""
        )
        
        # Add items to the return (usually not all items are returned)
        order_items = list(sales_order.items.all())
        
        # Determine how many items to return
        items_to_return = random.sample(
            order_items,
            random.randint(1, max(1, len(order_items)))
        )
        
        for order_item in items_to_return:
            # Usually return part of the order
            if random.random() > 0.3:  # 70% return partial quantity
                return_qty = round(order_item.quantity * random.uniform(0.3, 0.8))
                if return_qty < 1:
                    return_qty = 1
            else:
                # Return full quantity
                return_qty = order_item.quantity
                
            # Create return item
            SalesReturnItem.objects.create(
                tenant_id=tenant_id,
                sales_return=sales_return,
                sales_order_item=order_item,
                quantity=return_qty
            )
        
        return sales_return
    
    def generate_additional_returns(self, count=20):
        """Generate some additional returns for delivered orders."""
        print(f"Generating {count} additional sales returns...")
        
        created_returns = []
        
        # Find delivered orders that don't have returns yet
        for tenant_id in self.tenants:
            delivered_orders = SalesOrder.objects.filter(
                tenant_id=tenant_id,
                status='delivered'
            ).exclude(
                returns__isnull=False
            )
            
            if not delivered_orders:
                continue
                
            # Generate returns for this tenant
            returns_per_tenant = count // len(self.tenants) + 1
            
            # Select random orders to return
            orders_to_return = random.sample(
                list(delivered_orders),
                min(returns_per_tenant, delivered_orders.count())
            )
            
            for order in orders_to_return:
                # Create a return
                sales_return = self._create_sales_return(tenant_id, order)
                created_returns.append(sales_return)
                
                # Update the order status to 'returned'
                order.status = 'returned'
                order.save(update_fields=['status'])
        
        print(f"Created {len(created_returns)} additional sales returns")
        return created_returns
    
    @transaction.atomic
    def run(self):
        """Run the sales data generator."""
        print("Starting Sales Data Generator...")
        
        # Check if we have inventory items
        if not Item.objects.exists():
            print("❌ No inventory items found. Please run add_inventory_data.py first.")
            return
        
        # Generate data in the correct order
        self.generate_customers(50)
        self.generate_sales_orders(100)
        self.generate_additional_returns(20)
        
        print("✅ Sales data generation completed!")


def main():
    """Execute the sales data generator."""
    generator = SalesGenerator()
    generator.run()


if __name__ == "__main__":
    main() 