"""
Template tags for responsive table components.
"""
from django import template
from django.utils.translation import gettext_lazy as _

register = template.Library()

@register.inclusion_tag('components/responsive_table.html')
def render_responsive_table(headers, rows, id=None, classes=None, responsive_breakpoint=None, 
                           actions=None, empty_message=None):
    """
    Render a responsive table component.
    
    Args:
        headers (list): List of table headers
        rows (list): List of rows, each containing a list of cells
        id (str, optional): Table ID for JavaScript targeting
        classes (str, optional): Additional classes for the table
        responsive_breakpoint (str, optional): Breakpoint for responsive behavior
        actions (dict, optional): Action buttons configuration
        empty_message (str, optional): Message to display when there are no rows
        
    Returns:
        dict: Context for the template
    """
    return {
        'headers': headers,
        'rows': rows,
        'id': id,
        'classes': classes,
        'responsive_breakpoint': responsive_breakpoint,
        'actions': actions,
        'empty_message': empty_message
    }

@register.filter
def as_status_cell(value, color=None):
    """
    Format a value as a status cell.
    
    Args:
        value: The value to format
        color (str, optional): Status color (e.g., 'green', 'red', 'yellow')
        
    Returns:
        dict: Formatted cell data
    """
    return {
        'type': 'status',
        'value': value,
        'status_color': color
    }

@register.filter
def as_badge(value, color=None):
    """
    Format a value as a badge.
    
    Args:
        value: The value to format
        color (str, optional): Badge color (e.g., 'green', 'red', 'yellow')
        
    Returns:
        dict: Formatted cell data
    """
    return {
        'type': 'badge',
        'value': value,
        'badge_color': color
    }

@register.filter
def as_datetime(value):
    """
    Format a value as a datetime.
    
    Args:
        value: The datetime value to format
        
    Returns:
        dict: Formatted cell data
    """
    return {
        'type': 'datetime',
        'value': value
    }

@register.filter
def as_boolean(value):
    """
    Format a value as a boolean.
    
    Args:
        value: The boolean value to format
        
    Returns:
        dict: Formatted cell data
    """
    return {
        'type': 'boolean',
        'value': value
    }

@register.filter
def highlight(value, color=None):
    """
    Highlight a cell value.
    
    Args:
        value: The value to highlight
        color (str, optional): Highlight color (e.g., 'green', 'red', 'yellow')
        
    Returns:
        dict: Formatted cell data
    """
    if isinstance(value, dict):
        value['highlight'] = True
        value['highlight_color'] = color
        return value
    return {
        'value': value,
        'highlight': True,
        'highlight_color': color
    } 