"""
Template tags for form field styling.
"""
from django import template
from django.utils.translation import gettext_lazy as _

register = template.Library()

@register.inclusion_tag('components/form_field.html')
def render_field(field, label_class=None, input_class=None, help_text_class=None, 
                error_class=None, wrapper_class=None, horizontal=False, required=None,
                label=None, id=None, add_label=True, add_error=True, add_help=True, icon=None):
    """
    Render a form field using the Flowbite styled component.
    
    Args:
        field: Django form field
        label_class: Additional classes for the label
        input_class: Additional classes for the input
        help_text_class: Additional classes for the help text
        error_class: Additional classes for error messages
        wrapper_class: Additional classes for the field wrapper
        horizontal: Bo<PERSON>an for horizontal layout (label next to field)
        required: Bo<PERSON>an to override field.field.required
        label: Override for field.label
        id: Override for field.id_for_label
        add_label: Boolean, set to False to hide label
        add_error: Boolean, set to False to hide error
        add_help: <PERSON><PERSON>an, set to False to hide help text
        icon: Font Awesome icon class to display in the field
        
    Returns:
        dict: Context for the template
    """
    return {
        'field': field,
        'label_class': label_class,
        'input_class': input_class,
        'help_text_class': help_text_class,
        'error_class': error_class,
        'wrapper_class': wrapper_class,
        'horizontal': horizontal,
        'required': required,
        'label': label,
        'id': id,
        'add_label': add_label,
        'add_error': add_error,
        'add_help': add_help,
        'icon': icon
    }

@register.filter
def fieldtype(field):
    """
    Return the field type based on the widget.
    
    Args:
        field: Django form field
    
    Returns:
        str: Field type
    """
    widget_name = field.field.widget.__class__.__name__.lower()
    
    if widget_name == 'textarea':
        return 'textarea'
    elif widget_name in ('select', 'selectmultiple'):
        return widget_name
    elif widget_name == 'checkboxinput':
        return 'checkbox'
    elif widget_name == 'radioselect':
        return 'radio'
    elif widget_name in ('dateinput', 'datetimeinput'):
        return 'date'
    elif widget_name == 'fileinput':
        return 'file'
    else:
        return 'input'

@register.filter
def add_class(field, css_class):
    """
    Add a CSS class to a form field.
    
    Args:
        field: Django form field
        css_class: CSS class(es) to add
    
    Returns:
        field: Modified field
    """
    if hasattr(field, 'field') and hasattr(field.field, 'widget') and hasattr(field.field.widget, 'attrs'):
        existing_class = field.field.widget.attrs.get('class', '')
        if existing_class:
            field.field.widget.attrs['class'] = f"{existing_class} {css_class}"
        else:
            field.field.widget.attrs['class'] = css_class
    return field

@register.filter
def add_attr(field, attr_value):
    """
    Add an attribute to a form field.
    
    Args:
        field: Django form field
        attr_value: Attribute and value in format 'attr:value'
    
    Returns:
        field: Modified field
    """
    if ':' not in attr_value:
        return field
    
    attr, value = attr_value.split(':', 1)
    if hasattr(field, 'field') and hasattr(field.field, 'widget') and hasattr(field.field.widget, 'attrs'):
        field.field.widget.attrs[attr] = value
    return field

@register.inclusion_tag('components/form_field.html')
def render_non_field_errors(form, error_class=None):
    """
    Render non-field errors for a form.
    
    Args:
        form: Django form
        error_class: Additional classes for error messages
        
    Returns:
        dict: Context for the template
    """
    return {
        'non_field_errors': form.non_field_errors(),
        'error_class': error_class
    } 