import os
import sys
import django
import random
from datetime import datetime, timed<PERSON>ta
from django.db import transaction
from faker import Faker
import uuid

# Add the parent directory to sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

# Import necessary models
from setup.models import ServiceCenter, Franchise, Company, Customer, Vehicle, ServiceCenterType
from django.utils import timezone
from django.contrib.auth.models import User

# Add owner_name property to Vehicle model to fix the signal issue
def add_owner_name_property():
    """Add owner_name property to Vehicle model to fix signal issue"""
    # Define a property that returns the owner's name
    def owner_name(self):
        if self.owner:
            return str(self.owner)
        return "Unknown"
    
    # Add the property to the Vehicle class
    Vehicle.owner_name = property(owner_name)
    print("Added owner_name property to Vehicle model")

# Run the property patch
add_owner_name_property()

# Initialize Faker
fake = Faker('ar_EG')  # Using Egyptian Arabic locale for Egyptian market specifics

class SetupDataGenerator:
    """Generate setup data for the Aftersails system."""
    
    def __init__(self):
        # Create a new tenant_id
        self.tenant_id = str(uuid.uuid4())
        print(f"Using tenant ID: {self.tenant_id}")
        
        # Check if we have a user, create one if not
        if not User.objects.filter(is_superuser=True).exists():
            User.objects.create_superuser('admin', '<EMAIL>', 'admin')
            print("Created superuser 'admin' with password 'admin'")
    
    @transaction.atomic
    def generate_setup_data(self):
        """Generate core setup data with Egyptian market specifics."""
        print("Generating setup data...")
        
        # Create service center type
        center_type = self._create_service_center_type()
        
        # Create a franchise
        franchise = self._create_franchise()
        
        # Create companies under the franchise
        companies = self._create_companies(franchise)
        
        # Create service centers
        service_centers = self._create_service_centers(companies, center_type)
        
        # Create customers
        self._create_customers(service_centers)
        
        # Create vehicles
        self._create_vehicles()
    
    def _create_service_center_type(self):
        """Create service center type records."""
        center_type, created = ServiceCenterType.objects.get_or_create(
            name="Standard",
            defaults={
                'description': "Standard service center type",
                'max_capacity': 20,
                'is_active': True
            }
        )
        
        if created:
            print(f"Created Service Center Type: {center_type.name}")
        else:
            print(f"Using existing Service Center Type: {center_type.name}")
            
        return center_type
    
    def _create_franchise(self):
        """Create franchise records."""
        franchise_code = f"CairoAuto-{random.randint(1000, 9999)}"
        franchise_data = {
            'name': 'مجموعة القاهرة للسيارات',
            'code': franchise_code,
            'email': '<EMAIL>',
            'phone': fake.phone_number(),
            'address': fake.address(),
            'website': 'www.cairoauto.com',
            'is_active': True
        }
        
        franchise = Franchise.objects.create(**franchise_data)
        print(f"Created Franchise: {franchise.name}")
        
        return franchise
    
    def _create_companies(self, franchise):
        """Create companies under the franchise."""
        companies_data = [
            {
                'name': 'القاهرة لخدمات السيارات',
                'code': f'CS-CAI-{random.randint(1000, 9999)}',
                'city': 'القاهرة',
            },
            {
                'name': 'الإسكندرية لصيانة السيارات',
                'code': f'CS-ALX-{random.randint(1000, 9999)}',
                'city': 'الإسكندرية',
            },
            {
                'name': 'المنصورة للخدمات الفنية',
                'code': f'CS-MAN-{random.randint(1000, 9999)}',
                'city': 'المنصورة',
            }
        ]
        
        companies = []
        for data in companies_data:
            company = Company.objects.create(
                name=data['name'],
                code=data['code'],
                franchise=franchise,
                address=fake.address(),
                city=data['city'],
                phone=fake.phone_number(),
                email=f"info@{data['code'].lower()}.com",
                is_active=True
            )
            
            companies.append(company)
            print(f"Created Company: {company.name}")
        
        return companies
    
    def _create_service_centers(self, companies, center_type):
        """Create service centers for each company."""
        service_centers = []
        
        # Egyptian cities with different districts
        locations = {
            'القاهرة': ['مدينة نصر', 'المعادي', 'مصر الجديدة', 'وسط البلد', 'الزمالك', 'المهندسين'],
            'الإسكندرية': ['سموحة', 'سيدي جابر', 'ميامي', 'العجمي', 'المنتزه'],
            'المنصورة': ['الجامعة', 'ميت حدر', 'جديلة', 'ميت طلخا'],
            'الجيزة': ['الدقي', 'العجوزة', 'الهرم', 'فيصل', '6 أكتوبر'],
            'طنطا': ['وسط البلد', 'سبرباي'],
            'أسيوط': ['وسط المدينة', 'الحمراء'],
            'المنيا': ['المدينة الجديدة', 'أبو قرقاص']
        }
        
        # Create 2-3 service centers per company
        for company in companies:
            # Choose cities appropriate for this company
            if 'القاهرة' in company.name:
                city = 'القاهرة'
            elif 'الإسكندرية' in company.name:
                city = 'الإسكندرية'
            elif 'المنصورة' in company.name:
                city = 'المنصورة'
            else:
                city = random.choice(list(locations.keys()))
            
            # Choose districts for this city
            districts = locations.get(city, ['وسط المدينة'])
            
            # Create 2-3 service centers in different districts
            for i in range(random.randint(2, 3)):
                if districts:
                    district = random.choice(districts)
                    districts.remove(district)  # Don't reuse the same district
                else:
                    district = 'وسط المدينة'
                
                center_name = f"مركز {district} لخدمة السيارات"
                center_code = f"SC-{company.code}-{i+1}"
                
                service_center = ServiceCenter.objects.create(
                    tenant_id=self.tenant_id,
                    name=center_name,
                    code=center_code,
                    company=company,
                    center_type=center_type,
                    size=random.choice(['small', 'medium', 'large']),
                    address=f"{district}، {city}، مصر",
                    city=city,
                    country="مصر",
                    capacity=random.randint(5, 15),
                    is_active=True
                )
                
                service_centers.append(service_center)
                print(f"Created Service Center: {service_center.name}")
        
        return service_centers
    
    def _create_customers(self, service_centers, count=50):
        """Create customer records with Egyptian names and information."""
        # Egyptian first names
        egyptian_male_first_names = [
            'محمد', 'أحمد', 'مصطفى', 'عمر', 'خالد', 'حسين', 'محمود', 'علي', 'إبراهيم', 'يوسف',
            'عبد الله', 'عبد الرحمن', 'عمرو', 'كريم', 'هشام', 'أسامة', 'طارق', 'حسن', 'سيد', 'شريف'
        ]
        
        egyptian_female_first_names = [
            'نور', 'فاطمة', 'مريم', 'سارة', 'رنا', 'هدى', 'دينا', 'أمل', 'رانيا', 'ياسمين',
            'هبة', 'منى', 'سلمى', 'نيرة', 'سماح', 'إيمان', 'عايدة', 'ليلى', 'شيماء', 'ندى'
        ]
        
        # Egyptian last names
        egyptian_last_names = [
            'المصري', 'أحمد', 'محمد', 'علي', 'إبراهيم', 'حسن', 'محمود', 'السيد', 'عبد العزيز', 'رمضان',
            'البحيري', 'الشربيني', 'مصطفى', 'عبد الله', 'عثمان', 'حسين', 'فهمي', 'صلاح', 'صبري', 'الحسيني',
            'عمار', 'سالم', 'راضي', 'زكي', 'العدوي', 'السعدني', 'يوسف', 'البنا', 'عاشور', 'جاد'
        ]
        
        # Egyptian phone prefixes
        phone_prefixes = ['010', '011', '012', '015']
        
        # Egyptian email domains
        email_domains = ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 'egmail.com']
        
        # Create customers
        customers_created = 0
        for i in range(count):
            # Randomly choose gender and name
            gender = random.choice(['male', 'female'])
            if gender == 'male':
                first_name = random.choice(egyptian_male_first_names)
            else:
                first_name = random.choice(egyptian_female_first_names)
                
            last_name = random.choice(egyptian_last_names)
            
            # Generate email and phone
            email = f"{first_name.lower()}.{last_name.lower()}@{random.choice(email_domains)}"
            phone = f"{random.choice(phone_prefixes)}{random.randint(10000000, 99999999)}"
            
            # Assign to a random service center
            service_center = random.choice(service_centers)
            
            try:
                # Create customer
                customer = Customer.objects.create(
                    tenant_id=self.tenant_id,
                    first_name=first_name,
                    last_name=last_name,
                    email=email,
                    phone=phone,
                    address=fake.address(),
                    gender=gender,
                    service_center=service_center,
                    is_active=True,
                    city=service_center.city,
                    country="مصر"
                )
                customers_created += 1
            except Exception as e:
                print(f"Error creating customer: {e}")
                continue
        
        print(f"Created {customers_created} Customers")
    
    def _create_vehicles(self):
        """Create vehicle records for customers with Egyptian specifics."""
        # Get customers
        customers = list(Customer.objects.filter(tenant_id=self.tenant_id))
        if not customers:
            print("No customers found. Cannot create vehicles.")
            return
        
        # Egyptian car makes and models
        car_data = {
            'تويوتا': ['كورولا', 'يارس', 'كامري', 'لاند كروزر', 'فورتشنر', 'راف 4', 'هايلكس'],
            'هيونداي': ['النترا', 'توسان', 'اكسنت', 'فيرنا', 'كريتا', 'i10', 'i30'],
            'نيسان': ['صني', 'سنترا', 'قشقاي', 'باثفايندر', 'جوك'],
            'شيفروليه': ['افيو', 'اوبترا', 'كروز', 'لانوس', 'كابتيفا'],
            'كيا': ['سبورتاج', 'سيراتو', 'بيكانتو', 'سول', 'ريو'],
            'مرسيدس': ['C180', 'E200', 'GLC', 'A-Class', 'S-Class'],
            'بي إم دبليو': ['الفئة 3', 'الفئة 5', 'X5', 'X3', 'X1'],
            'فولكس فاجن': ['باسات', 'جيتا', 'تيجوان', 'بولو', 'جولف'],
            'رينو': ['لوجان', 'ميجان', 'سانديرو', 'داستر', 'كادجار'],
            'بيجو': ['301', '3008', '508', '208', '2008'],
            'ميتسوبيشي': ['لانسر', 'باجيرو', 'أوتلاندر', 'إكليبس'],
            'سوزوكي': ['سويفت', 'ألتو', 'دزاير', 'فيتارا'],
            'مازدا': ['3', '6', 'CX-5', 'CX-3'],
            'سكودا': ['اوكتافيا', 'سوبيرب', 'فابيا', 'كودياك'],
            'بريليانس': ['V5', 'H330'],
            'شيري': ['تيجو', 'اريزو', 'انفي'],
            'ام جي': ['ZS', 'HS', 'RX5'],
            'جيلي': ['امجراند', 'باندينو', 'X7'],
            'بي واي دي': ['F3', 'L3', 'S6']
        }
        
        # License plate letters (Egyptian format)
        license_letters = ['أ', 'ب', 'ج', 'د', 'هـ', 'و', 'ز', 'ح', 'ط', 'ي', 'ك', 'ل', 'م', 'ن', 'س', 'ع', 'ف', 'ص', 'ق', 'ر']
        
        # Create vehicles for customers
        vehicles_created = 0
        
        # Each customer will have 1-3 vehicles
        for customer in customers:
            num_vehicles = random.randint(1, 3)
            
            for i in range(num_vehicles):
                # Select random make and model
                make = random.choice(list(car_data.keys()))
                model = random.choice(car_data[make])
                
                # Generate random year
                year = random.randint(2010, 2023)
                
                # Generate Egyptian license plate (123 أ ب ج)
                license_plate = f"{random.randint(100, 999)} {random.choice(license_letters)} {random.choice(license_letters)} {random.choice(license_letters)}"
                
                # Generate VIN
                vin = f"EGY{year}{make[:2].upper()}{random.randint(100000, 999999)}"
                
                # Use customer's service center for the vehicle
                service_center = customer.service_center
                
                try:
                    # Create vehicle
                    vehicle = Vehicle.objects.create(
                        tenant_id=self.tenant_id,
                        owner=customer,
                        make=make,
                        model=model,
                        year=year,
                        license_plate=license_plate,
                        vin=vin,
                        color=random.choice(['أبيض', 'أسود', 'فضي', 'رمادي', 'أحمر', 'أزرق', 'بني']),
                        service_center=service_center
                    )
                    vehicles_created += 1
                except Exception as e:
                    print(f"Error creating vehicle: {e}")
        
        print(f"Created {vehicles_created} Vehicles")
    
    def run(self):
        """Run all data generation steps."""
        self.generate_setup_data()
        print("\nCompleted setup data generation")

def main():
    """Main entry point."""
    generator = SetupDataGenerator()
    generator.run()

if __name__ == "__main__":
    main() 