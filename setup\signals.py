import logging
from django.db.models.signals import post_save, pre_save
from django.dispatch import receiver
from .models import ServiceCenter, Company, Franchise, Vehicle, ServiceHistory

logger = logging.getLogger(__name__)

@receiver(post_save, sender=Franchise)
def franchise_saved(sender, instance, created, **kwargs):
    """Log when a franchise is created or updated"""
    if created:
        logger.info(f"New franchise created: {instance.name} ({instance.code})")
    else:
        logger.info(f"Franchise updated: {instance.name} ({instance.code})")

@receiver(post_save, sender=Company)
def company_saved(sender, instance, created, **kwargs):
    """Log when a company is created or updated"""
    if created:
        logger.info(f"New company created: {instance.name} ({instance.code}) under franchise {instance.franchise.name}")
    else:
        logger.info(f"Company updated: {instance.name} ({instance.code})")

@receiver(post_save, sender=ServiceCenter)
def service_center_saved(sender, instance, created, **kwargs):
    """Log when a service center is created or updated"""
    if created:
        logger.info(f"New service center created: {instance.name} ({instance.code}) under company {instance.company.name}")
    else:
        logger.info(f"Service center updated: {instance.name} ({instance.code})")

@receiver(post_save, sender=Vehicle)
def vehicle_saved(sender, instance, created, **kwargs):
    """Log when a vehicle is created or updated"""
    if created:
        logger.info(f"New vehicle registered: {instance} owned by {instance.owner_name}")
    else:
        logger.info(f"Vehicle updated: {instance}")

@receiver(post_save, sender=ServiceHistory)
def service_history_saved(sender, instance, created, **kwargs):
    """Log when a service history record is created or updated"""
    if created:
        logger.info(f"New service record created for {instance.vehicle} at {instance.service_center.name} on {instance.service_date}")
    else:
        logger.info(f"Service record updated for {instance.vehicle} at {instance.service_center.name} on {instance.service_date}") 