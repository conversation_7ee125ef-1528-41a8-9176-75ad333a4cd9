"""
AJAX view functions for the work_orders app.

These functions handle AJAX requests from the frontend and return JSON responses.
They follow a consistent pattern for error handling, tenant resolution,
and parameter validation.
"""
import json
import logging
from django.http import JsonResponse
from django.shortcuts import get_object_or_404
from django.db.models import Q
from django.utils.translation import gettext_lazy as _

from core.utils import get_tenant_id
from work_orders.utils import safe_uuid, validate_uuid_list, get_object_or_none
from work_orders.decorators import api_error_handler, tenant_required, role_permission_required
from user_roles.utils import apply_role_based_filters

from .models import WorkOrder, WorkOrderOperation, WorkOrderMaterial, MaintenanceSchedule
from inventory.models import Item
from setup.models import Vehicle, Customer, ServiceCenter, VehicleMake, VehicleModel

logger = logging.getLogger(__name__)


@api_error_handler
@tenant_required
def ajax_search_customers(request):
    """
    Search for customers based on provided search term.
    
    Query Parameters:
        term (str): Search term
        search_by (str): Field to search by (name, phone, id_number, all)
    
    Returns:
        JsonResponse: List of matching customers
    """
    term = request.GET.get('term', '')
    search_by = request.GET.get('search_by', 'all')
    
    if not term or len(term) < 2:  # Require at least 2 characters for search
        return JsonResponse({'customers': []})

    # Build query filter based on search type
    query_filter = Q()
    if search_by == 'name':
        query_filter = (
            Q(first_name__icontains=term) |
            Q(second_name__icontains=term) |
            Q(third_name__icontains=term) |
            Q(last_name__icontains=term)
        )
    elif search_by == 'phone':
        query_filter = Q(phone__icontains=term)
    elif search_by == 'id_number':
        query_filter = Q(id_number__icontains=term)
    else:  # Default to 'all'
        query_filter = (
            Q(first_name__icontains=term) |
            Q(second_name__icontains=term) |
            Q(third_name__icontains=term) |
            Q(last_name__icontains=term) |
            Q(phone__icontains=term) |
            Q(id_number__icontains=term)
        )

    # Get tenant ID from request
    tenant_id = request.tenant_id
    
    # Filter customers by tenant and search terms
    customers = Customer.objects.filter(query_filter, tenant_id=tenant_id)[:10]
    
    # Format results
    customers_data = []
    for customer in customers:
        # Directly concatenate name fields
        full_name = f"{customer.first_name or ''} {customer.second_name or ''} {customer.third_name or ''} {customer.last_name or ''}".strip()
        
        customers_data.append({
            'id': str(customer.id),
            'full_name': full_name,
            'phone_number': customer.phone or '',
            'national_id_number': customer.id_number or '',
            'id_type': customer.id_type or '',
            'gender': customer.gender or '',
        })
    
    return JsonResponse({'customers': customers_data})


@api_error_handler
@tenant_required
def ajax_search_vehicles(request):
    """
    Search for vehicles based on provided search term.
    
    Query Parameters:
        term (str): Search term
        customer_id (uuid): Filter by customer ID
        include_standard (bool): Include standard make/model info
        fetch_all (bool): Fetch all vehicles for a customer
    
    Returns:
        JsonResponse: List of matching vehicles
    """
    search_term = request.GET.get('term', '')
    customer_id = safe_uuid(request.GET.get('customer_id'))
    include_standard = request.GET.get('include_standard', 'true') == 'true'
    fetch_all = request.GET.get('fetch_all', 'false') == 'true'
    tenant_id = request.tenant_id
    
    # If fetch_all is false, we need a search term or customer_id
    if not search_term and not customer_id and not fetch_all:
        return JsonResponse({'results': []})

    # Start with base queryset
    vehicles = Vehicle.objects.filter(tenant_id=tenant_id)
    
    # Optimize query with select_related
    if include_standard:
        vehicles = vehicles.select_related('owner', 'standard_make', 'standard_model')
    else:
        vehicles = vehicles.select_related('owner')

    # Filter by customer if customer_id is provided
    if customer_id:
        vehicles = vehicles.filter(owner_id=customer_id)

    # Apply search term filter (if provided and not fetching all)
    if search_term and not (fetch_all and customer_id):
        vehicles = vehicles.filter(
            Q(license_plate__icontains=search_term) |
            Q(vin__icontains=search_term) |
            Q(make__icontains=search_term) |
            Q(model__icontains=search_term) |
            Q(standard_make__name__icontains=search_term) |
            Q(standard_model__name__icontains=search_term)
        )

    # Get vehicles with active work orders for flagging
    active_statuses = ['draft', 'planned', 'in_progress', 'on_hold']
    active_vehicle_ids = set(WorkOrder.objects.filter(
        status__in=active_statuses,
        vehicle__isnull=False,
        tenant_id=tenant_id
    ).values_list('vehicle_id', flat=True))

    # Limit results
    limit = 50 if fetch_all else 15
    vehicles = vehicles[:limit]

    # Format results
    results = []
    for vehicle in vehicles:
        # Check if this vehicle has active work orders
        has_active_work_order = vehicle.id in active_vehicle_ids
        
        # Get standard make and model info if available
        standard_make_id = str(vehicle.standard_make.id) if vehicle.standard_make else None
        standard_make_name = vehicle.standard_make.name if vehicle.standard_make else None
        standard_model_id = str(vehicle.standard_model.id) if vehicle.standard_model else None
        standard_model_name = vehicle.standard_model.name if vehicle.standard_model else None
        
        vehicle_data = {
            'id': str(vehicle.id),
            'text': f"{vehicle.make} {vehicle.model} ({vehicle.license_plate or vehicle.vin or 'N/A'})",
            'make': vehicle.make,
            'model': vehicle.model,
            'year': vehicle.year,
            'vin': vehicle.vin,
            'license_plate': vehicle.license_plate,
            'color': vehicle.color,
            'owner_id': str(vehicle.owner.id) if vehicle.owner else None,
            'owner_name': vehicle.owner.get_full_name() if vehicle.owner else '',
            'has_active_work_order': has_active_work_order
        }
        
        # Include standard make/model info if requested
        if include_standard:
            vehicle_data.update({
                'standard_make_id': standard_make_id,
                'standard_make_name': standard_make_name,
                'standard_model_id': standard_model_id,
                'standard_model_name': standard_model_name
            })
        
        results.append(vehicle_data)

    return JsonResponse({'results': results})


@api_error_handler
@tenant_required
def ajax_get_vehicle_makes(request):
    """
    Get list of all vehicle makes for dropdown.
    
    Returns:
        JsonResponse: List of vehicle makes
    """
    tenant_id = request.tenant_id
    
    makes = VehicleMake.objects.filter(is_active=True, tenant_id=tenant_id).order_by('name')
    
    results = [{'id': str(make.id), 'name': make.name} for make in makes]
    return JsonResponse({'results': results})


@api_error_handler
@tenant_required
def ajax_get_vehicle_models(request):
    """
    Get vehicle models for a specific make.
    
    Query Parameters:
        make_id (uuid): ID of the vehicle make
    
    Returns:
        JsonResponse: List of vehicle models for the make
    """
    make_id = safe_uuid(request.GET.get('make_id'))
    tenant_id = request.tenant_id
    
    if not make_id:
        return JsonResponse({'results': []})
    
    models = VehicleModel.objects.filter(
        make_id=make_id, 
        is_active=True, 
        tenant_id=tenant_id
    ).order_by('name')
    
    results = [
        {
            'id': str(model.id), 
            'name': model.name, 
            'vehicle_class': model.vehicle_class
        } 
        for model in models
    ]
    
    return JsonResponse({'results': results})


@api_error_handler
@tenant_required
@role_permission_required('work_orders.view_operations')
def ajax_get_operations_for_vehicle(request):
    """
    Get appropriate operations based on vehicle make, model, and odometer reading.
    
    Query Parameters:
        vehicle_id (uuid): ID of the vehicle
        make (str): Vehicle make
        model (str): Vehicle model
        odometer (int): Current odometer reading
    
    Returns:
        JsonResponse: List of suitable operations
    """
    # Get parameters from request
    vehicle_id = safe_uuid(request.GET.get('vehicle_id'))
    make = request.GET.get('make')
    model = request.GET.get('model')
    odometer = request.GET.get('odometer', 0)
    tenant_id = request.tenant_id
    
    try:
        odometer = int(odometer)
    except (ValueError, TypeError):
        odometer = 0
    
    # Initialize result structures
    scheduled_operations = []
    custom_operations = []
    recommended_schedule = None
    
    # Get vehicle info if not provided
    if vehicle_id and (not make or not model):
        vehicle = get_object_or_none(Vehicle, id=vehicle_id, tenant_id=tenant_id)
        if vehicle:
            make = vehicle.make
            model = vehicle.model
    
    # Get recommended maintenance operations based on mileage/make/model
    # This is a simplified version - the real implementation would need more logic
    if make and model:
        # Fetch applicable maintenance schedules
        schedules = MaintenanceSchedule.objects.filter(
            Q(vehicle_make__iexact=make) | Q(vehicle_make=''),
            Q(vehicle_model__iexact=model) | Q(vehicle_model=''),
            is_active=True,
            tenant_id=tenant_id
        )
        
        # If we have odometer, filter by mileage interval
        if odometer > 0:
            for schedule in schedules:
                if schedule.mileage_interval and odometer % schedule.mileage_interval < 1000:
                    # This is a crude approximation, would need more sophisticated logic
                    recommended_schedule = schedule
                    break
        
        # If no recommended schedule found, just take the first one
        if not recommended_schedule and schedules.exists():
            recommended_schedule = schedules.first()
        
        # If we have a recommended schedule, get its operations
        if recommended_schedule:
            for op in recommended_schedule.operations.all():
                scheduled_operations.append({
                    'name': op.name,
                    'description': op.description,
                    'duration_hours': op.duration_minutes / 60 if op.duration_minutes else 1,
                    'schedule_id': str(recommended_schedule.id)
                })
    
    # Get custom operations for this make/model
    # Simplified version - real implementation would query OperationCompatibility
    
    # Sample custom operations (in a real implementation, these would come from the database)
    default_operations = [
        {'name': 'تغيير زيت', 'duration': 0.5, 'price': 150},  # 30 minutes
        {'name': 'فحص فرامل', 'duration': 0.75, 'price': 200}, # 45 minutes
        {'name': 'صيانة دورية', 'duration': 2.0, 'price': 500}, # 2 hours
        {'name': 'تصليح كهرباء', 'duration': 1.5, 'price': 350}, # 1.5 hours
        {'name': 'تصليح ميكانيكا', 'duration': 2.5, 'price': 600}, # 2.5 hours
    ]
    
    for op in default_operations:
        custom_operations.append({
            'name': op['name'],
            'description': '',
            'duration_hours': op['duration'],
            'price': op['price']
        })
    
    return JsonResponse({
        'success': True,
        'scheduled_operations': scheduled_operations,
        'custom_operations': custom_operations,
        'recommended_schedule': {
            'id': str(recommended_schedule.id),
            'name': recommended_schedule.name,
            'description': recommended_schedule.description
        } if recommended_schedule else None
    })


@api_error_handler
@tenant_required
@role_permission_required('work_orders.create_work_order')
def ajax_create_customer(request):
    """
    Create a new customer.
    
    POST Parameters:
        first_name (str): First name
        last_name (str): Last name
        phone_number (str): Phone number
        ...and other customer fields
    
    Returns:
        JsonResponse: Created customer details
    """
    if request.method != 'POST':
        return JsonResponse({
            'success': False, 
            'error': _('Invalid request method')
        }, status=405)
    
    try:
        data = json.loads(request.body)
        tenant_id = request.tenant_id
        
        # Extract customer data
        first_name = data.get('first_name')
        second_name = data.get('second_name')
        third_name = data.get('third_name')
        last_name = data.get('last_name')
        phone_number = data.get('phone_number')
        email = data.get('email')
        gender = data.get('gender')
        date_of_birth = data.get('date_of_birth')
        customer_type = data.get('customer_type')
        address = data.get('address')
        id_type = data.get('id_type')
        id_number = data.get('id_number')

        # Basic validation
        if not first_name or not last_name or not phone_number:
            return JsonResponse({
                'success': False, 
                'error': _('Missing required fields: First Name, Last Name, Phone Number')
            }, status=400)

        # Create the new customer
        customer = Customer.objects.create(
            tenant_id=tenant_id,
            first_name=first_name,
            second_name=second_name,
            third_name=third_name,
            last_name=last_name,
            phone=phone_number,
            email=email,
            gender=gender,
            date_of_birth=date_of_birth if date_of_birth else None,
            customer_type=customer_type,
            address=address,
            id_type=id_type,
            id_number=id_number
        )
        
        # Return success response with the new customer's ID and full details
        return JsonResponse({
            'success': True, 
            'customer': {
                'id': str(customer.id),
                'full_name': customer.get_full_name(),
                'phone_number': customer.phone,
                'email': customer.email
            }
        })

    except json.JSONDecodeError:
        return JsonResponse({
            'success': False, 
            'error': _('Invalid JSON')
        }, status=400)


@api_error_handler
@tenant_required
@role_permission_required('work_orders.create_work_order')
def ajax_create_vehicle(request):
    """
    Create a new vehicle.
    
    POST Parameters:
        make (str): Vehicle make
        model (str): Vehicle model
        year (int): Vehicle year
        ...and other vehicle fields
    
    Returns:
        JsonResponse: Created vehicle details
    """
    if request.method != 'POST':
        return JsonResponse({
            'success': False, 
            'error': _('Invalid request method')
        }, status=405)
    
    try:
        data = json.loads(request.body)
        tenant_id = request.tenant_id
        
        # Extract vehicle data
        make = data.get('make')
        model = data.get('model') 
        year = data.get('year')
        license_plate = data.get('license_plate')
        vin = data.get('vin')
        color = data.get('color')
        owner_id = safe_uuid(data.get('owner_id'))
        service_center_id = safe_uuid(data.get('service_center_id'))
        notes = data.get('notes')
        
        # Standard make/model references
        standard_make_id = safe_uuid(data.get('standard_make_id'))
        standard_model_id = safe_uuid(data.get('standard_model_id'))

        # Basic validation
        if not make or not model:
            return JsonResponse({
                'success': False, 
                'error': _('Missing required fields: Make, Model')
            }, status=400)

        # Get related objects
        owner = get_object_or_none(Customer, id=owner_id, tenant_id=tenant_id) if owner_id else None
        service_center = get_object_or_none(ServiceCenter, id=service_center_id, tenant_id=tenant_id) if service_center_id else None
        standard_make = get_object_or_none(VehicleMake, id=standard_make_id, tenant_id=tenant_id) if standard_make_id else None
        standard_model = get_object_or_none(VehicleModel, id=standard_model_id, tenant_id=tenant_id) if standard_model_id else None
        
        # Ensure the model belongs to the selected make
        if standard_make and standard_model and standard_model.make != standard_make:
            standard_model = None

        # Create the new vehicle
        vehicle = Vehicle.objects.create(
            tenant_id=tenant_id,
            make=make,
            model=model,
            year=int(year) if year and str(year).isdigit() else None,
            license_plate=license_plate or '',
            vin=vin or '',
            color=color or '',
            owner=owner,
            service_center=service_center,
            standard_make=standard_make,
            standard_model=standard_model,
            notes=notes or ''
        )
        
        # Return success response with the new vehicle's ID and details
        vehicle_data = {
            'id': str(vehicle.id),
            'text': f"{vehicle.make} {vehicle.model} ({vehicle.license_plate or vehicle.vin or 'N/A'})",
            'make': vehicle.make,
            'model': vehicle.model,
            'year': vehicle.year,
            'vin': vehicle.vin,
            'license_plate': vehicle.license_plate,
            'color': vehicle.color,
            'owner_id': str(vehicle.owner.id) if vehicle.owner else None,
            'owner_name': vehicle.owner.get_full_name() if vehicle.owner else '',
            'has_active_work_order': False,
            'standard_make_id': str(standard_make.id) if standard_make else None,
            'standard_model_id': str(standard_model.id) if standard_model else None
        }
        
        return JsonResponse({'success': True, 'vehicle': vehicle_data})

    except json.JSONDecodeError:
        return JsonResponse({
            'success': False, 
            'error': _('Invalid JSON')
        }, status=400)


@api_error_handler
@tenant_required
def ajax_spare_parts_for_operation(request):
    """
    Get spare parts compatible with a specific operation.
    
    Query Parameters:
        operation_id (uuid): ID of the operation
    
    Returns:
        JsonResponse: List of compatible spare parts
    """
    operation_id = safe_uuid(request.GET.get('operation_id'))
    tenant_id = request.tenant_id
    
    if not operation_id:
        return JsonResponse({
            'success': False,
            'error': 'Operation ID is required'
        })
    
    # Get parts compatible with this operation
    # This is a simplified implementation - real one would be more complex
    parts = Item.objects.filter(
        operation_compatibilities__operation_type_id=operation_id,
        is_active=True,
        tenant_id=tenant_id
    ).distinct()
    
    # Format the parts data
    spare_parts = []
    for part in parts:
        part_data = {
            'id': str(part.id),
            'name': part.name,
            'sku': part.sku,
            'recommended_quantity': 1,  # Default quantity
            'unit_of_measurement': part.unit_of_measurement.symbol if hasattr(part, 'unit_of_measurement') and part.unit_of_measurement else '',
            'price': float(part.unit_price) if part.unit_price else 0,
            'current_stock': float(part.stock_level) if hasattr(part, 'stock_level') and part.stock_level else 0,
            'operation_id': operation_id,
        }
        
        # Get recommended quantity from compatibility if available
        op_compat = part.operation_compatibilities.filter(operation_type_id=operation_id).first()
        if op_compat and hasattr(op_compat, 'recommended_quantity') and op_compat.recommended_quantity:
            part_data['recommended_quantity'] = float(op_compat.recommended_quantity)
        
        spare_parts.append(part_data)
    
    # If no parts found by operation ID, try by name (this is a fallback)
    if not spare_parts and not safe_uuid(operation_id):
        # Search for parts with names that might match this operation
        parts = Item.objects.filter(
            Q(name__icontains=operation_id) | 
            Q(description__icontains=operation_id),
            is_active=True,
            tenant_id=tenant_id
        ).distinct()[:10]
        
        for part in parts:
            part_data = {
                'id': str(part.id),
                'name': part.name,
                'sku': part.sku,
                'recommended_quantity': 1,
                'unit_of_measurement': part.unit_of_measurement.symbol if hasattr(part, 'unit_of_measurement') and part.unit_of_measurement else '',
                'price': float(part.unit_price) if part.unit_price else 0,
                'current_stock': float(part.stock_level) if hasattr(part, 'stock_level') and part.stock_level else 0,
                'operation_id': operation_id,
            }
            spare_parts.append(part_data)
    
    return JsonResponse({
        'success': True,
        'operation_id': operation_id,
        'spare_parts': spare_parts
    })


@api_error_handler
@tenant_required
@role_permission_required('work_orders.add_material')
def ajax_add_material_to_work_order(request):
    """
    Add a material to a work order.
    
    POST Parameters:
        work_order_id (uuid): ID of the work order
        item_id (uuid): ID of the item to add
        quantity (float): Quantity to add
        operation_id (uuid, optional): ID of the operation to associate with
        notes (str, optional): Notes about this material
    
    Returns:
        JsonResponse: Added material details
    """
    if request.method != 'POST':
        return JsonResponse({
            'success': False, 
            'error': _('Invalid request method')
        }, status=405)
    
    try:
        data = json.loads(request.body)
        tenant_id = request.tenant_id
        
        # Extract material data
        work_order_id = safe_uuid(data.get('work_order_id'))
        item_id = safe_uuid(data.get('item_id'))
        operation_id = safe_uuid(data.get('operation_id'))
        quantity = data.get('quantity', 1)
        notes = data.get('notes', '')
        
        # Validate required fields
        if not work_order_id or not item_id:
            return JsonResponse({
                'success': False, 
                'error': _('Missing required fields: work_order_id, item_id')
            }, status=400)
        
        # Convert quantity to float
        try:
            quantity = float(quantity)
            if quantity <= 0:
                return JsonResponse({
                    'success': False, 
                    'error': _('Quantity must be greater than zero')
                }, status=400)
        except (ValueError, TypeError):
            return JsonResponse({
                'success': False, 
                'error': _('Invalid quantity value')
            }, status=400)
        
        # Get related objects
        work_order = get_object_or_none(WorkOrder, id=work_order_id, tenant_id=tenant_id)
        if not work_order:
            return JsonResponse({
                'success': False, 
                'error': _('Work order not found')
            }, status=404)
        
        item = get_object_or_none(Item, id=item_id, tenant_id=tenant_id)
        if not item:
            return JsonResponse({
                'success': False, 
                'error': _('Item not found')
            }, status=404)
        
        operation = None
        if operation_id:
            operation = get_object_or_none(WorkOrderOperation, id=operation_id, work_order_id=work_order_id, tenant_id=tenant_id)
        
        # Create the material
        material = WorkOrderMaterial.objects.create(
            tenant_id=tenant_id,
            work_order=work_order,
            item=item,
            operation=operation,
            quantity=quantity,
            unit_of_measure=item.unit_of_measurement.symbol if hasattr(item, 'unit_of_measurement') and item.unit_of_measurement else '',
            description=item.description,
            notes=notes
        )
        
        # Return success response
        return JsonResponse({
            'success': True,
            'material': {
                'id': str(material.id),
                'item_name': item.name,
                'item_sku': item.sku,
                'quantity': float(material.quantity),
                'unit_of_measure': material.unit_of_measure,
                'operation_name': operation.name if operation else None,
                'notes': material.notes
            }
        })
        
    except json.JSONDecodeError:
        return JsonResponse({
            'success': False, 
            'error': _('Invalid JSON')
        }, status=400) 