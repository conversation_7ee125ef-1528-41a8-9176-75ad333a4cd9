"""
Django settings for project project.

Generated by 'django-admin startproject' using Django 3.2.15.

For more information on this file, see
https://docs.djangoproject.com/en/3.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/3.2/ref/settings/
"""
import os
from pathlib import Path

import sentry_sdk
from django.utils.translation import gettext_lazy as _
from dotenv import load_dotenv

load_dotenv()

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/3.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = os.getenv('SECRET_KEY', 'django-insecure-p%^0vpl(ik0+s80+oe9xts9$n@t$tjk34ml4lz9r+g=76y!q4b')

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = eval(os.environ.get('DEBUG', 'True'))
ENV = os.getenv('ENV')

if DEBUG:
    ALLOWED_HOSTS = ['*']
else:
    ALLOWED_HOSTS = []

# Application definition

INSTALLED_APPS = [
    'jazzmin',
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'core',
    'admins',
    'website',
    'api',
    'feature_flags',
    'inventory',
    'warehouse',
    'sales',
    'purchases',
    'reports',
    'app_settings',
    'notifications',
    'work_orders',
    'setup',
    'user_roles',
    'franchise_setup',
    'billing',
    'rest_framework',
    'rest_framework.authtoken',
    'drf_api_logger',
    'import_export',
    'django.contrib.sites',
    'django.contrib.sitemaps',
    'django_crontab',
    'solo',
    'django_otp',
    'django_otp.plugins.otp_totp',
    'waffle',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.locale.LocaleMiddleware',
    'core.middleware.ForceArabicLanguageMiddleware',
    'core.middleware.AdminEnglishMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'django_otp.middleware.OTPMiddleware',
    'drf_api_logger.middleware.api_logger_middleware.APILoggerMiddleware',
    'core.middleware.CurrentTenantMiddleware',
    'waffle.middleware.WaffleMiddleware',
    'feature_flags.middleware.FeatureFlagMiddleware',
    'user_roles.middleware.RoleBasedAccessMiddleware',
]

ROOT_URLCONF = 'project.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [os.path.join(BASE_DIR, 'templates')],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
                'core.context_processors.admin_header_processor',
                'website.context_processors.website_context',
                'django.template.context_processors.i18n',
                'core.context_processors.language_context',
                'core.context_processors.user_roles_context',
                'core.context_processors.entity_filters',
            ],
        },
    },
]

WSGI_APPLICATION = 'project.wsgi.application'

# Database
# https://docs.djangoproject.com/en/3.2/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    }
    # "default": {
    #     "ENGINE": os.environ.get("SQL_ENGINE", "django.db.backends.postgresql"),
    #     "NAME": os.environ.get("SQL_DATABASE"),
    #     "USER": os.environ.get("SQL_USER"),
    #     "PASSWORD": os.environ.get("SQL_PASSWORD"),
    #     "HOST": os.environ.get("SQL_HOST"),
    #     "PORT": os.environ.get("SQL_PORT"),
    # }
}

# Password validation
# https://docs.djangoproject.com/en/3.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# Internationalization
# https://docs.djangoproject.com/en/3.2/topics/i18n/

LANGUAGE_CODE = 'ar'  # Use Arabic as the default language

# Only Arabic language is available
LANGUAGES = (
    ('ar', _('Arabic')),
    ('en', _('English')),
)

# Add the language selector cookie name and expiry time
LANGUAGE_COOKIE_NAME = 'django_language'
LANGUAGE_COOKIE_AGE = 60 * 60 * 24 * 365  # One year in seconds

# Force Arabic language regardless of browser settings
LANGUAGE_COOKIE_DOMAIN = None
LANGUAGE_COOKIE_PATH = '/'
LANGUAGE_COOKIE_SECURE = False
LANGUAGE_COOKIE_HTTPONLY = False
LANGUAGE_COOKIE_SAMESITE = None

# RTL settings for Arabic interface
TEXT_DIRECTION = 'rtl'

# Locale paths where translation files are stored
LOCALE_PATHS = [
    os.path.join(BASE_DIR, 'locale'),
    os.path.join(BASE_DIR, 'core/locale/'),
    os.path.join(BASE_DIR, 'admins/locale/'),
    os.path.join(BASE_DIR, 'website/locale/'),
    os.path.join(BASE_DIR, 'api/locale/'),
    os.path.join(BASE_DIR, 'inventory/locale/'),
    os.path.join(BASE_DIR, 'reports/locale/'),
]

TIME_ZONE = 'Africa/Cairo'
# TIME_ZONE = 'Etc/GMT-3'

USE_I18N = True

USE_L10N = True

USE_TZ = False

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/3.2/howto/static-files/

STATIC_URL = '/static/'

# Default primary key field type
# https://docs.djangoproject.com/en/3.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

DRF_API_LOGGER_DATABASE = True
DRF_API_LOGGER_SLOW_API_ABOVE = 200

REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES':
        ['rest_framework.authentication.SessionAuthentication',
         'rest_framework.authentication.TokenAuthentication',
         ],
    'DEFAULT_PERMISSION_CLASSES':
        ['rest_framework.permissions.IsAuthenticated'],
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 100,
    'DATETIME_FORMAT': "%Y-%m-%d %I:%M%p",
    'DEFAULT_FILTER_BACKENDS': ['django_filters.rest_framework.DjangoFilterBackend'],
    'DEFAULT_THROTTLE_CLASSES': [
        'core.throttling.IPAddressThrottle'
        ],
    'DEFAULT_THROTTLE_RATES': {
        'ip': '1000/m'
    }
}

MEDIA_URL = '/media/'
MEDIA_ROOT = '/media/'
STATIC_ROOT = '/static/'

if DEBUG:
    EMAIL_BACKEND = 'website.email_backend.EmailBackend'
EMAIL_HOST = os.getenv('EMAIL_HOST')
EMAIL_USE_TLS = True
EMAIL_PORT = os.getenv('EMAIL_PORT')
EMAIL_HOST_USER = os.getenv('EMAIL_HOST_USER')
EMAIL_HOST_PASSWORD = os.getenv('EMAIL_HOST_PASSWORD')
DEFAULT_FROM_EMAIL = EMAIL_HOST_USER

CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.db.DatabaseCache',
        'LOCATION': 'cache_table',
    }
}

SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')

CORS_ALLOWED_ORIGINS = [
    'http://gb-pcbe.ghabbour.com:8070'
]

IMPORT_EXPORT_SKIP_ADMIN_LOG = True
IMPORT_EXPORT_USE_TRANSACTIONS = True
DATA_UPLOAD_MAX_NUMBER_FIELDS = None

SITE_ID = 1

STATICFILES_DIRS = [
    os.path.join(BASE_DIR, 'static')
]

USE_THOUSAND_SEPARATOR = True

JAZZMIN_SETTINGS = {
    "custom_css": "css/admin-custom.css",
    "site_logo": "images/logo.png",
    "site_logo_classes": "",
}

if DEBUG:
    JAZZMIN_SETTINGS.update({
        "site_title": "Project (Test)",
        "site_header": "Project (Test)",
        "welcome_sign": "Welcome to Project (Test)",
    })
else:
    JAZZMIN_SETTINGS.update({
        "site_title": "Project",
        "site_header": "Project",
        "welcome_sign": "Welcome to Project",
    })

CRONJOBS = [
    ('0 0 * * *', 'core.crons.purge_logs', '>> /var/log/cron.log 2>&1'),
    ('*/5 * * * *', 'core.crons.retry_webhooks_job'),
    ('0 * * * *', 'core.crons.check_stock_levels'),
    ('0 1 * * *', 'billing.crons.evaluate_all_customers_classifications', '>> /var/log/cron.log 2>&1'),
    ('0 2 * * 0', 'billing.crons.check_for_classification_downgrades', '>> /var/log/cron.log 2>&1'),
]

CRONTAB_LOCK_JOBS = True
if DATABASES['default']['ENGINE'] != 'django.db.backends.sqlite3':
    CRONTAB_COMMAND_PREFIX = f"DEBUG={DEBUG} SQL_HOST={DATABASES['default']['HOST']} SQL_PORT={DATABASES['default']['PORT']} SQL_DATABASE={DATABASES['default']['NAME']} SQL_USER={DATABASES['default']['USER']} SQL_PASSWORD={DATABASES['default']['PASSWORD']}"

if not DEBUG:
    SECURE_SSL_REDIRECT = True
    SESSION_COOKIE_SECURE = True
    SECURE_BROWSER_XSS_FILTER = True
    SECURE_HSTS_SECONDS = 31536000
    SECURE_HSTS_INCLUDE_SUBDOMAINS = True
    SECURE_HSTS_PRELOAD = True
    SECURE_CONTENT_TYPE_NOSNIFF = True
    CSRF_COOKIE_SECURE = True
    CSRF_COOKIE_HTTPONLY = True
    SESSION_COOKIE_HTTPONLY = True
    COOKIE_SECURE = True
    COOKIE_HTTPONLY = True
    SECURE_COOKIE_SECURE = True
    sentry_sdk.init(
        dsn=os.environ.get("SENTRY_DSN"),
        # Set traces_sample_rate to 1.0 to capture 100%
        # of transactions for performance monitoring.
        traces_sample_rate=0.1,
        # Set profiles_sample_rate to 1.0 to profile 100%
        # of sampled transactions.
        # We recommend adjusting this value in production.
        profiles_sample_rate=0.1,
    )

# Waffle configuration
WAFFLE_FLAG_DEFAULT = False
WAFFLE_CREATE_MISSING_FLAGS = True

# API feature flags
WAFFLE_FLAGS = {
    'api': False,
    'advanced_reporting': True,  # Enable advanced reporting
    'export_import': True,  # Enable import/export
    'document_management': True,  # Enable document management
}

# Feature flags for advanced reporting
WAFFLE_FLAGS.update({
    'advanced_reporting': False,
    'export_import': True,  # Enable import/export
    'document_management': False,
})

# Add this only if DEBUG is True
if DEBUG:
    # Enable all feature flags in development
    WAFFLE_FLAG_DEFAULT = True
