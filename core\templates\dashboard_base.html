{% load i18n %}
{% load static %}
<!DOCTYPE html>
<html lang="{{ LANGUAGE_CODE|default:'ar' }}" dir="{% if LANGUAGE_CODE == 'ar' %}rtl{% else %}ltr{% endif %}" class="h-full">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>{% block title %}{% trans "نظام Aftersails" %}{% endblock %}</title>
  
      <!-- Tailwind CSS -->
    <script src="/static/js/tailwind.js"></script>
  
  <!-- Stylesheets -->
  <link rel="stylesheet" href="{% static 'css/app.css' %}">
  
  <!-- Fallback for Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  
  <!-- HTMX for interactive UI -->
  <script src="https://unpkg.com/htmx.org@1.9.4" defer></script>
  
  <!-- Google Tajawal Arabic Font -->
  <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
  
  <!-- RTL CSS -->
  <link href="{% static 'css/rtl.css' %}" rel="stylesheet">
  
  <style>
    /* Fix for RTL Font Awesome icons */
    .fa, .fas, .far, .fal, .fab {
      display: inline-block;
    }
    
    /* Base RTL styles */
    body {
      font-family: 'Tajawal', sans-serif;
    }
  </style>
  
  {% block extra_css %}{% endblock %}
</head>

<body class="h-full bg-gray-50 font-tajawal">
  <!-- Navigation Header (unified with base.html) -->
  <nav class="bg-white border-gray-200 px-4 py-2.5 shadow-md">
    <div class="container flex flex-wrap justify-between items-center mx-auto">
      <!-- Logo -->
      <a href="{% url 'core:main_dashboard' %}" class="flex items-center">
        <img src="{% static 'images/logo.png' %}" alt="Logo" class="h-10 {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}">
        <span class="self-center text-xl font-semibold whitespace-nowrap md:block hidden">{% trans "نظام Aftersails" %}</span>
      </a>
        
      <!-- Main Navigation - Icons Only -->
      <div class="flex items-center justify-center">
        <ul class="flex {% if LANGUAGE_CODE == 'ar' %}space-x-6 space-x-reverse rtl{% else %}space-x-6{% endif %}">
         
          {% if user.is_superuser or primary_role.can_access_reports %}
          <li class="{% if LANGUAGE_CODE == 'ar' %}mr-6{% else %}ml-6{% endif %}">
            <a href="{% url 'reports:report_list' %}" class="flex flex-col items-center text-gray-700 hover:text-blue-700" title="{% trans 'التقارير' %}">
              <i class="fas fa-chart-bar text-xl"></i>
              <span class="text-xs mt-1 font-bold">{% trans 'التقارير' %}</span>
            </a>
          </li>
          {% endif %}
          {% if user.is_superuser or primary_role.can_access_purchases %}
          <li>
            <a href="{% url 'purchases:dashboard' %}" class="flex flex-col items-center text-gray-700 hover:text-blue-700" title="{% trans 'المشتريات' %}">
              <i class="fas fa-shopping-basket text-xl"></i>
              <span class="text-xs mt-1 font-bold">{% trans 'المشتريات' %}</span>
            </a>
          </li>
          {% endif %}
          {% if user.is_superuser or primary_role.can_access_sales %}
          <li>
            <a href="{% url 'sales:dashboard' %}" class="flex flex-col items-center text-gray-700 hover:text-blue-700" title="{% trans 'المبيعات' %}">
              <i class="fas fa-shopping-cart text-xl"></i>
              <span class="text-xs mt-1 font-bold">{% trans 'المبيعات' %}</span>
            </a>
          </li>
          {% endif %}
          {% if user.is_superuser or primary_role.can_access_warehouse %}
          <li>
            <a href="{% url 'warehouse:dashboard' %}" class="flex flex-col items-center text-gray-700 hover:text-blue-700" title="{% trans 'المستودع' %}">
              <i class="fas fa-boxes text-xl"></i>
              <span class="text-xs mt-1 font-bold">{% trans 'المستودع' %}</span>
            </a>
          </li>
          {% endif %}
          {% if user.is_superuser or primary_role.can_access_work_orders %}
          <li>
            <a href="{% url 'work_orders:work_order_list' %}" class="flex flex-col items-center text-gray-700 hover:text-blue-700" title="{% trans 'أوامر العمل' %}">
              <i class="fas fa-tasks text-xl"></i>
              <span class="text-xs mt-1 font-bold">{% trans 'أوامر العمل' %}</span>
            </a>
          </li>
          {% endif %}
          {% if user.is_superuser or primary_role.can_access_setup %}
          <li>
            <a href="{% url 'setup:dashboard' %}" class="flex flex-col items-center text-gray-700 hover:text-blue-700" title="{% trans 'الإعدادات' %}">
              <i class="fas fa-cogs text-xl"></i>
              <span class="text-xs mt-1 font-bold">{% trans 'الإعدادات' %}</span>
            </a>
          </li>
          {% endif %}
          {% if user.is_superuser or primary_role.can_access_inventory %}
          <li>
            <a href="{% url 'inventory:dashboard' %}" class="flex flex-col items-center text-gray-700 hover:text-blue-700" title="{% trans 'المخزون' %}">
              <i class="fas fa-warehouse text-xl"></i>
              <span class="text-xs mt-1 font-bold">{% trans 'المخزون' %}</span>
            </a>
          </li>
          {% endif %}
        </ul>
      </div>
        
      <!-- User Menu -->
      <div class="flex items-center">
        {% if user.is_authenticated %}
        
        <!-- Notification Icon -->
        {% if messages %}
        <button type="button" class="flex items-center {% if LANGUAGE_CODE == 'ar' %}ml-4{% else %}mr-4{% endif %} text-sm" id="notification-button" aria-expanded="false" data-dropdown-toggle="notification-dropdown">
          <span class="sr-only">{% trans "عرض الإشعارات" %}</span>
          <div class="relative">
            <i class="fas fa-bell text-xl text-gray-600 hover:text-blue-600"></i>
            <span class="absolute top-0 right-0 inline-flex items-center justify-center w-4 h-4 text-xs font-bold text-white bg-red-500 rounded-full">{{ messages|length }}</span>
          </div>
        </button>
        <!-- Notification Dropdown -->
        <div class="hidden z-50 my-4 w-80 text-base list-none bg-white rounded-lg divide-y divide-gray-100 shadow-lg" id="notification-dropdown">
          <div class="py-2 px-4 bg-blue-600 rounded-t-lg">
            <span class="block text-sm font-semibold text-white">{% trans "الإشعارات" %}</span>
          </div>
          <div class="py-2 px-4 max-h-64 overflow-y-auto">
            {% for message in messages %}
            <div class="py-2 border-b border-gray-100 last:border-b-0">
              <div class="flex items-start">
                <i class="flex-shrink-0 w-5 h-5 text-blue-600 fas fa-info-circle mt-0.5"></i>
                <div class="{% if LANGUAGE_CODE == 'ar' %}mr-3{% else %}ml-3{% endif %} text-sm text-gray-700">
                  {{ message }}
                </div>
              </div>
            </div>
            {% endfor %}
          </div>
        </div>
        {% endif %}
        
        <button type="button" class="flex text-sm focus:ring-4 focus:ring-blue-300" id="user-menu-button" aria-expanded="false" data-dropdown-toggle="user-dropdown">
          <span class="sr-only">{% trans "فتح قائمة المستخدم" %}</span>
          <div class="w-8 h-8 text-white flex items-center justify-center bg-blue-600 rounded-full shadow-md">
            {{ user.username|first|upper }}
          </div>
        </button>
        <!-- Dropdown menu -->
        <div class="hidden z-50 my-4 w-56 text-base list-none bg-white rounded-lg divide-y divide-gray-100 shadow-lg" id="user-dropdown">
          <div class="py-3 px-4 bg-blue-600 rounded-t-lg">
            <span class="block text-sm font-semibold text-white">{{ user.username }}</span>
            <span class="block text-xs text-blue-100 truncate">{{ user.email }}</span>
          </div>
          <ul class="py-1" aria-labelledby="user-menu-button">
            <li>
              <a href="{% url 'inventory:dashboard' %}" class="flex items-center py-2 px-4 text-sm text-gray-700 hover:bg-gray-100 hover:text-blue-600 transition-colors">
                <i class="fas fa-tachometer-alt text-blue-500 {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i> {% trans "لوحة التحكم" %}
              </a>
            </li>
            <li>
              <a href="#" class="flex items-center py-2 px-4 text-sm text-gray-700 hover:bg-gray-100 hover:text-blue-600 transition-colors">
                <i class="fas fa-cog text-indigo-500 {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i> {% trans "الإعدادات" %}
              </a>
            </li>
            <li>
              <a href="{% url 'core:logout' %}" class="flex items-center py-2 px-4 text-sm text-gray-700 hover:bg-gray-100 hover:text-red-600 transition-colors">
                <i class="fas fa-sign-out-alt text-red-500 {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i> {% trans "تسجيل الخروج" %}
              </a>
            </li>
          </ul>
        </div>
        {% endif %}
          
        <!-- Mobile menu button -->
        <button data-collapse-toggle="mobile-menu" type="button" class="inline-flex items-center p-2 {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %} text-sm text-gray-500 rounded-lg md:hidden hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200" aria-controls="mobile-menu" aria-expanded="false">
          <span class="sr-only">{% trans "فتح القائمة الرئيسية" %}</span>
          <i class="fas fa-bars"></i>
        </button>
      </div>
    </div>
      
    <!-- Mobile Navigation -->
    <div class="hidden w-full md:hidden" id="mobile-menu">
      <ul class="flex flex-col mt-4 text-center">
        {% if user.is_superuser or primary_role.can_access_settings %}
        <li class="py-2">
          <a href="#" class="flex flex-col items-center py-2 px-3 text-gray-700 hover:bg-gray-50">
            <i class="fas fa-sliders-h {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i>
            <span class="text-xs mt-1 font-bold">{% trans "الإعدادات" %}</span>
          </a>
        </li>
        {% endif %}
        {% if user.is_superuser or primary_role.can_access_reports %}
        <li class="py-2 {% if LANGUAGE_CODE == 'ar' %}mr-6{% else %}ml-6{% endif %}">
          <a href="{% url 'reports:report_list' %}" class="flex flex-col items-center py-2 px-3 text-gray-700 hover:bg-gray-50">
            <i class="fas fa-chart-bar {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i>
            <span class="text-xs mt-1 font-bold">{% trans "التقارير" %}</span>
          </a>
        </li>
        {% endif %}
        {% if user.is_superuser or primary_role.can_access_purchases %}
        <li class="py-2">
          <a href="{% url 'purchases:dashboard' %}" class="flex flex-col items-center py-2 px-3 text-gray-700 hover:bg-gray-50">
            <i class="fas fa-shopping-basket {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i>
            <span class="text-xs mt-1 font-bold">{% trans "المشتريات" %}</span>
          </a>
        </li>
        {% endif %}
        {% if user.is_superuser or primary_role.can_access_sales %}
        <li class="py-2">
          <a href="{% url 'sales:dashboard' %}" class="flex flex-col items-center py-2 px-3 text-gray-700 hover:bg-gray-50">
            <i class="fas fa-shopping-cart {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i>
            <span class="text-xs mt-1 font-bold">{% trans "المبيعات" %}</span>
          </a>
        </li>
        {% endif %}
        {% if user.is_superuser or primary_role.can_access_warehouse %}
        <li class="py-2">
          <a href="{% url 'warehouse:dashboard' %}" class="flex flex-col items-center py-2 px-3 text-gray-700 hover:bg-gray-50">
            <i class="fas fa-boxes {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i>
            <span class="text-xs mt-1 font-bold">{% trans "المستودع" %}</span>
          </a>
        </li>
        {% endif %}
        {% if user.is_superuser or primary_role.can_access_work_orders %}
        <li class="py-2">
          <a href="{% url 'work_orders:work_order_list' %}" class="flex flex-col items-center py-2 px-3 text-gray-700 hover:bg-gray-50">
            <i class="fas fa-tasks {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i>
            <span class="text-xs mt-1 font-bold">{% trans "أوامر العمل" %}</span>
          </a>
        </li>
        {% endif %}
        {% if user.is_superuser or primary_role.can_access_setup %}
        <li class="py-2">
          <a href="{% url 'setup:dashboard' %}" class="flex flex-col items-center py-2 px-3 text-gray-700 hover:bg-gray-50">
            <i class="fas fa-cogs {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i>
            <span class="text-xs mt-1 font-bold">{% trans "الإعدادات" %}</span>
          </a>
        </li>
        {% endif %}
        {% if user.is_superuser or primary_role.can_access_inventory %}
        <li class="py-2">
          <a href="{% url 'inventory:dashboard' %}" class="flex flex-col items-center py-2 px-3 text-gray-700 hover:bg-gray-50">
            <i class="fas fa-warehouse {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i>
            <span class="text-xs mt-1 font-bold">{% trans "المخزون" %}</span>
          </a>
        </li>
        {% endif %}
      </ul>
    </div>
  </nav>
  
  <!-- Entity Filter Section -->
  <div class="entity-filter-container bg-blue-50 border-b border-blue-200 px-4 py-2">
    <div class="container mx-auto">
      <div class="flex flex-wrap items-center justify-between">
        <div class="flex items-center">
          <i class="fas fa-filter text-blue-500 {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i>
          <span class="text-sm text-blue-800 font-medium">
            {% if selected_franchise or selected_company or selected_service_center %}
              {% trans "تصفية النتائج:" %}
              {% if selected_franchise %}
                <span class="bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full text-xs {% if LANGUAGE_CODE == 'ar' %}mr-1{% else %}ml-1{% endif %}">{{ selected_franchise.name }}</span>
              {% endif %}
              {% if selected_company %}
                <span class="bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full text-xs {% if LANGUAGE_CODE == 'ar' %}mr-1{% else %}ml-1{% endif %}">{{ selected_company.name }}</span>
              {% endif %}
              {% if selected_service_center %}
                <span class="bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full text-xs {% if LANGUAGE_CODE == 'ar' %}mr-1{% else %}ml-1{% endif %}">{{ selected_service_center.name }}</span>
              {% endif %}
            {% else %}
              {% trans "تصفية حسب:" %} {% trans "عرض الكل" %}
            {% endif %}
          </span>
        </div>
        
        <button id="toggle-filter" class="text-blue-600 hover:text-blue-800 text-xs flex items-center"
                data-text-close="{% trans "إغلاق" %}" 
                data-text-open="{% trans "تغيير التصفية" %}">
          <i class="fas fa-sliders-h {% if LANGUAGE_CODE == 'ar' %}ml-1{% else %}mr-1{% endif %}"></i> {% trans "تغيير التصفية" %}
        </button>
      </div>
      
      <!-- Collapsible Filter Form -->
      <div id="filter-form-container" class="mt-3 pt-3 border-t border-blue-200 hidden">
        <form method="get" class="flex flex-wrap items-end gap-2">
          {% for key, value in preserved_query.items %}
            {% if key != 'franchise_id' and key != 'company_id' and key != 'service_center_id' %}
              <input type="hidden" name="{{ key }}" value="{{ value }}">
            {% endif %}
          {% endfor %}
          
          <div class="w-full flex flex-wrap items-center gap-3 mb-1">
            <div class="w-full sm:w-auto flex-1 min-w-[180px]">
              <label for="franchise_id" class="block text-xs text-gray-700 mb-1 font-medium">{% trans "الامتياز" %}</label>
              <div class="relative">
                <select id="franchise_id" name="franchise_id" class="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 text-sm py-1.5 px-3 w-full appearance-none">
                  <option value="">{% trans "الكل" %}</option>
                  {% for franchise in franchises %}
                    <option value="{{ franchise.id }}" {% if selected_franchise.id == franchise.id %}selected{% endif %}>{{ franchise.name }}</option>
                  {% endfor %}
                </select>
                <div class="absolute inset-y-0 {% if LANGUAGE_CODE == 'ar' %}left-0 pl-3{% else %}right-0 pr-3{% endif %} flex items-center pointer-events-none">
                  <i class="fas fa-chevron-down text-gray-400 text-xs"></i>
                </div>
              </div>
            </div>
            
            <div class="w-full sm:w-auto flex-1 min-w-[180px]">
              <label for="company_id" class="block text-xs text-gray-700 mb-1 font-medium">{% trans "الشركة" %}</label>
              <div class="relative">
                <select id="company_id" name="company_id" class="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 text-sm py-1.5 px-3 w-full appearance-none" {% if not selected_franchise %}disabled{% endif %}>
                  <option value="">{% trans "الكل" %}</option>
                  {% for company in companies %}
                    <option value="{{ company.id }}" {% if selected_company.id == company.id %}selected{% endif %}>{{ company.name }}</option>
                  {% endfor %}
                </select>
                <div class="absolute inset-y-0 {% if LANGUAGE_CODE == 'ar' %}left-0 pl-3{% else %}right-0 pr-3{% endif %} flex items-center pointer-events-none">
                  <i class="fas fa-chevron-down text-gray-400 text-xs"></i>
                </div>
              </div>
            </div>
            
            <div class="w-full sm:w-auto flex-1 min-w-[180px]">
              <label for="service_center_id" class="block text-xs text-gray-700 mb-1 font-medium">{% trans "مركز الخدمة" %}</label>
              <div class="relative">
                <select id="service_center_id" name="service_center_id" class="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 text-sm py-1.5 px-3 w-full appearance-none" {% if not selected_company %}disabled{% endif %}>
                  <option value="">{% trans "الكل" %}</option>
                  {% for service_center in service_centers %}
                    <option value="{{ service_center.id }}" {% if selected_service_center.id == service_center.id %}selected{% endif %}>{{ service_center.name }}</option>
                  {% endfor %}
                </select>
                <div class="absolute inset-y-0 {% if LANGUAGE_CODE == 'ar' %}left-0 pl-3{% else %}right-0 pr-3{% endif %} flex items-center pointer-events-none">
                  <i class="fas fa-chevron-down text-gray-400 text-xs"></i>
                </div>
              </div>
            </div>
          </div>
          
          <div class="w-full flex justify-end gap-2 mt-3">
            <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white rounded-md py-1.5 px-4 text-sm flex items-center">
              <i class="fas fa-search {% if LANGUAGE_CODE == 'ar' %}ml-1{% else %}mr-1{% endif %}"></i> {% trans "تصفية" %}
            </button>
            <a href="{{ request.path }}" class="bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-md py-1.5 px-4 text-sm flex items-center">
              <i class="fas fa-times {% if LANGUAGE_CODE == 'ar' %}ml-1{% else %}mr-1{% endif %}"></i> {% trans "إعادة ضبط" %}
            </a>
          </div>
        </form>
      </div>
    </div>
  </div>
  
  <!-- Main Content -->
  <main class="p-4 sm:p-6 lg:p-8">
    <!-- Flash Messages / Notifications (now hidden, displayed in popup) -->
    <div class="hidden">
      {% if messages %}
      <div class="container mx-auto px-4 py-2 mb-6">
        {% for message in messages %}
        <div id="alert-{{ forloop.counter }}" class="flex p-4 mb-4 {{ message.tags }} bg-blue-100 text-blue-700 rounded-lg" role="alert">
          <i class="flex-shrink-0 w-5 h-5 text-blue-700 fas fa-info-circle"></i>
          <div class="{% if LANGUAGE_CODE == 'ar' %}mr-3{% else %}ml-3{% endif %} text-sm font-medium">
            {{ message }}
          </div>
          <button type="button" class="ml-auto -mx-1.5 -my-1.5 bg-blue-100 text-blue-500 rounded-lg focus:ring-2 focus:ring-blue-400 p-1.5 hover:bg-blue-200 inline-flex h-8 w-8" data-dismiss-target="#alert-{{ forloop.counter }}" aria-label="Close">
            <span class="sr-only">{% trans "إغلاق" %}</span>
            <i class="w-5 h-5 fas fa-times"></i>
          </button>
        </div>
        {% endfor %}
      </div>
      {% endif %}
    </div>
    
    <!-- Main Content -->
    {% block content %}
    <!-- Dashboard Modules Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {% if user.is_superuser or primary_role.can_access_inventory %}
      <!-- Inventory Module -->
      <div class="bg-white overflow-hidden shadow rounded-lg transition transform hover:scale-105">
        <div class="p-5 border-b border-gray-200">
          <div class="flex items-center">
            <div class="flex-shrink-0 p-3 rounded-lg bg-blue-500">
              <i class="fas fa-warehouse text-white text-2xl"></i>
            </div>
            <div class="{% if LANGUAGE_CODE == 'ar' %}mr-4{% else %}ml-4{% endif %}">
              <h3 class="text-lg font-semibold text-gray-800">{% trans "المخزون" %}</h3>
              <p class="text-sm text-gray-500">{% trans "إدارة المخزون والأصناف" %}</p>
            </div>
          </div>
        </div>
        <div class="px-5 py-3">
          <div class="flex justify-between items-center mb-3">
            <span class="text-sm font-medium text-gray-500">{% trans "إجمالي الأصناف" %}</span>
            <span class="text-xl font-bold text-blue-600">{{ inventory_count|default:"0" }}</span>
          </div>
          <div class="flex justify-between items-center mb-3">
            <span class="text-sm font-medium text-gray-500">{% trans "أصناف نفدت" %}</span>
            <span class="text-xl font-bold text-red-500">{{ low_stock_count|default:"0" }}</span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-sm font-medium text-gray-500">{% trans "حركات المخزون" %}</span>
            <span class="text-xl font-bold text-green-600">{{ movement_count|default:"0" }}</span>
          </div>
        </div>
        <div class="bg-gray-50 px-5 py-3 border-t border-gray-200 text-center">
          <a href="{% url 'inventory:dashboard' %}" class="text-blue-600 hover:text-blue-800 font-medium">
            {% trans "فتح لوحة تحكم المخزون" %} <i class="fas fa-arrow-circle-right {% if LANGUAGE_CODE == 'ar' %}mr-1{% else %}ml-1{% endif %}"></i>
          </a>
        </div>
      </div>
      {% endif %}

      {% if user.is_superuser or primary_role.can_access_work_orders %}
      <!-- Work Orders Module -->
      <div class="bg-white overflow-hidden shadow rounded-lg transition transform hover:scale-105">
        <div class="p-5 border-b border-gray-200">
          <div class="flex items-center">
            <div class="flex-shrink-0 p-3 rounded-lg bg-indigo-500">
              <i class="fas fa-tasks text-white text-2xl"></i>
            </div>
            <div class="{% if LANGUAGE_CODE == 'ar' %}mr-4{% else %}ml-4{% endif %}">
              <h3 class="text-lg font-semibold text-gray-800">{% trans "أوامر العمل" %}</h3>
              <p class="text-sm text-gray-500">{% trans "إدارة عمليات الصيانة والإصلاح" %}</p>
            </div>
          </div>
        </div>
        <div class="px-5 py-3">
          <div class="flex justify-between items-center mb-3">
            <span class="text-sm font-medium text-gray-500">{% trans "إجمالي أوامر العمل" %}</span>
            <span class="text-xl font-bold text-indigo-600">{{ work_orders_count|default:"0" }}</span>
          </div>
          <div class="flex justify-between items-center mb-3">
            <span class="text-sm font-medium text-gray-500">{% trans "قيد التنفيذ" %}</span>
            <span class="text-xl font-bold text-amber-500">{{ active_work_orders_count|default:"0" }}</span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-sm font-medium text-gray-500">{% trans "المكتملة" %}</span>
            <span class="text-xl font-bold text-green-600">{{ completed_work_orders_count|default:"0" }}</span>
          </div>
        </div>
        <div class="bg-gray-50 px-5 py-3 border-t border-gray-200 text-center">
          <a href="{% url 'work_orders:work_order_list' %}" class="text-indigo-600 hover:text-indigo-800 font-medium">
            {% trans "فتح قائمة أوامر العمل" %} <i class="fas fa-arrow-circle-right {% if LANGUAGE_CODE == 'ar' %}mr-1{% else %}ml-1{% endif %}"></i>
          </a>
        </div>
      </div>
      {% endif %}
      
      {% if user.is_superuser or primary_role.can_access_sales %}
      <!-- Sales Module -->
      <div class="bg-white overflow-hidden shadow rounded-lg transition transform hover:scale-105">
        <div class="p-5 border-b border-gray-200">
          <div class="flex items-center">
            <div class="flex-shrink-0 p-3 rounded-lg bg-green-500">
              <i class="fas fa-shopping-cart text-white text-2xl"></i>
            </div>
            <div class="{% if LANGUAGE_CODE == 'ar' %}mr-4{% else %}ml-4{% endif %}">
              <h3 class="text-lg font-semibold text-gray-800">{% trans "المبيعات" %}</h3>
              <p class="text-sm text-gray-500">{% trans "إدارة المبيعات والعملاء" %}</p>
            </div>
          </div>
        </div>
        <div class="px-5 py-3">
          <div class="flex justify-between items-center mb-3">
            <span class="text-sm font-medium text-gray-500">{% trans "إجمالي المبيعات" %}</span>
            <span class="text-xl font-bold text-green-600">{{ sales_count|default:"0" }}</span>
          </div>
          <div class="flex justify-between items-center mb-3">
            <span class="text-sm font-medium text-gray-500">{% trans "العملاء" %}</span>
            <span class="text-xl font-bold text-blue-600">{{ customers_count|default:"0" }}</span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-sm font-medium text-gray-500">{% trans "المبيعات الشهرية" %}</span>
            <span class="text-xl font-bold text-emerald-600">{{ monthly_sales_amount|default:"0" }} {% trans "ر.س" %}</span>
          </div>
        </div>
        <div class="bg-gray-50 px-5 py-3 border-t border-gray-200 text-center">
          <a href="{% url 'sales:dashboard' %}" class="text-green-600 hover:text-green-800 font-medium">
            {% trans "فتح لوحة تحكم المبيعات" %} <i class="fas fa-arrow-circle-right {% if LANGUAGE_CODE == 'ar' %}mr-1{% else %}ml-1{% endif %}"></i>
          </a>
        </div>
      </div>
      {% endif %}

      {% if user.is_superuser or primary_role.can_access_purchases %}
      <!-- Purchases Module -->
      <div class="bg-white overflow-hidden shadow rounded-lg transition transform hover:scale-105">
        <div class="p-5 border-b border-gray-200">
          <div class="flex items-center">
            <div class="flex-shrink-0 p-3 rounded-lg bg-amber-500">
              <i class="fas fa-shopping-basket text-white text-2xl"></i>
            </div>
            <div class="{% if LANGUAGE_CODE == 'ar' %}mr-4{% else %}ml-4{% endif %}">
              <h3 class="text-lg font-semibold text-gray-800">{% trans "المشتريات" %}</h3>
              <p class="text-sm text-gray-500">{% trans "إدارة المشتريات والموردين" %}</p>
            </div>
          </div>
        </div>
        <div class="px-5 py-3">
          <div class="flex justify-between items-center mb-3">
            <span class="text-sm font-medium text-gray-500">{% trans "إجمالي المشتريات" %}</span>
            <span class="text-xl font-bold text-amber-600">{{ purchases_count|default:"0" }}</span>
          </div>
          <div class="flex justify-between items-center mb-3">
            <span class="text-sm font-medium text-gray-500">{% trans "الموردين" %}</span>
            <span class="text-xl font-bold text-blue-600">{{ suppliers_count|default:"0" }}</span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-sm font-medium text-gray-500">{% trans "طلبات الشراء المعلقة" %}</span>
            <span class="text-xl font-bold text-red-500">{{ pending_po_count|default:"0" }}</span>
          </div>
        </div>
        <div class="bg-gray-50 px-5 py-3 border-t border-gray-200 text-center">
          <a href="{% url 'purchases:dashboard' %}" class="text-amber-600 hover:text-amber-800 font-medium">
            {% trans "فتح لوحة تحكم المشتريات" %} <i class="fas fa-arrow-circle-right {% if LANGUAGE_CODE == 'ar' %}mr-1{% else %}ml-1{% endif %}"></i>
          </a>
        </div>
      </div>
      {% endif %}

      {% if user.is_superuser or primary_role.can_access_warehouse %}
      <!-- Warehouse Module -->
      <div class="bg-white overflow-hidden shadow rounded-lg transition transform hover:scale-105">
        <div class="p-5 border-b border-gray-200">
          <div class="flex items-center">
            <div class="flex-shrink-0 p-3 rounded-lg bg-teal-500">
              <i class="fas fa-boxes text-white text-2xl"></i>
            </div>
            <div class="{% if LANGUAGE_CODE == 'ar' %}mr-4{% else %}ml-4{% endif %}">
              <h3 class="text-lg font-semibold text-gray-800">{% trans "المستودع" %}</h3>
              <p class="text-sm text-gray-500">{% trans "إدارة المستودعات والمواقع" %}</p>
            </div>
          </div>
        </div>
        <div class="px-5 py-3">
          <div class="flex justify-between items-center mb-3">
            <span class="text-sm font-medium text-gray-500">{% trans "المستودعات" %}</span>
            <span class="text-xl font-bold text-teal-600">{{ warehouses_count|default:"0" }}</span>
          </div>
          <div class="flex justify-between items-center mb-3">
            <span class="text-sm font-medium text-gray-500">{% trans "المواقع" %}</span>
            <span class="text-xl font-bold text-blue-600">{{ locations_count|default:"0" }}</span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-sm font-medium text-gray-500">{% trans "نسبة الإشغال" %}</span>
            <span class="text-xl font-bold text-purple-600">{{ warehouse_utilization|default:"0" }}%</span>
          </div>
        </div>
        <div class="bg-gray-50 px-5 py-3 border-t border-gray-200 text-center">
          <a href="{% url 'warehouse:dashboard' %}" class="text-teal-600 hover:text-teal-800 font-medium">
            {% trans "فتح لوحة تحكم المستودع" %} <i class="fas fa-arrow-circle-right {% if LANGUAGE_CODE == 'ar' %}mr-1{% else %}ml-1{% endif %}"></i>
          </a>
        </div>
      </div>
      {% endif %}

      {% if user.is_superuser or primary_role.can_access_reports %}
      <!-- Reports Module -->
      <div class="bg-white overflow-hidden shadow rounded-lg transition transform hover:scale-105">
        <div class="p-5 border-b border-gray-200">
          <div class="flex items-center">
            <div class="flex-shrink-0 p-3 rounded-lg bg-purple-500">
              <i class="fas fa-chart-bar text-white text-2xl"></i>
            </div>
            <div class="{% if LANGUAGE_CODE == 'ar' %}mr-4{% else %}ml-4{% endif %}">
              <h3 class="text-lg font-semibold text-gray-800">{% trans "التقارير" %}</h3>
              <p class="text-sm text-gray-500">{% trans "تقارير وإحصائيات النظام" %}</p>
            </div>
          </div>
        </div>
        <div class="px-5 py-3">
          <div class="flex justify-between items-center mb-3">
            <span class="text-sm font-medium text-gray-500">{% trans "التقارير المتاحة" %}</span>
            <span class="text-xl font-bold text-purple-600">{{ reports_count|default:"0" }}</span>
          </div>
          <div class="flex justify-between items-center mb-3">
            <span class="text-sm font-medium text-gray-500">{% trans "لوحات المعلومات" %}</span>
            <span class="text-xl font-bold text-blue-600">{{ dashboards_count|default:"0" }}</span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-sm font-medium text-gray-500">{% trans "عناصر التقارير" %}</span>
            <span class="text-xl font-bold text-indigo-600">{{ widgets_count|default:"0" }}</span>
          </div>
        </div>
        <div class="bg-gray-50 px-5 py-3 border-t border-gray-200 text-center">
          <a href="{% url 'reports:report_list' %}" class="text-purple-600 hover:text-purple-800 font-medium">
            {% trans "فتح قائمة التقارير" %} <i class="fas fa-arrow-circle-right {% if LANGUAGE_CODE == 'ar' %}mr-1{% else %}ml-1{% endif %}"></i>
          </a>
        </div>
      </div>
      {% endif %}

      {% if user.is_superuser or primary_role.can_access_setup %}
      <!-- Setup Module -->
      <div class="bg-white overflow-hidden shadow rounded-lg transition transform hover:scale-105">
        <div class="p-5 border-b border-gray-200">
          <div class="flex items-center">
            <div class="flex-shrink-0 p-3 rounded-lg bg-gray-500">
              <i class="fas fa-cogs text-white text-2xl"></i>
            </div>
           
          </div>
        </div>
        <div class="px-5 py-3">
          <div class="flex justify-between items-center mb-3">
            <span class="text-sm font-medium text-gray-500">{% trans "المؤسسات" %}</span>
            <span class="text-xl font-bold text-gray-600">{{ franchises_count|default:"0" }}</span>
          </div>
          <div class="flex justify-between items-center mb-3">
            <span class="text-sm font-medium text-gray-500">{% trans "الشركات" %}</span>
            <span class="text-xl font-bold text-blue-600">{{ companies_count|default:"0" }}</span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-sm font-medium text-gray-500">{% trans "مراكز الخدمة" %}</span>
            <span class="text-xl font-bold text-emerald-600">{{ service_centers_count|default:"0" }}</span>
          </div>
        </div>
        <div class="bg-gray-50 px-5 py-3 border-t border-gray-200 text-center">
          <a href="{% url 'setup:dashboard' %}" class="text-gray-600 hover:text-gray-800 font-medium">
            {% trans "فتح لوحة تحكم الإعدادات" %} <i class="fas fa-arrow-circle-right {% if LANGUAGE_CODE == 'ar' %}mr-1{% else %}ml-1{% endif %}"></i>
          </a>
        </div>
      </div>
      {% endif %}
    </div>

    <!-- System Alerts Section -->
    <div class="mt-8">
      <h2 class="text-lg font-semibold text-gray-800 mb-4">{% trans "تنبيهات النظام" %}</h2>
      <div class="bg-white shadow rounded-lg overflow-hidden">
        {% if alerts %}
          <ul class="divide-y divide-gray-200">
            {% for alert in alerts %}
              <li class="p-4 hover:bg-gray-50">
                <div class="flex items-start">
                  {% if alert.level == 'warning' %}
                    <div class="flex-shrink-0">
                      <i class="fas fa-exclamation-triangle text-amber-500 text-xl"></i>
                    </div>
                  {% elif alert.level == 'danger' %}
                    <div class="flex-shrink-0">
                      <i class="fas fa-exclamation-circle text-red-500 text-xl"></i>
                    </div>
                  {% elif alert.level == 'info' %}
                    <div class="flex-shrink-0">
                      <i class="fas fa-info-circle text-blue-500 text-xl"></i>
                    </div>
                  {% elif alert.level == 'success' %}
                    <div class="flex-shrink-0">
                      <i class="fas fa-check-circle text-green-500 text-xl"></i>
                    </div>
                  {% endif %}
                  <div class="{% if LANGUAGE_CODE == 'ar' %}mr-3{% else %}ml-3{% endif %} flex-1">
                    <p class="text-sm font-medium text-gray-900">{{ alert.title }}</p>
                    <p class="text-sm text-gray-500">{{ alert.message }}</p>
                    <p class="text-xs text-gray-400 mt-1">{{ alert.timestamp|date:"d/m/Y H:i" }}</p>
                  </div>
                </div>
              </li>
            {% endfor %}
          </ul>
        {% else %}
          <div class="p-4 text-center text-gray-500">
            <i class="fas fa-check-circle text-green-500 text-2xl mb-2"></i>
            <p>{% trans "لا توجد تنبيهات حالية" %}</p>
          </div>
        {% endif %}
      </div>
    </div>

    <!-- Recent Activity Section -->
    <div class="mt-8">
      <h2 class="text-lg font-semibold text-gray-800 mb-4">{% trans "النشاط الحديث" %}</h2>
      <div class="bg-white shadow rounded-lg overflow-hidden">
        {% if recent_activities %}
          <ul class="divide-y divide-gray-200">
            {% for activity in recent_activities %}
              <li class="p-4 hover:bg-gray-50">
                <div class="flex items-start">
                  <div class="flex-shrink-0">
                    {% if activity.type == 'inventory' %}
                      <i class="fas fa-warehouse text-blue-500"></i>
                    {% elif activity.type == 'work_order' %}
                      <i class="fas fa-tasks text-indigo-500"></i>
                    {% elif activity.type == 'sale' %}
                      <i class="fas fa-shopping-cart text-green-500"></i>
                    {% elif activity.type == 'purchase' %}
                      <i class="fas fa-shopping-basket text-amber-500"></i>
                    {% else %}
                      <i class="fas fa-history text-gray-500"></i>
                    {% endif %}
                  </div>
                  <div class="{% if LANGUAGE_CODE == 'ar' %}mr-3{% else %}ml-3{% endif %} flex-1">
                    <p class="text-sm font-medium text-gray-900">{{ activity.description }}</p>
                    <p class="text-xs text-gray-400">{{ activity.timestamp|date:"d/m/Y H:i" }}</p>
                  </div>
                </div>
              </li>
            {% endfor %}
          </ul>
        {% else %}
          <div class="p-4 text-center text-gray-500">
            <p>{% trans "لا يوجد نشاط حديث" %}</p>
          </div>
        {% endif %}
      </div>
    </div>
    {% endblock %}
  </main>
  
  <!-- Footer -->
  <footer class="bg-white shadow-inner mt-8 py-4">
    <div class="container mx-auto px-4">
      <p class="text-center text-gray-500 text-sm">
        {% trans "نظام Aftersails" %} &copy; {% now "Y" %}
      </p>
    </div>
  </footer>
  
  <!-- Scripts -->
  <script src="{% static 'js/flowbite.min.js' %}"></script>
  <script src="{% static 'js/scripts.js' %}"></script>
  
  <!-- Include base.html block JS -->
  {% block extra_js %}{% endblock %}
  
  <!-- Global JavaScript for the dashboard -->
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Initialize entity filter functionality
      initEntityFilter();
    });
    
    function initEntityFilter() {
      const toggleFilterBtn = document.getElementById('toggle-filter');
      const filterFormContainer = document.getElementById('filter-form-container');
      
      if (toggleFilterBtn && filterFormContainer) {
        // Toggle filter form visibility
        toggleFilterBtn.addEventListener('click', function() {
          filterFormContainer.classList.toggle('hidden');
          
          if (!filterFormContainer.classList.contains('hidden')) {
            toggleFilterBtn.innerHTML = '<i class="fas fa-times {% if LANGUAGE_CODE == "ar" %}ml-1{% else %}mr-1{% endif %}"></i> ' + toggleFilterBtn.dataset.textClose;
          } else {
            toggleFilterBtn.innerHTML = '<i class="fas fa-sliders-h {% if LANGUAGE_CODE == "ar" %}ml-1{% else %}mr-1{% endif %}"></i> ' + toggleFilterBtn.dataset.textOpen;
          }
        });
        
        // Handle cascading dropdowns
        const franchiseSelect = document.getElementById('franchise_id');
        const companySelect = document.getElementById('company_id');
        const serviceCenterSelect = document.getElementById('service_center_id');
        
        if (franchiseSelect && companySelect) {
          franchiseSelect.addEventListener('change', function() {
            // Reset dependent filters
            if (companySelect) companySelect.value = '';
            if (serviceCenterSelect) serviceCenterSelect.value = '';
            
            // Enable/disable company select based on franchise selection
            if (companySelect) {
              companySelect.disabled = !this.value;
            }
            
            // Auto-submit when franchise changes
            if (this.value) {
              this.form.submit();
            }
          });
        }
        
        if (companySelect && serviceCenterSelect) {
          companySelect.addEventListener('change', function() {
            // Reset service center selection
            if (serviceCenterSelect) serviceCenterSelect.value = '';
            
            // Enable/disable service center select based on company selection
            if (serviceCenterSelect) {
              serviceCenterSelect.disabled = !this.value;
            }
            
            // Auto-submit when company changes
            if (this.value) {
              this.form.submit();
            }
          });
        }
      }
    }
  </script>
  
  {% if not user.is_authenticated %}
  <!-- Login Modal -->
  {% include "core/login_modal.html" %}
  {% endif %}
</body>
</html> 