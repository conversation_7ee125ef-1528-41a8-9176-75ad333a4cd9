pipeline {
    agent any

    stages {
        stage('Docker Compose Build') {
            steps {
                echo 'Building Docker containers using docker-compose...'
                script {
                    try {
                        sh 'docker-compose up --build -d'
                    } catch (Exception e) {
                        currentBuild.result = 'FAILURE'
                        error("Build failed: ${e.message}")
                    }
                }
            }
        }
    }

    post {
        success {
            echo 'Pipeline succeeded!'
            office365ConnectorSend message: "Build succeeded for ${env.JOB_NAME} (${env.BUILD_NUMBER})", status: 'Success', webhookUrl: 'https://ghabbour.webhook.office.com/webhookb2/a5005bdf-46f4-4959-b361-e93562a8a37d@fd2a060a-0596-4843-be2e-8e2abd3e1dc0/JenkinsCI/4df7f5f095fa44499c49cecf7542fc8f/34200590-9834-4629-8f26-453ad10da378'
        }

        failure {
            echo 'Pipeline failed!'
            script {
                def failureReason = currentBuild.rawBuild.getLog(1000).join('\n')
                office365ConnectorSend message: "Build failed for ${env.JOB_NAME} (${env.BUILD_NUMBER})\nFailure Reason:\n${failureReason}", status: 'Failure', webhookUrl: 'https://ghabbour.webhook.office.com/webhookb2/a5005bdf-46f4-4959-b361-e93562a8a37d@fd2a060a-0596-4843-be2e-8e2abd3e1dc0/JenkinsCI/4df7f5f095fa44499c49cecf7542fc8f/34200590-9834-4629-8f26-453ad10da378'
            }
        }
    }
}
