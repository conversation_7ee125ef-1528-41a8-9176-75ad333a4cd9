import os
import sys
import django
import random
from datetime import timedelta, datetime

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

# Import Django models
from django.db import transaction
from django.utils import timezone
from django.contrib.auth.models import User

# Import organization models
from website.models import (
    Franchise, Company, ServiceCenter, VehicleHistory, 
    Vehicle, UserActivity, ServiceCenterManager
)

class OrganizationSetupGenerator:
    def __init__(self):
        print("Organization setup generator initialized")
    
    def generate_franchise_data(self):
        print("\nPopulating franchises...")
        
        # Check if we already have franchises
        count = Franchise.objects.count()
        if count > 0:
            print(f"Franchise table already has {count} records. Skipping.")
            return
        
        # Get company IDs for foreign key
        companies = Company.objects.all()[:4]
        if not companies:
            print("No companies found. Please ensure Company table has data.")
            return
        
        # Insert franchise data
        franchise_data = [
            {
                'name': 'أفتر سيلز للصيانة الرئيسية',
                'address': 'شارع التحرير، القاهرة',
                'phone': '+20 ************',
                'email': '<EMAIL>',
                'is_active': True,
                'company': companies[0]
            },
            {
                'name': 'الدلتا للصيانة',
                'address': 'شارع الجلاء، المنصورة',
                'phone': '+20 ************',
                'email': '<EMAIL>',
                'is_active': True,
                'company': companies[min(1, len(companies)-1)]
            },
            {
                'name': 'الإسكندرية أوتو سيرفيس',
                'address': 'طريق الكورنيش، الإسكندرية',
                'phone': '+20 ************',
                'email': '<EMAIL>',
                'is_active': True,
                'company': companies[min(2, len(companies)-1)]
            },
            {
                'name': 'الصعيد للخدمات الفنية',
                'address': 'شارع أسيوط الرئيسي، أسيوط',
                'phone': '+20 ************',
                'email': '<EMAIL>',
                'is_active': True,
                'company': companies[min(3, len(companies)-1)]
            }
        ]
        
        franchises_created = 0
        
        for data in franchise_data:
            try:
                Franchise.objects.create(**data)
                franchises_created += 1
                print(f"Created franchise: {data['name']}")
            except Exception as e:
                print(f"Error creating franchise '{data['name']}': {e}")
        
        print(f"Created {franchises_created} franchises")
    
    def generate_service_center_managers(self):
        print("\nPopulating service center managers...")
        
        # Check if we already have service center managers
        count = ServiceCenterManager.objects.count()
        if count > 0:
            print(f"ServiceCenterManager table already has {count} records. Skipping.")
            return
        
        # Get service centers
        service_centers = ServiceCenter.objects.all()
        if not service_centers:
            print("No service centers found. Please ensure ServiceCenter table has data.")
            return
        
        # Get or create users for managers
        users = User.objects.all()[:10]
        if not users or len(users) < 3:
            print("Not enough users found. Creating some users...")
            
            # Create some manager users if needed
            manager_users = [
                ('manager1', '<EMAIL>', 'Manager1'),
                ('manager2', '<EMAIL>', 'Manager2'),
                ('manager3', '<EMAIL>', 'Manager3')
            ]
            
            for username, email, password in manager_users:
                # Check if user exists
                if not User.objects.filter(username=username).exists():
                    User.objects.create_user(
                        username=username,
                        email=email,
                        password=password,
                        is_staff=True
                    )
            
            # Get users again
            users = User.objects.all()[:10]
        
        managers_created = 0
        
        for service_center in service_centers:
            # Skip some centers randomly to avoid creating managers for all
            if random.random() > 0.7:
                continue
                
            user = random.choice(users)
            
            try:
                # Check if model has name field
                has_name_field = hasattr(ServiceCenterManager, 'name')
                
                if has_name_field:
                    ServiceCenterManager.objects.create(
                        user=user,
                        service_center=service_center,
                        name=f"مدير {service_center.name}",
                        phone=f"+20 10{random.randint(10000000, 99999999)}",
                        email=f"manager_{service_center.id}@aftersales.com",
                        active=True
                    )
                else:
                    # Create without name field
                    ServiceCenterManager.objects.create(
                        user=user,
                        service_center=service_center
                    )
                
                managers_created += 1
                print(f"Created manager for service center: {service_center.name}")
            except Exception as e:
                print(f"Error creating manager for {service_center.name}: {e}")
        
        print(f"Created {managers_created} service center managers")
    
    def generate_vehicle_history(self):
        print("\nPopulating vehicle history...")
        
        # Check if we already have vehicle history
        count = VehicleHistory.objects.count()
        if count > 0:
            print(f"VehicleHistory table already has {count} records. Skipping.")
            return
        
        # Get vehicles
        vehicles = Vehicle.objects.all()[:50]
        if not vehicles:
            print("No vehicles found. Please ensure Vehicle table has data.")
            return
        
        histories_created = 0
        
        # Event types for vehicle history
        event_types = ['service', 'accident', 'purchase', 'repair', 'inspection']
        
        for vehicle in vehicles:
            # Create 1-3 history records per vehicle
            for i in range(random.randint(1, 3)):
                # Random date in last 2 years
                service_date = datetime.now().date() - timedelta(days=random.randint(1, 730))
                next_service_date = service_date + timedelta(days=random.randint(90, 180))
                
                mileage = random.randint(1000, 100000)
                action = random.choice(event_types)
                description = f"سجل {action} رقم {i+1} للمركبة {vehicle.make} {vehicle.model}"
                
                try:
                    VehicleHistory.objects.create(
                        vehicle=vehicle,
                        service_date=service_date,
                        next_service_date=next_service_date,
                        mileage=mileage,
                        action=action,
                        description=description,
                        notes="ملاحظات إضافية"
                    )
                    histories_created += 1
                except Exception as e:
                    print(f"Error creating vehicle history for {vehicle.id}: {e}")
        
        print(f"Created {histories_created} vehicle history records")
    
    def generate_user_activities(self):
        print("\nPopulating user activities...")
        
        # Check if we already have user activities
        count = UserActivity.objects.count()
        if count > 0:
            print(f"UserActivity table already has {count} records. Skipping.")
            return
        
        # Get users
        users = User.objects.all()
        if not users:
            print("No users found. Please ensure User table has data.")
            return
        
        # Types of activities
        activities = [
            "تسجيل دخول", "تسجيل خروج", "إنشاء طلب صيانة", "تحديث بيانات عميل",
            "طباعة فاتورة", "تحديث حالة طلب صيانة", "إضافة قطع غيار للمخزون",
            "نقل قطع غيار بين المخازن", "إنشاء عميل جديد", "إضافة مركبة جديدة"
        ]
        
        activities_created = 0
        
        # Create 5-10 activities per user
        for user in users:
            # Generate 5-10 random activities
            for _ in range(random.randint(5, 10)):
                # Random timestamp in last 30 days
                timestamp = timezone.now() - timedelta(days=random.randint(0, 30), 
                                              hours=random.randint(0, 23), 
                                              minutes=random.randint(0, 59))
                activity = random.choice(activities)
                ip_address = f"192.168.1.{random.randint(1, 255)}"
                
                try:
                    UserActivity.objects.create(
                        user=user,
                        activity=activity,
                        timestamp=timestamp,
                        ip_address=ip_address,
                        user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
                    )
                    activities_created += 1
                except Exception as e:
                    print(f"Error creating user activity for {user.username}: {e}")
        
        print(f"Created {activities_created} user activities")
    
    @transaction.atomic
    def run(self):
        print("Starting organization tables population...")
        
        # Display tables with record counts
        tables = {
            'Franchise': Franchise,
            'ServiceCenterManager': ServiceCenterManager,
            'UserActivity': UserActivity,
            'VehicleHistory': VehicleHistory
        }
        
        print("Current table record counts:")
        for name, model in tables.items():
            count = model.objects.count()
            print(f"{name}: {count} records")
        
        # Generate data for empty tables
        self.generate_franchise_data()
        self.generate_service_center_managers()
        self.generate_user_activities()
        self.generate_vehicle_history()
        
        # Show updated record counts
        print("\nUpdated table record counts:")
        for name, model in tables.items():
            count = model.objects.count()
            print(f"{name}: {count} records")
        
        print("\nOrganization tables population completed!")

if __name__ == "__main__":
    generator = OrganizationSetupGenerator()
    generator.run() 