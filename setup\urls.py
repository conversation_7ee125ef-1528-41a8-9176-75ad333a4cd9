from django.urls import path, include
from rest_framework.routers import Default<PERSON>out<PERSON>
from . import views

# Create a router for API viewsets
router = DefaultRouter()
router.register(r'api/service-center-makes-models', views.ServiceCenterMakeModelViewSet)
router.register(r'api/vehicle-makes', views.VehicleMakeViewSet)
router.register(r'api/vehicle-models', views.VehicleModelViewSet)

app_name = 'setup'

urlpatterns = [
    # Franchise URLs
    path('franchises/', views.FranchiseListView.as_view(), name='franchise_list'),
    path('franchises/<uuid:pk>/', views.FranchiseDetailView.as_view(), name='franchise_detail'),
    
    # Company URLs
    path('companies/', views.CompanyListView.as_view(), name='company_list'),
    path('companies/<uuid:pk>/', views.CompanyDetailView.as_view(), name='company_detail'),
    
    # Service Center URLs
    path('service-centers/', views.ServiceCenterListView.as_view(), name='service_center_list'),
    path('service-centers/<uuid:pk>/', views.ServiceCenterDetailView.as_view(), name='service_center_detail'),
    
    # Vehicle URLs
    path('vehicles/', views.VehicleListView.as_view(), name='vehicle_list'),
    path('vehicles/<uuid:pk>/', views.VehicleDetailView.as_view(), name='vehicle_detail'),
    path('vehicles/<uuid:vehicle_pk>/service-history/', views.VehicleServiceHistoryListView.as_view(), name='vehicle_service_history'),
    
    # Vehicle Ownership Transfer URLs
    path('vehicles/transfer/initiate/', views.InitiateVehicleTransferView.as_view(), name='initiate_vehicle_transfer'),
    path('vehicles/transfer/<uuid:pk>/', views.VehicleTransferDetailView.as_view(), name='vehicle_transfer_detail'),
    
    # Dashboard
    path('', views.SetupDashboardView.as_view(), name='dashboard'),
    
    # API endpoints
    path('', include(router.urls)),
    path('api/service-centers/<uuid:pk>/supported-makes/', views.service_center_supported_makes, name='service_center_supported_makes'),
    path('api/vehicle-makes-models/', views.vehicle_makes_models, name='vehicle_makes_models'),
    path('api/check-vehicle-support/', views.check_service_center_supports_vehicle, name='check_vehicle_support'),
    path('api/vehicle-models-by-make/<uuid:make_id>/', views.vehicle_models_by_make, name='vehicle_models_by_make'),
] 