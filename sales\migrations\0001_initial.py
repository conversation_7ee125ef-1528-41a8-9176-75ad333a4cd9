# Generated by Django 4.2.20 on 2025-05-07 10:01

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('inventory', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Customer',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('name', models.CharField(max_length=255, verbose_name='Name')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='Email')),
                ('phone', models.CharField(blank=True, max_length=50, verbose_name='Phone')),
                ('address', models.TextField(blank=True, verbose_name='Address')),
                ('is_active', models.BooleanField(default=True, verbose_name='Active')),
            ],
            options={
                'verbose_name': 'Customer',
                'verbose_name_plural': 'Customers',
            },
        ),
        migrations.CreateModel(
            name='SalesOrder',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('order_number', models.CharField(max_length=50, unique=True, verbose_name='Order Number')),
                ('order_date', models.DateField(verbose_name='Order Date')),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('confirmed', 'Confirmed'), ('shipped', 'Shipped'), ('delivered', 'Delivered'), ('cancelled', 'Cancelled'), ('returned', 'Returned')], default='draft', max_length=20, verbose_name='Status')),
                ('shipping_address', models.TextField(blank=True, verbose_name='Shipping Address')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('total_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Total Amount')),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='sales_orders', to='sales.customer', verbose_name='Customer')),
            ],
            options={
                'verbose_name': 'Sales Order',
                'verbose_name_plural': 'Sales Orders',
                'ordering': ['-order_date'],
            },
        ),
        migrations.CreateModel(
            name='SalesOrderItem',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('quantity', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Quantity')),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Unit Price')),
                ('discount', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Discount')),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='sales_items', to='inventory.item', verbose_name='Item')),
                ('sales_order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='sales.salesorder', verbose_name='Sales Order')),
            ],
            options={
                'verbose_name': 'Sales Order Item',
                'verbose_name_plural': 'Sales Order Items',
                'unique_together': {('sales_order', 'item')},
            },
        ),
        migrations.CreateModel(
            name='SalesReturn',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('return_number', models.CharField(max_length=50, unique=True, verbose_name='Return Number')),
                ('return_date', models.DateField(verbose_name='Return Date')),
                ('reason', models.TextField(verbose_name='Reason')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('sales_order', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='returns', to='sales.salesorder', verbose_name='Sales Order')),
            ],
            options={
                'verbose_name': 'Sales Return',
                'verbose_name_plural': 'Sales Returns',
                'ordering': ['-return_date'],
            },
        ),
        migrations.CreateModel(
            name='SalesReturnItem',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('quantity', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Quantity')),
                ('sales_order_item', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='returns', to='sales.salesorderitem', verbose_name='Sales Order Item')),
                ('sales_return', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='sales.salesreturn', verbose_name='Sales Return')),
            ],
            options={
                'verbose_name': 'Sales Return Item',
                'verbose_name_plural': 'Sales Return Items',
            },
        ),
    ]
