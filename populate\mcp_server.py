#!/usr/bin/env python3
import os
import sys
import json
import logging
import psycopg2
from psycopg2.extras import RealDictCursor
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    stream=sys.stderr
)
logger = logging.getLogger(__name__)

load_dotenv()

def get_db_connection():
    """Create a database connection using environment variables."""
    try:
        logger.info("Attempting database connection...")
        conn = psycopg2.connect(
            dbname=os.environ.get('SQL_DATABASE', 'postgres'),
            user=os.environ.get('SQL_USER', 'postgres'),
            password=os.environ.get('SQL_PASSWORD', 'postgrespw'),
            host=os.environ.get('SQL_HOST', '**************'),
            port=os.environ.get('SQL_PORT', '8136'),
            cursor_factory=RealDictCursor
        )
        logger.info("Database connection successful")
        return conn
    except Exception as e:
        logger.error(f"Database connection failed: {str(e)}")
        raise

def execute_query(query, params=None):
    """Execute a read-only query and return results."""
    try:
        conn = get_db_connection()
        cur = conn.cursor()
        logger.info(f"Executing query: {query}")
        cur.execute(query, params)
        results = cur.fetchall()
        return {'status': 'success', 'data': results}
    except Exception as e:
        logger.error(f"Query execution failed: {str(e)}")
        return {'status': 'error', 'message': str(e)}
    finally:
        if 'cur' in locals():
            cur.close()
        if 'conn' in locals():
            conn.close()

def get_schema():
    """Get database schema information."""
    try:
        conn = get_db_connection()
        cur = conn.cursor()
        logger.info("Retrieving database schema...")
        
        # Get tables
        cur.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public'
        """)
        tables = cur.fetchall()
        
        schema = {}
        for table in tables:
            table_name = table['table_name']
            # Get columns for each table
            cur.execute("""
                SELECT column_name, data_type, is_nullable
                FROM information_schema.columns
                WHERE table_schema = 'public'
                AND table_name = %s
            """, (table_name,))
            columns = cur.fetchall()
            schema[table_name] = columns
            
        logger.info("Schema retrieval successful")
        return {'status': 'success', 'data': schema}
    except Exception as e:
        logger.error(f"Schema retrieval failed: {str(e)}")
        return {'status': 'error', 'message': str(e)}
    finally:
        if 'cur' in locals():
            cur.close()
        if 'conn' in locals():
            conn.close()

def handle_request(request):
    """Handle incoming MCP requests."""
    try:
        logger.info(f"Received request: {request}")
        data = json.loads(request)
        command = data.get('command')
        params = data.get('params', {})
        
        if command == 'query':
            return execute_query(params.get('query'), params.get('params'))
        elif command == 'schema':
            return get_schema()
        else:
            msg = f'Unknown command: {command}'
            logger.error(msg)
            return {'status': 'error', 'message': msg}
    except Exception as e:
        logger.error(f"Request handling failed: {str(e)}")
        return {'status': 'error', 'message': str(e)}

def announce_tools():
    """Announce available tools to Cursor."""
    tools = {
        "version": "1.0.0",
        "tools": [
            {
                "name": "query",
                "description": "Execute a read-only SQL query against the PostgreSQL database",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "query": {
                            "type": "string",
                            "description": "The SQL query to execute"
                        },
                        "params": {
                            "type": "object",
                            "description": "Optional query parameters",
                            "optional": True
                        }
                    },
                    "required": ["query"]
                }
            },
            {
                "name": "schema",
                "description": "Get the database schema information",
                "parameters": {
                    "type": "object",
                    "properties": {}
                }
            }
        ]
    }
    print(json.dumps(tools), flush=True)
    logger.info("Tool definitions announced")

def main():
    """Main MCP server loop."""
    logger.info("MCP server starting...")
    
    # Announce tools immediately on startup
    announce_tools()
    
    while True:
        try:
            line = sys.stdin.readline()
            if not line:
                logger.info("Input stream closed, shutting down")
                break
                
            response = handle_request(line.strip())
            print(json.dumps(response), flush=True)
            
        except Exception as e:
            logger.error(f"Main loop error: {str(e)}")
            print(json.dumps({'status': 'error', 'message': str(e)}), flush=True)

if __name__ == '__main__':
    main() 