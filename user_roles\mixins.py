"""
Mixins for the user_roles app.
"""
import logging
from django.db.models import Q

logger = logging.getLogger(__name__)

class RoleBasedQuerySetMixin:
    """
    Mixin that filters querysets based on user role scope.
    
    This mixin should be used in views that need to filter data based on user role.
    It handles different role levels (service center, company, franchise) and
    properly applies the appropriate filters to the queryset.
    
    Usage:
        class MyListView(LoginRequiredMixin, RoleBasedQuerySetMixin, ListView):
            model = MyModel
            
            def get_queryset(self):
                queryset = super().get_queryset()
                return self.get_role_filtered_queryset(queryset, 'service_center')
    """
    
    def get_role_filtered_queryset(self, queryset, entity_field_path=None):
        """
        Filter queryset based on user role scope.
        
        Args:
            queryset: The base queryset to filter
            entity_field_path: The path to the entity field to filter on
                              (e.g., 'service_center', 'service_center__company')
        
        Returns:
            Filtered queryset based on user role scope
        """
        user = self.request.user
        
        # Superusers see everything
        if user.is_superuser:
            return queryset
        
        # If no user roles, return empty queryset
        if not hasattr(user, 'user_roles'):
            logger.warning(f"User {user.username} has no user_roles attribute")
            return queryset.none()
        
        # Get active user roles
        user_roles = user.user_roles.filter(is_active=True)
        if not user_roles.exists():
            logger.warning(f"User {user.username} has no active roles")
            return queryset.none()
        
        # Get primary role or first role
        primary_role = next((r for r in user_roles if r.is_primary), None) or user_roles.first()
        
        # Apply filters based on role scope
        filters = Q()
        
        # Service Center scope
        if primary_role.service_center and entity_field_path:
            if entity_field_path == 'service_center':
                filters |= Q(service_center_id=primary_role.service_center.id)
            else:
                filters |= Q(**{f"{entity_field_path}_id": primary_role.service_center.id})
        
        # Company scope
        elif primary_role.company and entity_field_path:
            if 'company' in entity_field_path:
                # Direct company field
                field_name = entity_field_path.split('__')[-1]
                filters |= Q(**{f"{field_name}_id": primary_role.company.id})
            elif 'service_center' in entity_field_path:
                # Filter service centers in this company
                if entity_field_path == 'service_center':
                    filters |= Q(service_center__company_id=primary_role.company.id)
                else:
                    filters |= Q(**{f"{entity_field_path}__company_id": primary_role.company.id})
        
        # Franchise scope
        elif primary_role.franchise and entity_field_path:
            if 'franchise' in entity_field_path:
                # Direct franchise field
                field_name = entity_field_path.split('__')[-1]
                filters |= Q(**{f"{field_name}_id": primary_role.franchise.id})
            elif 'company' in entity_field_path:
                # Filter companies in this franchise
                if entity_field_path == 'company':
                    filters |= Q(company__franchise_id=primary_role.franchise.id)
                else:
                    filters |= Q(**{f"{entity_field_path}__franchise_id": primary_role.franchise.id})
            elif 'service_center' in entity_field_path:
                # Filter service centers in companies in this franchise
                if entity_field_path == 'service_center':
                    filters |= Q(service_center__company__franchise_id=primary_role.franchise.id)
                else:
                    filters |= Q(**{f"{entity_field_path}__company__franchise_id": primary_role.franchise.id})
        
        # If we have filters, apply them
        if filters:
            return queryset.filter(filters)
        
        # If no filters but we have a role, default to empty
        # This is a safety measure to prevent accidental data leakage
        logger.warning(f"User {user.username} has role {primary_role} but no filter could be applied for {entity_field_path}")
        return queryset.none()
    
    def get_available_service_centers(self):
        """
        Get service centers available to the current user based on their role.
        
        Returns:
            QuerySet of ServiceCenter objects
        """
        from setup.models import ServiceCenter
        user = self.request.user
        
        # Superusers see all service centers
        if user.is_superuser:
            return ServiceCenter.objects.all()
        
        # If no user roles, return empty queryset
        if not hasattr(user, 'user_roles'):
            return ServiceCenter.objects.none()
        
        # Get active user roles
        user_roles = user.user_roles.filter(is_active=True)
        if not user_roles.exists():
            return ServiceCenter.objects.none()
        
        # Get primary role or first role
        primary_role = next((r for r in user_roles if r.is_primary), None) or user_roles.first()
        
        # Service Center scope
        if primary_role.service_center:
            return ServiceCenter.objects.filter(id=primary_role.service_center.id)
        
        # Company scope
        elif primary_role.company:
            return ServiceCenter.objects.filter(company_id=primary_role.company.id)
        
        # Franchise scope
        elif primary_role.franchise:
            return ServiceCenter.objects.filter(company__franchise_id=primary_role.franchise.id)
        
        # Default to empty queryset
        return ServiceCenter.objects.none() 