import os
import sys
import django
import importlib.util
import time

# Add the parent directory to the Python path so imports work correctly
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

def load_module_from_file(file_path, module_name):
    """Load a Python module from a file path."""
    spec = importlib.util.spec_from_file_location(module_name, file_path)
    module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(module)
    return module

class ComprehensiveDataGenerator:
    def __init__(self):
        self.scripts_dir = os.path.dirname(os.path.abspath(__file__))
        print(f"Comprehensive Data Generator initialized. Scripts directory: {self.scripts_dir}")
        
    def run_generator(self, script_name, generator_class=None):
        """Run a specific generator script with timing."""
        script_path = os.path.join(self.scripts_dir, script_name)
        
        if not os.path.exists(script_path):
            print(f"⚠️ Script not found: {script_path}")
            return False
        
        print(f"\n{'='*80}")
        print(f"Running {script_name}...")
        print(f"{'='*80}")
        
        start_time = time.time()
        try:
            # If no specific generator class is provided, just import and run the script
            if not generator_class:
                module = load_module_from_file(script_path, script_name.replace('.py', ''))
                if hasattr(module, 'main'):
                    module.main()
            else:
                # Import the module and run the specified generator class
                module = load_module_from_file(script_path, script_name.replace('.py', ''))
                if hasattr(module, generator_class):
                    generator = getattr(module, generator_class)()
                    generator.run()
                else:
                    print(f"⚠️ Generator class {generator_class} not found in {script_name}")
                    return False
            
            elapsed_time = time.time() - start_time
            print(f"\n✅ {script_name} completed successfully in {elapsed_time:.2f} seconds\n")
            return True
        
        except Exception as e:
            elapsed_time = time.time() - start_time
            print(f"\n❌ Error running {script_name}: {str(e)}")
            print(f"Failed after {elapsed_time:.2f} seconds\n")
            return False
    
    def generate_all(self):
        """Run all data generation scripts in the correct order."""
        total_start_time = time.time()
        
        print("\n=== 🚀 Starting Comprehensive Data Generation Process ===\n")
        
        # Record success/failure for each step
        results = {}
        
        # === Phase 1: Core Setup Data ===
        print("\n=== Phase 1: Core Setup Data ===\n")
        
        # Step 1: Monkey patch vehicle model
        results["monkey_patch_vehicle.py"] = self.run_generator("monkey_patch_vehicle.py")
        
        # Step 2: Generate organization data
        results["add_organization_data.py"] = self.run_generator("add_organization_data.py", "OrganizationDataGenerator")
        
        # Step 3: Generate franchise data
        results["add_franchise_data.py"] = self.run_generator("add_franchise_data.py", "FranchiseGenerator")
        
        # Step 4: Generate vehicles
        results["add_simple_vehicles.py"] = self.run_generator("add_simple_vehicles.py", "SimpleVehicleGenerator")
        
        # Step 5: Create service centers
        results["add_service_center_data.py"] = self.run_generator("add_service_center_data.py", "ServiceCenterGenerator") 
        
        # Step 6: Create service center make/model associations
        results["add_service_center_make_models.py"] = self.run_generator("add_service_center_make_models.py", "ServiceCenterMakeModelGenerator")
        
        # Step 7: Generate vehicle history
        results["add_vehicle_history.py"] = self.run_generator("add_vehicle_history.py", "VehicleHistoryGenerator")
        
        # === Phase 2: Inventory and Warehouse ===
        print("\n=== Phase 2: Inventory and Warehouse ===\n")
        
        # Step 8: Generate inventory data
        results["add_inventory_data.py"] = self.run_generator("add_inventory_data.py", "InventoryDataGenerator")
        
        # Step 9: Generate inventory documents and fix operation compatibilities
        results["add_item_documents.py"] = self.run_generator("add_item_documents.py", "ItemDocumentGenerator")
        
        # Step 10: Generate work order types
        results["add_work_order_types.py"] = self.run_generator("add_work_order_types.py", "WorkOrderTypeGenerator")
        
        # Step 11: Generate stock transactions
        results["add_stock_transactions.py"] = self.run_generator("add_stock_transactions.py", "StockTransactionGenerator")
        
        # Step 12: Generate warehouse data
        results["add_warehouse_data.py"] = self.run_generator("add_warehouse_data.py", "WarehouseGenerator")
        
        # === Phase 3: Sales and Purchases ===
        print("\n=== Phase 3: Sales and Purchases ===\n")
        
        # Step 13: Generate purchases data
        results["add_purchases_data.py"] = self.run_generator("add_purchases_data.py", "PurchasesGenerator")
        
        # Step 14: Generate sales data
        results["add_sales_data.py"] = self.run_generator("add_sales_data.py", "SalesGenerator")
        
        # === Phase 4: Work Orders ===
        print("\n=== Phase 4: Work Orders ===\n")
        
        # Step 15: Generate work orders data
        results["add_work_orders.py"] = self.run_generator("add_work_orders.py", "WorkOrderGenerator")
        
        # === Phase 5: User Activity and Roles ===
        print("\n=== Phase 5: User Activity and Roles ===\n")
        
        # Step 16: Generate user activity
        results["add_user_activity.py"] = self.run_generator("add_user_activity.py", "UserActivityGenerator")
        
        # Step 17: Generate basic user roles and permissions
        results["add_user_roles.py"] = self.run_generator("add_user_roles.py", "UserRolesGenerator")
        
        # Step 18: Generate comprehensive user roles data
        results["generate_user_roles_data.py"] = self.run_generator("generate_user_roles_data.py", "UserRolesDataGenerator")
        
        # Calculate total time
        total_end_time = time.time()
        total_time = total_end_time - total_start_time
        
        # Display summary
        print("\n=== 📋 Data Generation Summary ===\n")
        
        success_count = sum(1 for result in results.values() if result)
        failure_count = len(results) - success_count
        
        for script, success in results.items():
            status = "✅ Success" if success else "❌ Failed"
            print(f"{script}: {status}")
        
        print(f"\nTotal scripts: {len(results)}")
        print(f"Successful: {success_count}")
        print(f"Failed: {failure_count}")
        print(f"Total time: {total_time:.2f} seconds")
        
        if failure_count == 0:
            print("\n=== 🎉 All data generation scripts completed successfully! ===\n")
        else:
            print(f"\n=== ⚠️ {failure_count} scripts failed during the data generation process ===\n")
        
        return success_count, failure_count

if __name__ == "__main__":
    generator = ComprehensiveDataGenerator()
    generator.generate_all() 
 