import os
import sys
import django
import random
from django.db import transaction
from decimal import Decimal
from faker import Faker
from datetime import datetime, timedelta
from django.utils import timezone

# Add the parent directory to sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

# Import necessary models
from work_orders.models import (
    WorkOrderType, BillOfMaterials, BOMItem, MaintenanceSchedule,
    ScheduleOperation, OperationPart, WorkOrder, WorkOrderOperation, 
    WorkOrderMaterial
)
from inventory.models import Item
from setup.models import ServiceCenter, Vehicle, Customer

# Initialize Faker
fake = Faker('ar_EG')  # Using Egyptian Arabic locale for Egyptian market specifics

class WorkOrdersDataGenerator:
    """Generate work orders data for the Aftersails system."""
    
    def __init__(self):
        # Use an existing tenant_id
        from setup.models import Customer, ServiceCenter
        
        # Try to get tenant_id from different sources
        tenant_id = None
        
        # Try Customer first
        customer = Customer.objects.first()
        if customer:
            tenant_id = customer.tenant_id
            print(f"Using customer tenant ID: {tenant_id}")
        
        # If no customer tenant, try ServiceCenter
        if not tenant_id:
            service_center = ServiceCenter.objects.first()
            if service_center:
                tenant_id = service_center.tenant_id
                print(f"Using service center tenant ID: {tenant_id}")
        
        # Fallback to default
        if not tenant_id:
            tenant_id = "default_tenant"
            
        self.tenant_id = tenant_id
        print(f"Using tenant ID: {self.tenant_id}")
    
    @transaction.atomic
    def generate_work_orders_data(self):
        """Generate work orders data with Egyptian specifics."""
        print("Generating work orders data...")
        
        # Create work order types if they don't exist
        work_order_types = self._get_or_create_work_order_types()
        
        # Create bills of materials
        bills_of_materials = self._create_bills_of_materials()
        
        # Create maintenance schedules
        maintenance_schedules = self._create_maintenance_schedules(work_order_types)
        
        # Create work orders
        self._create_work_orders(work_order_types, bills_of_materials, maintenance_schedules)
    
    def _get_or_create_work_order_types(self):
        """Get existing work order types or create new ones if they don't exist."""
        # Check if we already have work order types (created by operation_compatibilities.py)
        existing_types = list(WorkOrderType.objects.filter(tenant_id=self.tenant_id))
        if existing_types:
            print(f"Using {len(existing_types)} existing Work Order Types")
            return existing_types
        
        # Create work order types if none exist
        work_order_types_data = [
            {"name": "صيانة دورية", "description": "صيانة دورية للسيارة"},
            {"name": "إصلاح محرك", "description": "إصلاح أعطال المحرك"},
            {"name": "ضبط كهرباء", "description": "ضبط وإصلاح النظام الكهربائي"},
            {"name": "إصلاح فرامل", "description": "إصلاح وضبط نظام الفرامل"},
            {"name": "فحص شامل", "description": "فحص شامل للسيارة"},
            {"name": "تغيير زيت", "description": "تغيير زيت المحرك والفلاتر"},
            {"name": "إصلاح تكييف", "description": "إصلاح وشحن نظام التكييف"},
            {"name": "إصلاح عفشة", "description": "إصلاح نظام التعليق"},
            {"name": "تصليح هيكل", "description": "إصلاح وصيانة هيكل السيارة"}
        ]
        
        work_order_types = []
        for type_data in work_order_types_data:
            wo_type, created = WorkOrderType.objects.get_or_create(
                tenant_id=self.tenant_id,
                name=type_data["name"],
                defaults={
                    "description": type_data["description"],
                    "is_active": True
                }
            )
            work_order_types.append(wo_type)
            
            if created:
                print(f"Created Work Order Type: {wo_type.name}")
            else:
                print(f"Using existing Work Order Type: {wo_type.name}")
        
        return work_order_types
    
    def _create_bills_of_materials(self):
        """Create bill of materials records."""
        # Get all items
        items = list(Item.objects.filter(tenant_id=self.tenant_id))
        if not items:
            print("No items found. Cannot create bills of materials.")
            return []
        
        # Create BOMs for certain items (which could be "assemblies")
        # Typically these might be service packages or assembled products
        assemblies_data = [
            {
                "name": "طقم الصيانة الشاملة",
                "description": "مجموعة قطع الغيار اللازمة للصيانة الكاملة",
            },
            {
                "name": "طقم الفرامل الأمامي",
                "description": "طقم كامل للفرامل الأمامية",
            },
            {
                "name": "طقم الفرامل الخلفي",
                "description": "طقم كامل للفرامل الخلفية",
            },
            {
                "name": "طقم تبديل الزيت",
                "description": "طقم كامل لتغيير الزيت والفلاتر",
            },
            {
                "name": "طقم صيانة نظام التبريد",
                "description": "طقم كامل لصيانة نظام التبريد",
            }
        ]
        
        bills_of_materials = []
        
        # For each assembly, create a BOM with component items
        for assembly_data in assemblies_data:
            # Select a random item to be the "finished" item
            finished_item = random.choice(items)
            
            # Try to get or create BOM
            try:
                bom, created = BillOfMaterials.objects.get_or_create(
                    tenant_id=self.tenant_id,
                    name=assembly_data["name"],
                    version="1.0",
                    defaults={
                        "description": assembly_data["description"],
                        "finished_item": finished_item,
                        "is_active": True
                    }
                )
                
                bills_of_materials.append(bom)
                
                if created:
                    # Get available items for components (excluding the finished item)
                    available_items = [item for item in items if item != finished_item]
                    
                    if not available_items:
                        continue
                        
                    # Add 3-7 component items to the BOM
                    num_components = random.randint(3, 7)
                    selected_components = random.sample(
                        available_items,
                        min(num_components, len(available_items))
                    )
                    
                    for i, component_item in enumerate(selected_components):
                        # Determine unit of measure
                        unit_of_measure = component_item.unit_of_measure if hasattr(component_item, 'unit_of_measure') else "قطعة"
                        
                        # Create BOM item
                        BOMItem.objects.create(
                            tenant_id=self.tenant_id,
                            bom=bom,
                            item=component_item,
                            quantity=Decimal(str(random.randint(1, 5))),
                            unit_of_measure=unit_of_measure,
                            is_optional=random.random() > 0.8,  # 20% chance of being optional
                            sequence=(i+1) * 10
                        )
                    
                    print(f"Created Bill of Materials: {bom.name} with {len(selected_components)} components")
                else:
                    print(f"Using existing Bill of Materials: {bom.name}")
            except Exception as e:
                print(f"Error creating bill of materials '{assembly_data['name']}': {e}")
            
        return bills_of_materials
    
    def _create_maintenance_schedules(self, work_order_types):
        """Create maintenance schedule records."""
        # Common Egyptian vehicle makes and models
        vehicle_makes = ['تويوتا', 'هيونداي', 'نيسان', 'شيفروليه', 'كيا', 'مرسيدس', 'بي إم دبليو']
        vehicle_models = {
            'تويوتا': ['كورولا', 'يارس', 'كامري', 'لاند كروزر'],
            'هيونداي': ['النترا', 'توسان', 'اكسنت', 'فيرنا'],
            'نيسان': ['صني', 'سنترا', 'قشقاي'],
            'شيفروليه': ['افيو', 'اوبترا', 'كروز', 'لانوس'],
            'كيا': ['سبورتاج', 'سيراتو', 'بيكانتو'],
            'مرسيدس': ['C180', 'E200', 'GLC'],
            'بي إم دبليو': ['الفئة 3', 'الفئة 5', 'X5']
        }
        
        # Get items for the operations
        items = list(Item.objects.filter(tenant_id=self.tenant_id))
        if not items:
            print("No items found. Cannot create maintenance schedules with parts.")
            return []
        
        maintenance_schedules_data = [
            {
                "name": "صيانة 10,000 كم",
                "description": "الصيانة الدورية عند 10,000 كم",
                "interval_type": "mileage",
                "mileage_interval": 10000,
            },
            {
                "name": "صيانة 20,000 كم",
                "description": "الصيانة الدورية عند 20,000 كم",
                "interval_type": "mileage",
                "mileage_interval": 20000,
            },
            {
                "name": "صيانة 40,000 كم",
                "description": "الصيانة الدورية الكبرى عند 40,000 كم",
                "interval_type": "mileage",
                "mileage_interval": 40000,
            },
            {
                "name": "الصيانة السنوية",
                "description": "الصيانة الدورية السنوية",
                "interval_type": "time",
                "time_interval_months": 12,
            },
            {
                "name": "صيانة نصف سنوية",
                "description": "الصيانة الدورية كل 6 أشهر",
                "interval_type": "time",
                "time_interval_months": 6,
            },
            {
                "name": "صيانة مزدوجة",
                "description": "صيانة كل 15,000 كم أو 6 أشهر (أيهما أقرب)",
                "interval_type": "both",
                "mileage_interval": 15000,
                "time_interval_months": 6,
            }
        ]
        
        # Common operation names for maintenance
        operations_data = [
            {"name": "تغيير زيت المحرك", "duration": 30},
            {"name": "تغيير فلتر الزيت", "duration": 15},
            {"name": "تغيير فلتر الهواء", "duration": 10},
            {"name": "فحص وضبط الفرامل", "duration": 45},
            {"name": "فحص السوائل وتعبئتها", "duration": 20},
            {"name": "فحص نظام التعليق", "duration": 30},
            {"name": "فحص وضبط الإطارات", "duration": 20},
            {"name": "فحص البطارية", "duration": 10},
            {"name": "فحص نظام التبريد", "duration": 15},
            {"name": "فحص نظام العادم", "duration": 20},
            {"name": "فحص نظام القيادة والتوجيه", "duration": 30},
            {"name": "فحص أنظمة الإنارة", "duration": 15},
            {"name": "تشحيم المفاصل والوصلات", "duration": 25},
            {"name": "فحص مساحات الزجاج", "duration": 10},
            {"name": "فحص شامل بالكمبيوتر", "duration": 60}
        ]
        
        maintenance_schedules = []
        
        # Check for existing schedules
        existing_schedules = {schedule.name: schedule for schedule in 
                             MaintenanceSchedule.objects.filter(tenant_id=self.tenant_id)}
        
        for schedule_data in maintenance_schedules_data:
            # Check if this schedule already exists
            schedule = existing_schedules.get(schedule_data["name"])
            
            if schedule:
                print(f"Using existing Maintenance Schedule: {schedule.name}")
                maintenance_schedules.append(schedule)
                continue
            
            # Randomly assign to specific make/model or leave generic
            make = random.choice(vehicle_makes) if random.random() > 0.3 else ""
            model = random.choice(vehicle_models.get(make, [""])) if make and random.random() > 0.5 else ""
            
            try:
                # Create maintenance schedule
                schedule = MaintenanceSchedule.objects.create(
                    tenant_id=self.tenant_id,
                    name=schedule_data["name"],
                    description=schedule_data["description"],
                    interval_type=schedule_data["interval_type"],
                    mileage_interval=schedule_data.get("mileage_interval"),
                    time_interval_months=schedule_data.get("time_interval_months"),
                    vehicle_make=make,
                    vehicle_model=model,
                    year_from=random.randint(2010, 2015) if random.random() > 0.5 else None,
                    year_to=random.randint(2020, 2023) if random.random() > 0.5 else None,
                    is_active=True
                )
                
                # Determine which operations to include based on schedule type
                num_operations = random.randint(3, 8)
                selected_operations = random.sample(operations_data, min(num_operations, len(operations_data)))
                
                # Add operations to this schedule
                for i, operation_data in enumerate(selected_operations):
                    # Find a suitable work order type
                    suitable_types = [wot for wot in work_order_types 
                                     if any(kw in wot.name for kw in operation_data["name"].split())]
                    operation_type = random.choice(suitable_types) if suitable_types else random.choice(work_order_types)
                    
                    # Create schedule operation
                    operation = ScheduleOperation.objects.create(
                        tenant_id=self.tenant_id,
                        maintenance_schedule=schedule,
                        name=operation_data["name"],
                        description=f"خطوة ضمن {schedule.name}",
                        duration_minutes=operation_data["duration"],
                        sequence=(i+1) * 10,
                        is_required=random.random() > 0.2,  # 80% chance of being required
                        operation_type=operation_type
                    )
                    
                    # Add parts to this operation (1-3 parts per operation)
                    num_parts = random.randint(1, 3)
                    selected_parts = random.sample(items, min(num_parts, len(items)))
                    
                    for part_item in selected_parts:
                        OperationPart.objects.create(
                            tenant_id=self.tenant_id,
                            schedule_operation=operation,
                            item=part_item,
                            quantity=Decimal(str(random.randint(1, 3))),
                            is_required=random.random() > 0.3  # 70% chance of being required
                        )
                
                maintenance_schedules.append(schedule)
                print(f"Created Maintenance Schedule: {schedule.name} with {len(selected_operations)} operations")
            except Exception as e:
                print(f"Error creating maintenance schedule '{schedule_data['name']}': {e}")
            
        return maintenance_schedules
    
    def _create_work_orders(self, work_order_types, bills_of_materials, maintenance_schedules):
        """Create work order records with operations and materials."""
        # Get service centers
        service_centers = list(ServiceCenter.objects.filter(tenant_id=self.tenant_id))
        print(f"Searching for service centers with tenant_id: {self.tenant_id}")
        print(f"Found {len(service_centers)} service centers")
        
        if not service_centers:
            # Try fetching any service center as fallback
            all_service_centers = list(ServiceCenter.objects.all()[:5])
            if all_service_centers:
                print(f"Found {len(all_service_centers)} service centers with other tenant IDs:")
                for sc in all_service_centers:
                    print(f"  - {sc.name}: {sc.tenant_id}")
                
                # Use the first available service center's tenant ID
                self.tenant_id = all_service_centers[0].tenant_id
                print(f"Switching to tenant ID: {self.tenant_id}")
                service_centers = all_service_centers
            else:
                print("No service centers found at all. Cannot create work orders.")
                return []
        
        # Get vehicles that match the tenant ID
        vehicles = list(Vehicle.objects.filter(tenant_id=self.tenant_id))
        if not vehicles:
            print(f"No vehicles found with tenant_id: {self.tenant_id}")
            # Try using a different tenant ID if there are vehicles
            any_vehicles = list(Vehicle.objects.all()[:5])
            if any_vehicles:
                print(f"Found vehicles with other tenant IDs:")
                for v in any_vehicles[:3]:
                    print(f"  - {v.make} {v.model}: {v.tenant_id}")
                # Use the first available vehicle's tenant ID
                self.tenant_id = any_vehicles[0].tenant_id
                print(f"Switching to tenant ID: {self.tenant_id}")
                vehicles = any_vehicles
                # Re-fetch service centers with this tenant ID
                service_centers = list(ServiceCenter.objects.filter(tenant_id=self.tenant_id))
                if not service_centers:
                    service_centers = [ServiceCenter.objects.first()]
            else:
                print("No vehicles found at all. Cannot create work orders with vehicles.")
                return []
        
        print(f"Using {len(vehicles)} vehicles and {len(service_centers)} service centers")
        
        # Get items
        items = list(Item.objects.filter(tenant_id=self.tenant_id))
        if not items:
            print(f"No items found with tenant_id: {self.tenant_id}")
            # Try to get any items
            items = list(Item.objects.all()[:20])
            if not items:
                print("No items found at all. Cannot create work order materials.")
                return []
            print(f"Using {len(items)} items with mixed tenant IDs")
        
        work_orders = []
        work_order_count = 30  # Number of work orders to create
        
        for i in range(work_order_count):
            # Determine if this is a scheduled maintenance work order
            is_scheduled = random.random() < 0.6  # 60% are scheduled maintenance
            
            # Select random service center, vehicle, and customer
            service_center = random.choice(service_centers)
            vehicle = random.choice(vehicles)
            customer = vehicle.owner or None
            
            # Select work order type based on whether it's scheduled maintenance
            if is_scheduled:
                work_order_type = next((wot for wot in work_order_types if "صيانة" in wot.name), 
                                      random.choice(work_order_types))
            else:
                # For non-scheduled, prefer repair-related types
                repair_types = [wot for wot in work_order_types if "إصلاح" in wot.name]
                work_order_type = random.choice(repair_types) if repair_types else random.choice(work_order_types)
            
            # Select bill of materials (rarely used)
            bill_of_materials = random.choice(bills_of_materials) if bills_of_materials and random.random() < 0.2 else None
            
            # Select maintenance schedule for scheduled maintenance
            maintenance_schedule = None
            if is_scheduled:
                # Try to find a suitable schedule based on vehicle make/model
                suitable_schedules = [
                    ms for ms in maintenance_schedules
                    if (not ms.vehicle_make or ms.vehicle_make == vehicle.make) and
                       (not ms.vehicle_model or ms.vehicle_model == vehicle.model)
                ]
                maintenance_schedule = random.choice(suitable_schedules) if suitable_schedules else random.choice(maintenance_schedules)
            
            # Determine status and dates
            status = random.choices(
                ['draft', 'planned', 'in_progress', 'on_hold', 'completed', 'cancelled'],
                weights=[0.1, 0.2, 0.3, 0.1, 0.25, 0.05],  # Weights to make some statuses more common
                k=1
            )[0]
            
            # Generate dates based on status
            now = timezone.now()
            
            # Planned dates are in the future for draft/planned, in the past for others
            if status in ['draft', 'planned']:
                planned_start_date = now + timedelta(days=random.randint(1, 30))
                planned_end_date = planned_start_date + timedelta(hours=random.randint(2, 48))
                actual_start_date = None
                actual_end_date = None
            else:
                planned_start_date = now - timedelta(days=random.randint(5, 60))
                planned_end_date = planned_start_date + timedelta(hours=random.randint(2, 48))
                
                if status in ['in_progress', 'on_hold']:
                    actual_start_date = planned_start_date + timedelta(hours=random.randint(-5, 5))  # Start near planned time
                    actual_end_date = None
                elif status == 'completed':
                    actual_start_date = planned_start_date + timedelta(hours=random.randint(-5, 5))
                    actual_end_date = actual_start_date + timedelta(hours=random.randint(1, 72))
                else:  # cancelled
                    actual_start_date = None
                    actual_end_date = None
            
            # Determine priority
            priority = random.choices(
                ['low', 'medium', 'high', 'critical'],
                weights=[0.2, 0.5, 0.2, 0.1],
                k=1
            )[0]
            
            # Generate work order number
            work_order_number = f"WO-{service_center.code}-{now.strftime('%y%m%d')}-{i+1:04d}"
            
            # Generate estimated and actual costs
            estimated_cost = Decimal(str(random.randint(500, 5000)))
            actual_cost = Decimal(str(random.randint(450, 6000))) if status == 'completed' else None
            
            try:
                # Create the work order
                work_order = WorkOrder.objects.create(
                    tenant_id=self.tenant_id,
                    work_order_number=work_order_number,
                    work_order_type=work_order_type,
                    bill_of_materials=bill_of_materials,
                    description=fake.paragraph(),
                    priority=priority,
                    status=status,
                    operation_category='scheduled' if is_scheduled else 'custom',
                    maintenance_schedule=maintenance_schedule,
                    
                    # Service center and vehicle info
                    service_center=service_center,
                    vehicle=vehicle,
                    current_odometer=random.randint(5000, 150000) if vehicle else None,
                    fuel_level=random.randint(10, 100) if vehicle else None,
                    
                    # Dates
                    planned_start_date=planned_start_date,
                    planned_end_date=planned_end_date,
                    actual_start_date=actual_start_date,
                    actual_end_date=actual_end_date,
                    
                    # Customer info
                    customer=customer,
                    customer_name=customer.get_full_name() if customer and hasattr(customer, 'get_full_name') else (customer.name if customer and hasattr(customer, 'name') else fake.name()),
                    customer_phone=customer.phone if customer and hasattr(customer, 'phone') else fake.phone_number(),
                    customer_email=customer.email if customer and hasattr(customer, 'email') else fake.email(),
                    
                    # Service information
                    service_item_serial=f"SN-{fake.random_number(digits=8)}" if random.random() > 0.7 else "",
                    warranty_status=random.random() < 0.3,  # 30% chance of being under warranty
                    
                    # Financial info
                    estimated_cost=estimated_cost,
                    actual_cost=actual_cost,
                    
                    notes=fake.paragraph() if random.random() > 0.7 else ""
                )
                
                # If using maintenance schedule, apply it automatically
                if maintenance_schedule:
                    # Don't call the method - manually create operations to avoid errors
                    for schedule_op in maintenance_schedule.operations.all():
                        # Create the work order operation
                        work_order_op = WorkOrderOperation.objects.create(
                            tenant_id=self.tenant_id,
                            work_order=work_order,
                            sequence=schedule_op.sequence,
                            name=schedule_op.name,
                            description=schedule_op.description,
                            duration_minutes=schedule_op.duration_minutes,
                            is_completed=status == 'completed',
                            completed_at=actual_end_date if status == 'completed' else None
                        )
                        
                        # Add materials from the operation parts
                        for part in schedule_op.parts.all():
                            WorkOrderMaterial.objects.create(
                                tenant_id=self.tenant_id,
                                work_order=work_order,
                                item=part.item,
                                quantity=part.quantity,
                                unit_of_measure="قطعة", # Default
                                is_consumed=status == 'completed'
                            )
                else:
                    # Create custom operations (3-5)
                    num_operations = random.randint(3, 5)
                    
                    for j in range(num_operations):
                        # Create operation
                        operation = WorkOrderOperation.objects.create(
                            tenant_id=self.tenant_id,
                            work_order=work_order,
                            sequence=(j+1) * 10,
                            name=f"عملية {j+1} - {work_order_type.name}",
                            description=fake.sentence(),
                            duration_minutes=random.randint(15, 120),
                            is_completed=status == 'completed' or (status == 'in_progress' and j < num_operations//2),
                            completed_at=actual_end_date if status == 'completed' else 
                                       (actual_start_date + timedelta(hours=random.randint(1, 5)) 
                                        if status == 'in_progress' and j < num_operations//2 else None)
                        )
                    
                    # Add materials (2-6)
                    num_materials = random.randint(2, 6)
                    selected_materials = random.sample(items, min(num_materials, len(items)))
                    
                    for material_item in selected_materials:
                        WorkOrderMaterial.objects.create(
                            tenant_id=self.tenant_id,
                            work_order=work_order,
                            item=material_item,
                            quantity=Decimal(str(random.randint(1, 5))),
                            unit_of_measure="قطعة", # Default
                            is_consumed=status == 'completed'
                        )
                
                work_orders.append(work_order)
            except Exception as e:
                print(f"Error creating work order {work_order_number}: {e}")
            
            # Log progress every 5 work orders
            if (i+1) % 5 == 0 or i == 0 or i == work_order_count-1:
                print(f"Created {i+1}/{work_order_count} Work Orders")
        
        print(f"Created total of {len(work_orders)} Work Orders")
        return work_orders
    
    def run(self):
        """Run all data generation steps."""
        self.generate_work_orders_data()
        print("\nCompleted work orders data generation")

def main():
    """Main entry point."""
    generator = WorkOrdersDataGenerator()
    generator.run()

if __name__ == "__main__":
    main() 