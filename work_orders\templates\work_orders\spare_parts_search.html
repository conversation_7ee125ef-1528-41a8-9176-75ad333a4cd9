{% load i18n %}
{% load static %}

<!-- Spare Parts Search Dropdown Template -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">{% trans "بحث عن قطعة غيار" %}</h5>
    </div>
    <div class="card-body">
        <!-- CSRF Token -->
        {% csrf_token %}
        
        <!-- Operation Selection -->
        <div class="form-group">
            <label for="selected-operation">{% trans "العملية" %}</label>
            <select id="selected-operation" class="form-control">
                <option value="">{% trans "اختر العملية" %}</option>
                {% for operation in operations %}
                    <option value="{{ operation.id }}">{{ operation.name }}</option>
                {% endfor %}
            </select>
        </div>
        
        <!-- Vehicle Selection (if not already selected) -->
        {% if not vehicle %}
        <div class="form-group">
            <label for="selected-vehicle">{% trans "المركبة" %}</label>
            <select id="selected-vehicle" class="form-control">
                <option value="">{% trans "اختر المركبة" %}</option>
                {% for vehicle in vehicles %}
                    <option value="{{ vehicle.id }}">{{ vehicle.make }} {{ vehicle.model }} ({{ vehicle.year }})</option>
                {% endfor %}
            </select>
        </div>
        {% else %}
        <div class="form-group">
            <label>{% trans "المركبة" %}</label>
            <div class="form-control bg-light">{{ vehicle.make }} {{ vehicle.model }} ({{ vehicle.year }})</div>
            <input type="hidden" id="selected-vehicle" value="{{ vehicle.id }}">
        </div>
        {% endif %}
        
        <!-- Service Center (Hidden if not applicable) -->
        {% if service_center %}
        <input type="hidden" id="selected-service-center" value="{{ service_center.id }}">
        {% endif %}
        
        <!-- Spare Parts Search with Dropdown -->
        <div class="form-group mt-4 spare-parts-search-container position-relative">
            <label for="spare-parts-search">{% trans "بحث عن قطعة غيار" %}</label>
            <div class="input-group">
                <input type="text" id="spare-parts-search" class="form-control" placeholder="{% trans 'اكتب اسم أو رمز قطعة الغيار' %}">
                <div class="input-group-append">
                    <button class="btn btn-outline-secondary" type="button" id="search-parts-button">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
            
            <!-- Dropdown Results -->
            <div id="spare-parts-dropdown" class="dropdown-menu w-100 mt-1 shadow" style="display: none; max-height: 300px; overflow-y: auto; z-index: 1050;">
                <div class="text-center p-3">{% trans "اختر عملية لعرض قطع الغيار المتوافقة" %}</div>
            </div>
            
            <!-- Hidden Part ID -->
            <input type="hidden" id="selected-part-id" name="part_id">
        </div>
        
        <!-- Selected Part Info Display -->
        <div id="selected-part-info" class="mt-3"></div>
        
        <!-- Action Buttons (Add to Cart, etc.) -->
        <div class="form-group mt-4">
            <label for="part-quantity">{% trans "الكمية" %}</label>
            <input type="number" id="part-quantity" class="form-control" value="1" min="1">
        </div>
        
        <button id="add-part-to-work-order" class="btn btn-primary mt-3">
            <i class="fas fa-plus-circle"></i> {% trans "إضافة قطعة الغيار" %}
        </button>
    </div>
</div>

<!-- Include the JavaScript -->
<script src="{% static 'work_orders/js/spare_parts_search.js' %}"></script>

<!-- Handler for Add Button -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const addButton = document.getElementById('add-part-to-work-order');
    if (addButton) {
        addButton.addEventListener('click', function() {
            const partId = document.getElementById('selected-part-id').value;
            const quantity = document.getElementById('part-quantity').value || 1;
            
            if (!partId) {
                alert("{% trans 'الرجاء اختيار قطعة غيار أولاً' %}");
                return;
            }
            
            // Add to work order
            addPartToWorkOrder(partId, quantity);
        });
    }
    
    // Function to add part to work order
    function addPartToWorkOrder(partId, quantity) {
        // Get current work order ID if available
        const workOrderId = document.getElementById('work-order-id')?.value;
        
        if (workOrderId) {
            // AJAX request to add part to work order
            fetch('/{{ LANGUAGE_CODE }}/work-orders/api/add-material/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCsrfToken()
                },
                body: JSON.stringify({
                    work_order_id: workOrderId,
                    part_id: partId,
                    quantity: quantity
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message
                    showNotification("{% trans 'تمت إضافة قطعة الغيار بنجاح' %}", 'success');
                    
                    // Clear form
                    document.getElementById('spare-parts-search').value = '';
                    document.getElementById('selected-part-id').value = '';
                    document.getElementById('selected-part-info').innerHTML = '';
                    document.getElementById('part-quantity').value = 1;
                    
                    // Refresh parts list if available
                    if (typeof refreshWorkOrderMaterials === 'function') {
                        refreshWorkOrderMaterials();
                    }
                } else {
                    showNotification(data.error || "{% trans 'حدث خطأ أثناء إضافة قطعة الغيار' %}", 'error');
                }
            })
            .catch(error => {
                console.error('Error adding part to work order:', error);
                showNotification("{% trans 'حدث خطأ أثناء إضافة قطعة الغيار' %}", 'error');
            });
        } else {
            // If no work order ID, assume we're in the work order creation form
            // Add the part to the form's material list
            const partName = document.getElementById('spare-parts-search').value;
            const partPrice = document.querySelector('#selected-part-info .badge')?.textContent || '';
            
            // Add to materials table if it exists
            const materialsTable = document.getElementById('materials-table');
            if (materialsTable) {
                const tbody = materialsTable.querySelector('tbody');
                if (tbody) {
                    const newRow = document.createElement('tr');
                    newRow.innerHTML = `
                        <td>
                            <input type="hidden" name="part_id[]" value="${partId}">
                            ${partName}
                        </td>
                        <td>
                            <input type="number" name="part_quantity[]" class="form-control form-control-sm" value="${quantity}" min="1">
                        </td>
                        <td>${partPrice}</td>
                        <td>
                            <button type="button" class="btn btn-sm btn-danger remove-part">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                        </td>
                    `;
                    
                    // Add remove button event handler
                    const removeButton = newRow.querySelector('.remove-part');
                    if (removeButton) {
                        removeButton.addEventListener('click', function() {
                            newRow.remove();
                        });
                    }
                    
                    tbody.appendChild(newRow);
                    
                    // Show success message
                    showNotification("{% trans 'تمت إضافة قطعة الغيار إلى القائمة' %}", 'success');
                    
                    // Clear form
                    document.getElementById('spare-parts-search').value = '';
                    document.getElementById('selected-part-id').value = '';
                    document.getElementById('selected-part-info').innerHTML = '';
                    document.getElementById('part-quantity').value = 1;
                }
            }
        }
    }
    
    // Get CSRF token
    function getCsrfToken() {
        return document.querySelector('input[name="csrfmiddlewaretoken"]').value;
    }
    
    // Show notification
    function showNotification(message, type) {
        const notificationContainer = document.getElementById('notification-container');
        if (!notificationContainer) {
            // Create notification container if it doesn't exist
            const container = document.createElement('div');
            container.id = 'notification-container';
            container.style.position = 'fixed';
            container.style.top = '20px';
            container.style.right = '20px';
            container.style.zIndex = '9999';
            document.body.appendChild(container);
        }
        
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show`;
        notification.innerHTML = `
            ${message}
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        `;
        
        document.getElementById('notification-container').appendChild(notification);
        
        // Auto dismiss after 5 seconds
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 300);
        }, 5000);
    }
});
</script> 