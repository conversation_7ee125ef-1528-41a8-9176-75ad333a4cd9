"""project URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/3.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.conf.urls.i18n import i18n_patterns
from django.utils.translation import gettext_lazy as _
from django.views.i18n import set_language
from feature_flags.services import is_module_active
from django.views.generic import RedirectView
from work_orders.api_spare_parts import api_get_spare_parts

# List of module URLs that can be dynamically enabled/disabled
MODULE_URLS = [
    ('inventory/', include('inventory.urls')),
    ('warehouse/', include('warehouse.urls')),
    ('sales/', include('sales.urls')),
    ('purchases/', include('purchases.urls')),
    ('reports/', include('reports.urls')),
    ('settings/', include('app_settings.urls')),
    ('work-orders/', include('work_orders.urls')),
    ('setup/', include('setup.urls')),
    ('franchise-setup/', include('franchise_setup.urls')),
]

# Base URL patterns (not i18n-prefixed)
urlpatterns = [
    path('dzJAMvwB/', admin.site.urls),  # Original admin URL
    path('api/', include('api.urls')),
]

# Add Django's authentication URLs
# These are not prefixed with a language code
urlpatterns += [
    path('accounts/', include('django.contrib.auth.urls')), 
]

# Add API endpoint for spare parts
urlpatterns += [
    path('api/spare-parts/', api_get_spare_parts, name='api_get_spare_parts_direct'),
]

# Add static and media URL patterns in debug mode
if settings.DEBUG:
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)

# Add module URLs based on feature flags
# This is a simplified approach; in reality, you'd check these dynamically
# when the URLs are accessed, not just when the server starts
urlpatterns += i18n_patterns(
    path('', include('website.urls')),
    # Add all module URLs - the middleware will handle disabling access
    # to modules that are turned off
    *[path(url_pattern, include_pattern) for url_pattern, include_pattern in MODULE_URLS],
    prefix_default_language=True
)

# Add core URLs
urlpatterns += [
    path('core/', include('core.urls')),
    path('user-roles/', include('user_roles.urls')),  # Add user_roles URLs
]

# Add login page redirect from root
urlpatterns += [
    path('', RedirectView.as_view(pattern_name='core:login'), name='home'),
]

# Add i18n patterns for JavaScript - disabled for now as we're using Arabic only
urlpatterns += [
    path('i18n/', include('django.conf.urls.i18n')),
]

# handler404 = 'website.views.page_not_found_view'
