from django.http import Http404
from django.urls import resolve
from django.utils.deprecation import MiddlewareMixin
from feature_flags.services import is_module_active, is_feature_active


class FeatureFlagMiddleware(MiddlewareMixin):
    """
    Middleware to check if views should be accessible based on feature flags.
    """
    
    def process_view(self, request, view_func, view_args, view_kwargs):
        """
        Check if the requested view is accessible based on module and feature flags.
        
        Views can be protected by decorating them with @requires_module or @requires_feature
        or by adding a requires_module or requires_feature attribute to the view class.
        """
        if not hasattr(view_func, 'view_class'):
            # For function-based views, check for the attributes on the function
            module_name = getattr(view_func, 'requires_module', None)
            feature_name = getattr(view_func, 'requires_feature', None)
        else:
            # For class-based views, check for the attributes on the class
            view_class = view_func.view_class
            module_name = getattr(view_class, 'requires_module', None)
            feature_name = getattr(view_class, 'requires_feature', None)
        
        # Get tenant ID from request (set by CurrentTenantMiddleware)
        tenant_id = getattr(request, 'tenant_id', None)
        
        # Check module flag
        if module_name and not is_module_active(module_name, tenant_id):
            raise Http404("Module is not active")
            
        # Check feature flag
        if feature_name and not is_feature_active(feature_name, tenant_id):
            raise Http404("Feature is not active")
            
        # Allow access
        return None


def requires_module(module_name):
    """
    Decorator for views that require a specific module to be active.
    
    Example:
        @requires_module('inventory')
        def inventory_view(request):
            ...
    """
    def decorator(view_func):
        view_func.requires_module = module_name
        return view_func
    return decorator


def requires_feature(feature_name):
    """
    Decorator for views that require a specific feature to be active.
    
    Example:
        @requires_feature('barcode_scanning')
        def scan_item(request):
            ...
    """
    def decorator(view_func):
        view_func.requires_feature = feature_name
        return view_func
    return decorator 