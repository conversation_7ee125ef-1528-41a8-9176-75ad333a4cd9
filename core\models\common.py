import uuid
from django.db import models
from django.utils.translation import gettext_lazy as _


class TimeStampedModel(models.Model):
    """
    An abstract base class model that provides self-updating
    created and modified fields.
    """
    created_at = models.DateTimeField(_("Created at"), auto_now_add=True)
    updated_at = models.DateTimeField(_("Updated at"), auto_now=True)

    class Meta:
        abstract = True


class UUIDPrimaryKeyModel(models.Model):
    """
    An abstract base class model that provides a UUID primary key
    field.
    """
    id = models.UUIDField(
        _("ID"),
        primary_key=True,
        default=uuid.uuid4,
        editable=False
    )

    class Meta:
        abstract = True


class TenantAwareModel(models.Model):
    """
    An abstract base class model that provides tenant
    scoping for multi-tenancy.
    """
    tenant_id = models.UUIDField(_("Tenant ID"), db_index=True)
    
    class Meta:
        abstract = True 