"""
Decorators for the work_orders app.
"""
import logging
import traceback
from functools import wraps

from django.http import JsonResponse

logger = logging.getLogger(__name__)

def api_error_handler(f):
    """
    Decorator for API views to standardize error handling.
    Catches exceptions and returns a standardized JSON response.
    
    Usage:
        @api_error_handler
        def my_api_view(request):
            # Your code here
    """
    @wraps(f)
    def wrapper(request, *args, **kwargs):
        try:
            return f(request, *args, **kwargs)
        except Exception as e:
            error_traceback = traceback.format_exc()
            
            # Log detailed error information
            logger.error(f"ERROR in {f.__name__}: {str(e)}")
            logger.error(f"Request: {request.method} {request.path}")
            logger.error(f"Parameters: GET={request.GET}, POST={request.POST}")
            logger.error(f"Traceback: {error_traceback}")
            
            # Return standardized error response
            return JsonResponse({
                'success': False,
                'error': str(e),
                'message': "An error occurred. Please try again or contact support if the problem persists."
            }, status=500)
    
    return wrapper

def tenant_required(f):
    """
    Decorator to ensure a tenant ID is present in the request.
    If no tenant ID is found, returns a JSON error response.
    
    Usage:
        @tenant_required
        def my_view(request):
            # Your code here
    """
    @wraps(f)
    def wrapper(request, *args, **kwargs):
        from core.utils import get_tenant_id
        
        tenant_id = get_tenant_id(request)
        if not tenant_id:
            return JsonResponse({
                'success': False,
                'error': "Tenant ID is required",
                'message': "Please select a tenant before continuing."
            }, status=400)
        
        # Add tenant_id to request for convenience
        request.tenant_id = tenant_id
        return f(request, *args, **kwargs)
    
    return wrapper

def role_permission_required(permission_name):
    """
    Decorator to check if the user has a specific permission.
    If the user doesn't have the permission, returns a JSON error response.
    
    Usage:
        @role_permission_required('work_orders.can_create')
        def my_view(request):
            # Your code here
    """
    def decorator(f):
        @wraps(f)
        def wrapper(request, *args, **kwargs):
            if not request.user.is_authenticated:
                return JsonResponse({
                    'success': False,
                    'error': "Authentication required",
                    'message': "Please log in to access this feature."
                }, status=401)
            
            # Superusers have all permissions
            if request.user.is_superuser:
                return f(request, *args, **kwargs)
            
            # Check user roles for permission
            has_permission = False
            
            if hasattr(request.user, 'user_roles'):
                for role in request.user.user_roles.filter(is_active=True):
                    if role.has_permission(permission_name):
                        has_permission = True
                        break
            
            if not has_permission:
                return JsonResponse({
                    'success': False,
                    'error': "Permission denied",
                    'message': "You don't have permission to perform this action."
                }, status=403)
            
            return f(request, *args, **kwargs)
        
        return wrapper
    
    return decorator 