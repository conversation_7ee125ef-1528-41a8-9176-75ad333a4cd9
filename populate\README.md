# Aftersails Demo Data Generation Scripts

This directory contains scripts for generating demo data for the Aftersails vehicle service management system, specifically tailored for the Egyptian market.

## Script Execution Order

For best results, execute the scripts in the following order:

1. `add_franchise_data.py` - Creates franchise entities
2. `add_organization_data.py` - Generates organization structure including companies and service centers
3. `monkey_patch_vehicle.py` - Adds the `owner_name` property to the Vehicle model to fix signal issues
4. `add_simple_vehicles.py` - Adds basic vehicle data
5. `add_vehicles.py` - Adds more detailed vehicle data with Egyptian makes/models
6. `add_service_center_models.py` - Associates vehicle makes and models with service centers
7. `add_vehicle_history.py` - Adds vehicle service and maintenance history
8. `add_user_activity.py` - Adds user activity tracking data for analytics
9. `add_stock_transactions.py` - Adds inventory stock transactions
10. `populate_all_organization_tables.py` - Comprehensively generates remaining data for organization tables

Alternatively, you can run the master script `generate_all_data.py` which will execute all scripts in the correct order.

## Script Descriptions

### `add_franchise_data.py`
Creates the main franchises for the Aftersails system with Arabic names and Egyptian locations.

### `add_organization_data.py`
Generates service levels, service center types, and the organizational structure of companies and service centers throughout Egyptian cities.

### `monkey_patch_vehicle.py`
Adds the `owner_name` property to the Vehicle model. This is required to fix an issue with a signal handler that expects this property.

### `add_simple_vehicles.py`
Adds a basic set of vehicles with Egyptian makes and models, properly associated with owners and service centers.

### `add_vehicles.py`
Adds more detailed vehicle data with comprehensive Egyptian makes and models, colors, and registration details.

### `add_service_center_models.py`
Associates specific vehicle makes and models with service centers to indicate which service centers can service which types of vehicles.

### `add_vehicle_history.py`
Generates vehicle service history, maintenance records, and repair details with realistic dates and services.

### `add_user_activity.py`
Creates user activity logs for tracking user actions in the system for audit trails and analytics.

### `add_stock_transactions.py`
Generates inventory stock transactions including receipts, transfers between warehouses, adjustments, and consumption for service orders.

### `populate_all_organization_tables.py`
Comprehensively generates data for all remaining organization tables, focusing on the business relationships between entities.

### `generate_all_data.py`
Master script that runs all data generation scripts in the correct order with error handling.

## Additional Utility Scripts

### `debug_vehicle_model.py` 
Utility script to debug Vehicle model issues, specifically related to the missing `owner_name` property.

### `fix_vehicle_creation.py`
Workaround script to fix vehicle creation issues by using direct SQL statements.

### `create_vehicle_data.py`
Alternative approach to create vehicle data when standard Django ORM encounters issues.

## Generated Data Features

All generated data includes:
- Arabic names and text
- Egyptian cities and locations
- Authentic Egyptian vehicle makes and models
- Proper tenant IDs for multi-tenant architecture
- Realistic relationships between entities
- Date ranges that make sense for the business context

## Note on Multi-Tenancy

All data is generated with tenant IDs to support the multi-tenant franchise model. The scripts automatically handle tenant ID propagation from existing records or generate new tenant IDs when needed. 