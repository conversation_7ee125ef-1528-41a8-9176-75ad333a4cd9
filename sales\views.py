from django.shortcuts import render, get_object_or_404, redirect
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.urls import reverse_lazy
from django.contrib import messages
from django.utils.translation import gettext_lazy as _

from .models import Customer, SalesOrder, SalesOrderItem, SalesReturn


class SalesDashboardView(LoginRequiredMixin, ListView):
    """Dashboard view for sales module"""
    model = SalesOrder
    template_name = 'sales/dashboard.html'
    context_object_name = 'sales_orders'
    paginate_by = 10
    
    def get_queryset(self):
        """Filter by tenant ID"""
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            return SalesOrder.objects.filter(tenant_id=tenant_id).order_by('-created_at')
        return SalesOrder.objects.none()
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        tenant_id = getattr(self.request, 'tenant_id', None)
        
        # Calculate summary statistics
        if tenant_id:
            context['total_orders'] = SalesOrder.objects.filter(tenant_id=tenant_id).count()
            context['pending_orders'] = SalesOrder.objects.filter(
                tenant_id=tenant_id, 
                status__in=['draft', 'confirmed']
            ).count()
            context['completed_orders'] = SalesOrder.objects.filter(
                tenant_id=tenant_id, 
                status='delivered'
            ).count()
            context['customers_count'] = Customer.objects.filter(tenant_id=tenant_id).count()
            
            # Calculate total sales amount
            sales_orders = SalesOrder.objects.filter(tenant_id=tenant_id)
            context['total_sales_amount'] = sum(order.total_amount for order in sales_orders)
            
            # Get recent orders
            context['recent_orders'] = SalesOrder.objects.filter(
                tenant_id=tenant_id
            ).order_by('-created_at')[:5]
        else:
            context['missing_tenant'] = True
            
        return context


class SalesOrderListView(LoginRequiredMixin, ListView):
    """View to list all sales orders"""
    model = SalesOrder
    template_name = 'sales/sales_order_list.html'
    context_object_name = 'sales_orders'
    paginate_by = 20
    
    def get_queryset(self):
        """Filter by tenant ID and apply any search/filter params"""
        tenant_id = getattr(self.request, 'tenant_id', None)
        if not tenant_id:
            return SalesOrder.objects.none()
            
        queryset = SalesOrder.objects.filter(tenant_id=tenant_id)
        
        # Apply filters if provided in GET parameters
        status = self.request.GET.get('status')
        if status:
            queryset = queryset.filter(status=status)
            
        customer = self.request.GET.get('customer')
        if customer:
            queryset = queryset.filter(customer_id=customer)
            
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(order_number__icontains=search)
            
        # Default ordering
        return queryset.order_by('-order_date')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        tenant_id = getattr(self.request, 'tenant_id', None)
        
        if tenant_id:
            context['customers'] = Customer.objects.filter(tenant_id=tenant_id)
            context['missing_tenant'] = False
        else:
            context['missing_tenant'] = True
            
        # Preserve filter params for pagination
        context['current_status'] = self.request.GET.get('status', '')
        context['current_customer'] = self.request.GET.get('customer', '')
        context['current_search'] = self.request.GET.get('search', '')
        
        return context


class SalesOrderDetailView(LoginRequiredMixin, DetailView):
    """View to display sales order details"""
    model = SalesOrder
    template_name = 'sales/sales_order_detail.html'
    context_object_name = 'sales_order'
    
    def get_queryset(self):
        """Filter by tenant ID"""
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            return SalesOrder.objects.filter(tenant_id=tenant_id)
        return SalesOrder.objects.none()
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['items'] = self.object.items.all()
        context['returns'] = self.object.returns.all()
        context['missing_tenant'] = getattr(self.request, 'tenant_id', None) is None
        return context


class SalesOrderCreateView(LoginRequiredMixin, CreateView):
    """View to create a new sales order"""
    model = SalesOrder
    template_name = 'sales/sales_order_form.html'
    fields = ['customer', 'order_number', 'order_date', 'shipping_address', 'notes']
    
    def get_form(self, form_class=None):
        form = super().get_form(form_class)
        tenant_id = getattr(self.request, 'tenant_id', None)
        
        if tenant_id:
            # Filter customer choices by tenant
            form.fields['customer'].queryset = Customer.objects.filter(tenant_id=tenant_id)
        
        return form
    
    def form_valid(self, form):
        tenant_id = getattr(self.request, 'tenant_id', None)
        if not tenant_id:
            messages.error(self.request, _("Tenant ID is missing. Please set the X-Tenant-ID header."))
            return redirect('sales:sales_order_list')
            
        # Set tenant ID on the object
        form.instance.tenant_id = tenant_id
        
        # Set default status as 'draft'
        form.instance.status = 'draft'
        
        return super().form_valid(form)
    
    def get_success_url(self):
        return reverse_lazy('sales:sales_order_detail', kwargs={'pk': self.object.pk})


class SalesOrderUpdateView(LoginRequiredMixin, UpdateView):
    """View to update an existing sales order"""
    model = SalesOrder
    template_name = 'sales/sales_order_form.html'
    fields = ['customer', 'order_number', 'order_date', 'status', 'shipping_address', 'notes']
    
    def get_queryset(self):
        """Filter by tenant ID"""
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            return SalesOrder.objects.filter(tenant_id=tenant_id)
        return SalesOrder.objects.none()
    
    def get_form(self, form_class=None):
        form = super().get_form(form_class)
        tenant_id = getattr(self.request, 'tenant_id', None)
        
        if tenant_id:
            # Filter customer choices by tenant
            form.fields['customer'].queryset = Customer.objects.filter(tenant_id=tenant_id)
        
        return form
    
    def get_success_url(self):
        return reverse_lazy('sales:sales_order_detail', kwargs={'pk': self.object.pk})


class CustomerListView(LoginRequiredMixin, ListView):
    """View to list all customers"""
    model = Customer
    template_name = 'sales/customer_list.html'
    context_object_name = 'customers'
    paginate_by = 20
    
    def get_queryset(self):
        """Filter by tenant ID"""
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            queryset = Customer.objects.filter(tenant_id=tenant_id)
            
            # Apply search if provided
            search = self.request.GET.get('search')
            if search:
                queryset = queryset.filter(name__icontains=search)
                
            return queryset.order_by('name')
        return Customer.objects.none()
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['missing_tenant'] = getattr(self.request, 'tenant_id', None) is None
        context['current_search'] = self.request.GET.get('search', '')
        return context


class CustomerDetailView(LoginRequiredMixin, DetailView):
    """View to display customer details"""
    model = Customer
    template_name = 'sales/customer_detail.html'
    context_object_name = 'customer'
    
    def get_queryset(self):
        """Filter by tenant ID"""
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            return Customer.objects.filter(tenant_id=tenant_id)
        return Customer.objects.none()
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['sales_orders'] = SalesOrder.objects.filter(customer=self.object).order_by('-order_date')
        context['missing_tenant'] = getattr(self.request, 'tenant_id', None) is None
        return context


class CustomerCreateView(LoginRequiredMixin, CreateView):
    """View to create a new customer"""
    model = Customer
    template_name = 'sales/customer_form.html'
    fields = ['name', 'email', 'phone', 'address', 'is_active']
    
    def form_valid(self, form):
        tenant_id = getattr(self.request, 'tenant_id', None)
        if not tenant_id:
            messages.error(self.request, _("Tenant ID is missing. Please set the X-Tenant-ID header."))
            return redirect('sales:customer_list')
            
        # Set tenant ID on the object
        form.instance.tenant_id = tenant_id
        
        return super().form_valid(form)
    
    def get_success_url(self):
        return reverse_lazy('sales:customer_detail', kwargs={'pk': self.object.pk})


class CustomerUpdateView(LoginRequiredMixin, UpdateView):
    """View to update an existing customer"""
    model = Customer
    template_name = 'sales/customer_form.html'
    fields = ['name', 'email', 'phone', 'address', 'is_active']
    
    def get_queryset(self):
        """Filter by tenant ID"""
        tenant_id = getattr(self.request, 'tenant_id', None)
        if tenant_id:
            return Customer.objects.filter(tenant_id=tenant_id)
        return Customer.objects.none()
    
    def get_success_url(self):
        return reverse_lazy('sales:customer_detail', kwargs={'pk': self.object.pk})
