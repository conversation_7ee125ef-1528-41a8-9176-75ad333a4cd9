from django.urls import path
from inventory import views
from inventory.views_custom import CustomDashboardView

app_name = 'inventory'

urlpatterns = [
    path('', CustomDashboardView.as_view(), name='dashboard'),
    path('items/', views.ItemListView.as_view(), name='item_list'),
    path('items/<uuid:pk>/', views.ItemDetailView.as_view(), name='item_detail'),
    path('items/create/', views.ItemCreateView.as_view(), name='item_create'),
    path('movements/', views.MovementListView.as_view(), name='movement_list'),
    path('movements/create/', views.MovementCreateView.as_view(), name='movement_create'),
    path('scan-barcode/', views.scan_barcode, name='scan_barcode'),
    
    # Unit of measurement routes
    path('units/', views.UnitOfMeasurementListView.as_view(), name='unit_list'),
    path('units/<uuid:pk>/', views.UnitOfMeasurementDetailView.as_view(), name='unit_detail'),
    path('units/create/', views.UnitOfMeasurementCreateView.as_view(), name='unit_create'),
    
    # Unit conversion routes
    path('conversions/', views.UnitConversionListView.as_view(), name='conversion_list'),
    path('conversions/create/', views.UnitConversionCreateView.as_view(), name='conversion_create'),

    # Movement Types URLs
    path('movement-types/', views.MovementTypeListView.as_view(), name='movement_type_list'),
    path('movement-types/create/', views.MovementTypeCreateView.as_view(), name='movement_type_create'),
    path('movement-types/<uuid:pk>/update/', views.MovementTypeUpdateView.as_view(), name='movement_type_update'),
] 