from django.db import models
from django.utils.translation import gettext_lazy as _
from core.models import TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel
from core.querysets import BaseQuerySet
import waffle


class LocationType(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """
    Types of warehouse locations (e.g., Main Warehouse, Service Center Storage, Shelf, Bin, etc.)
    """
    name = models.CharField(_("Name"), max_length=100)
    code = models.CharField(_("Code"), max_length=50)
    description = models.TextField(_("Description"), blank=True)
    # UI display settings
    icon = models.CharField(_("Icon"), max_length=50, blank=True, help_text=_("Icon name for UI display"))
    color = models.CharField(_("Color"), max_length=20, blank=True, help_text=_("Color code for UI display"))
    # Configuration
    is_active = models.BooleanField(_("Active"), default=True)
    requires_bin_locations = models.BooleanField(_("Requires Bin Locations"), default=False, 
                                               help_text=_("If true, this location type must have bin locations defined"))
    is_storage = models.BooleanField(_("Is Storage Location"), default=True, 
                                   help_text=_("If true, this location can store inventory"))
    is_receiving = models.BooleanField(_("Is Receiving Location"), default=False,
                                     help_text=_("If true, this location can receive inventory from vendors"))
    is_shipping = models.BooleanField(_("Is Shipping Location"), default=False,
                                    help_text=_("If true, this location can ship inventory to customers"))
    is_service = models.BooleanField(_("Is Service Location"), default=False,
                                   help_text=_("If true, this location is associated with a service center"))
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Location Type")
        verbose_name_plural = _("Location Types")
        unique_together = [['tenant_id', 'code']]
        ordering = ['name']
        
    def __str__(self):
        return self.name


class Location(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """
    Warehouse location model for inventory items
    """
    name = models.CharField(_("Name"), max_length=255)
    code = models.CharField(_("Code"), max_length=50)
    location_type = models.ForeignKey(
        LocationType,
        on_delete=models.PROTECT,
        related_name="locations",
        verbose_name=_("Location Type"),
        null=True, blank=True
    )
    parent = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True, blank=True,
        related_name="children",
        verbose_name=_("Parent Location"),
        help_text=_("If this is a sub-location (e.g., a shelf within a warehouse)")
    )
    # Physical address
    address = models.TextField(_("Address"), blank=True)
    city = models.CharField(_("City"), max_length=100, blank=True)
    state = models.CharField(_("State/Province"), max_length=100, blank=True)
    country = models.CharField(_("Country"), max_length=100, blank=True)
    postal_code = models.CharField(_("Postal Code"), max_length=20, blank=True)
    latitude = models.DecimalField(_("Latitude"), max_digits=10, decimal_places=7, null=True, blank=True)
    longitude = models.DecimalField(_("Longitude"), max_digits=10, decimal_places=7, null=True, blank=True)
    # Contact information
    contact_name = models.CharField(_("Contact Name"), max_length=100, blank=True)
    phone = models.CharField(_("Phone"), max_length=50, blank=True)
    email = models.EmailField(_("Email"), blank=True)
    # Storage capacity
    area_sqm = models.DecimalField(_("Area (sqm)"), max_digits=10, decimal_places=2, null=True, blank=True,
                                  help_text=_("Storage area in square meters"))
    max_items = models.PositiveIntegerField(_("Maximum Items"), null=True, blank=True,
                                          help_text=_("Maximum number of items this location can store"))
    max_volume = models.DecimalField(_("Maximum Volume"), max_digits=10, decimal_places=2, null=True, blank=True,
                                   help_text=_("Maximum volume in cubic meters"))
    max_weight = models.DecimalField(_("Maximum Weight"), max_digits=10, decimal_places=2, null=True, blank=True,
                                   help_text=_("Maximum weight in kilograms"))
    # Status
    is_active = models.BooleanField(_("Active"), default=True)
    # Additional details
    notes = models.TextField(_("Notes"), blank=True)
    attributes = models.JSONField(_("Custom Attributes"), default=dict, blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Location")
        verbose_name_plural = _("Locations")
        unique_together = [['tenant_id', 'code']]
        
    def __str__(self):
        return f"{self.name} ({self.code})"
    
    def get_full_path(self):
        """Return full location path including parent locations"""
        path = []
        current = self
        while current:
            path.insert(0, current.name)
            current = current.parent
        return " > ".join(path)
    
    def get_storage_summary(self):
        """Get summary of items stored in this location"""
        total_items = self.items.count()
        unique_items = self.items.values('item').distinct().count()
        total_quantity = sum(item_loc.quantity for item_loc in self.items.all())
        
        return {
            "total_items": total_items,
            "unique_items": unique_items,
            "total_quantity": total_quantity
        }
    
    def clean(self):
        """Validate the location data"""
        from django.core.exceptions import ValidationError
        
        # Prevent circular references in the parent hierarchy
        if self.parent:
            current = self.parent
            while current:
                if current == self:
                    raise ValidationError(_("Circular reference detected in parent hierarchy"))
                current = current.parent
        
        # Ensure location type settings are consistent
        if self.location_type and self.location_type.requires_bin_locations and not self.children.exists():
            # Only warn, don't prevent saving
            from django.core.exceptions import ValidationError
            raise ValidationError(
                {'location_type': _("This location type requires bin locations, but none are defined.")},
                code='warning'  # Custom code to indicate this is a warning, not an error
            )


class BinLocation(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """
    Specific storage bin within a location (e.g., shelves, racks, bins)
    """
    location = models.ForeignKey(
        Location,
        on_delete=models.CASCADE,
        related_name="bins",
        verbose_name=_("Location")
    )
    name = models.CharField(_("Name"), max_length=100)
    code = models.CharField(_("Code"), max_length=50)
    description = models.TextField(_("Description"), blank=True)
    is_active = models.BooleanField(_("Active"), default=True)
    # Bin position information
    aisle = models.CharField(_("Aisle"), max_length=20, blank=True, help_text=_("Aisle identifier"))
    rack = models.CharField(_("Rack"), max_length=20, blank=True, help_text=_("Rack identifier"))
    shelf = models.CharField(_("Shelf"), max_length=20, blank=True, help_text=_("Shelf identifier"))
    position = models.CharField(_("Position"), max_length=20, blank=True, help_text=_("Position identifier"))
    barcode = models.CharField(_("Barcode"), max_length=100, blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Bin Location")
        verbose_name_plural = _("Bin Locations")
        unique_together = [['tenant_id', 'location', 'code']]
        ordering = ['location', 'aisle', 'rack', 'shelf', 'position']
    
    def __str__(self):
        pos_parts = []
        if self.aisle:
            pos_parts.append(f"A{self.aisle}")
        if self.rack:
            pos_parts.append(f"R{self.rack}")
        if self.shelf:
            pos_parts.append(f"S{self.shelf}")
        if self.position:
            pos_parts.append(f"P{self.position}")
            
        position_str = "-".join(pos_parts) if pos_parts else ""
        
        if position_str:
            return f"{self.name} ({position_str})"
        return self.name


class ItemLocation(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """
    Many-to-many relationship between items and locations with quantity
    """
    item = models.ForeignKey('inventory.Item', related_name='locations', on_delete=models.CASCADE)
    location = models.ForeignKey(Location, related_name='items', on_delete=models.CASCADE)
    quantity = models.DecimalField(_("Quantity"), max_digits=10, decimal_places=2, default=0)
    bin_location = models.ForeignKey(
        BinLocation, 
        related_name='items',
        on_delete=models.SET_NULL,
        null=True, blank=True,
        verbose_name=_("Bin Location")
    )
    # Additional tracking information
    reorder_point = models.DecimalField(_("Reorder Point"), max_digits=10, decimal_places=2, null=True, blank=True,
                                      help_text=_("Minimum quantity before reordering for this specific location"))
    max_stock = models.DecimalField(_("Maximum Stock"), max_digits=10, decimal_places=2, null=True, blank=True,
                                  help_text=_("Maximum quantity to stock at this location"))
    min_stock = models.DecimalField(_("Minimum Stock"), max_digits=10, decimal_places=2, null=True, blank=True,
                                  help_text=_("Minimum quantity to maintain at this location"))
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Item Location")
        verbose_name_plural = _("Item Locations")
        unique_together = [['tenant_id', 'item', 'location', 'bin_location']]
        
    def __str__(self):
        bin_info = f" ({self.bin_location})" if self.bin_location else ""
        return f"{self.item.name} at {self.location.name}{bin_info}: {self.quantity}"
    
    @property
    def is_low_stock(self):
        """Check if item is below minimum stock level at this location"""
        if self.min_stock is None:
            return False
        return self.quantity < self.min_stock


class TransferOrder(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """
    Transfer order for moving items between locations
    """
    STATUS_CHOICES = (
        ('draft', _('Draft')),
        ('pending', _('Pending')),
        ('in_transit', _('In Transit')),
        ('completed', _('Completed')),
        ('cancelled', _('Cancelled')),
    )
    
    reference = models.CharField(_("Reference"), max_length=100, unique=True)
    source_location = models.ForeignKey(
        Location, related_name='outgoing_transfers', 
        on_delete=models.PROTECT, verbose_name=_("Source Location")
    )
    destination_location = models.ForeignKey(
        Location, related_name='incoming_transfers', 
        on_delete=models.PROTECT, verbose_name=_("Destination Location")
    )
    status = models.CharField(_("Status"), max_length=20, choices=STATUS_CHOICES, default='draft')
    notes = models.TextField(_("Notes"), blank=True)
    items_count = models.IntegerField(_("Items Count"), default=0, help_text=_("Total number of items in this transfer"))
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Transfer Order")
        verbose_name_plural = _("Transfer Orders")
        
    def __str__(self):
        return f"Transfer {self.reference}: {self.source_location.name} → {self.destination_location.name}"


class TransferOrderItem(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """
    Items included in a transfer order
    """
    transfer_order = models.ForeignKey(
        TransferOrder, related_name='items', 
        on_delete=models.CASCADE, verbose_name=_("Transfer Order")
    )
    item = models.ForeignKey(
        'inventory.Item', related_name='transfers',
        on_delete=models.PROTECT, verbose_name=_("Item")
    )
    quantity = models.DecimalField(_("Quantity"), max_digits=10, decimal_places=2)
    notes = models.TextField(_("Notes"), blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Transfer Order Item")
        verbose_name_plural = _("Transfer Order Items")
        unique_together = [['transfer_order', 'item']]
        
    def __str__(self):
        return f"{self.quantity} x {self.item.name} in {self.transfer_order.reference}"


class Transfer(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """
    Transfer record for moved items between locations
    
    This is a simplified transfer record model for reporting purposes.
    """
    source_location = models.ForeignKey(
        Location, related_name='outgoing_simple_transfers', 
        on_delete=models.PROTECT, verbose_name=_("Source Location")
    )
    destination_location = models.ForeignKey(
        Location, related_name='incoming_simple_transfers', 
        on_delete=models.PROTECT, verbose_name=_("Destination Location")
    )
    items_count = models.IntegerField(_("Items Count"), default=0)
    reference = models.CharField(_("Reference"), max_length=100, blank=True)
    notes = models.TextField(_("Notes"), blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Transfer")
        verbose_name_plural = _("Transfers")
        
    def __str__(self):
        return f"Transfer {self.id}: {self.source_location.name} → {self.destination_location.name}"
