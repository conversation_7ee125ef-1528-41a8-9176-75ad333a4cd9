<!DOCTYPE html>
{% load i18n %}
<html lang="{{ LANGUAGE_CODE }}" dir="{% if LANGUAGE_CODE == 'ar' %}rtl{% else %}ltr{% endif %}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{% trans "نظام إدارة المخزون" %}{% endblock %}</title>
    
    <!-- Tailwind CSS -->
    <link href="/static/css/tailwind/tailwind.output.css" rel="stylesheet">
    
    <!-- Flowbite CSS & JS from CDN -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/flowbite/2.2.0/flowbite.min.css" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/flowbite/2.2.0/flowbite.min.js" defer></script>
    
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- HTMX -->
    <script src="https://unpkg.com/htmx.org@1.9.4" defer></script>
    
    <!-- Alpine.js -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.12.0/dist/cdn.min.js"></script>
    
    <!-- Google Tajawal Arabic Font -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- RTL CSS -->
    <link href="/static/css/rtl.css" rel="stylesheet">
    
    <style>
        /* Animations and transitions */
        .hover-scale {
            transition: transform 0.3s ease;
        }
        .hover-scale:hover {
            transform: scale(1.03);
        }
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        @keyframes fadeIn {
            0% { opacity: 0; }
            100% { opacity: 1; }
        }
        /* Fix for RTL Font Awesome icons */
        .fa, .fas, .far, .fal, .fab {
            display: inline-block;
        }
        
        /* Base RTL styles */
        body {
            font-family: 'Tajawal', sans-serif;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white border-gray-200 px-4 py-2.5 shadow-md">
        <div class="container flex flex-wrap justify-between items-center mx-auto">
            <!-- Logo -->
            <a href="{% url 'home' %}" class="flex items-center">
                <img src="/static/images/logo.png" alt="Logo" class="h-10 {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}">
                <span class="self-center text-xl font-semibold whitespace-nowrap md:block hidden">{% trans "نظام Aftersails" %}</span>
            </a>
            
            <!-- Main Navigation - Icons Only -->
            <div class="flex items-center justify-center">
                <ul class="flex {% if LANGUAGE_CODE == 'ar' %}space-x-6 space-x-reverse rtl{% else %}space-x-6{% endif %}">
                    {% if user.is_superuser or primary_role.can_access_inventory %}
                    <li>
                        <a href="{% url 'inventory:dashboard' %}" class="text-gray-700 hover:text-blue-700" title="{% trans 'المخزون' %}">
                            <i class="fas fa-warehouse text-xl"></i>
                        </a>
                    </li>
                    {% endif %}
                    
                    {% if user.is_superuser or primary_role.can_access_setup %}
                    <li>
                        <a href="{% url 'setup:dashboard' %}" class="text-gray-700 hover:text-blue-700" title="{% trans 'الإعدادات' %}">
                            <i class="fas fa-cogs text-xl"></i>
                        </a>
                    </li>
                    {% endif %}
                    
                    {% if user.is_superuser or primary_role.can_access_work_orders %}
                    <li>
                        <a href="{% url 'work_orders:work_order_list' %}" class="text-gray-700 hover:text-blue-700" title="{% trans 'أوامر العمل' %}">
                            <i class="fas fa-tasks text-xl"></i>
                        </a>
                    </li>
                    {% endif %}
                    
                    {% if user.is_superuser or primary_role.can_access_warehouse %}
                    <li>
                        <a href="#" class="text-gray-700 hover:text-blue-700" title="{% trans 'المستودع' %}">
                            <i class="fas fa-boxes text-xl"></i>
                        </a>
                    </li>
                    {% endif %}
                    
                    {% if user.is_superuser or primary_role.can_access_sales %}
                    <li>
                        <a href="#" class="text-gray-700 hover:text-blue-700" title="{% trans 'المبيعات' %}">
                            <i class="fas fa-shopping-cart text-xl"></i>
                        </a>
                    </li>
                    {% endif %}
                    
                    {% if user.is_superuser or primary_role.can_access_purchases %}
                    <li>
                        <a href="#" class="text-gray-700 hover:text-blue-700" title="{% trans 'المشتريات' %}">
                            <i class="fas fa-shopping-basket text-xl"></i>
                        </a>
                    </li>
                    {% endif %}
                    
                    {% if user.is_superuser or primary_role.can_access_reports %}
                    <li>
                        <a href="{% url 'reports:report_list' %}" class="text-gray-700 hover:text-blue-700" title="{% trans 'التقارير' %}">
                            <i class="fas fa-chart-bar text-xl"></i>
                        </a>
                    </li>
                    {% endif %}
                    
                    {% if user.is_superuser or primary_role.can_access_settings %}
                    <li>
                        <a href="#" class="text-gray-700 hover:text-blue-700" title="{% trans 'الإعدادات' %}">
                            <i class="fas fa-sliders-h text-xl"></i>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </div>
            
            <!-- User Menu -->
            <div class="flex items-center">
                {% if user.is_authenticated %}
                
                <!-- Notification Icon -->
                {% if messages %}
                <button type="button" class="flex items-center {% if LANGUAGE_CODE == 'ar' %}ml-4{% else %}mr-4{% endif %} text-sm" id="notification-button" aria-expanded="false" data-dropdown-toggle="notification-dropdown">
                    <span class="sr-only">{% trans "عرض الإشعارات" %}</span>
                    <div class="relative">
                        <i class="fas fa-bell text-xl text-gray-600 hover:text-blue-600"></i>
                        <span class="absolute top-0 right-0 inline-flex items-center justify-center w-4 h-4 text-xs font-bold text-white bg-red-500 rounded-full">{{ messages|length }}</span>
                    </div>
                </button>
                <!-- Notification Dropdown -->
                <div class="hidden z-50 my-4 w-80 text-base list-none bg-white rounded-lg divide-y divide-gray-100 shadow-lg" id="notification-dropdown">
                    <div class="py-2 px-4 bg-blue-600 rounded-t-lg">
                        <span class="block text-sm font-semibold text-white">{% trans "الإشعارات" %}</span>
                    </div>
                    <div class="py-2 px-4 max-h-64 overflow-y-auto">
                        {% for message in messages %}
                        <div class="py-2 border-b border-gray-100 last:border-b-0">
                            <div class="flex items-start">
                                <i class="flex-shrink-0 w-5 h-5 text-blue-600 fas fa-info-circle mt-0.5"></i>
                                <div class="{% if LANGUAGE_CODE == 'ar' %}mr-3{% else %}ml-3{% endif %} text-sm text-gray-700">
                                    {{ message }}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
                
                <button type="button" class="flex text-sm focus:ring-4 focus:ring-blue-300" id="user-menu-button" aria-expanded="false" data-dropdown-toggle="user-dropdown">
                    <span class="sr-only">{% trans "فتح قائمة المستخدم" %}</span>
                    <div class="w-8 h-8 text-white flex items-center justify-center bg-blue-600 rounded-full shadow-md">
                        {{ user.username|first|upper }}
                    </div>
                </button>
                <!-- Dropdown menu -->
                <div class="hidden z-50 my-4 w-56 text-base list-none bg-white rounded-lg divide-y divide-gray-100 shadow-lg" id="user-dropdown">
                    <div class="py-3 px-4 bg-blue-600 rounded-t-lg">
                        <span class="block text-sm font-semibold text-white">{{ user.username }}</span>
                        <span class="block text-xs text-blue-100 truncate">{{ user.email }}</span>
                    </div>
                    <ul class="py-1" aria-labelledby="user-menu-button">
                        <li>
                            <a href="{% url 'inventory:dashboard' %}" class="flex items-center py-2 px-4 text-sm text-gray-700 hover:bg-gray-100 hover:text-blue-600 transition-colors">
                                <i class="fas fa-tachometer-alt text-blue-500 {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i> {% trans "لوحة التحكم" %}
                            </a>
                        </li>
                        <li>
                            <a href="#" class="flex items-center py-2 px-4 text-sm text-gray-700 hover:bg-gray-100 hover:text-blue-600 transition-colors">
                                <i class="fas fa-cog text-indigo-500 {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i> {% trans "الإعدادات" %}
                            </a>
                        </li>
                        <li>
                            <a href="{% url 'core:logout' %}" class="flex items-center py-2 px-4 text-sm text-gray-700 hover:bg-gray-100 hover:text-red-600 transition-colors">
                                <i class="fas fa-sign-out-alt text-red-500 {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i> {% trans "تسجيل الخروج" %}
                            </a>
                        </li>
                    </ul>
                </div>
                {% endif %}
                
                <!-- Mobile menu button -->
                <button data-collapse-toggle="mobile-menu" type="button" class="inline-flex items-center p-2 {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %} text-sm text-gray-500 rounded-lg md:hidden hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200" aria-controls="mobile-menu" aria-expanded="false">
                    <span class="sr-only">{% trans "فتح القائمة الرئيسية" %}</span>
                    <i class="fas fa-bars"></i>
                </button>
            </div>
        </div>
        
        <!-- Mobile Navigation -->
        <div class="hidden w-full md:hidden" id="mobile-menu">
            <ul class="flex flex-col mt-4 text-center">
                {% if user.is_superuser or primary_role.can_access_inventory %}
                <li class="py-2">
                    <a href="{% url 'inventory:dashboard' %}" class="block py-2 px-3 text-gray-700 hover:bg-gray-50">
                        <i class="fas fa-warehouse {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i> {% trans "المخزون" %}
                    </a>
                </li>
                {% endif %}
                
                {% if user.is_superuser or primary_role.can_access_setup %}
                <li class="py-2">
                    <a href="{% url 'setup:dashboard' %}" class="block py-2 px-3 text-gray-700 hover:bg-gray-50">
                        <i class="fas fa-cogs {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i> {% trans "الإعدادات" %}
                    </a>
                </li>
                {% endif %}
                
                {% if user.is_superuser or primary_role.can_access_work_orders %}
                <li class="py-2">
                    <a href="{% url 'work_orders:work_order_list' %}" class="block py-2 px-3 text-gray-700 hover:bg-gray-50">
                        <i class="fas fa-tasks {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i> {% trans "أوامر العمل" %}
                    </a>
                </li>
                {% endif %}
                
                {% if user.is_superuser or primary_role.can_access_warehouse %}
                <li class="py-2">
                    <a href="#" class="block py-2 px-3 text-gray-700 hover:bg-gray-50">
                        <i class="fas fa-boxes {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i> {% trans "المستودع" %}
                    </a>
                </li>
                {% endif %}
                
                {% if user.is_superuser or primary_role.can_access_sales %}
                <li class="py-2">
                    <a href="#" class="block py-2 px-3 text-gray-700 hover:bg-gray-50">
                        <i class="fas fa-shopping-cart {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i> {% trans "المبيعات" %}
                    </a>
                </li>
                {% endif %}
                
                {% if user.is_superuser or primary_role.can_access_purchases %}
                <li class="py-2">
                    <a href="#" class="block py-2 px-3 text-gray-700 hover:bg-gray-50">
                        <i class="fas fa-shopping-basket {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i> {% trans "المشتريات" %}
                    </a>
                </li>
                {% endif %}
                
                {% if user.is_superuser or primary_role.can_access_reports %}
                <li class="py-2">
                    <a href="{% url 'reports:report_list' %}" class="block py-2 px-3 text-gray-700 hover:bg-gray-50">
                        <i class="fas fa-chart-bar {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i> {% trans "التقارير" %}
                    </a>
                </li>
                {% endif %}
                
                {% if user.is_superuser or primary_role.can_access_settings %}
                <li class="py-2">
                    <a href="#" class="block py-2 px-3 text-gray-700 hover:bg-gray-50">
                        <i class="fas fa-sliders-h {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i> {% trans "الإعدادات" %}
                    </a>
                </li>
                {% endif %}
            </ul>
        </div>
    </nav>
    
    <!-- Messages section (now hidden, displayed in popup) -->
    <div class="hidden">
        {% if messages %}
        <div class="container mx-auto px-4 py-2">
            {% for message in messages %}
            <div id="alert-{{ forloop.counter }}" class="flex p-4 mb-4 {{ message.tags }} bg-blue-100 text-blue-700 rounded-lg" role="alert">
                <i class="flex-shrink-0 w-5 h-5 text-blue-700 fas fa-info-circle"></i>
                <div class="{% if LANGUAGE_CODE == 'ar' %}mr-3{% else %}ml-3{% endif %} text-sm font-medium">
                    {{ message }}
                </div>
                <button type="button" class="ml-auto -mx-1.5 -my-1.5 bg-blue-100 text-blue-500 rounded-lg focus:ring-2 focus:ring-blue-400 p-1.5 hover:bg-blue-200 inline-flex h-8 w-8" data-dismiss-target="#alert-{{ forloop.counter }}" aria-label="Close">
                    <span class="sr-only">{% trans "إغلاق" %}</span>
                    <i class="w-5 h-5 fas fa-times"></i>
                </button>
            </div>
            {% endfor %}
        </div>
        {% endif %}
    </div>
    
    <!-- Main Content -->
    <main>
        {% block content %}{% endblock %}
    </main>
    
    <!-- Footer -->
    <footer class="bg-white shadow-inner mt-8 py-4">
        <div class="container mx-auto px-4">
            <p class="text-center text-gray-500 text-sm">
                {% trans "نظام إدارة المخزون" %} &copy; {% now "Y" %}
            </p>
        </div>
    </footer>
    
    {% block extra_js %}{% endblock %}
</body>
</html> 