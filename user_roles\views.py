from django.shortcuts import render
from django.http import JsonResponse
from django.contrib.auth.decorators import login_required
from setup.models import ServiceCenter, Company
import json
import uuid

# Create your views here.

@login_required
def get_tenant_info(request):
    """
    API endpoint to get tenant_id from ServiceCenter or Company objects
    """
    entity_type = request.GET.get('entity_type')
    entity_id = request.GET.get('entity_id')
    
    if not entity_type or not entity_id:
        return JsonResponse({'error': 'Missing parameters'}, status=400)
    
    try:
        if entity_type == 'service_center':
            entity = ServiceCenter.objects.get(id=entity_id)
            return JsonResponse({'tenant_id': str(entity.tenant_id)})
        elif entity_type == 'company':
            entity = Company.objects.get(id=entity_id)
            return JsonResponse({'tenant_id': str(entity.tenant_id)})
        else:
            return JsonResponse({'error': 'Invalid entity type'}, status=400)
    except (ServiceCenter.DoesNotExist, Company.DoesNotExist):
        return JsonResponse({'error': 'Entity not found'}, status=404)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)
