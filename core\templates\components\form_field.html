{% load i18n %}
{% load form_tags %}

{# 
  Flowbite form field component
  
  Parameters:
  - field: Django form field
  - label_class: Optional additional classes for the label
  - input_class: Optional additional classes for the input
  - help_text_class: Optional additional classes for the help text
  - error_class: Optional additional classes for error messages
  - wrapper_class: Optional additional classes for the field wrapper
  - horizontal: Optional boolean for horizontal layout (label next to field)
  - required: Optional boolean to override field.field.required
  - label: Optional override for field.label
  - id: Optional override for field.id_for_label
  - add_label: Optional boolean, set to False to hide label
  - add_error: Optional boolean, set to False to hide error
  - add_help: Optional boolean, set to False to hide help text
  - icon: Optional Font Awesome icon class to display in the field
#}

{% with field_id=id|default:field.auto_id field_required=required|default:field.field.required field_label=label|default:field.label %}
<div class="mb-4 {{ wrapper_class|default:'' }}">
  {% if horizontal %}
    <div class="sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start">
  {% endif %}
  
  {% if add_label|default:True %}
    <label 
      for="{{ field_id }}" 
      class="block mb-2 text-sm font-medium text-gray-900 dark:text-white {{ label_class|default:'' }} {% if horizontal %}sm:mt-px sm:pt-2{% endif %}"
    >
      {{ field_label }}
      {% if field_required %}<span class="text-red-500">*</span>{% endif %}
    </label>
  {% endif %}
  
  {% if horizontal %}
    <div class="sm:col-span-2 sm:mt-0">
  {% endif %}
  
  <div class="relative">
    {% if icon %}
    <div class="absolute inset-y-0 left-0 rtl:right-0 rtl:left-auto flex items-center pl-3 rtl:pr-3 rtl:pl-0 pointer-events-none">
      <i class="fas {{ icon }} text-gray-400"></i>
    </div>
    {% endif %}
  
    {% if field.field.widget.input_type == 'checkbox' %}
      <div class="flex items-center">
        {{ field }}
        <label for="{{ field_id }}" class="ml-2 text-sm font-medium text-gray-900 dark:text-gray-300">
          {{ field_label }}
        </label>
      </div>
    {% elif field|fieldtype == 'select' or field|fieldtype == 'selectmultiple' %}
      {{ field|add_class:"bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"|add_class:input_class|default:'' }}
    {% elif field|fieldtype == 'textarea' %}
      {{ field|add_class:"bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"|add_class:input_class|default:'' }}
    {% elif field|fieldtype == 'radio' %}
      <div class="flex flex-col space-y-2">
        {% for radio in field %}
          <div class="flex items-center">
            {{ radio.tag }}
            <label for="{{ radio.id_for_label }}" class="ml-2 text-sm font-medium text-gray-900 dark:text-gray-300">
              {{ radio.choice_label }}
            </label>
          </div>
        {% endfor %}
      </div>
    {% elif field|fieldtype == 'date' or field|fieldtype == 'datetime' %}
      <div class="relative">
        {{ field|add_class:"bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 pl-10 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"|add_class:input_class|default:'' }}
        <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
          <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
          </svg>
        </div>
      </div>
    {% elif field|fieldtype == 'file' %}
      <div class="flex items-center justify-center w-full">
        <label for="{{ field_id }}" class="flex flex-col items-center justify-center w-full h-24 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 dark:hover:bg-bray-800 dark:bg-gray-700 hover:bg-gray-100 dark:border-gray-600 dark:hover:border-gray-500 dark:hover:bg-gray-600">
          <div class="flex flex-col items-center justify-center pt-5 pb-6">
            <svg class="w-8 h-8 mb-4 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 16">
              <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2"/>
            </svg>
            <p class="mb-2 text-sm text-gray-500 dark:text-gray-400"><span class="font-semibold">{% trans "Click to upload" %}</span> {% trans "or drag and drop" %}</p>
            <p class="text-xs text-gray-500 dark:text-gray-400">{{ field.help_text }}</p>
          </div>
          {{ field|add_class:"hidden"|add_class:input_class|default:'' }}
        </label>
      </div>
    {% else %}
      {% if icon %}
        {{ field|add_class:"bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 pl-10 rtl:pr-10 rtl:pl-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"|add_class:input_class|default:'' }}
      {% else %}
        {{ field|add_class:"bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"|add_class:input_class|default:'' }}
      {% endif %}
    {% endif %}
  </div>
  
  {% if add_help|default:True and field.help_text and field|fieldtype != 'file' %}
    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400 {{ help_text_class|default:'' }}">{{ field.help_text }}</p>
  {% endif %}
  
  {% if add_error|default:True and field.errors %}
    {% for error in field.errors %}
      <p class="mt-1 text-sm text-red-600 dark:text-red-500 {{ error_class|default:'' }}">{{ error }}</p>
    {% endfor %}
  {% endif %}
  
  {% if horizontal %}
    </div>
  </div>
  {% endif %}
</div>
{% endwith %} 