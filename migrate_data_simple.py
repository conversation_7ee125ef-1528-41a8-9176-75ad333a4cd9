#!/usr/bin/env python3
"""
Simple Data Migration Script: SQLite to PostgreSQL
=================================================

This script uses raw SQLite connections and Django ORM for PostgreSQL
to avoid Django database configuration conflicts.

Usage:
    python migrate_data_simple.py [--dry-run] [--sqlite-path path/to/db.sqlite3]
"""

import os
import sys
import django
from pathlib import Path
import logging
import sqlite3

# Add the project root to Python path
BASE_DIR = Path(__file__).resolve().parent
sys.path.insert(0, str(BASE_DIR))

# Set up Django with proper timezone handling
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
os.environ.setdefault('TZ', 'Africa/Cairo')
django.setup()

from django.apps import apps
from django.conf import settings
from django.core.management import call_command
from django.db.models import ForeignKey, OneToOneField
from collections import defaultdict
import argparse

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(message)s')
logger = logging.getLogger(__name__)


class SimpleDataMigrator:
    def __init__(self, sqlite_path='db.sqlite3', dry_run=False):
        self.sqlite_path = sqlite_path
        self.dry_run = dry_run
        self.exclude_apps = [
            'contenttypes', 'auth', 'sessions', 'admin', 'messages',
            'django_otp', 'waffle', 'drf_api_logger', 'authtoken'
        ]
        # Priority models (foundation tables)
        self.priority_models = [
            'api.WebServiceLog', 'feature_flags.ModuleFlag', 'inventory.ItemClassification',
            'inventory.UnitOfMeasurement', 'inventory.MovementType', 'inventory.InventoryValuationMethod',
            'warehouse.LocationType', 'purchases.Supplier', 'reports.Report', 'reports.Dashboard',
            'app_settings.TenantSetting', 'app_settings.SystemSetting', 'app_settings.TenantProfile',
            'notifications.NotificationType', 'notifications.ActionItem', 'notifications.NotificationPreference',
            'notifications.WebhookEndpoint', 'notifications.EmailTemplate', 'notifications.EmailAction',
            'work_orders.WorkOrderType', 'work_orders.MaintenanceSchedule', 'setup.ServiceLevel',
            'setup.Franchise', 'setup.ServiceCenterType', 'setup.VehicleMake', 'setup.UserRole',
            'setup.TechnicianSpecialization', 'user_roles.Role', 'franchise_setup.FranchiseTemplate',
            'billing.CustomerClassification', 'billing.PromotionRule', 'billing.DiscountRule',
            'billing.InsuranceCompany', 'billing.WarrantyType', 'billing.DiscountType',
            'billing.PaymentMethod', 'sites.Site', 'otp_totp.TOTPDevice'
        ]
        
    def migrate(self):
        """Main migration method"""
        if not os.path.exists(self.sqlite_path):
            logger.error(f"❌ SQLite file not found: {self.sqlite_path}")
            return False
            
        logger.info(f"🚀 Starting migration from {self.sqlite_path} to PostgreSQL")
        
        if self.dry_run:
            logger.info("⚠️  DRY RUN MODE - No data will be actually migrated")
        
        try:
            # Test SQLite connection
            self._test_connections()
            
            # Get all models to migrate
            models_to_migrate = self._get_models_to_migrate()
            sorted_models = self._sort_models_by_dependencies(models_to_migrate)
            
            total_records = 0
            migrated_records = 0
            failed_models = []
            
            logger.info(f"📋 Found {len(sorted_models)} models to migrate")
            
            # Migrate each model
            for model in sorted_models:
                try:
                    count = self._migrate_model_simple(model)
                    if count is not None:
                        total_records += count
                        if not self.dry_run:
                            migrated_records += count
                    else:
                        failed_models.append(model.__name__)
                except Exception as e:
                    logger.error(f"❌ Critical error migrating {model.__name__}: {e}")
                    failed_models.append(model.__name__)
                    continue
            
            # Report results
            if failed_models:
                logger.warning(f"⚠️  Failed to migrate {len(failed_models)} models: {', '.join(failed_models)}")
            
            if self.dry_run:
                logger.info(f"✅ DRY RUN COMPLETE: Would migrate {total_records} total records")
            else:
                logger.info(f"✅ Migration completed: {migrated_records}/{total_records} records migrated")
                
            return len(failed_models) == 0  # Success if no failures
            
        except Exception as e:
            logger.error(f"❌ Migration failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def _test_connections(self):
        """Test database connections"""
        # Test SQLite connection
        try:
            conn = sqlite3.connect(self.sqlite_path)
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            conn.close()
            logger.info("✅ SQLite connection established")
        except Exception as e:
            raise Exception(f"Failed to connect to SQLite: {e}")
        
        # Test PostgreSQL connection
        try:
            from django.db import connection
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
            logger.info("✅ PostgreSQL connection established")
        except Exception as e:
            raise Exception(f"Failed to connect to PostgreSQL: {e}")
    
    def _get_models_to_migrate(self):
        """Get all Django models that should be migrated"""
        models = []
        for app_config in apps.get_app_configs():
            if app_config.label not in self.exclude_apps:
                for model in app_config.get_models():
                    if not model._meta.abstract and not model._meta.proxy:
                        models.append(model)
        return models
    
    def _sort_models_by_dependencies(self, models):
        """Sort models by dependencies using the priority list and basic analysis"""
        model_lookup = {f"{model._meta.app_label}.{model.__name__}": model for model in models}
        
        # Start with priority models that exist
        sorted_models = []
        remaining_models = models.copy()
        
        # Add priority models first
        for priority_key in self.priority_models:
            if priority_key in model_lookup:
                model = model_lookup[priority_key]
                if model in remaining_models:
                    sorted_models.append(model)
                    remaining_models.remove(model)
        
        # Simple dependency sort for remaining models
        max_iterations = len(remaining_models) * 2
        iteration = 0
        
        while remaining_models and iteration < max_iterations:
            iteration += 1
            added_this_round = []
            
            for model in remaining_models[:]:
                # Check if model has foreign keys to models still in remaining list
                has_blocking_deps = False
                
                for field in model._meta.get_fields():
                    if isinstance(field, (ForeignKey, OneToOneField)):
                        related_model = field.related_model
                        if related_model and related_model in remaining_models and related_model != model:
                            has_blocking_deps = True
                            break
                
                if not has_blocking_deps:
                    added_this_round.append(model)
            
            if added_this_round:
                sorted_models.extend(added_this_round)
                for model in added_this_round:
                    remaining_models.remove(model)
            elif remaining_models:
                # Break circular dependency by adding first remaining model
                model = remaining_models.pop(0)
                sorted_models.append(model)
                logger.warning(f"⚠️  Breaking circular dependency for {model.__name__}")
        
        logger.info(f"📊 Model migration order determined for {len(sorted_models)} models")
        return sorted_models
    
    def _migrate_model_simple(self, model):
        """Migrate a single model using raw SQLite and Django ORM for PostgreSQL"""
        model_name = f"{model._meta.app_label}.{model.__name__}"
        table_name = model._meta.db_table
        
        try:
            # Connect to SQLite
            sqlite_conn = sqlite3.connect(self.sqlite_path)
            sqlite_conn.row_factory = sqlite3.Row
            cursor = sqlite_conn.cursor()
            
            # Check if table exists in SQLite
            cursor.execute(
                "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
                [table_name]
            )
            if not cursor.fetchone():
                logger.info(f"📋 {model_name}: Table not found in SQLite - skipping")
                sqlite_conn.close()
                return 0
            
            # Get record count
            cursor.execute(f"SELECT COUNT(*) FROM `{table_name}`")
            record_count = cursor.fetchone()[0]
            
            if record_count == 0:
                logger.info(f"📋 {model_name}: 0 records - skipping")
                sqlite_conn.close()
                return 0
            
            logger.info(f"🔄 Migrating {model_name}: {record_count} records...")
            
            if self.dry_run:
                logger.info(f"   Would migrate {record_count} records from {table_name}")
                sqlite_conn.close()
                return record_count
            
            # Clear existing data in PostgreSQL
            model.objects.all().delete()
            
            # Get all records from SQLite
            cursor.execute(f"SELECT * FROM `{table_name}`")
            records = cursor.fetchall()
            sqlite_conn.close()
            
            if not records:
                return 0
            
            # Get field mapping
            field_names = [desc[0] for desc in cursor.description]
            model_fields = {f.column: f for f in model._meta.fields}
            
            # Create Django objects
            objects_to_create = []
            batch_size = 500
            
            for record in records:
                obj = model()
                
                # Map SQLite data to Django model fields
                for i, field_name in enumerate(field_names):
                    if field_name in model_fields and not model_fields[field_name].primary_key:
                        try:
                            value = record[i]
                            setattr(obj, field_name, value)
                        except Exception as e:
                            logger.warning(f"⚠️  Could not set field {field_name}: {e}")
                            continue
                
                objects_to_create.append(obj)
                
                # Batch create for memory efficiency
                if len(objects_to_create) >= batch_size:
                    model.objects.bulk_create(objects_to_create, ignore_conflicts=True)
                    objects_to_create = []
            
            # Create remaining objects
            if objects_to_create:
                model.objects.bulk_create(objects_to_create, ignore_conflicts=True)
            
            migrated_count = model.objects.count()
            logger.info(f"✅ Migrated {migrated_count} records from {model_name}")
            return migrated_count
            
        except Exception as e:
            logger.error(f"❌ Error migrating {model_name}: {e}")
            return None


def main():
    parser = argparse.ArgumentParser(description='Simple migration from SQLite to PostgreSQL')
    parser.add_argument(
        '--sqlite-path',
        default='db.sqlite3',
        help='Path to SQLite database file (default: db.sqlite3)'
    )
    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='Show what would be migrated without actually doing it'
    )
    
    args = parser.parse_args()
    
    print("🔄 Simple Data Migration: SQLite → PostgreSQL")
    print("=" * 50)
    
    # Check PostgreSQL configuration
    default_db = settings.DATABASES.get('default', {})
    if default_db.get('ENGINE') != 'django.db.backends.postgresql':
        logger.error("❌ PostgreSQL is not configured as the default database")
        logger.error("   Please update your settings.py DATABASES configuration")
        sys.exit(1)
    
    # Run Django migrations on PostgreSQL first
    if not args.dry_run:
        logger.info("🔄 Running Django migrations on PostgreSQL...")
        try:
            call_command('migrate', verbosity=0)
            logger.info("✅ Django migrations completed")
        except Exception as e:
            logger.error(f"❌ Failed to run Django migrations: {e}")
            sys.exit(1)
    
    # Create migrator and run migration
    migrator = SimpleDataMigrator(
        sqlite_path=args.sqlite_path,
        dry_run=args.dry_run
    )
    
    success = migrator.migrate()
    
    if success:
        logger.info("\n🎉 Migration process completed successfully!")
        if not args.dry_run:
            logger.info("💡 Post-migration steps:")
            logger.info("   1. Test your application with the migrated data")
            logger.info("   2. Verify critical business data")
            logger.info("   3. Backup your PostgreSQL database")
    else:
        logger.error("\n❌ Migration process completed with errors!")
        logger.error("   Please review the error messages above and retry")
        sys.exit(1)


if __name__ == '__main__':
    main() 