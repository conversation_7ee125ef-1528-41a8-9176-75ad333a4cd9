/**
 * Work Order Form JavaScript
 * Handles dynamic interactions for the multi-step work order form
 */

document.addEventListener('DOMContentLoaded', function() {
    initFormNavigation();
    setupOperationsHandling();
    setupPartsHandling();
    initDatepickers();
});

/**
 * Initialize the multi-step form navigation
 */
function initFormNavigation() {
    const workOrderForm = document.getElementById('work-order-form');
    const nextButtons = document.querySelectorAll('.next-step');
    const prevButtons = document.querySelectorAll('.prev-step');
    
    // Handle next step buttons
    nextButtons.forEach(button => {
        button.addEventListener('click', function() {
            const nextStep = this.dataset.next;
            const currentInput = document.querySelector('input[name="current_step"]');
            
            // Update the current step and submit if using specific validation
            if (this.hasAttribute('data-validate')) {
                // Handle custom validation through Alpine.js methods
                return; // Alpine.js will handle the navigation
            }
            
            // Otherwise update the step and navigate
            if (currentInput) {
                currentInput.value = nextStep;
            }
            
            window.location.href = `${window.location.pathname}?step=${nextStep}`;
        });
    });
    
    // Handle previous step buttons
    prevButtons.forEach(button => {
        button.addEventListener('click', function() {
            const prevStep = this.dataset.prev;
            const currentInput = document.querySelector('input[name="current_step"]');
            
            if (currentInput) {
                currentInput.value = prevStep;
            }
            
            window.location.href = `${window.location.pathname}?step=${prevStep}`;
        });
    });
}

/**
 * Initialize date picker elements
 */
function initDatepickers() {
    // Check if we're using a date picker library
    const dateInputs = document.querySelectorAll('input[type="date"]');
    
    // If using flatpickr and it's available
    if (typeof flatpickr !== 'undefined') {
        flatpickr(dateInputs, {
            dateFormat: "Y-m-d",
            allowInput: true
        });
    }
}

/**
 * Set up the operations section handling
 */
function setupOperationsHandling() {
    const addOperationBtn = document.getElementById('add-operation-btn');
    const operationsTable = document.getElementById('operations-table');
    const operationsList = document.getElementById('operations-list');
    
    if (!addOperationBtn) return;
    
    // Open the operation modal when add button is clicked
    addOperationBtn.addEventListener('click', function() {
        const operationModal = document.getElementById('operation-modal');
        const bsModal = new bootstrap.Modal(operationModal);
        bsModal.show();
    });
    
    // Handle saving the operation
    const saveOperationBtn = document.getElementById('save-operation-btn');
    if (saveOperationBtn) {
        saveOperationBtn.addEventListener('click', function() {
            // Get operation data from modal
            const operationSelect = document.getElementById('operation-select');
            const estimatedTimeInput = document.getElementById('estimated-time');
            const operationNotesInput = document.getElementById('operation-notes');
            
            if (!operationSelect || !operationSelect.value) {
                alert(gettext('Please select an operation'));
                return;
            }
            
            // Create new operation row
            const newRow = document.createElement('tr');
            const selectedOption = operationSelect.options[operationSelect.selectedIndex];
            
            newRow.innerHTML = `
                <td>${selectedOption.text}</td>
                <td>${estimatedTimeInput ? estimatedTimeInput.value : '-'}</td>
                <td>${operationNotesInput ? operationNotesInput.value : '-'}</td>
                <td>
                    <button type="button" class="btn btn-sm btn-danger remove-operation">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
                <input type="hidden" name="operations[]" value="${operationSelect.value}">
                <input type="hidden" name="operation_times[]" value="${estimatedTimeInput ? estimatedTimeInput.value : ''}">
                <input type="hidden" name="operation_notes[]" value="${operationNotesInput ? operationNotesInput.value : ''}">
            `;
            
            // Add to table
            if (operationsTable) {
                const tbody = operationsTable.querySelector('tbody') || operationsTable;
                tbody.appendChild(newRow);
            }
            
            // Close the modal
            const operationModal = document.getElementById('operation-modal');
            const bsModal = bootstrap.Modal.getInstance(operationModal);
            if (bsModal) {
                bsModal.hide();
            }
            
            // Reset the form
            if (operationSelect) operationSelect.value = '';
            if (estimatedTimeInput) estimatedTimeInput.value = '';
            if (operationNotesInput) operationNotesInput.value = '';
            
            // Set up removal for the new row
            setupOperationRemoval(newRow.querySelector('.remove-operation'));
        });
    }
    
    // Set up existing removal buttons
    document.querySelectorAll('.remove-operation').forEach(button => {
        setupOperationRemoval(button);
    });
}

/**
 * Set up removal functionality for operation buttons
 */
function setupOperationRemoval(button) {
    if (!button) return;
    
    button.addEventListener('click', function() {
        const row = this.closest('tr');
        if (row) {
            row.remove();
        }
    });
}

/**
 * Set up the parts section handling
 */
function setupPartsHandling() {
    const addPartBtn = document.getElementById('add-part-btn');
    const partsTable = document.getElementById('parts-table');
    
    if (!addPartBtn) return;
    
    // Open the part modal when add button is clicked
    addPartBtn.addEventListener('click', function() {
        const partModal = document.getElementById('part-modal');
        const bsModal = new bootstrap.Modal(partModal);
        bsModal.show();
    });
    
    // Handle saving the part
    const savePartBtn = document.getElementById('save-part-btn');
    if (savePartBtn) {
        savePartBtn.addEventListener('click', function() {
            // Get part data from modal
            const partSelect = document.getElementById('part-select');
            const quantityInput = document.getElementById('part-quantity');
            
            if (!partSelect || !partSelect.value) {
                alert(gettext('Please select a part'));
                return;
            }
            
            if (!quantityInput || !quantityInput.value || parseInt(quantityInput.value) < 1) {
                alert(gettext('Please enter a valid quantity'));
                return;
            }
            
            // Create new part row
            const newRow = document.createElement('tr');
            const selectedOption = partSelect.options[partSelect.selectedIndex];
            
            newRow.innerHTML = `
                <td>${selectedOption.text}</td>
                <td>${quantityInput.value}</td>
                <td>
                    <button type="button" class="btn btn-sm btn-danger remove-part">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
                <input type="hidden" name="parts[]" value="${partSelect.value}">
                <input type="hidden" name="part_quantities[]" value="${quantityInput.value}">
            `;
            
            // Add to table
            if (partsTable) {
                const tbody = partsTable.querySelector('tbody') || partsTable;
                tbody.appendChild(newRow);
            }
            
            // Close the modal
            const partModal = document.getElementById('part-modal');
            const bsModal = bootstrap.Modal.getInstance(partModal);
            if (bsModal) {
                bsModal.hide();
            }
            
            // Reset the form
            if (partSelect) partSelect.value = '';
            if (quantityInput) quantityInput.value = '1';
            
            // Set up removal for the new row
            setupPartRemoval(newRow.querySelector('.remove-part'));
        });
    }
    
    // Set up existing removal buttons
    document.querySelectorAll('.remove-part').forEach(button => {
        setupPartRemoval(button);
    });
}

/**
 * Set up removal functionality for part buttons
 */
function setupPartRemoval(button) {
    if (!button) return;
    
    button.addEventListener('click', function() {
        const row = this.closest('tr');
        if (row) {
            row.remove();
        }
    });
}

/**
 * Get translation function fallback
 * Use Django's gettext if available, otherwise return the string
 */
function gettext(string) {
    if (window.gettext) {
        return window.gettext(string);
    }
    return string;
}

/**
 * Get CSRF token from cookies
 */
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            // Does this cookie string begin with the name we want?
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
} 