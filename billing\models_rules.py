from django.db import models
from django.utils.translation import gettext_lazy as _
from core.models.common import TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel
from core.querysets import BaseQuerySet
from django.utils import timezone
from setup.models import Customer, Vehicle, ServiceCenter
from decimal import Decimal
import uuid
import json

class PromotionRule(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """
    Dynamic rule for promotions, discounts, and special pricing
    """
    RULE_TYPE_CHOICES = [
        ('discount', _('Discount')),
        ('warranty', _('Extended Warranty')),
        ('insurance', _('Insurance Coverage')),
        ('recall', _('Recall')),
        ('special', _('Special Offer')),
    ]
    
    APPROVAL_LEVEL_CHOICES = [
        ('none', _('No Approval Needed')),
        ('manager', _('Manager Approval')),
        ('director', _('Director Approval')),
        ('executive', _('Executive Approval')),
    ]
    
    name = models.CharField(_('Rule Name'), max_length=255)
    description = models.TextField(_('Description'), blank=True)
    rule_type = models.CharField(_('Rule Type'), max_length=20, choices=RULE_TYPE_CHOICES)
    
    # Priority (higher number = higher priority when multiple rules match)
    priority = models.PositiveIntegerField(_('Priority'), default=0)
    
    # Validity period
    start_date = models.DateTimeField(_('Start Date/Time'), default=timezone.now)
    end_date = models.DateTimeField(_('End Date/Time'), null=True, blank=True)
    
    # Limit usage
    max_usages = models.PositiveIntegerField(_('Maximum Uses'), null=True, blank=True)
    current_usages = models.PositiveIntegerField(_('Current Uses'), default=0)
    
    # Status
    is_active = models.BooleanField(_('Is Active'), default=True)
    
    # Approval settings
    requires_approval = models.BooleanField(_('Requires Approval'), default=False)
    approval_level = models.CharField(_('Approval Level'), max_length=20, choices=APPROVAL_LEVEL_CHOICES, default='none')
    
    # Other settings
    created_by = models.UUIDField(_('Created By User ID'), null=True, blank=True)
    notes = models.TextField(_('Notes'), blank=True)
    attributes = models.JSONField(_('Custom Attributes'), default=dict, blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _('Promotion Rule')
        verbose_name_plural = _('Promotion Rules')
        ordering = ['-priority', 'name']
    
    def __str__(self):
        return self.name
    
    @property
    def is_valid(self):
        now = timezone.now()
        if not self.is_active:
            return False
        if self.start_date and self.start_date > now:
            return False
        if self.end_date and self.end_date < now:
            return False
        if self.max_usages and self.current_usages >= self.max_usages:
            return False
        return True
    
    def increment_usage(self):
        self.current_usages += 1
        self.save(update_fields=['current_usages'])


class RuleCondition(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """
    Conditions for when a promotion rule applies
    """
    CONDITION_TYPE_CHOICES = [
        # Customer related
        ('customer_id', _('Specific Customer')),
        ('customer_group', _('Customer Group')),
        ('customer_status', _('Customer Status')),
        ('customer_since', _('Customer Since')),
        ('customer_age', _('Customer Age')),
        ('customer_gender', _('Customer Gender')),
        ('customer_location', _('Customer Location')),
        
        # Vehicle related
        ('vehicle_id', _('Specific Vehicle')),
        ('vehicle_make', _('Vehicle Make')),
        ('vehicle_model', _('Vehicle Model')),
        ('vehicle_year', _('Vehicle Year')),
        ('vehicle_type', _('Vehicle Type')),
        ('vehicle_age', _('Vehicle Age')),
        ('vehicle_mileage', _('Vehicle Mileage')),
        
        # Service/Part related
        ('service_id', _('Specific Service')),
        ('service_type', _('Service Type')),
        ('service_category', _('Service Category')),
        ('part_id', _('Specific Part')),
        ('part_category', _('Part Category')),
        ('part_manufacturer', _('Part Manufacturer')),
        
        # Order related
        ('order_total', _('Order Total')),
        ('order_items', _('Number of Items')),
        ('order_date', _('Order Date')),
        ('service_center', _('Service Center')),
        
        # Time related
        ('day_of_week', _('Day of Week')),
        ('month', _('Month')),
        ('time_of_day', _('Time of Day')),
        ('special_day', _('Special Day')),
        
        # Other
        ('custom', _('Custom Condition')),
    ]
    
    OPERATOR_CHOICES = [
        ('eq', _('Equals')),
        ('neq', _('Not Equals')),
        ('gt', _('Greater Than')),
        ('gte', _('Greater Than or Equal')),
        ('lt', _('Less Than')),
        ('lte', _('Less Than or Equal')),
        ('contains', _('Contains')),
        ('not_contains', _('Doesn\'t Contain')),
        ('starts_with', _('Starts With')),
        ('ends_with', _('Ends With')),
        ('in', _('In List')),
        ('not_in', _('Not In List')),
        ('between', _('Between')),
        ('not_between', _('Not Between')),
    ]
    
    rule = models.ForeignKey(
        PromotionRule,
        on_delete=models.CASCADE,
        related_name='conditions',
        verbose_name=_('Rule')
    )
    
    # Condition details
    condition_type = models.CharField(_('Condition Type'), max_length=50, choices=CONDITION_TYPE_CHOICES)
    operator = models.CharField(_('Operator'), max_length=20, choices=OPERATOR_CHOICES)
    value = models.TextField(_('Value'), help_text=_('String, number, or JSON list/object as needed'))
    value2 = models.TextField(_('Second Value'), blank=True, help_text=_('For operators like "between" that need two values'))
    
    # Condition grouping
    group_id = models.CharField(_('Group ID'), max_length=50, blank=True, help_text=_('Group ID for AND/OR logic'))
    
    # Logic joining
    is_or_condition = models.BooleanField(_('Is OR Condition'), default=False, help_text=_('If true, this condition uses OR logic with the previous condition'))
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _('Rule Condition')
        verbose_name_plural = _('Rule Conditions')
        ordering = ['rule', 'group_id']
    
    def __str__(self):
        return f"{self.get_condition_type_display()} {self.get_operator_display()} {self.value}"
    
    def get_value_as_type(self):
        """Convert stored value to appropriate type based on condition_type"""
        # Numeric types
        if self.condition_type in ('customer_age', 'vehicle_age', 'vehicle_mileage', 'order_total', 'order_items'):
            try:
                return Decimal(self.value)
            except:
                return 0
        
        # Date types
        elif self.condition_type in ('customer_since', 'order_date'):
            try:
                return timezone.datetime.fromisoformat(self.value)
            except:
                return None
        
        # List types
        elif self.operator in ('in', 'not_in'):
            try:
                return json.loads(self.value)
            except:
                return []
                
        # Default string
        return self.value


class RuleEffect(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """
    Effects/outcomes when a rule matches (discounts, etc.)
    """
    EFFECT_TYPE_CHOICES = [
        # Discount types
        ('item_discount_percentage', _('Item Discount %')),
        ('item_discount_fixed', _('Item Fixed Discount')),
        ('order_discount_percentage', _('Order Discount %')),
        ('order_discount_fixed', _('Order Fixed Discount')),
        ('free_item', _('Free Item')),
        ('buy_x_get_y', _('Buy X Get Y')),
        
        # Price adjustments
        ('set_special_price', _('Set Special Price')),
        ('price_multiplier', _('Price Multiplier')),
        
        # Insurance/Warranty
        ('set_warranty_coverage', _('Set Warranty Coverage')),
        ('extend_warranty', _('Extend Warranty')),
        ('set_insurance_coverage', _('Set Insurance Coverage')),
        
        # Other effects
        ('apply_service_package', _('Apply Service Package')),
        ('set_payment_terms', _('Set Payment Terms')),
        ('set_priority', _('Set Service Priority')),
        ('custom_effect', _('Custom Effect')),
    ]
    
    APPLY_TO_CHOICES = [
        ('all', _('Entire Order')),
        ('parts_only', _('Parts Only')),
        ('labor_only', _('Labor Only')),
        ('specific_items', _('Specific Items')),
        ('specific_categories', _('Specific Categories')),
    ]
    
    rule = models.ForeignKey(
        PromotionRule,
        on_delete=models.CASCADE,
        related_name='effects',
        verbose_name=_('Rule')
    )
    
    # Effect details
    effect_type = models.CharField(_('Effect Type'), max_length=50, choices=EFFECT_TYPE_CHOICES)
    effect_value = models.DecimalField(_('Effect Value'), max_digits=15, decimal_places=2, default=0)
    effect_data = models.JSONField(_('Effect Data'), default=dict, blank=True, help_text=_('Additional data for complex effects'))
    
    # Application scope
    apply_to = models.CharField(_('Apply To'), max_length=50, choices=APPLY_TO_CHOICES, default='all')
    max_items = models.PositiveIntegerField(_('Maximum Items'), null=True, blank=True, help_text=_('Maximum number of items this effect applies to'))
    max_amount = models.DecimalField(_('Maximum Amount'), max_digits=15, decimal_places=2, null=True, blank=True)
    item_filter = models.JSONField(_('Item Filter'), default=dict, blank=True, help_text=_('Filter criteria for which items this applies to'))
    
    # Description for invoice
    description = models.CharField(_('Description'), max_length=255, blank=True, help_text=_('Description to show on invoice'))
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _('Rule Effect')
        verbose_name_plural = _('Rule Effects')
    
    def __str__(self):
        return f"{self.get_effect_type_display()} ({self.effect_value})"


class RuleLog(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """
    Log of rule applications for auditing and analytics
    """
    rule = models.ForeignKey(
        PromotionRule,
        on_delete=models.PROTECT,
        related_name='logs',
        verbose_name=_('Rule')
    )
    
    # Application context
    invoice = models.ForeignKey(
        'billing.Invoice',
        on_delete=models.SET_NULL,
        related_name='rule_logs',
        verbose_name=_('Invoice'),
        null=True, blank=True
    )
    
    work_order = models.ForeignKey(
        'work_orders.WorkOrder',
        on_delete=models.SET_NULL,
        related_name='rule_logs',
        verbose_name=_('Work Order'),
        null=True, blank=True
    )
    
    customer = models.ForeignKey(
        Customer,
        on_delete=models.SET_NULL,
        related_name='rule_logs',
        verbose_name=_('Customer'),
        null=True, blank=True
    )
    
    vehicle = models.ForeignKey(
        Vehicle,
        on_delete=models.SET_NULL,
        related_name='rule_logs',
        verbose_name=_('Vehicle'),
        null=True, blank=True
    )
    
    # Effect details
    effect_type = models.CharField(_('Effect Type'), max_length=50)
    amount = models.DecimalField(_('Amount'), max_digits=15, decimal_places=2, default=0)
    
    # Approval information
    required_approval = models.BooleanField(_('Required Approval'), default=False)
    approved = models.BooleanField(_('Approved'), null=True, blank=True)
    approved_by = models.UUIDField(_('Approved By User ID'), null=True, blank=True)
    approval_date = models.DateTimeField(_('Approval Date'), null=True, blank=True)
    approval_notes = models.TextField(_('Approval Notes'), blank=True)
    
    # Detailed data
    applied_items = models.JSONField(_('Applied Items'), default=list, blank=True, help_text=_('List of items the rule was applied to'))
    rule_data = models.JSONField(_('Rule Data'), default=dict, blank=True, help_text=_('Snapshot of rule at application time'))
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _('Rule Log')
        verbose_name_plural = _('Rule Logs')
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.rule.name} - {self.created_at.strftime('%Y-%m-%d %H:%M')}" 