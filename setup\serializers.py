from rest_framework import serializers
from .models import ServiceCenter, ServiceCenterMakeModel, VehicleMake, VehicleModel

class ServiceCenterMakeModelSerializer(serializers.ModelSerializer):
    """Serializer for the ServiceCenterMakeModel model"""
    
    class Meta:
        model = ServiceCenterMakeModel
        fields = ['id', 'make', 'model', 'is_active', 'notes']


class ServiceCenterMakeSerializer(serializers.Serializer):
    """Serializer for vehicle makes supported by a service center"""
    make = serializers.CharField()
    models = serializers.ListField(child=serializers.CharField(), required=False)


class VehicleMakeModelSerializer(serializers.Serializer):
    """Serializer for vehicle makes and models (used for dropdowns)"""
    make = serializers.CharField()
    models = serializers.ListField(child=serializers.CharField())


class VehicleMakeSerializer(serializers.ModelSerializer):
    """Serializer for the VehicleMake model"""
    models_count = serializers.IntegerField(read_only=True, source='models.count')
    
    class Meta:
        model = VehicleMake
        fields = ['id', 'name', 'description', 'logo', 'country_of_origin', 
                 'is_active', 'attributes', 'models_count']


class VehicleModelSerializer(serializers.ModelSerializer):
    """Serializer for the VehicleModel model"""
    make_name = serializers.CharField(source='make.name', read_only=True)
    
    class Meta:
        model = VehicleModel
        fields = ['id', 'name', 'make', 'make_name', 'description', 'year_introduced', 
                 'year_discontinued', 'vehicle_class', 'is_active', 'attributes'] 