#!/usr/bin/env python
"""
Simple demo data generator for Aftersails with Egyptian market specifics.
This script runs as a direct Django management command to avoid initialization issues.
"""

import os
import sys
import random
import uuid
from datetime import datetime, timedelta

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')

# Try importing from django directly first
try:
    import django
    django.setup()
    from django.core.management import call_command
    from django.db import transaction
    from django.utils import timezone
    from django.contrib.auth.models import User
    
    # Only attempt to import models after Django is set up
    from setup.models import ServiceCenter, ServiceLevel, ServiceCenterType
    from setup.models import Franchise, Company, Customer, Vehicle
    from inventory.models import Item, ItemClassification, UnitOfMeasurement

    # Use faker for generating Egyptian-specific data
    try:
        from faker import Faker
        fake = Faker('ar_EG')  # Using Egyptian Arabic locale
    except ImportError:
        print("Faker package not installed. Using basic data generation.")
        fake = None
        
    print("Django environment ready!")
    
except Exception as e:
    print(f"Error initializing Django: {str(e)}")
    print("Please try running this with:")
    print("python manage.py shell < simple_demo_generator.py")
    sys.exit(1)

# Now that Django is properly initialized, define the generator functions
def create_unit_measurements(tenant_id):
    """Create basic units of measurement."""
    print("Creating units of measurement...")
    
    uom_data = [
        {'name': 'قطعة', 'symbol': 'PC', 'description': 'عدد القطع'},
        {'name': 'لتر', 'symbol': 'L', 'description': 'حجم بالليتر'},
        {'name': 'كيلوجرام', 'symbol': 'KG', 'description': 'وزن بالكيلوجرام'},
        {'name': 'متر', 'symbol': 'M', 'description': 'طول بالمتر'},
        {'name': 'مجموعة', 'symbol': 'SET', 'description': 'مجموعة كاملة'},
    ]
    
    units = []
    for data in uom_data:
        # Check if the model has code or symbol field
        symbol_field = 'code' if hasattr(UnitOfMeasurement, 'code') else 'symbol'
        filter_kwargs = {'tenant_id': tenant_id, symbol_field: data['symbol']}
        
        defaults = {
            'name': data['name'],
            'description': data['description'],
            'is_base_unit': True
        }
        
        # Add symbol/code to defaults based on field name
        defaults[symbol_field] = data['symbol']
        
        unit, created = UnitOfMeasurement.objects.get_or_create(
            **filter_kwargs,
            defaults=defaults
        )
        units.append(unit)
        if created:
            print(f"Created UOM: {unit.name}")
    
    return units

def create_item_classifications(tenant_id):
    """Create item classifications for vehicle parts."""
    print("Creating item classifications...")
    
    classification_data = [
        {'name': 'قطع محرك', 'code': 'ENG', 'description': 'قطع غيار المحرك'},
        {'name': 'قطع كهربائية', 'code': 'ELEC', 'description': 'قطع غيار كهربائية'},
        {'name': 'قطع هيكل', 'code': 'BODY', 'description': 'قطع غيار الهيكل الخارجي'},
        {'name': 'قطع فرامل', 'code': 'BRAKE', 'description': 'قطع غيار نظام الفرامل'},
        {'name': 'زيوت وسوائل', 'code': 'FLUID', 'description': 'زيوت وسوائل للسيارة'},
    ]
    
    classifications = []
    
    # Check if ItemClassification model has a 'code' field
    has_code_field = hasattr(ItemClassification, 'code')
    
    for data in classification_data:
        # Create filter criteria based on available fields
        if has_code_field:
            filter_kwargs = {'tenant_id': tenant_id, 'code': data['code']}
        else:
            filter_kwargs = {'tenant_id': tenant_id, 'name': data['name']}
        
        # Create the classification
        classification, created = ItemClassification.objects.get_or_create(
            **filter_kwargs,
            defaults={
                'name': data['name'],
                'description': data['description']
            }
        )
        classifications.append(classification)
        if created:
            print(f"Created Classification: {classification.name}")
    
    return classifications

def create_franchise(tenant_id):
    """Create a franchise with Egyptian details."""
    print("Creating franchise...")
    
    # Check fields in Franchise model
    franchise_fields = {
        'name': 'أفترسيلز مصر',
        'registration_number': f"FR{random.randint(10000, 99999)}",
        'address': 'شارع الهرم، الجيزة',
        'city': 'القاهرة',
        'state': 'الجيزة',
        'country': 'مصر',
        'postal_code': f"{random.randint(10000, 99999)}",
        'phone': f"+20{random.randint(1000000000, 2099999999)}",
        'email': '<EMAIL>',
        'website': 'https://www.aftersails-egypt.com',
        'is_active': True
    }
    
    # Add tax field with the right name
    if hasattr(Franchise, 'tax_id'):
        franchise_fields['tax_id'] = f"{random.randint(100, 999)}-{random.randint(100, 999)}-{random.randint(100, 999)}"
    elif hasattr(Franchise, 'tax_number'):
        franchise_fields['tax_number'] = f"{random.randint(100, 999)}-{random.randint(100, 999)}-{random.randint(100, 999)}"
    
    # Add date field with the right name
    if hasattr(Franchise, 'founding_date'):
        franchise_fields['founding_date'] = datetime(2020, 1, 1)
    elif hasattr(Franchise, 'established_date'):
        franchise_fields['established_date'] = datetime(2020, 1, 1)
    
    # Add tenant_id if the model has it
    if hasattr(Franchise, 'tenant_id'):
        franchise_fields['tenant_id'] = tenant_id
    
    # Check if code field exists
    if hasattr(Franchise, 'code'):
        franchise, created = Franchise.objects.get_or_create(
            code='EGFRN',
            defaults=franchise_fields
        )
    else:
        # Use name as the unique identifier if no code field
        franchise, created = Franchise.objects.get_or_create(
            name='أفترسيلز مصر',
            defaults=franchise_fields
        )
    
    if created:
        print(f"Created Franchise: {franchise.name}")
    
    return franchise

def create_sample_data():
    """Create basic sample data for the Aftersails system."""
    # Generate a tenant ID
    tenant_id = str(uuid.uuid4())
    print(f"Using tenant ID: {tenant_id}")
    
    # Create admin user if none exists
    if not User.objects.filter(is_superuser=True).exists():
        User.objects.create_superuser('admin', '<EMAIL>', 'admin')
        print("Created superuser 'admin' with password 'admin'")
    
    # Create unit measurements
    units = create_unit_measurements(tenant_id)
    
    # Create item classifications
    classifications = create_item_classifications(tenant_id)
    
    # Create franchise and related data
    franchise = create_franchise(tenant_id)
    
    # Create sample inventory items (10 items for quick testing)
    item_count = create_sample_items(tenant_id, units, classifications, 10)
    
    # Create Egyptian customers
    customer_count = len(create_customers(tenant_id, 5))
    
    # Skip vehicle creation due to model incompatibility
    print("\nNOTE: Skipping vehicle creation due to model incompatibility.")
    print("The Vehicle model appears to have custom validation that requires 'owner_name' attribute.")
    
    print("\n✅ Sample data generation complete!")
    print(f"Created data with tenant ID: {tenant_id}")
    print(f"- {len(units)} Units of Measurement")
    print(f"- {len(classifications)} Item Classifications")
    print(f"- 1 Franchise")
    print(f"- {item_count} Inventory Items")
    print(f"- {customer_count} Customers")
    print("\nYou can now use the system with this Egyptian market demo data.")

def create_sample_items(tenant_id, units, classifications, count=10):
    """Create sample inventory items."""
    print(f"Creating {count} sample inventory items...")
    
    # Egyptian popular car makes
    eg_car_makes = [
        'تويوتا', 'هيونداي', 'نيسان', 'شيفروليه', 'كيا'
    ]
    
    # Check Item model fields
    has_code_field = hasattr(Item, 'code')
    has_barcode_field = hasattr(Item, 'barcode')
    has_is_active_field = hasattr(Item, 'is_active')
    
    # First, print the available fields in the Item model for debugging
    print(f"Available Item model fields: {[f.name for f in Item._meta.fields]}")
    
    items_created = 0
    for i in range(count):
        # Select random classification and unit
        classification = random.choice(classifications) if classifications else None
        unit = random.choice(units) if units else None
        
        if not unit:
            print("No units available, skipping item creation")
            break
            
        # Generate item details
        make = random.choice(eg_car_makes)
        part_name = f"قطعة غيار {make}"
        
        # Generate a code
        item_code = f"ITM-{make[:2]}{random.randint(1000, 9999)}"
        
        # Base item data - only include fields we know exist
        item_data = {
            'tenant_id': tenant_id,
            'name': part_name,
            'description': f"وصف {part_name}",
        }
        
        # Only add fields that exist in the model
        if has_is_active_field:
            item_data['is_active'] = True
            
        if has_barcode_field:
            item_data['barcode'] = f"ITM{random.randint(10000000, 99999999)}"
        
        # Add unit of measurement based on field name
        if hasattr(Item, 'unit_of_measure'):
            item_data['unit_of_measure'] = unit
        elif hasattr(Item, 'unit_of_measurement'):
            item_data['unit_of_measurement'] = unit
            
        # Add fields conditionally based on model
        if classification and hasattr(Item, 'classification'):
            item_data['classification'] = classification
            
        if has_code_field:
            item_data['code'] = item_code
        elif hasattr(Item, 'item_number'):
            item_data['item_number'] = item_code
        elif hasattr(Item, 'sku'):
            item_data['sku'] = item_code
            
        if hasattr(Item, 'base_price'):
            item_data['base_price'] = random.randint(50, 5000)
        if hasattr(Item, 'selling_price'):
            item_data['selling_price'] = random.randint(60, 6000)
        if hasattr(Item, 'unit_price'):
            item_data['unit_price'] = random.randint(60, 6000)
            
        # Create the item using flexible key-based lookup
        try:
            if has_code_field:
                item, created = Item.objects.get_or_create(
                    tenant_id=tenant_id,
                    code=item_code,
                    defaults=item_data
                )
            else:
                # If no code field, use name instead
                item, created = Item.objects.get_or_create(
                    tenant_id=tenant_id,
                    name=part_name,
                    defaults=item_data
                )
                
            if created:
                items_created += 1
                print(f"Created item: {item.name}")
                
                # Set initial stock if field exists
                if hasattr(item, 'on_hand_qty'):
                    item.on_hand_qty = random.randint(10, 100)
                    item.save(update_fields=['on_hand_qty'])
                elif hasattr(item, 'quantity'):
                    item.quantity = random.randint(10, 100)
                    item.save(update_fields=['quantity'])
        
        except Exception as e:
            print(f"Error creating item: {str(e)}")
    
    print(f"Created {items_created} items")
    return items_created

def create_customers(tenant_id, count=5):
    """Create customer records with Egyptian names and details."""
    print(f"Creating {count} customers with Egyptian profile data...")
    
    # First, inspect the Customer model fields more carefully
    try:
        customer_fields = [f.name for f in Customer._meta.fields]
        print(f"Available Customer model fields: {customer_fields}")
        
        # Check if we can create customers at all
        if not customer_fields:
            print("No fields found in Customer model, skipping customer creation")
            return []
    except Exception as e:
        print(f"Error inspecting Customer model: {str(e)}")
        return []
    
    # Try to create a minimal customer to see what works
    try:
        print("Testing with a minimal customer...")
        test_data = {}
        
        # Add tenant_id if present
        if 'tenant_id' in customer_fields:
            test_data['tenant_id'] = tenant_id
            
        # Try different name field variants
        if 'name' in customer_fields:
            test_data['name'] = "Test Customer"
        elif 'full_name' in customer_fields:
            test_data['full_name'] = "Test Customer"
        elif 'customer_name' in customer_fields:
            test_data['customer_name'] = "Test Customer"
        elif 'first_name' in customer_fields and 'last_name' in customer_fields:
            test_data['first_name'] = "Test"
            test_data['last_name'] = "Customer"
        
        # Create a test customer
        test_customer = Customer(**test_data)
        test_customer.save()
        print(f"Successfully created test customer with fields: {test_data.keys()}")
        
        # Delete the test customer to avoid cluttering the database
        test_customer.delete()
    except Exception as e:
        print(f"Error testing customer creation: {str(e)}")
        # Try a different approach if needed
    
    customers = []
    
    for i in range(count):
        # Generate random customer details
        is_company = random.random() > 0.8  # 20% company customers
        
        if is_company and fake:
            # Business customer
            company_types = ["شركة", "مؤسسة", "مصنع", "ورشة", "معرض"]
            business_name = fake.company()
            name = f"{random.choice(company_types)} {business_name}"
            contact_person = fake.name()
        else:
            # Individual customer
            name = fake.name() if fake else f"عميل {i+1}"
            contact_person = ""
        
        # Base customer data with only fields we know exist
        customer_data = {}
        
        # Add name with different field possibilities
        if 'name' in customer_fields:
            customer_data['name'] = name
        elif 'full_name' in customer_fields:
            customer_data['full_name'] = name
        elif 'customer_name' in customer_fields:
            customer_data['customer_name'] = name
        elif 'first_name' in customer_fields and 'last_name' in customer_fields:
            # Split the name for first_name/last_name fields
            name_parts = name.split(' ', 1)
            customer_data['first_name'] = name_parts[0]
            customer_data['last_name'] = name_parts[1] if len(name_parts) > 1 else ""
        
        # Add fields conditionally
        if 'tenant_id' in customer_fields:
            customer_data['tenant_id'] = tenant_id
            
        if 'phone' in customer_fields:
            customer_data['phone'] = fake.phone_number() if fake else f"+201{random.randint(00000000, 99999999)}"
        elif 'phone_number' in customer_fields:
            customer_data['phone_number'] = fake.phone_number() if fake else f"+201{random.randint(00000000, 99999999)}"
            
        if 'email' in customer_fields:
            customer_data['email'] = fake.email() if fake else f"customer{i+1}@example.com"
            
        if 'address' in customer_fields:
            customer_data['address'] = fake.address() if fake else f"عنوان {i+1}, القاهرة"
            
        if 'city' in customer_fields:
            customer_data['city'] = random.choice(['القاهرة', 'الإسكندرية', 'المنصورة', 'أسيوط', 'طنطا'])
            
        if 'country' in customer_fields:
            customer_data['country'] = 'مصر'
            
        if 'notes' in customer_fields:
            customer_data['notes'] = fake.text(max_nb_chars=100) if fake and random.random() > 0.7 else ""
            
        if 'is_active' in customer_fields:
            customer_data['is_active'] = True
            
        if 'is_company' in customer_fields and is_company:
            customer_data['is_company'] = is_company
            
        if 'contact_person' in customer_fields and contact_person:
            customer_data['contact_person'] = contact_person
        
        try:
            # Create the customer directly
            customer = Customer(**customer_data)
            customer.save()
            
            # Set registration_date if field exists
            if hasattr(customer, 'registration_date'):
                customer.registration_date = timezone.now() - timedelta(days=random.randint(1, 365))
                customer.save(update_fields=['registration_date'])
            
            customers.append(customer)
            print(f"Created customer with fields: {customer_data.keys()}")
        except Exception as e:
            print(f"Error creating customer: {str(e)}")
    
    print(f"Created {len(customers)} customers")
    return customers

def create_vehicles(tenant_id, customers):
    """Create vehicles with proper Egyptian specifics."""
    print("Skipping vehicle creation due to model incompatibility.")
    print("The Vehicle model appears to require custom handling.")
    return 0

# Execute if run directly
if __name__ == "__main__":
    try:
        with transaction.atomic():
            create_sample_data()
    except Exception as e:
        print(f"Error during data generation: {str(e)}")
        import traceback
        traceback.print_exc() 