// Spare Parts Search Dropdown
// Shows parts related to the selected operation with their prices

document.addEventListener('DOMContentLoaded', function() {
    initSparePartsSearch();
});

function initSparePartsSearch() {
    // Initialize the spare parts search dropdown
    const searchInput = document.getElementById('spare-parts-search');
    const searchResults = document.getElementById('spare-parts-dropdown');
    const selectedOperationInput = document.getElementById('selected-operation');
    const selectedVehicleInput = document.getElementById('selected-vehicle');
    const searchContainer = document.querySelector('.spare-parts-search-container');
    const searchButton = document.getElementById('search-parts-button');

    if (!searchInput || !searchContainer) return;

    // Function to trigger the search
    function triggerSearch() {
        if (searchResults) {
            searchResults.style.display = 'block';
        }

        // Load parts if we have an operation selected
        const operationId = selectedOperationInput ? selectedOperationInput.value : null;
        const vehicleId = selectedVehicleInput ? selectedVehicleInput.value : null;
        
        if (operationId) {
            loadPartsForOperation(operationId, vehicleId);
        } else {
            showNoOperationMessage();
        }
    }

    // Show the dropdown when the input is focused
    searchInput.addEventListener('focus', function() {
        triggerSearch();
    });

    // Handle search button click
    if (searchButton) {
        searchButton.addEventListener('click', function() {
            triggerSearch();
        });
    }

    // Hide the dropdown when clicking outside
    document.addEventListener('click', function(event) {
        if (!searchContainer.contains(event.target) && searchResults) {
            searchResults.style.display = 'none';
        }
    });

    // Handle input for filtering
    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        filterPartsDropdown(searchTerm);
    });

    // Listen for operation selection change
    if (selectedOperationInput) {
        selectedOperationInput.addEventListener('change', function() {
            const operationId = this.value;
            const vehicleId = selectedVehicleInput ? selectedVehicleInput.value : null;
            
            if (operationId) {
                loadPartsForOperation(operationId, vehicleId);
                // Show the dropdown when operation changes
                if (searchResults) {
                    searchResults.style.display = 'block';
                }
            } else {
                showNoOperationMessage();
            }
        });
    }

    // Listen for vehicle selection change
    if (selectedVehicleInput) {
        selectedVehicleInput.addEventListener('change', function() {
            const vehicleId = this.value;
            const operationId = selectedOperationInput ? selectedOperationInput.value : null;
            
            if (operationId) {
                loadPartsForOperation(operationId, vehicleId);
                // Show the dropdown when vehicle changes
                if (searchResults) {
                    searchResults.style.display = 'block';
                }
            }
        });
    }

    // Function to load parts for the selected operation
    function loadPartsForOperation(operationId, vehicleId) {
        if (!searchResults) return;

        // Show loading indicator
        searchResults.innerHTML = '<div class="text-center p-3"><div class="spinner-border spinner-border-sm text-primary" role="status"></div><span class="ml-2">جاري التحميل...</span></div>';

        // Get service center ID if available
        const serviceCenterInput = document.getElementById('selected-service-center');
        const serviceCenterId = serviceCenterInput ? serviceCenterInput.value : '';

        // Get language code from URL or default to 'ar'
        const languageCode = window.location.pathname.split('/')[1] || 'ar';
        
        // Construct the API URL - using proper URL with language code
        let apiUrl = `/${languageCode}/work-orders/api/get-spare-parts/?operation_id=${encodeURIComponent(operationId)}`;
        if (vehicleId) {
            apiUrl += `&vehicle_id=${encodeURIComponent(vehicleId)}`;
        }
        if (serviceCenterId) {
            apiUrl += `&service_center_id=${encodeURIComponent(serviceCenterId)}`;
        }

        console.log("Fetching spare parts from: " + apiUrl);

        // Call the API
        fetch(apiUrl)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log("Received spare parts data:", data);
                if (data.success && data.parts && data.parts.length > 0) {
                    renderPartsDropdown(data.parts);
                } else {
                    searchResults.innerHTML = '<div class="text-center p-3">لا توجد قطع غيار متوفرة لهذه العملية</div>';
                }
            })
            .catch(error => {
                console.error('Error fetching spare parts:', error);
                searchResults.innerHTML = '<div class="text-center p-3 text-danger">حدث خطأ أثناء تحميل قطع الغيار: ' + error.message + '</div>';
            });
    }

    // Function to render parts in the dropdown
    function renderPartsDropdown(parts) {
        if (!searchResults) return;

        // Sort parts by name
        parts.sort((a, b) => a.name.localeCompare(b.name));

        // Create the dropdown content
        let dropdownContent = '<div class="list-group">';
        
        parts.forEach(part => {
            const price = part.price || part.standard_price;
            const availabilityClass = part.total_available > 0 ? 'text-success' : 'text-danger';
            const availabilityText = part.total_available > 0 ? `متوفر (${part.total_available})` : 'غير متوفر';
            
            dropdownContent += `
                <div class="list-group-item list-group-item-action part-item" data-part-id="${part.id}" data-part-name="${part.name}" data-part-price="${price}" data-part-sku="${part.sku}">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <strong>${part.name}</strong>
                            <small class="d-block text-muted">${part.sku}</small>
                        </div>
                        <div class="text-left">
                            <span class="badge badge-primary">${price} SAR</span>
                            <small class="d-block ${availabilityClass}">${availabilityText}</small>
                        </div>
                    </div>
                </div>
            `;
        });
        
        dropdownContent += '</div>';
        searchResults.innerHTML = dropdownContent;

        // Add click handlers for part items
        const partItems = searchResults.querySelectorAll('.part-item');
        partItems.forEach(item => {
            item.addEventListener('click', function() {
                selectPart(this.dataset.partId, this.dataset.partName, this.dataset.partPrice, this.dataset.partSku);
            });
        });
    }

    // Function to filter the parts dropdown by search term
    function filterPartsDropdown(searchTerm) {
        const partItems = searchResults ? searchResults.querySelectorAll('.part-item') : [];
        
        partItems.forEach(item => {
            const name = item.dataset.partName.toLowerCase();
            const sku = item.dataset.partSku.toLowerCase();
            
            if (name.includes(searchTerm) || sku.includes(searchTerm)) {
                item.style.display = 'block';
            } else {
                item.style.display = 'none';
            }
        });
    }

    // Function to select a part from the dropdown
    function selectPart(partId, partName, partPrice, partSku) {
        // Fill the search input with the selected part name
        if (searchInput) {
            searchInput.value = partName;
        }

        // Hide the dropdown
        if (searchResults) {
            searchResults.style.display = 'none';
        }

        // Set hidden part ID input if it exists
        const partIdInput = document.getElementById('selected-part-id');
        if (partIdInput) {
            partIdInput.value = partId;
        }

        // Update part info display if it exists
        const partInfoElement = document.getElementById('selected-part-info');
        if (partInfoElement) {
            partInfoElement.innerHTML = `
                <div class="alert alert-info">
                    <strong>${partName}</strong> (${partSku})<br>
                    <span class="badge badge-primary">${partPrice} SAR</span>
                </div>
            `;
        }

        // Trigger change event
        if (partIdInput) {
            const event = new Event('change');
            partIdInput.dispatchEvent(event);
        }
    }

    // Function to show message when no operation is selected
    function showNoOperationMessage() {
        if (searchResults) {
            searchResults.innerHTML = '<div class="text-center p-3">الرجاء اختيار عملية أولاً</div>';
        }
    }

    // Auto-trigger search if operation is already selected on page load
    if (selectedOperationInput && selectedOperationInput.value) {
        // Wait a short time for everything to initialize
        setTimeout(triggerSearch, 500);
    }
} 