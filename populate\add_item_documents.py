import os
import sys
import django
import random
import uuid
from datetime import datetime, timedelta
from django.core.files import File
import io
import base64
from PIL import Image, ImageDraw, ImageFont

# Add the parent directory to the Python path so imports work correctly
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

# Import Django models
from django.db import transaction
from django.utils import timezone
from django.core.files.base import ContentFile

try:
    from inventory.models import (
        Item, ItemDocument, OperationCompatibility
    )
except ImportError as e:
    print(f"Could not import inventory models: {e}")
    print("Please check app structure and model names.")
    sys.exit(1)

class ItemDocumentGenerator:
    def __init__(self):
        print("Item Document Generator initialized")
        # Get a tenant ID if available
        self.tenant_id = self._get_tenant_id()
        
        # Create a directory for temp files if it doesn't exist
        self.temp_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'temp')
        os.makedirs(self.temp_dir, exist_ok=True)
    
    def _get_tenant_id(self):
        """Get a tenant ID from an existing record"""
        try:
            item = Item.objects.first()
            if item:
                return item.tenant_id
        except:
            pass
        
        # Create a new one if none found
        return uuid.uuid4()
    
    def create_sample_image(self, item_name, file_path):
        """Create a sample image for an item"""
        # Create a simple image with item name
        img = Image.new('RGB', (800, 600), color=(73, 109, 137))
        d = ImageDraw.Draw(img)
        
        # Try to use a font, fallback to default if not available
        try:
            font = ImageFont.truetype("arial.ttf", 36)
        except IOError:
            font = None
        
        # Draw text on image
        d.text((50, 50), f"ITEM: {item_name}", fill=(255, 255, 0), font=font)
        d.text((50, 150), f"DOCUMENT DATE: {datetime.now().strftime('%Y-%m-%d')}", 
               fill=(255, 255, 0), font=font)
        d.text((50, 250), "DEMO IMAGE FOR INVENTORY SYSTEM", fill=(255, 255, 0), font=font)
        
        # Save the image
        img.save(file_path)
        return file_path
    
    def create_sample_pdf(self, item_name, file_path):
        """Create a simple text file with PDF extension (fake PDF)"""
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(f"Item: {item_name}\n")
            f.write(f"Date: {datetime.now().strftime('%Y-%m-%d')}\n")
            f.write("This is a demo document for the inventory system.\n")
            f.write("It contains specification details for the item.\n")
        return file_path
    
    def generate_item_documents(self, count=100):
        """Generate documents for items"""
        print(f"\nGenerating {count} item documents...")
        
        # Get all items 
        items = list(Item.objects.all())
        
        if not items:
            print("No items found. Cannot generate documents.")
            return 0
        
        # Document types (use very short codes)
        doc_types = ["S", "D", "W", "C", "M", "G", "I"]
        
        created_count = 0
        
        for i in range(min(count, len(items))):
            try:
                # Select a random item
                item = random.choice(items)
                
                # Determine document type
                doc_type = random.choice(doc_types)
                
                # Determine file type (image or PDF)
                is_image = random.choice([True, False])
                
                # Generate very short file name
                file_name = f"{i}_{doc_type}"
                
                # Generate temp file path
                if is_image:
                    file_name += ".jpg"
                    temp_file_path = os.path.join(self.temp_dir, file_name)
                    self.create_sample_image(item.name, temp_file_path)
                else:
                    file_name += ".pdf"
                    temp_file_path = os.path.join(self.temp_dir, file_name)
                    self.create_sample_pdf(item.name, temp_file_path)
                
                # Generate document title (using full descriptive names)
                titles = {
                    "S": f"مواصفات {item.name}",
                    "D": f"ورقة بيانات {item.name}",
                    "W": f"شهادة ضمان {item.name}",
                    "C": f"شهادة جودة {item.name}",
                    "M": f"دليل استخدام {item.name}",
                    "G": f"دليل تركيب {item.name}",
                    "I": f"معلومات السلامة {item.name}"
                }
                
                # Map the short code to a more descriptive document type for storage
                doc_type_full = {
                    "S": "SPEC",
                    "D": "DATA",
                    "W": "WARRANTY",
                    "C": "CERT",
                    "M": "MANUAL",
                    "G": "GUIDE",
                    "I": "SAFETY"
                }
                
                title = titles.get(doc_type, f"وثيقة {item.name}")
                description = f"وصف {title}"
                is_public = random.choice([True, False])
                
                # Create ItemDocument with file upload
                with open(temp_file_path, 'rb') as f:
                    item_doc = ItemDocument(
                        item=item,
                        title=title,
                        document_type=doc_type_full.get(doc_type, "DOC"),
                        description=description,
                        is_public=is_public,
                        tenant_id=self.tenant_id
                    )
                    # Save the model first
                    item_doc.save()
                    # Then attach the file with a simple name
                    item_doc.file.save(file_name, File(f), save=True)
                
                created_count += 1
                
                # Clean up temp file
                if os.path.exists(temp_file_path):
                    os.remove(temp_file_path)
                    
                if created_count % 10 == 0:
                    print(f"Created {created_count} documents")
                
            except Exception as e:
                print(f"Error creating document for item {i+1}: {e}")
        
        print(f"Successfully created {created_count} item documents")
        
        # Clean up temp directory
        try:
            if os.path.exists(self.temp_dir):
                for filename in os.listdir(self.temp_dir):
                    os.remove(os.path.join(self.temp_dir, filename))
                os.rmdir(self.temp_dir)
        except Exception as e:
            print(f"Warning: Could not clean up temp directory: {e}")
            
        return created_count
    
    def fix_operation_compatibilities(self):
        """Create operation compatibility records to link inventory items with work order types"""
        print("Fixing operation compatibilities...")
        
        # Get work order types from database
        try:
            from work_orders.models import WorkOrderType
            operation_types = list(WorkOrderType.objects.all())
            
            if not operation_types:
                print("No operation types found in database. Please run add_work_order_types.py first.")
                return 0
                
            print(f"Found {len(operation_types)} operation types")
            
            # Get items
            items = list(Item.objects.all())
            
            if not items:
                print("No items found. Cannot create operation compatibilities.")
                return 0
            
            # Clear existing records
            OperationCompatibility.objects.all().delete()
            print("Cleared existing operation compatibilities")
            
            # Create new operation compatibilities
            created_count = 0
            
            # Add 100 random operation compatibilities
            for i in range(100):
                try:
                    # Select random item and operation type
                    item = random.choice(items)
                    operation_type = random.choice(operation_types)
                    
                    # Create the record
                    OperationCompatibility.objects.create(
                        tenant_id=item.tenant_id,
                        item=item,
                        operation_type=operation_type,
                        is_required=random.choice([True, False]),
                        is_common=random.choice([True, False, True]),  # More likely to be common
                        default_quantity=random.randint(1, 10),
                        notes=f"Used for {operation_type.name} operations"
                    )
                    
                    created_count += 1
                    
                    if created_count % 10 == 0:
                        print(f"Created {created_count} operation compatibilities")
                        
                except Exception as e:
                    print(f"Error creating operation compatibility {i+1}: {e}")
            
            print(f"Successfully created {created_count} operation compatibilities")
            return created_count
            
        except ImportError as e:
            print(f"Could not import WorkOrderType model: {e}")
            return 0
    
    def run(self):
        """Run the document generator"""
        print("Starting document generation...")
        
        try:
            with transaction.atomic():
                self.generate_item_documents(count=50)
        except Exception as e:
            print(f"Error generating item documents: {e}")
        
        # Run operation compatibilities WITHOUT a transaction
        try:
            self.fix_operation_compatibilities()
        except Exception as e:
            print(f"Error fixing operation compatibilities: {e}")
        
        print("\nDocument generation complete!")

if __name__ == "__main__":
    generator = ItemDocumentGenerator()
    generator.run() 