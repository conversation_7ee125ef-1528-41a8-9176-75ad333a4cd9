# Data Migration: SQLite to PostgreSQL (IMPROVED)

This guide will help you migrate all your data from SQLite to PostgreSQL database with **improved error handling, timezone support, and dependency management**.

## 🆕 NEW: Improved Migration Scripts

### Files Created

1. **`migrate_data_fixed_ids.py`** - 🏆 **BEST** - ID-preserving migration (solves foreign key issues)
2. **`migrate_data_fixed_ids.bat`** - 🏆 **BEST** - Windows batch file for ID-preserving migration
3. **`migrate_data_fixed.py`** - 🆕 **GOOD** - Improved migration script with timezone fixes
4. **`migrate_data_improved.bat`** - 🆕 **GOOD** - Enhanced Windows batch file
5. **`migrate_data_simple.py`** - ✅ **WORKING** - Simple migration that avoids Django dual-DB issues
6. **`analyze_dependencies.py`** - 🆕 **UTILITY** - Analyze table relationships and dependencies
7. **`migrate_data.py`** - Original migration script (Django ORM based)
8. **`migrate_data.bat`** - Original Windows batch file
9. **`core/management/commands/migrate_sqlite_to_postgresql.py`** - Django management command
10. **`migration_config.env`** - Example environment configuration

## 🚀 Quick Start (RECOMMENDED METHOD)

### 🏆 **BEST SOLUTION:** ID-Preserving Migration

This solves the foreign key constraint issues from the previous attempts.

**Windows (Easiest):**
1. **Double-click** `migrate_data_fixed_ids.bat`
2. **Choose 'Y'** for dry-run first (recommended)
3. **Review results** and proceed with actual migration

**Command Line:**
```bash
# RECOMMENDED: Test first with dry-run  
python migrate_data_fixed_ids.py --dry-run

# Run actual migration with ID preservation
python migrate_data_fixed_ids.py --sqlite-path "db.sqlite3"
```

### Step 1: Analyze Dependencies (Optional but Recommended)

```bash
python analyze_dependencies.py
```

This will show you:
- Model dependencies and relationships
- Safe migration order
- Potential circular dependencies
- Table analysis by Django app

### Alternative: Simple Migration (Without ID Preservation)

If you don't need to preserve exact primary key values:

**Windows:**
1. **Double-click** `migrate_data_improved.bat`
2. **Choose 'Y'** for dry-run first (recommended)
3. **Review results** and proceed with actual migration

**Command Line:**
```bash
# Simple migration (working but doesn't preserve IDs)
python migrate_data_simple.py --dry-run
python migrate_data_simple.py --sqlite-path "db.sqlite3"
```

## 🔧 What's Fixed in the Improved Version

### ✅ Timezone Issues Resolved
- Proper handling of `USE_TZ = False` setting
- Fixed `'TIME_ZONE'` errors that occurred in the original script
- Timezone environment properly set before Django initialization

### ✅ Enhanced Dependency Management
- **Advanced topological sorting** algorithm
- **Priority model handling** for foundational tables
- **Circular dependency detection** and automatic resolution
- **Better foreign key constraint handling**

### ✅ Improved Error Handling
- **Individual model error isolation** - if one fails, others continue
- **Detailed progress reporting** with emoji indicators
- **PostgreSQL foreign key constraints temporarily disabled** during migration
- **Batch processing optimization** for better memory usage

### ✅ Connection Management
- **Proper database connection setup** and cleanup
- **Connection testing** before migration starts
- **Timeout handling** for database operations

## 📊 Migration Process (Improved)

The improved migration script will:

1. ✅ **Set timezone** correctly (`Africa/Cairo`)
2. ✅ **Test connections** to both SQLite and PostgreSQL
3. ✅ **Run Django migrations** on PostgreSQL first
4. ✅ **Analyze dependencies** and sort models optimally
5. ✅ **Disable foreign key checks** during migration
6. ✅ **Migrate data** in dependency-correct order
7. ✅ **Handle errors gracefully** - continue with other tables
8. ✅ **Re-enable foreign key checks** after migration
9. ✅ **Report detailed results** with success/failure counts

## 🎯 Priority Models (Migrated First)

The improved script prioritizes these foundational models:

- `setup.Company`, `setup.Franchise`, `setup.ServiceLevel`
- `setup.VehicleMake`, `setup.VehicleModel`
- `user_roles.Role`, `feature_flags.ModuleFlag`
- `inventory.ItemClassification`, `inventory.UnitOfMeasurement`
- `billing.CustomerClassification`, `billing.PaymentMethod`
- And other foundational lookup tables

## 🔍 Using the Dependency Analyzer

```bash
python analyze_dependencies.py
```

**Sample Output:**
```
📱 Dependencies by App:
🔸 SETUP (12 models):
  Company → no dependencies ✅
  ServiceCenter → depends on: setup.Company, setup.ServiceCenterType
  Vehicle → depends on: setup.Customer, setup.VehicleMake

🎯 Models with NO Dependencies (Safe to migrate first):
 1. billing.CustomerClassification
 2. billing.DiscountType
 3. inventory.ItemClassification
 ...

📋 Suggested Migration Order:
  1. 🟢 setup.Company                    (table: setup_company)
  2. 🟢 setup.VehicleMake                (table: setup_vehiclemake)
  3. 🟡(1) setup.ServiceCenter           (table: setup_servicecenter)
```

## ⚠️ Troubleshooting Common Issues

### 1. TimeZone Errors (FIXED)
**Error:** `'TIME_ZONE'` or timezone-related errors
**Solution:** Use `migrate_data_fixed.py` - this is resolved in the improved version

### 2. Foreign Key Constraint Violations
**Error:** Foreign key constraint errors during migration
**Solution:** The improved script automatically:
- Disables foreign key checks: `SET session_replication_role = replica`
- Migrates in correct dependency order
- Re-enables checks after migration

### 3. Memory Issues with Large Datasets
**Improvement:** The new script uses smaller batch sizes (500 vs 1000) and better memory management

### 4. Connection Issues
**Improvement:** Connection testing and proper cleanup in the improved version

## 📈 Performance Improvements

| Feature | Original Script | Improved Script |
|---------|----------------|-----------------|
| Batch Size | 1,000 records | 500 records (better memory) |
| Error Handling | Stop on first error | Continue with other tables |
| Dependency Sorting | Basic | Advanced topological sort |
| Foreign Key Handling | Manual | Automatic constraint management |
| Progress Reporting | Basic | Detailed with emojis |
| Timezone Support | Limited | Full support |

## 🎉 Expected Results

**Improved Script Output:**
```
🔄 Improved Data Migration: SQLite → PostgreSQL
✅ SQLite connection established
✅ PostgreSQL connection established
🔄 Running Django migrations on PostgreSQL...
✅ Django migrations completed
🚀 Starting migration from db.sqlite3 to PostgreSQL
📋 Found 85 models to migrate
📊 Model migration order determined for 85 models

🔄 Migrating setup.Company: 1 records...
✅ Migrated 1 records from setup.Company
🔄 Migrating inventory.ItemClassification: 12 records...
✅ Migrated 12 records from inventory.ItemClassification
...
✅ Migration completed: 15,678/15,678 records migrated

🎉 Migration process completed successfully!
```

## 🛡️ Safety Features (Enhanced)

- **🔒 Dry-run mode** - Test migration without changes
- **🔄 Automatic rollback** on critical errors
- **📊 Progress tracking** - Know exactly what's happening
- **⚡ Foreign key constraint management** - Automatic handling
- **🎯 Priority-based sorting** - Foundational tables first
- **💾 Memory optimization** - Better handling of large datasets

## 🛠️ Foreign Key Issues Solution (NEW)

### Problem: Previous migrations failed with foreign key constraint violations
```
❌ Error: insert or update on table "user_roles_role" violates foreign key constraint
DETAIL: Key (group_id)=(1) is not present in table "auth_group".
```

### ✅ Solution: ID-Preserving Migration

The **`migrate_data_fixed_ids.py`** script solves this by:

1. **🔑 Preserving Original Primary Keys** - Maintains exact ID values from SQLite
2. **🔓 Disabling Foreign Key Constraints** - Temporarily disables constraints during migration
3. **🔄 Creating Missing Auth Groups** - Automatically creates referenced auth records
4. **🎯 Using Raw SQL Inserts** - Bypasses Django ORM to preserve exact IDs
5. **🔒 Re-enabling Constraints** - Safely re-enables all constraints after migration

This ensures that when a record references `group_id=1`, that exact group exists with `id=1`.

## 📋 Updated Recommendation

1. **🏆 Use `migrate_data_fixed_ids.py`** (the ID-preserving version) - **BEST CHOICE**
2. **Always run dry-run first**: `python migrate_data_fixed_ids.py --dry-run`
3. **Use the new batch file**: `migrate_data_fixed_ids.bat` for guided process
4. **Alternative**: `migrate_data_simple.py` if you don't need exact ID preservation
5. **Optional**: `python analyze_dependencies.py` (helpful for understanding relationships)

## 🎯 Post-Migration

After successful migration with the improved script:

1. ✅ **Verify critical data** - Check important business records
2. ✅ **Test application functionality** - Key features working
3. ✅ **Check foreign key relationships** - Data integrity maintained
4. ✅ **Backup PostgreSQL database** - Safety first
5. ✅ **Monitor performance** - Check query performance vs SQLite

---

## 🆚 Script Comparison

| Use Case | Recommended Script |
|----------|-------------------|
| **Production Migration** | `migrate_data_fixed.py` |
| **Windows Users** | `migrate_data_improved.bat` |
| **Dependency Analysis** | `analyze_dependencies.py` |
| **Django Management** | `python manage.py migrate_sqlite_to_postgresql` |
| **Basic Migration** | `migrate_data.py` (original) |

The improved scripts address all the timezone and dependency issues from the original version and provide a much more robust migration experience! 