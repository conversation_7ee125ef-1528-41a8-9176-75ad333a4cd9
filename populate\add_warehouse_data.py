import os
import sys
import django
import random
from datetime import datetime, timedelta
from django.db import transaction
from faker import Faker

# Add the parent directory to sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

# Import necessary models
from warehouse.models import (
    LocationType, Location, BinLocation, ItemLocation, 
    TransferOrder, TransferOrderItem, Transfer
)
from inventory.models import Item
from setup.models import ServiceCenter
from django.utils import timezone

# Initialize Faker
fake = Faker('ar_EG')  # Using Egyptian Arabic locale

class WarehouseGenerator:
    """Generate warehouse data for the Aftersails system."""
    
    def __init__(self):
        self.tenants = self._get_tenant_ids()
        self.inventory_items = {}
        self.service_centers = {}
        
        for tenant_id in self.tenants:
            self.inventory_items[tenant_id] = list(Item.objects.filter(tenant_id=tenant_id))
            self.service_centers[tenant_id] = list(ServiceCenter.objects.filter(tenant_id=tenant_id))
            
        print(f"Initialized WarehouseGenerator with {len(self.tenants)} tenants")
        for tenant_id in self.tenants:
            print(f"Tenant {tenant_id}: {len(self.inventory_items.get(tenant_id, []))} items, "
                  f"{len(self.service_centers.get(tenant_id, []))} service centers")
    
    def _get_tenant_ids(self):
        """Get unique tenant IDs from existing records."""
        tenant_ids = Item.objects.values_list('tenant_id', flat=True).distinct()
        return tenant_ids
    
    def generate_location_types(self):
        """Generate standard location types."""
        print("Generating location types...")
        
        # Define standard location types in Arabic with their configurations
        location_types = [
            {
                'name': 'المستودع الرئيسي',
                'code': 'MAIN',
                'description': 'المستودع الرئيسي للشركة',
                'icon': 'warehouse',
                'color': '#3498db',
                'is_active': True,
                'requires_bin_locations': True,
                'is_storage': True,
                'is_receiving': True,
                'is_shipping': False,
                'is_service': False
            },
            {
                'name': 'مركز الخدمة',
                'code': 'SC',
                'description': 'مخزن مركز الخدمة',
                'icon': 'tools',
                'color': '#2ecc71',
                'is_active': True,
                'requires_bin_locations': True,
                'is_storage': True,
                'is_receiving': False,
                'is_shipping': False,
                'is_service': True
            },
            {
                'name': 'منطقة الاستلام',
                'code': 'RCV',
                'description': 'منطقة استلام البضائع',
                'icon': 'truck-loading',
                'color': '#f39c12',
                'is_active': True,
                'requires_bin_locations': False,
                'is_storage': True,
                'is_receiving': True,
                'is_shipping': False,
                'is_service': False
            },
            {
                'name': 'منطقة الشحن',
                'code': 'SHIP',
                'description': 'منطقة شحن البضائع',
                'icon': 'shipping-fast',
                'color': '#e74c3c',
                'is_active': True,
                'requires_bin_locations': False,
                'is_storage': True,
                'is_receiving': False,
                'is_shipping': True,
                'is_service': False
            },
            {
                'name': 'رف',
                'code': 'SHELF',
                'description': 'رف تخزين',
                'icon': 'archive',
                'color': '#9b59b6',
                'is_active': True,
                'requires_bin_locations': True,
                'is_storage': True,
                'is_receiving': False,
                'is_shipping': False,
                'is_service': False
            },
            {
                'name': 'صندوق',
                'code': 'BIN',
                'description': 'صندوق تخزين',
                'icon': 'box',
                'color': '#34495e',
                'is_active': True,
                'requires_bin_locations': False,
                'is_storage': True,
                'is_receiving': False,
                'is_shipping': False,
                'is_service': False
            }
        ]
        
        created_types = []
        
        for tenant_id in self.tenants:
            for lt_data in location_types:
                # Check if this location type already exists
                existing = LocationType.objects.filter(
                    tenant_id=tenant_id,
                    code=lt_data['code']
                ).first()
                
                if not existing:
                    lt = LocationType.objects.create(
                        tenant_id=tenant_id,
                        **lt_data
                    )
                    created_types.append(lt)
                    print(f"Created location type: {lt.name} for tenant {tenant_id}")
                else:
                    print(f"Location type {lt_data['code']} already exists for tenant {tenant_id}")
        
        print(f"Created {len(created_types)} location types")
        return created_types
    
    def generate_locations(self):
        """Generate warehouse locations."""
        print("Generating warehouse locations...")
        
        created_locations = []
        
        for tenant_id in self.tenants:
            # Get location types for this tenant
            location_types = LocationType.objects.filter(tenant_id=tenant_id)
            
            if not location_types:
                print(f"No location types found for tenant {tenant_id}, skipping location generation")
                continue
            
            # Get location types by code for easy reference
            location_types_by_code = {lt.code: lt for lt in location_types}
            
            # Get service centers for this tenant to create service center storage locations
            service_centers = self.service_centers.get(tenant_id, [])
            
            # Create main warehouse
            main_warehouse = Location.objects.create(
                tenant_id=tenant_id,
                name='المستودع الرئيسي',
                code='MAIN-WH',
                location_type=location_types_by_code.get('MAIN'),
                address=fake.address(),
                city=fake.city(),
                state=fake.state(),
                country='مصر',
                postal_code=fake.postcode(),
                contact_name=fake.name(),
                phone=fake.phone_number(),
                email=fake.email(),
                area_sqm=random.randint(500, 2000),
                max_items=random.randint(5000, 20000),
                is_active=True,
                notes='المستودع الرئيسي للشركة'
            )
            created_locations.append(main_warehouse)
            
            # Create receiving and shipping areas as children of main warehouse
            receiving_area = Location.objects.create(
                tenant_id=tenant_id,
                name='منطقة الاستلام',
                code='MAIN-RCV',
                location_type=location_types_by_code.get('RCV'),
                parent=main_warehouse,
                area_sqm=random.randint(50, 200),
                is_active=True
            )
            created_locations.append(receiving_area)
            
            shipping_area = Location.objects.create(
                tenant_id=tenant_id,
                name='منطقة الشحن',
                code='MAIN-SHIP',
                location_type=location_types_by_code.get('SHIP'),
                parent=main_warehouse,
                area_sqm=random.randint(50, 200),
                is_active=True
            )
            created_locations.append(shipping_area)
            
            # Create storage shelves in main warehouse
            for i in range(5):
                shelf = Location.objects.create(
                    tenant_id=tenant_id,
                    name=f'رف {i+1}',
                    code=f'MAIN-SHELF-{i+1}',
                    location_type=location_types_by_code.get('SHELF'),
                    parent=main_warehouse,
                    area_sqm=random.randint(10, 50),
                    max_items=random.randint(100, 500),
                    is_active=True
                )
                created_locations.append(shelf)
                
                # Create bin locations for this shelf
                self._create_bin_locations(tenant_id, shelf)
            
            # Create service center storage locations
            for sc in service_centers:
                sc_storage = Location.objects.create(
                    tenant_id=tenant_id,
                    name=f'مخزن {sc.name}',
                    code=f'SC-{sc.code}' if sc.code else f'SC-{sc.id}',
                    location_type=location_types_by_code.get('SC'),
                    address=sc.address,
                    city=sc.city,
                    state=sc.state,
                    country=sc.country,
                    postal_code=sc.postal_code,
                    area_sqm=random.randint(50, 200),
                    max_items=random.randint(500, 2000),
                    is_active=True,
                    notes=f'مخزن مركز خدمة {sc.name}'
                )
                created_locations.append(sc_storage)
                
                # Create shelves for service center
                for i in range(3):
                    sc_shelf = Location.objects.create(
                        tenant_id=tenant_id,
                        name=f'رف {i+1}',
                        code=f'SC-{sc.code}-SHELF-{i+1}' if sc.code else f'SC-{sc.id}-SHELF-{i+1}',
                        location_type=location_types_by_code.get('SHELF'),
                        parent=sc_storage,
                        area_sqm=random.randint(5, 20),
                        max_items=random.randint(50, 200),
                        is_active=True
                    )
                    created_locations.append(sc_shelf)
                    
                    # Create bin locations for this shelf
                    self._create_bin_locations(tenant_id, sc_shelf, count=4)  # Fewer bins for service center shelves
        
        print(f"Created {len(created_locations)} locations")
        return created_locations
    
    def _create_bin_locations(self, tenant_id, parent_location, count=10):
        """Create bin locations for a shelf."""
        created_bins = []
        
        for i in range(count):
            bin_loc = BinLocation.objects.create(
                tenant_id=tenant_id,
                location=parent_location,
                name=f'Bin {chr(65 + i // 5)}{i % 5 + 1}',  # Creates names like A1, A2, ..., B1, B2, etc.
                code=f'{parent_location.code}-{chr(65 + i // 5)}{i % 5 + 1}',
                aisle=str(random.randint(1, 3)),
                rack=str(random.randint(1, 5)),
                shelf=str(random.randint(1, 4)),
                position=str(i + 1),
                barcode=f'BIN-{parent_location.code}-{i+1}',
                is_active=True
            )
            created_bins.append(bin_loc)
        
        return created_bins
    
    def distribute_items_to_locations(self):
        """Distribute inventory items to warehouse locations."""
        print("Distributing inventory items to locations...")
        
        created_item_locations = []
        
        for tenant_id in self.tenants:
            # Get items for this tenant
            items = self.inventory_items.get(tenant_id, [])
            
            if not items:
                print(f"No inventory items found for tenant {tenant_id}, skipping distribution")
                continue
            
            # Get locations for this tenant
            locations = Location.objects.filter(
                tenant_id=tenant_id,
                is_storage=True
            )
            
            if not locations:
                print(f"No storage locations found for tenant {tenant_id}, skipping distribution")
                continue
            
            # Get bin locations for this tenant
            bin_locations = BinLocation.objects.filter(
                tenant_id=tenant_id,
                location__in=locations
            )
            
            # Organize locations by type
            main_warehouse = locations.filter(location_type__code='MAIN', parent__isnull=True).first()
            service_center_locations = locations.filter(location_type__code='SC')
            
            if not main_warehouse:
                print(f"No main warehouse found for tenant {tenant_id}, skipping distribution")
                continue
            
            # Get shelf locations under main warehouse
            main_shelves = locations.filter(parent=main_warehouse, location_type__code='SHELF')
            
            # Distribute items to locations
            for item in items:
                # Each item will be in 1-3 locations with different quantities
                num_locations = random.randint(1, 3)
                
                # Always put some stock in main warehouse
                main_quantity = random.randint(10, 100)
                
                # Select a random shelf in the main warehouse
                if main_shelves:
                    shelf = random.choice(main_shelves)
                    
                    # Select a random bin in this shelf
                    bins_in_shelf = bin_locations.filter(location=shelf)
                    bin_loc = random.choice(bins_in_shelf) if bins_in_shelf else None
                    
                    # Create item location
                    ItemLocation.objects.create(
                        tenant_id=tenant_id,
                        item=item,
                        location=shelf,
                        bin_location=bin_loc,
                        quantity=main_quantity,
                        reorder_point=main_quantity * 0.2,  # 20% of quantity
                        max_stock=main_quantity * 2,
                        min_stock=main_quantity * 0.3  # 30% of quantity
                    )
                    created_item_locations.append(f"{item.name} in {shelf.name}")
                
                # Distribute to service centers if num_locations > 1
                if num_locations > 1 and service_center_locations:
                    # Select random service center locations
                    sc_count = min(num_locations - 1, len(service_center_locations))
                    selected_scs = random.sample(list(service_center_locations), sc_count)
                    
                    for sc_loc in selected_scs:
                        # Smaller quantity in service centers
                        sc_quantity = random.randint(1, 20)
                        
                        # Select a child shelf if available
                        sc_shelves = locations.filter(parent=sc_loc, location_type__code='SHELF')
                        target_loc = random.choice(sc_shelves) if sc_shelves else sc_loc
                        
                        # Select a bin if available
                        bins_in_target = bin_locations.filter(location=target_loc)
                        bin_loc = random.choice(bins_in_target) if bins_in_target else None
                        
                        # Create item location
                        ItemLocation.objects.create(
                            tenant_id=tenant_id,
                            item=item,
                            location=target_loc,
                            bin_location=bin_loc,
                            quantity=sc_quantity,
                            reorder_point=2,  # Lower reorder point for service centers
                            max_stock=30,
                            min_stock=5
                        )
                        created_item_locations.append(f"{item.name} in {target_loc.name}")
        
        print(f"Created {len(created_item_locations)} item location records")
        return created_item_locations
    
    def generate_transfer_orders(self, count=50):
        """Generate transfer orders between locations."""
        print(f"Generating {count} transfer orders...")
        
        created_transfer_orders = []
        
        for tenant_id in self.tenants:
            # Get locations for this tenant
            locations = Location.objects.filter(tenant_id=tenant_id, is_storage=True)
            
            if not locations or locations.count() < 2:
                print(f"Not enough locations for tenant {tenant_id}, skipping transfer orders")
                continue
            
            # Get item locations with quantity > 0
            item_locations = ItemLocation.objects.filter(
                tenant_id=tenant_id,
                quantity__gt=0
            )
            
            if not item_locations:
                print(f"No item locations with stock for tenant {tenant_id}, skipping transfer orders")
                continue
            
            # Create transfer orders for this tenant
            transfers_per_tenant = count // len(self.tenants) + 1
            
            # Status distribution for more realistic data
            status_weights = {
                'draft': 10,
                'pending': 25,
                'in_transit': 20,
                'completed': 40,
                'cancelled': 5
            }
            
            for i in range(transfers_per_tenant):
                # Generate random source and destination locations
                # Make sure they're different
                all_locations = list(locations)
                source_location = random.choice(all_locations)
                all_locations.remove(source_location)
                destination_location = random.choice(all_locations)
                
                # Generate reference number
                reference = f"TO-{tenant_id}-{datetime.now().strftime('%y%m%d')}-{i+1:04d}"
                
                # Select status
                status = random.choices(
                    list(status_weights.keys()),
                    weights=list(status_weights.values()),
                    k=1
                )[0]
                
                # Create transfer order
                transfer_order = TransferOrder.objects.create(
                    tenant_id=tenant_id,
                    reference=reference,
                    source_location=source_location,
                    destination_location=destination_location,
                    status=status,
                    notes=fake.paragraph() if random.random() > 0.7 else "",
                    items_count=0  # Will be updated after adding items
                )
                created_transfer_orders.append(transfer_order)
                
                # Get items available at source location
                available_items = item_locations.filter(location=source_location)
                
                if not available_items:
                    # Try parent location if this is a child location
                    if source_location.parent:
                        available_items = item_locations.filter(location=source_location.parent)
                
                if not available_items:
                    # If still no items, delete the transfer order and continue
                    transfer_order.delete()
                    continue
                
                # Add random items to the transfer order
                num_items = random.randint(1, min(5, available_items.count()))
                selected_items = random.sample(list(available_items), num_items)
                
                for item_loc in selected_items:
                    # Transfer a portion of the available quantity
                    transfer_qty = min(
                        item_loc.quantity * random.uniform(0.1, 0.5),  # 10-50% of available
                        item_loc.quantity - 1  # Leave at least 1 unit behind
                    )
                    
                    if transfer_qty < 1:
                        transfer_qty = 1
                    
                    # Create transfer order item
                    TransferOrderItem.objects.create(
                        tenant_id=tenant_id,
                        transfer_order=transfer_order,
                        item=item_loc.item,
                        quantity=transfer_qty,
                        notes=""
                    )
                
                # Update items count
                transfer_order.items_count = transfer_order.items.count()
                transfer_order.save()
                
                # For completed transfers, create a Transfer record
                if status == 'completed':
                    Transfer.objects.create(
                        tenant_id=tenant_id,
                        source_location=source_location,
                        destination_location=destination_location,
                        items_count=transfer_order.items_count,
                        reference=reference,
                        notes=f"Completed transfer {reference}"
                    )
        
        print(f"Created {len(created_transfer_orders)} transfer orders")
        return created_transfer_orders
    
    @transaction.atomic
    def run(self):
        """Run the warehouse data generator."""
        print("Starting Warehouse Data Generator...")
        
        # Check if we have inventory items
        if not Item.objects.exists():
            print("❌ No inventory items found. Please run add_inventory_data.py first.")
            return
        
        # Generate data in the correct order
        self.generate_location_types()
        self.generate_locations()
        self.distribute_items_to_locations()
        self.generate_transfer_orders(50)
        
        print("✅ Warehouse data generation completed!")


def main():
    """Execute the warehouse data generator."""
    generator = WarehouseGenerator()
    generator.run()


if __name__ == "__main__":
    main() 