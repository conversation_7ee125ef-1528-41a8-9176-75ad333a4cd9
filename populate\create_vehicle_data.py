import os
import sys
import django
import random
import uuid
from datetime import datetime, timedelta

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

# Import Django models
from django.db import connection
from django.utils import timezone

# Import setup models
from setup.models import Customer, Vehicle, ServiceCenter

# Egyptian vehicle data
EGYPTIAN_MAKES_MODELS = [
    {"make": "تويوتا", "models": ["كورولا", "لاند كروزر", "كامري", "هايلكس", "فورتشنر", "راف 4", "يارس"]},
    {"make": "هيونداي", "models": ["النترا", "اكسنت", "توسان", "كريتا", "سوناتا", "فيرنا"]},
    {"make": "نيسان", "models": ["صني", "سنترا", "قشقاي", "جوك", "اكس تريل", "باترول"]},
    {"make": "شيفروليه", "models": ["أفيو", "أوبترا", "لانوس", "كروز", "كابتيفا"]},
    {"make": "كيا", "models": ["سيراتو", "سبورتاج", "بيكانتو", "سورينتو", "ريو"]},
    {"make": "ميتسوبيشي", "models": ["لانسر", "أوتلاندر", "باجيرو", "إكليبس"]},
    {"make": "بي ام دبليو", "models": ["الفئة الثالثة", "الفئة الخامسة", "الفئة السابعة", "X5", "X3"]},
    {"make": "مرسيدس", "models": ["C200", "E180", "GLC", "A Class", "GLE", "S Class"]},
    {"make": "أودي", "models": ["A4", "A6", "Q5", "Q7", "A3"]},
    {"make": "فولكس فاجن", "models": ["باسات", "جيتا", "تيجوان", "جولف"]},
    {"make": "بيجو", "models": ["301", "3008", "508", "208", "2008"]},
    {"make": "رينو", "models": ["لوجان", "داستر", "كادجار", "ميجان", "فلوانس"]},
    {"make": "فيات", "models": ["تيبو", "500", "دوبلو", "لينيا"]},
    {"make": "جيلي", "models": ["إمجراند", "باندينو", "GC6", "X7"]},
    {"make": "BYD", "models": ["F3", "L3", "S6", "F0"]},
    {"make": "DFSK", "models": ["Glory 330", "Glory 580", "C35", "C37"]},
    {"make": "شيري", "models": ["تيجو", "اريزو", "إنفي", "E5"]}
]

COLORS = ["أبيض", "أسود", "فضي", "رمادي", "أحمر", "أزرق", "بني", "ذهبي", "أخضر", "برتقالي"]

def create_vehicles(count=100):
    print(f"\nCreating {count} vehicles...")
    
    # Get customers and service centers
    customers = list(Customer.objects.all())
    if not customers:
        print("No customers found. Please generate customers first.")
        return 0
        
    service_centers = list(ServiceCenter.objects.all())
    if not service_centers:
        print("No service centers found. Please generate service centers first.")
        return 0
        
    vehicles_created = 0
    current_year = datetime.now().year
    current_time = timezone.now()
    tenant_id = str(uuid.uuid4())
    
    # Create vehicles directly in the database
    for i in range(1, count + 1):
        try:
            # Select random make and model
            make_model = random.choice(EGYPTIAN_MAKES_MODELS)
            make = make_model["make"]
            model = random.choice(make_model["models"])
            
            # Generate random vehicle data
            year = random.randint(current_year - 15, current_year)
            color = random.choice(COLORS)
            
            # Generate VIN and license plate
            vin = f"EGT{random.randint(100000, 999999)}{random.randint(1000, 9999)}"
            license_plate = f"{random.randint(100, 999)} {random.choice('أبتثجحخدذرزسشصضطظعغفقكلمنهوي')}{random.choice('أبتثجحخدذرزسشصضطظعغفقكلمنهوي')}{random.choice('أبتثجحخدذرزسشصضطظعغفقكلمنهوي')}"
            
            # Select random owner and service center
            owner = random.choice(customers)
            service_center = random.choice(service_centers)
            
            # Generate dates
            purchase_date = datetime(year, random.randint(1, 12), random.randint(1, 28)).date()
            warranty_years = random.randint(3, 7)
            warranty_end_date = datetime(year + warranty_years, 
                                        purchase_date.month, 
                                        min(purchase_date.day, 28)).date()
            
            # Create the vehicle using Django ORM
            try:
                # Create vehicle using Django ORM
                vehicle = Vehicle(
                    id=str(uuid.uuid4()),
                    tenant_id=tenant_id,
                    make=make,
                    model=model,
                    year=year,
                    vin=vin,
                    license_plate=license_plate,
                    color=color,
                    owner=owner,
                    service_center=service_center,
                    purchase_date=purchase_date,
                    warranty_end_date=warranty_end_date,
                    notes=f"سيارة {make} {model} موديل {year}",
                    attributes={}
                )
                vehicle.save()
                
                # Save was successful
                vehicles_created += 1
                
                if vehicles_created % 10 == 0:
                    print(f"Created {vehicles_created} vehicles...")
                    
            except Exception as e:
                print(f"Error creating vehicle using ORM: {e}")
                continue
                
        except Exception as e:
            print(f"Error creating vehicle {i}: {e}")
    
    print(f"Created {vehicles_created} vehicles")
    return vehicles_created

def main():
    print("Starting vehicle creation...")
    
    # Create vehicles directly
    vehicles_created = create_vehicles(100)
    
    print("\nVehicle creation complete!")
    
    # Return number of vehicles created for verification
    return vehicles_created

if __name__ == "__main__":
    created_count = main()
    print(f"Total vehicles created: {created_count}") 