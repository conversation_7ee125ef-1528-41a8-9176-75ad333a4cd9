# Generated by Django 4.2.20 on 2025-05-26 11:20

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("work_orders", "0004_workorder_customer"),
        ("setup", "0012_populate_egyptian_vehicle_data"),
        ("inventory", "0019_auto_20250526_0931"),
    ]

    operations = [
        migrations.AlterUniqueTogether(
            name="operationpricing",
            unique_together=set(),
        ),
        migrations.AddField(
            model_name="operationpricing",
            name="company",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="operation_prices",
                to="setup.company",
                verbose_name="Company",
            ),
        ),
        migrations.AddField(
            model_name="operationpricing",
            name="franchise",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="operation_prices",
                to="setup.franchise",
                verbose_name="Franchise",
            ),
        ),
        migrations.AddField(
            model_name="operationpricing",
            name="service_center",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="operation_prices",
                to="setup.servicecenter",
                verbose_name="Service Center",
            ),
        ),
        migrations.AddField(
            model_name="partpricing",
            name="company",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="part_prices",
                to="setup.company",
                verbose_name="Company",
            ),
        ),
        migrations.AddField(
            model_name="partpricing",
            name="franchise",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="part_prices",
                to="setup.franchise",
                verbose_name="Franchise",
            ),
        ),
        migrations.AddField(
            model_name="partpricing",
            name="service_center",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="part_prices",
                to="setup.servicecenter",
                verbose_name="Service Center",
            ),
        ),
        migrations.AlterUniqueTogether(
            name="operationpricing",
            unique_together={
                (
                    "tenant_id",
                    "operation_type",
                    "vehicle_make",
                    "vehicle_model",
                    "year_from",
                    "year_to",
                    "franchise",
                    "company",
                    "service_center",
                )
            },
        ),
    ]
