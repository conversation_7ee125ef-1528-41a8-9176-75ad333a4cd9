import os
import re

# Path to the populate directory
populate_dir = "populate"

# Files to update
python_files = [f for f in os.listdir(populate_dir) if f.endswith('.py')]

# Pattern to match the Django setup code
settings_pattern = re.compile(r'os\.environ\.setdefault\(\'DJANGO_SETTINGS_MODULE\',\s*[\'\"]([^\'\"]+)[\'\"]\)')

# Path insertion code to add
path_insert_code = "\n# Add the parent directory to the Python path so imports work correctly\nsys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))\n"

# Counter for files updated
files_updated = 0

# Process each Python file
for filename in python_files:
    file_path = os.path.join(populate_dir, filename)
    
    # Read the file content
    with open(file_path, 'r', encoding='utf-8') as file:
        content = file.read()
    
    # Check if we need to update the settings module path
    settings_match = settings_pattern.search(content)
    if settings_match and settings_match.group(1) != 'project.settings':
        content = settings_pattern.sub("os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')", content)
    
    # Check if we need to add the sys.path insertion
    if "sys.path.insert" not in content:
        # Find the position after the imports but before the Django setup
        import_section_end = content.find("os.environ.setdefault")
        
        # If we can't find it, try an alternative location
        if import_section_end == -1:
            import_section_end = max(
                content.find("# Set up Django environment"),
                content.rfind("import ", 0, 200)  # Look within first 200 chars
            )
        
        # If we found a suitable position
        if import_section_end > 0:
            # Ensure we have sys import
            if "import sys" not in content[:import_section_end]:
                if "import os" in content[:import_section_end]:
                    # Add sys to the same line as os
                    content = content.replace("import os", "import os\nimport sys", 1)
                else:
                    # Add sys import as a new line
                    content = content[:import_section_end] + "\nimport sys" + content[import_section_end:]
            
            # Insert the path code after the imports
            modified_line_pos = content.find("os.environ.setdefault")
            if modified_line_pos > 0:
                content = content[:modified_line_pos] + path_insert_code + content[modified_line_pos:]
    
    # Write the updated content back to the file
    with open(file_path, 'w', encoding='utf-8') as file:
        file.write(content)
    
    files_updated += 1
    print(f"Updated {filename}")

print(f"\nUpdated {files_updated} files in {populate_dir} directory.") 