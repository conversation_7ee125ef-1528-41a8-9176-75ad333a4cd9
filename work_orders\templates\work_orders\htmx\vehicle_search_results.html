{% load i18n %}

{% if vehicles %}
    {% for vehicle in vehicles %}
        <div class="p-2 border-b border-gray-200 hover:bg-gray-100 cursor-pointer"
             hx-post="{% url 'work_orders:htmx_select_vehicle' %}"
             hx-vals='{"vehicle_id": "{{ vehicle.id }}"}'
             hx-target="#vehicle-details"
             hx-swap="innerHTML">
            <div class="flex justify-between">
                <div>
                    <div class="font-medium">{{ vehicle.make }} {{ vehicle.model }} {% if vehicle.year %}({{ vehicle.year }}){% endif %}</div>
                    <div class="text-sm text-gray-600">
                        {% if vehicle.license_plate %}
                            {% trans "License" %}: {{ vehicle.license_plate }}
                        {% endif %}
                        {% if vehicle.vin %}
                            | {% trans "VIN" %}: {{ vehicle.vin }}
                        {% endif %}
                    </div>
                </div>
                <button type="button" class="text-blue-600 hover:text-blue-800"
                        onclick="selectVehicle('{{ vehicle.id }}', '{{ vehicle.make }} {{ vehicle.model }}{% if vehicle.year %} ({{ vehicle.year }}){% endif %}')">
                    {% trans "Select" %}
                </button>
            </div>
        </div>
    {% endfor %}
{% else %}
    <div class="p-3 text-center text-gray-500">
        {% trans "No vehicles found matching your search criteria" %}
    </div>
{% endif %} 