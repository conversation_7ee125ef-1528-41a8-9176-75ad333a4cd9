{% load i18n %}

{# 
  Multi-step form navigation component
  
  Parameters:
  - steps: List of dictionaries with keys:
      - name: Step name
      - label: Translated label to display
      - icon: Optional icon class (from Font Awesome)
  - current_step: Name of the current active step
  - step_param: Optional parameter name for the step in URL (default: 'step')
  - base_url: Optional base URL for step links
#}

<div class="mx-4 mb-4">
    <ol class="flex items-center w-full text-sm font-medium text-center text-gray-500 dark:text-gray-400 sm:text-base">
        {% for step in steps %}
            <li class="flex {% if not forloop.last %}md:w-full{% endif %} items-center 
                {% if step.name == current_step %}
                    text-blue-600 dark:text-blue-500
                {% elif forloop.counter0 < current_step_index %}
                    text-green-600 dark:text-green-500
                {% endif %} 
                {% if not forloop.last %}
                    sm:after:content-[''] after:w-full after:h-1 after:border-b after:border-gray-200 after:border-1 after:hidden sm:after:inline-block after:mx-6 xl:after:mx-10 dark:after:border-gray-700
                    {% if forloop.counter0 < current_step_index %}
                        after:border-green-500 dark:after:border-green-500
                    {% endif %}
                {% endif %}">
                
                <span class="flex items-center after:content-['/'] sm:after:hidden after:mx-2 after:text-gray-200 dark:after:text-gray-500">
                    {% if forloop.counter0 < current_step_index %}
                        <span class="flex items-center justify-center w-6 h-6 mr-2 text-xs border border-green-500 rounded-full shrink-0 dark:border-green-500">
                            <i class="fas fa-check"></i>
                        </span>
                    {% else %}
                        <span class="flex items-center justify-center w-6 h-6 mr-2 text-xs 
                            {% if step.name == current_step %}
                                border border-blue-600 dark:border-blue-500
                            {% else %}
                                bg-gray-100 dark:bg-gray-700
                            {% endif %} rounded-full shrink-0">
                            {{ forloop.counter }}
                        </span>
                    {% endif %}
                    
                    {% if base_url and step.name != current_step %}
                        <a href="{{ base_url }}{% if step_param %}?{{ step_param }}={{ step.name }}{% endif %}" 
                           class="{% if not forloop.counter0 < current_step_index %}hover:text-gray-900 dark:hover:text-white{% endif %}">
                            {% if step.icon %}<i class="{{ step.icon }} mr-1"></i>{% endif %}
                            {{ step.label }}
                        </a>
                    {% else %}
                        {% if step.icon %}<i class="{{ step.icon }} mr-1"></i>{% endif %}
                        {{ step.label }}
                    {% endif %}
                </span>
            </li>
        {% endfor %}
    </ol>
</div> 