{% load i18n %}

{% if error %}
    <div class="p-3 text-center text-red-500 bg-red-50 border border-red-200 rounded">
        <div class="font-medium">{% trans "Error occurred" %}</div>
        <div class="text-sm">{{ error }}</div>
    </div>
{% elif customers %}
    {% for customer in customers %}
        <div class="p-2 border-b border-gray-200 hover:bg-gray-100 cursor-pointer"
             hx-post="{% url 'work_orders:htmx_select_customer' %}"
             hx-vals='{"customer_id": "{{ customer.id }}"}'
             hx-target="#customer-details"
             hx-swap="innerHTML">
            <div class="flex justify-between">
                <div>
                    <div class="font-medium">{{ customer.full_name }}</div>
                    <div class="text-sm text-gray-600">{{ customer.phone }}</div>
                </div>
                <button type="button" class="text-blue-600 hover:text-blue-800"
                        onclick="selectCustomer('{{ customer.id }}', '{{ customer.full_name|escapejs }}')">
                    {% trans "Select" %}
                </button>
            </div>
        </div>
    {% endfor %}
{% else %}
    <div class="p-3 text-center text-gray-500">
        {% trans "No customers found matching your search criteria" %}
    </div>
{% endif %} 