from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from warehouse.models import Location, ItemLocation, TransferOrder, TransferOrderItem, Transfer, LocationType, BinLocation


class BinLocationInline(admin.TabularInline):
    model = BinLocation
    extra = 1
    fields = ('name', 'code', 'aisle', 'rack', 'shelf', 'position', 'is_active')


class ItemLocationInline(admin.TabularInline):
    model = ItemLocation
    extra = 0
    raw_id_fields = ('item',)
    autocomplete_fields = ('bin_location',)
    fieldsets = (
        (None, {
            'fields': ('item', 'quantity', 'bin_location')
        }),
        (_('Stock Levels'), {
            'fields': ('min_stock', 'max_stock', 'reorder_point'),
            'classes': ('collapse',)
        }),
    )


class TransferOrderItemInline(admin.TabularInline):
    model = TransferOrderItem
    extra = 0
    raw_id_fields = ('item',)


@admin.register(LocationType)
class LocationTypeAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'is_storage', 'is_receiving', 'is_shipping', 'is_service', 'is_active', 'tenant_id')
    list_filter = ('is_active', 'is_storage', 'is_receiving', 'is_shipping', 'is_service')
    search_fields = ('name', 'code', 'description')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        (None, {
            'fields': ('tenant_id', 'name', 'code', 'description', 'is_active')
        }),
        (_('Configuration'), {
            'fields': (
                'is_storage', 'is_receiving', 'is_shipping', 'is_service', 
                'requires_bin_locations'
            )
        }),
        (_('UI Display'), {
            'fields': ('icon', 'color'),
            'classes': ('collapse',)
        }),
        (_('Metadata'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(Location)
class LocationAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'location_type', 'parent', 'city', 'is_active', 'tenant_id')
    list_filter = ('is_active', 'location_type', 'country', 'city')
    search_fields = ('name', 'code', 'address', 'city')
    readonly_fields = ('created_at', 'updated_at')
    inlines = [BinLocationInline, ItemLocationInline]
    autocomplete_fields = ('location_type', 'parent')
    fieldsets = (
        (None, {
            'fields': ('tenant_id', 'name', 'code', 'location_type', 'parent', 'is_active')
        }),
        (_('Contact Information'), {
            'fields': ('contact_name', 'phone', 'email')
        }),
        (_('Address'), {
            'fields': ('address', 'city', 'state', 'country', 'postal_code', 'latitude', 'longitude')
        }),
        (_('Storage Capacity'), {
            'fields': ('area_sqm', 'max_items', 'max_volume', 'max_weight')
        }),
        (_('Additional Information'), {
            'fields': ('notes', 'attributes'),
            'classes': ('collapse',)
        }),
        (_('Metadata'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(BinLocation)
class BinLocationAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'location', 'position_display', 'is_active', 'tenant_id')
    list_filter = ('location', 'is_active')
    search_fields = ('name', 'code', 'aisle', 'rack', 'shelf', 'position', 'barcode')
    readonly_fields = ('created_at', 'updated_at')
    autocomplete_fields = ('location',)
    fieldsets = (
        (None, {
            'fields': ('tenant_id', 'location', 'name', 'code', 'description', 'is_active')
        }),
        (_('Position Information'), {
            'fields': ('aisle', 'rack', 'shelf', 'position', 'barcode')
        }),
        (_('Metadata'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def position_display(self, obj):
        pos_parts = []
        if obj.aisle:
            pos_parts.append(f"A{obj.aisle}")
        if obj.rack:
            pos_parts.append(f"R{obj.rack}")
        if obj.shelf:
            pos_parts.append(f"S{obj.shelf}")
        if obj.position:
            pos_parts.append(f"P{obj.position}")
            
        return "-".join(pos_parts) if pos_parts else "-"
    position_display.short_description = _("Position")


@admin.register(ItemLocation)
class ItemLocationAdmin(admin.ModelAdmin):
    list_display = ('item', 'location', 'bin_location', 'quantity', 'is_low_stock', 'tenant_id')
    list_filter = ('location', 'bin_location')
    search_fields = ('item__name', 'item__sku', 'location__name', 'bin_location__name')
    readonly_fields = ('created_at', 'updated_at', 'is_low_stock')
    raw_id_fields = ('item',)
    autocomplete_fields = ('location', 'bin_location')
    fieldsets = (
        (None, {
            'fields': ('tenant_id', 'item', 'location', 'bin_location', 'quantity')
        }),
        (_('Stock Levels'), {
            'fields': ('min_stock', 'max_stock', 'reorder_point', 'is_low_stock')
        }),
        (_('Metadata'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def is_low_stock(self, obj):
        return obj.is_low_stock
    is_low_stock.boolean = True
    is_low_stock.short_description = _("Low Stock")


@admin.register(TransferOrder)
class TransferOrderAdmin(admin.ModelAdmin):
    list_display = ('reference', 'source_location', 'destination_location', 'status', 'created_at', 'tenant_id')
    list_filter = ('status', 'source_location', 'destination_location')
    search_fields = ('reference', 'notes')
    readonly_fields = ('created_at', 'updated_at')
    inlines = [TransferOrderItemInline]
    fieldsets = (
        (None, {
            'fields': ('tenant_id', 'reference', 'source_location', 'destination_location', 'status')
        }),
        (_('Additional Information'), {
            'fields': ('notes',)
        }),
        (_('Metadata'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(TransferOrderItem)
class TransferOrderItemAdmin(admin.ModelAdmin):
    list_display = ('transfer_order', 'item', 'quantity', 'tenant_id')
    list_filter = ('transfer_order__status',)
    search_fields = ('transfer_order__reference', 'item__name', 'item__sku', 'notes')
    readonly_fields = ('created_at', 'updated_at')
    raw_id_fields = ('transfer_order', 'item')
    fieldsets = (
        (None, {
            'fields': ('tenant_id', 'transfer_order', 'item', 'quantity')
        }),
        (_('Additional Information'), {
            'fields': ('notes',)
        }),
        (_('Metadata'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(Transfer)
class TransferAdmin(admin.ModelAdmin):
    list_display = ('id', 'source_location', 'destination_location', 'items_count', 'created_at', 'tenant_id')
    list_filter = ('source_location', 'destination_location')
    search_fields = ('reference', 'notes')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        (None, {
            'fields': ('tenant_id', 'source_location', 'destination_location', 'items_count', 'reference')
        }),
        (_('Additional Information'), {
            'fields': ('notes',)
        }),
        (_('Metadata'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
