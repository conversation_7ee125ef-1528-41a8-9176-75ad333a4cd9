{% extends 'base.html' %}
{% load i18n %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}{% trans "إضافة مواد إلى أمر العمل" %}{% endblock %}

{% block extra_head %}
<style>
    .item-card {
        cursor: pointer;
        transition: all 0.2s;
        margin-bottom: 15px;
    }
    
    .item-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    
    .item-card.selected {
        border-color: #28a745;
        background-color: #f8fff8;
    }
    
    .compatibility-badge {
        position: absolute;
        top: 10px;
        right: 10px;
    }
    
    .stock-info {
        font-size: 0.85rem;
    }
    
    .filter-section {
        margin-bottom: 20px;
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 5px;
    }
    
    /* RTL-specific styling */
    html[dir="rtl"] .compatibility-badge {
        right: auto;
        left: 10px;
    }
    
    html[dir="rtl"] .mr-1,
    html[dir="rtl"] .mr-2,
    html[dir="rtl"] .mr-3 {
        margin-right: 0;
    }
    
    html[dir="rtl"] .mr-1 {
        margin-left: 0.25rem;
    }
    
    html[dir="rtl"] .mr-2 {
        margin-left: 0.5rem;
    }
    
    html[dir="rtl"] .mr-3 {
        margin-left: 0.75rem;
    }
    
    html[dir="rtl"] .ml-1,
    html[dir="rtl"] .ml-2,
    html[dir="rtl"] .ml-3 {
        margin-left: 0;
    }
    
    html[dir="rtl"] .ml-1 {
        margin-right: 0.25rem;
    }
    
    html[dir="rtl"] .ml-2 {
        margin-right: 0.5rem;
    }
    
    html[dir="rtl"] .ml-3 {
        margin-right: 0.75rem;
    }
    
    .metric-value {
        direction: ltr; /* Always show numbers left-to-right */
        display: inline-block;
        font-family: 'Tajawal', sans-serif;
        text-align: start;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>{% trans "إضافة مواد إلى أمر العمل" %} #<span class="metric-value">{{ work_order.work_order_number }}</span></h1>
                <a href="{% url 'work_orders:work_order_detail' work_order.id %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left {% if LANGUAGE_CODE == 'ar' %}ml-1{% else %}mr-1{% endif %}"></i> {% trans "العودة إلى أمر العمل" %}
                </a>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-3">
            <!-- Filters sidebar -->
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">{% trans "التصفية" %}</h5>
                </div>
                <div class="card-body">
                    <!-- Vehicle Filter -->
                    {% if vehicle %}
                    <div class="filter-section">
                        <h6>{% trans "المركبة" %}</h6>
                        <div class="card">
                            <div class="card-body py-2">
                                <strong>{{ vehicle.make }} {{ vehicle.model }}</strong><br>
                                <small class="text-muted">{{ vehicle.year }} - {{ vehicle.license_plate }}</small>
                                <input type="hidden" id="vehicle-make" value="{{ vehicle.make }}">
                                <input type="hidden" id="vehicle-model" value="{{ vehicle.model }}">
                                <input type="hidden" id="vehicle-year" value="{{ vehicle.year }}">
                            </div>
                        </div>
                    </div>
                    {% else %}
                    <div class="filter-section">
                        <h6>{% trans "المركبة" %}</h6>
                        <div class="form-group">
                            <label for="make-filter">{% trans "الصانع" %}</label>
                            <input type="text" class="form-control" id="make-filter" placeholder="{% trans 'مثال: تويوتا' %}">
                        </div>
                        <div class="form-group">
                            <label for="model-filter">{% trans "الطراز" %}</label>
                            <input type="text" class="form-control" id="model-filter" placeholder="{% trans 'مثال: كورولا' %}">
                        </div>
                        <div class="form-group">
                            <label for="year-filter">{% trans "السنة" %}</label>
                            <input type="number" class="form-control" id="year-filter" placeholder="{% trans 'مثال: 2019' %}">
                        </div>
                    </div>
                    {% endif %}
                    
                    <!-- Operation Filter -->
                    <div class="filter-section">
                        <h6>{% trans "العملية" %}</h6>
                        <div class="card">
                            <div class="card-body py-2">
                                <strong>{{ work_order.work_order_type.name }}</strong>
                                <input type="hidden" id="operation-type" value="{{ work_order.work_order_type.id }}">
                            </div>
                        </div>
                    </div>
                    
                    <!-- Warehouse Filter -->
                    {% if warehouses %}
                    <div class="filter-section">
                        <h6>{% trans "المستودع" %}</h6>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="warehouse-filter" checked>
                            <label class="form-check-label" for="warehouse-filter">
                                {% trans "عرض العناصر المتوفرة في المخزون فقط" %}
                            </label>
                        </div>
                        <div id="warehouse-list" class="mt-2">
                            {% for warehouse in warehouses %}
                            <div class="warehouse-item">
                                <small>{{ warehouse.name }}</small>
                            </div>
                            {% endfor %}
                        </div>
                        <input type="hidden" id="service-center" value="{{ service_center.id }}">
                    </div>
                    {% endif %}
                    
                    <!-- Category Filter -->
                    <div class="filter-section">
                        <h6>{% trans "التصنيف" %}</h6>
                        <div class="form-group">
                            <select class="form-control" id="category-filter">
                                <option value="">{% trans "جميع التصنيفات" %}</option>
                                <option value="part">{% trans "قطع الغيار" %}</option>
                                <option value="consumable">{% trans "المواد الاستهلاكية" %}</option>
                                <option value="tool">{% trans "الأدوات" %}</option>
                                <option value="equipment">{% trans "المعدات" %}</option>
                                <option value="material">{% trans "المواد" %}</option>
                            </select>
                        </div>
                    </div>
                    
                    <button id="apply-filters" class="btn btn-primary btn-block mt-3">
                        <i class="fas fa-filter {% if LANGUAGE_CODE == 'ar' %}ml-1{% else %}mr-1{% endif %}"></i> {% trans "تطبيق التصفية" %}
                    </button>
                </div>
            </div>
        </div>
        
        <div class="col-md-9">
            <!-- Spare Parts Search Component -->
            {% if include_spare_parts_search %}
                {% include 'work_orders/spare_parts_search.html' with operations=operations vehicle=vehicle service_center=service_center %}
            {% endif %}
            
            <!-- Main content -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">{% trans "اختر المواد" %}</h5>
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <div class="input-group">
                            <input type="text" id="item-search" class="form-control" placeholder="{% trans 'البحث بالاسم أو الرمز' %}">
                            <div class="input-group-append">
                                <button class="btn btn-outline-secondary" type="button">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div id="items-container" class="row">
                        <!-- Items will be loaded here via JS -->
                        <div class="col-12 text-center py-5">
                            <div class="spinner-border text-primary" role="status">
                                <span class="sr-only">{% trans "جاري التحميل..." %}</span>
                            </div>
                            <p class="mt-3">{% trans "جاري تحميل العناصر..." %}</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Material Form -->
            <div class="card" id="material-form-card" style="display: none;">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">{% trans "تفاصيل المواد" %}</h5>
                </div>
                <div class="card-body">
                    <form method="post" id="material-form">
                        {% csrf_token %}
                        
                        <div class="form-row">
                            <div class="col-md-8">
                                <div id="selected-item-info" class="mb-3"></div>
                                {{ form.item|as_crispy_field }}
                            </div>
                            <div class="col-md-4">
                                {{ form.quantity|as_crispy_field }}
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="col-md-6">
                                {{ form.unit_of_measure|as_crispy_field }}
                            </div>
                            <div class="col-md-6">
                                {{ form.notes|as_crispy_field }}
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-plus {% if LANGUAGE_CODE == 'ar' %}ml-1{% else %}mr-1{% endif %}"></i> {% trans "إضافة مواد" %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

{% if service_center %}
<input type="hidden" id="selected-service-center" value="{{ service_center.id }}">
{% endif %}

<!-- Work Order ID -->
<input type="hidden" id="work-order-id" value="{{ work_order.id }}">
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Initial variables
        let allItems = [];
        let operationItems = [];
        let warehouseItems = [];
        let warehouseStock = {};
        
        // Load data on page load
        loadItems();
        
        // Handle applying filters
        $('#apply-filters').click(function() {
            loadItems();
        });
        
        // Handle search
        $('#item-search').on('keyup', function() {
            const searchTerm = $(this).val().toLowerCase();
            filterItems(searchTerm);
        });
        
        // Function to load items based on filters
        function loadItems() {
            // Show loading
            $('#items-container').html(`
                <div class="col-12 text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="sr-only">{% trans "جاري التحميل..." %}</span>
                    </div>
                    <p class="mt-3">{% trans "جاري تحميل العناصر..." %}</p>
                </div>
            `);
            
            // Build query parameters
            const params = new URLSearchParams();
            
            // Add vehicle filters
            const make = $('#vehicle-make').val() || $('#make-filter').val();
            const model = $('#vehicle-model').val() || $('#model-filter').val();
            const year = $('#vehicle-year').val() || $('#year-filter').val();
            
            if (make) params.append('make', make);
            if (model) params.append('model', model);
            if (year) params.append('year', year);
            
            // Add operation filter
            const operationType = $('#operation-type').val();
            if (operationType) params.append('operation_type', operationType);
            
            // Add service center filter
            const serviceCenter = $('#service-center').val();
            if (serviceCenter) params.append('service_center', serviceCenter);
            
            // Make API call
            $.ajax({
                url: `{% url 'work_orders:api_filter_items' %}?${params.toString()}`,
                method: 'GET',
                success: function(data) {
                    // Store data
                    allItems = data.items;
                    operationItems = data.operation_items;
                    warehouseItems = data.warehouse_items;
                    warehouseStock = data.warehouse_stock;
                    
                    // Filter by category if selected
                    const category = $('#category-filter').val();
                    if (category) {
                        allItems = allItems.filter(item => item.category === category);
                    }
                    
                    // Filter by warehouse availability if checkbox is checked
                    if ($('#warehouse-filter').is(':checked') && warehouseItems.length > 0) {
                        allItems = allItems.filter(item => warehouseItems.includes(item.id));
                    }
                    
                    // Render items
                    renderItems(allItems);
                },
                error: function() {
                    $('#items-container').html(`
                        <div class="col-12 text-center py-5">
                            <div class="alert alert-danger">
                                {% trans "خطأ في تحميل العناصر. يرجى المحاولة مرة أخرى." %}
                            </div>
                        </div>
                    `);
                }
            });
        }
        
        // Function to render items
        function renderItems(items) {
            if (items.length === 0) {
                $('#items-container').html(`
                    <div class="col-12 text-center py-5">
                        <div class="alert alert-info">
                            {% trans "لم يتم العثور على عناصر تطابق معايير البحث." %}
                        </div>
                    </div>
                `);
                return;
            }
            
            let html = '';
            
            items.forEach(item => {
                // Determine compatibility badges
                let badges = '';
                
                if (operationItems.includes(item.id)) {
                    badges += `<span class="badge badge-success compatibility-badge">{% trans "العملية" %}</span>`;
                }
                
                if (warehouseItems.includes(item.id)) {
                    badges += `<span class="badge badge-info compatibility-badge ml-1">{% trans "في المخزون" %}</span>`;
                }
                
                // Stock information
                let stockHtml = '';
                if (warehouseStock[item.id]) {
                    stockHtml = '<div class="stock-info mt-2">';
                    stockHtml += '<strong>{% trans "المخزون:" %}</strong><br>';
                    
                    warehouseStock[item.id].forEach(stock => {
                        stockHtml += `<small>${stock.warehouse_name}: <span class="metric-value">${stock.quantity}</span></small><br>`;
                    });
                    
                    stockHtml += '</div>';
                }
                
                // Category display
                let categoryDisplay = '';
                if (item.category) {
                    const categories = {
                        'part': '{% trans "قطع غيار" %}',
                        'consumable': '{% trans "مواد استهلاكية" %}',
                        'tool': '{% trans "أدوات" %}',
                        'equipment': '{% trans "معدات" %}',
                        'material': '{% trans "مواد" %}',
                        'finished_good': '{% trans "منتج نهائي" %}'
                    };
                    
                    categoryDisplay = `<span class="badge badge-secondary">${categories[item.category] || item.category}</span>`;
                }
                
                html += `
                    <div class="col-md-4">
                        <div class="card item-card" data-item-id="${item.id}">
                            <div class="card-body">
                                ${badges}
                                <h5 class="card-title">${item.name}</h5>
                                <h6 class="card-subtitle mb-2 text-muted">${item.sku}</h6>
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>${categoryDisplay}</div>
                                    <div class="text-success font-weight-bold">${item.unit_price}</div>
                                </div>
                                ${stockHtml}
                            </div>
                        </div>
                    </div>
                `;
            });
            
            $('#items-container').html(html);
            
            // Handle item selection
            $('.item-card').click(function() {
                const itemId = $(this).data('item-id');
                selectItem(itemId);
            });
        }
        
        // Function to filter items by search term
        function filterItems(searchTerm) {
            if (!searchTerm) {
                renderItems(allItems);
                return;
            }
            
            const filtered = allItems.filter(item => 
                item.name.toLowerCase().includes(searchTerm) || 
                item.sku.toLowerCase().includes(searchTerm)
            );
            
            renderItems(filtered);
        }
        
        // Function to select an item
        function selectItem(itemId) {
            // Highlight selected card
            $('.item-card').removeClass('selected');
            $(`.item-card[data-item-id="${itemId}"]`).addClass('selected');
            
            // Set form value
            $('#id_item').val(itemId);
            
            // Find the selected item
            const item = allItems.find(i => i.id === itemId);
            
            // Show item info
            let infoHtml = `
                <div class="alert alert-success">
                    <strong>${item.name}</strong> (${item.sku})<br>
                    <small class="text-muted">{% trans "سعر الوحدة:" %} ${item.unit_price}</small>
                </div>
            `;
            
            $('#selected-item-info').html(infoHtml);
            
            // Show form
            $('#material-form-card').show();
            
            // Scroll to form
            $('html, body').animate({
                scrollTop: $('#material-form-card').offset().top - 100
            }, 500);
        }
    });
</script>
{% endblock %} 