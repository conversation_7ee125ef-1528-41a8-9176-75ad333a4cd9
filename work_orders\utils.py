"""
Utility functions for the work_orders app.
"""
import logging
from uuid import UUID

logger = logging.getLogger(__name__)

def safe_uuid(uuid_string):
    """
    Safely validate and convert a UUID string to a standardized format.
    
    Args:
        uuid_string: String or object to validate as UUID
        
    Returns:
        String representation of UUID if valid, None otherwise
    """
    if not uuid_string:
        return None
        
    try:
        # Try to parse the UUID string
        return str(UUID(str(uuid_string), version=4))
    except (ValueError, TypeError, AttributeError) as e:
        logger.warning(f"Invalid UUID format: {uuid_string}, Error: {str(e)}")
        return None

def validate_uuid_list(uuid_list):
    """
    Validate a list of UUID strings and return only the valid ones.
    
    Args:
        uuid_list: List of UUID strings
        
    Returns:
        List of valid UUID strings
    """
    if not uuid_list:
        return []
        
    valid_uuids = []
    for uuid_string in uuid_list:
        valid_uuid = safe_uuid(uuid_string)
        if valid_uuid:
            valid_uuids.append(valid_uuid)
            
    return valid_uuids

def get_object_or_none(model_class, **kwargs):
    """
    Get a single object from a model class, safely handling UUID fields.
    Like get_object_or_404 but returns None instead of raising 404.
    
    Args:
        model_class: Django model class
        **kwargs: Lookup parameters, will validate any UUID fields
        
    Returns:
        Model instance or None if not found
    """
    # Process any UUID fields
    for key, value in kwargs.items():
        if 'uuid' in key.lower() or key == 'id' or key.endswith('_id'):
            kwargs[key] = safe_uuid(value)
            if kwargs[key] is None and value is not None:
                # If we had a value but it's not a valid UUID, we won't find the object
                return None
    
    try:
        return model_class.objects.get(**kwargs)
    except (model_class.DoesNotExist, ValueError, TypeError):
        return None 