# Generated by Django 4.2.20 on 2025-05-08 18:51

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0006_itemclassification_item_classification'),
        ('warehouse', '0002_transferorder_items_count_transfer'),
    ]

    operations = [
        migrations.AddField(
            model_name='itemlocation',
            name='max_stock',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Maximum quantity to stock at this location', max_digits=10, null=True, verbose_name='Maximum Stock'),
        ),
        migrations.AddField(
            model_name='itemlocation',
            name='min_stock',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Minimum quantity to maintain at this location', max_digits=10, null=True, verbose_name='Minimum Stock'),
        ),
        migrations.AddField(
            model_name='itemlocation',
            name='reorder_point',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Minimum quantity before reordering for this specific location', max_digits=10, null=True, verbose_name='Reorder Point'),
        ),
        migrations.AddField(
            model_name='location',
            name='area_sqm',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Storage area in square meters', max_digits=10, null=True, verbose_name='Area (sqm)'),
        ),
        migrations.AddField(
            model_name='location',
            name='attributes',
            field=models.JSONField(blank=True, default=dict, verbose_name='Custom Attributes'),
        ),
        migrations.AddField(
            model_name='location',
            name='city',
            field=models.CharField(blank=True, max_length=100, verbose_name='City'),
        ),
        migrations.AddField(
            model_name='location',
            name='contact_name',
            field=models.CharField(blank=True, max_length=100, verbose_name='Contact Name'),
        ),
        migrations.AddField(
            model_name='location',
            name='country',
            field=models.CharField(blank=True, max_length=100, verbose_name='Country'),
        ),
        migrations.AddField(
            model_name='location',
            name='email',
            field=models.EmailField(blank=True, max_length=254, verbose_name='Email'),
        ),
        migrations.AddField(
            model_name='location',
            name='latitude',
            field=models.DecimalField(blank=True, decimal_places=7, max_digits=10, null=True, verbose_name='Latitude'),
        ),
        migrations.AddField(
            model_name='location',
            name='longitude',
            field=models.DecimalField(blank=True, decimal_places=7, max_digits=10, null=True, verbose_name='Longitude'),
        ),
        migrations.AddField(
            model_name='location',
            name='max_items',
            field=models.PositiveIntegerField(blank=True, help_text='Maximum number of items this location can store', null=True, verbose_name='Maximum Items'),
        ),
        migrations.AddField(
            model_name='location',
            name='max_volume',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Maximum volume in cubic meters', max_digits=10, null=True, verbose_name='Maximum Volume'),
        ),
        migrations.AddField(
            model_name='location',
            name='max_weight',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Maximum weight in kilograms', max_digits=10, null=True, verbose_name='Maximum Weight'),
        ),
        migrations.AddField(
            model_name='location',
            name='notes',
            field=models.TextField(blank=True, verbose_name='Notes'),
        ),
        migrations.AddField(
            model_name='location',
            name='parent',
            field=models.ForeignKey(blank=True, help_text='If this is a sub-location (e.g., a shelf within a warehouse)', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='warehouse.location', verbose_name='Parent Location'),
        ),
        migrations.AddField(
            model_name='location',
            name='phone',
            field=models.CharField(blank=True, max_length=50, verbose_name='Phone'),
        ),
        migrations.AddField(
            model_name='location',
            name='postal_code',
            field=models.CharField(blank=True, max_length=20, verbose_name='Postal Code'),
        ),
        migrations.AddField(
            model_name='location',
            name='state',
            field=models.CharField(blank=True, max_length=100, verbose_name='State/Province'),
        ),
        migrations.CreateModel(
            name='LocationType',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('name', models.CharField(max_length=100, verbose_name='Name')),
                ('code', models.CharField(max_length=50, verbose_name='Code')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('icon', models.CharField(blank=True, help_text='Icon name for UI display', max_length=50, verbose_name='Icon')),
                ('color', models.CharField(blank=True, help_text='Color code for UI display', max_length=20, verbose_name='Color')),
                ('is_active', models.BooleanField(default=True, verbose_name='Active')),
                ('requires_bin_locations', models.BooleanField(default=False, help_text='If true, this location type must have bin locations defined', verbose_name='Requires Bin Locations')),
                ('is_storage', models.BooleanField(default=True, help_text='If true, this location can store inventory', verbose_name='Is Storage Location')),
                ('is_receiving', models.BooleanField(default=False, help_text='If true, this location can receive inventory from vendors', verbose_name='Is Receiving Location')),
                ('is_shipping', models.BooleanField(default=False, help_text='If true, this location can ship inventory to customers', verbose_name='Is Shipping Location')),
                ('is_service', models.BooleanField(default=False, help_text='If true, this location is associated with a service center', verbose_name='Is Service Location')),
            ],
            options={
                'verbose_name': 'Location Type',
                'verbose_name_plural': 'Location Types',
                'ordering': ['name'],
                'unique_together': {('tenant_id', 'code')},
            },
        ),
        migrations.CreateModel(
            name='BinLocation',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('name', models.CharField(max_length=100, verbose_name='Name')),
                ('code', models.CharField(max_length=50, verbose_name='Code')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('is_active', models.BooleanField(default=True, verbose_name='Active')),
                ('aisle', models.CharField(blank=True, help_text='Aisle identifier', max_length=20, verbose_name='Aisle')),
                ('rack', models.CharField(blank=True, help_text='Rack identifier', max_length=20, verbose_name='Rack')),
                ('shelf', models.CharField(blank=True, help_text='Shelf identifier', max_length=20, verbose_name='Shelf')),
                ('position', models.CharField(blank=True, help_text='Position identifier', max_length=20, verbose_name='Position')),
                ('barcode', models.CharField(blank=True, max_length=100, verbose_name='Barcode')),
                ('location', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='bins', to='warehouse.location', verbose_name='Location')),
            ],
            options={
                'verbose_name': 'Bin Location',
                'verbose_name_plural': 'Bin Locations',
                'ordering': ['location', 'aisle', 'rack', 'shelf', 'position'],
                'unique_together': {('tenant_id', 'location', 'code')},
            },
        ),
        migrations.AlterUniqueTogether(
            name='itemlocation',
            unique_together=set(),
        ),
        migrations.AddField(
            model_name='itemlocation',
            name='bin_location',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='items', to='warehouse.binlocation', verbose_name='Bin Location'),
        ),
        migrations.AddField(
            model_name='location',
            name='location_type',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='locations', to='warehouse.locationtype', verbose_name='Location Type'),
        ),
        migrations.AlterUniqueTogether(
            name='itemlocation',
            unique_together={('tenant_id', 'item', 'location', 'bin_location')},
        ),
    ]
