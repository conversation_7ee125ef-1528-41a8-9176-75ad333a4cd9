import csv
import json
import logging
import pandas as pd
import tempfile
from datetime import datetime, timedelta
from django.db import connection
from django.utils import timezone
from django.conf import settings
from django.http import HttpResponse
from django.db.models import Sum, Count, Avg, Min, Max, F, Q, Value
from django.db.models.functions import ExtractMonth, ExtractYear, TruncMonth, TruncDay

from inventory.models import Item, Movement
from warehouse.models import Location, Transfer, TransferOrder
from sales.models import SalesOrder, SalesOrderItem
from purchases.models import PurchaseOrder, PurchaseOrderItem
from .models import Report, ReportExecution, Dashboard, DashboardWidget

logger = logging.getLogger(__name__)


def execute_report(report_id, parameters=None, tenant_id=None, user=None):
    """
    Execute a report and return the results
    
    Args:
        report_id: The ID of the report to execute
        parameters: Optional parameters to override the default report parameters
        tenant_id: The tenant ID to use for filtering
        user: The user executing the report
        
    Returns:
        ReportExecution: The report execution record
    """
    try:
        report = Report.objects.get(id=report_id)
        
        # Create execution record
        execution = ReportExecution.objects.create(
            report=report,
            parameters=parameters or report.parameters,
            status='running',
            start_time=timezone.now(),
            tenant_id=tenant_id or report.tenant_id
        )
        
        # Merge parameters
        merged_params = {**report.parameters, **(parameters or {})}
        
        # Get report data based on report type
        try:
            if report.report_type == 'inventory':
                data = generate_inventory_report(merged_params, tenant_id)
            elif report.report_type == 'sales':
                data = generate_sales_report(merged_params, tenant_id)
            elif report.report_type == 'purchases':
                data = generate_purchases_report(merged_params, tenant_id)
            elif report.report_type == 'warehouse':
                data = generate_warehouse_report(merged_params, tenant_id)
            elif report.report_type == 'custom' and report.query:
                data = execute_custom_query(report.query, merged_params, tenant_id)
            else:
                raise ValueError(f"Unsupported report type: {report.report_type}")
                
            # Generate export file if needed
            if 'export_format' in merged_params:
                export_format = merged_params['export_format']
                file_path = export_report_data(data, report.name, export_format)
                execution.result_file = file_path
                
            # Update execution record
            execution.status = 'completed'
            execution.end_time = timezone.now()
            execution.save()
            
            return execution, data
            
        except Exception as e:
            logger.exception(f"Error executing report {report.name}: {e}")
            execution.status = 'failed'
            execution.error_message = str(e)
            execution.end_time = timezone.now()
            execution.save()
            raise
            
    except Report.DoesNotExist:
        logger.error(f"Report with ID {report_id} not found")
        raise ValueError(f"Report with ID {report_id} not found")


def generate_inventory_report(parameters, tenant_id):
    """
    Generate inventory report data
    
    Args:
        parameters: Report parameters
        tenant_id: Tenant ID for filtering
        
    Returns:
        dict: Report data
    """
    # Base queryset filtered by tenant
    queryset = Item.objects.filter(tenant_id=tenant_id)
    
    # Apply filters based on parameters
    if 'category' in parameters:
        queryset = queryset.filter(category=parameters['category'])
        
    if 'low_stock_only' in parameters and parameters['low_stock_only']:
        queryset = queryset.filter(quantity__lt=F('min_stock_level'))
        
    if 'search' in parameters:
        search = parameters['search']
        queryset = queryset.filter(
            Q(name__icontains=search) | 
            Q(sku__icontains=search) | 
            Q(description__icontains=search)
        )
    
    # Prepare report data
    items_data = []
    total_value = 0
    total_items = 0
    
    for item in queryset:
        item_value = item.quantity * item.unit_price
        total_value += item_value
        total_items += 1
        
        items_data.append({
            'id': str(item.id),
            'sku': item.sku,
            'name': item.name,
            'quantity': float(item.quantity),
            'unit_price': float(item.unit_price),
            'value': float(item_value),
            'min_stock_level': float(item.min_stock_level),
            'is_low_stock': item.is_low_stock,
        })
    
    # Add summary statistics
    movements = Movement.objects.filter(
        tenant_id=tenant_id,
        created_at__gte=timezone.now() - timedelta(days=30)
    )
    
    incoming = movements.filter(
        movement_type__in=['purchase', 'return', 'adjustment'],
    ).aggregate(total=Sum('quantity'))['total'] or 0
    
    outgoing = movements.filter(
        movement_type__in=['sale', 'transfer'],
    ).aggregate(total=Sum('quantity'))['total'] or 0
    
    return {
        'title': 'Inventory Report',
        'generated_at': timezone.now().isoformat(),
        'parameters': parameters,
        'items': items_data,
        'summary': {
            'total_items': total_items,
            'total_value': float(total_value),
            'avg_item_value': float(total_value / total_items) if total_items > 0 else 0,
            'low_stock_items': sum(1 for item in items_data if item['is_low_stock']),
            'incoming_30_days': float(incoming),
            'outgoing_30_days': float(outgoing),
        }
    }


def generate_sales_report(parameters, tenant_id):
    """
    Generate sales report data
    
    Args:
        parameters: Report parameters
        tenant_id: Tenant ID for filtering
        
    Returns:
        dict: Report data
    """
    # Base queryset filtered by tenant
    queryset = SalesOrder.objects.filter(tenant_id=tenant_id)
    
    # Apply date filters
    start_date = parameters.get('start_date')
    end_date = parameters.get('end_date')
    
    if start_date:
        start_date = datetime.fromisoformat(start_date)
        queryset = queryset.filter(created_at__gte=start_date)
        
    if end_date:
        end_date = datetime.fromisoformat(end_date)
        queryset = queryset.filter(created_at__lte=end_date)
    
    # Prepare report data
    sales_data = []
    total_revenue = 0
    total_profit = 0
    
    for sale in queryset:
        revenue = sale.total_amount
        cost = sale.total_cost if hasattr(sale, 'total_cost') else 0
        profit = revenue - cost
        
        total_revenue += revenue
        total_profit += profit
        
        sales_data.append({
            'id': str(sale.id),
            'date': sale.created_at.isoformat(),
            'customer': sale.customer.name if hasattr(sale, 'customer') else 'N/A',
            'items_count': sale.items.count(),
            'revenue': float(revenue),
            'cost': float(cost),
            'profit': float(profit),
        })
    
    # Generate time series data for chart
    if 'time_series' in parameters and parameters['time_series']:
        time_field = parameters.get('time_field', 'month')
        
        if time_field == 'day':
            time_data = queryset.annotate(
                date=TruncDay('created_at')
            ).values('date').annotate(
                revenue=Sum('total_amount')
            ).order_by('date')
            
            time_series = [
                {'date': item['date'].isoformat(), 'revenue': float(item['revenue'])}
                for item in time_data
            ]
        else:  # Default to monthly
            time_data = queryset.annotate(
                month=TruncMonth('created_at')
            ).values('month').annotate(
                revenue=Sum('total_amount')
            ).order_by('month')
            
            time_series = [
                {'date': item['month'].isoformat(), 'revenue': float(item['revenue'])}
                for item in time_data
            ]
    else:
        time_series = []
    
    return {
        'title': 'Sales Report',
        'generated_at': timezone.now().isoformat(),
        'parameters': parameters,
        'sales': sales_data,
        'time_series': time_series,
        'summary': {
            'total_sales': len(sales_data),
            'total_revenue': float(total_revenue),
            'total_profit': float(total_profit),
            'profit_margin': float(total_profit / total_revenue) * 100 if total_revenue > 0 else 0,
        }
    }


def generate_purchases_report(parameters, tenant_id):
    """
    Generate purchases report data
    
    Args:
        parameters: Report parameters
        tenant_id: Tenant ID for filtering
        
    Returns:
        dict: Report data
    """
    # Base queryset filtered by tenant
    queryset = PurchaseOrder.objects.filter(tenant_id=tenant_id)
    
    # Apply date filters
    start_date = parameters.get('start_date')
    end_date = parameters.get('end_date')
    
    if start_date:
        start_date = datetime.fromisoformat(start_date)
        queryset = queryset.filter(created_at__gte=start_date)
        
    if end_date:
        end_date = datetime.fromisoformat(end_date)
        queryset = queryset.filter(created_at__lte=end_date)
    
    # Prepare report data
    purchases_data = []
    total_cost = 0
    
    for purchase in queryset:
        cost = purchase.total_amount
        total_cost += cost
        
        purchases_data.append({
            'id': str(purchase.id),
            'date': purchase.created_at.isoformat(),
            'supplier': purchase.supplier.name if hasattr(purchase, 'supplier') else 'N/A',
            'items_count': purchase.items.count(),
            'cost': float(cost),
        })
    
    # Generate supplier data for chart
    if 'supplier_analysis' in parameters and parameters['supplier_analysis']:
        supplier_data = queryset.values(
            'supplier__name'
        ).annotate(
            total=Sum('total_amount'),
            count=Count('id')
        ).order_by('-total')[:10]  # Top 10 suppliers
        
        suppliers = [
            {
                'supplier': item['supplier__name'] or 'Unknown',
                'total': float(item['total']),
                'count': item['count']
            }
            for item in supplier_data
        ]
    else:
        suppliers = []
    
    return {
        'title': 'Purchases Report',
        'generated_at': timezone.now().isoformat(),
        'parameters': parameters,
        'purchases': purchases_data,
        'suppliers': suppliers,
        'summary': {
            'total_purchases': len(purchases_data),
            'total_cost': float(total_cost),
            'avg_purchase_cost': float(total_cost / len(purchases_data)) if purchases_data else 0,
        }
    }


def generate_warehouse_report(parameters, tenant_id):
    """
    Generate warehouse report data
    
    Args:
        parameters: Report parameters
        tenant_id: Tenant ID for filtering
        
    Returns:
        dict: Report data
    """
    # Base queryset filtered by tenant
    locations = Location.objects.filter(tenant_id=tenant_id)
    
    # Prepare report data
    locations_data = []
    total_items = 0
    total_value = 0
    
    for location in locations:
        # This is a simplified approach - in a real system you'd have a proper relation
        # between items and locations with quantities
        items_count = location.items.count() if hasattr(location, 'items') else 0
        location_value = 0
        
        # Calculate location value if possible
        if hasattr(location, 'items'):
            for item in location.items.all():
                item_value = item.quantity * item.unit_price
                location_value += item_value
        
        total_items += items_count
        total_value += location_value
        
        locations_data.append({
            'id': str(location.id),
            'name': location.name,
            'items_count': items_count,
            'value': float(location_value),
        })
    
    # Analyze transfers if requested
    if 'transfers_analysis' in parameters and parameters['transfers_analysis']:
        # Get transfers for the last 30 days
        end_date = timezone.now()
        start_date = end_date - timedelta(days=30)
        
        transfers = Transfer.objects.filter(
            tenant_id=tenant_id,
            created_at__gte=start_date,
            created_at__lte=end_date
        )
        
        transfers_data = transfers.values(
            'source_location__name',
            'destination_location__name'
        ).annotate(
            count=Count('id'),
            items=Sum('items_count')
        ).order_by('-count')[:20]  # Top 20 transfer routes
        
        transfers_summary = [
            {
                'source': item['source_location__name'] or 'Unknown',
                'destination': item['destination_location__name'] or 'Unknown',
                'count': item['count'],
                'items': item['items']
            }
            for item in transfers_data
        ]
    else:
        transfers_summary = []
    
    return {
        'title': 'Warehouse Report',
        'generated_at': timezone.now().isoformat(),
        'parameters': parameters,
        'locations': locations_data,
        'transfers': transfers_summary,
        'summary': {
            'total_locations': len(locations_data),
            'total_items': total_items,
            'total_value': float(total_value),
        }
    }


def execute_custom_query(query, parameters, tenant_id):
    """
    Execute a custom SQL query with proper tenant isolation
    
    Args:
        query: SQL query template with parameter placeholders
        parameters: Parameters to inject into the query
        tenant_id: Tenant ID for filtering
        
    Returns:
        list: Query results
    """
    # Ensure tenant isolation by adding tenant_id filter
    # This is a very simplified approach - in a real system you'd need
    # much more robust SQL injection protection and validation
    if 'tenant_id' not in parameters:
        parameters['tenant_id'] = tenant_id
    
    with connection.cursor() as cursor:
        try:
            cursor.execute(query, parameters)
            columns = [col[0] for col in cursor.description]
            data = [dict(zip(columns, row)) for row in cursor.fetchall()]
            return {
                'title': 'Custom Query Results',
                'generated_at': timezone.now().isoformat(),
                'columns': columns,
                'data': data,
                'summary': {
                    'rows_returned': len(data)
                }
            }
        except Exception as e:
            logger.exception(f"Error executing custom query: {e}")
            raise ValueError(f"Error executing custom query: {e}")


def export_report_data(data, report_name, export_format):
    """
    Export report data to a file
    
    Args:
        data: Report data
        report_name: Name of the report
        export_format: Format to export (csv, excel, json)
        
    Returns:
        str: Path to the exported file
    """
    timestamp = timezone.now().strftime('%Y%m%d_%H%M%S')
    safe_name = "".join(c if c.isalnum() else "_" for c in report_name)
    filename = f"{safe_name}_{timestamp}"
    
    if export_format == 'json':
        # Export to JSON
        file_path = f"reports/{filename}.json"
        with open(file_path, 'w') as f:
            json.dump(data, f, indent=2)
        return file_path
        
    elif export_format in ('csv', 'excel'):
        # Convert to DataFrame for CSV/Excel export
        if 'items' in data:
            df = pd.DataFrame(data['items'])
        elif 'sales' in data:
            df = pd.DataFrame(data['sales'])
        elif 'purchases' in data:
            df = pd.DataFrame(data['purchases'])
        elif 'locations' in data:
            df = pd.DataFrame(data['locations'])
        elif 'data' in data:
            df = pd.DataFrame(data['data'])
        else:
            # Convert the entire data to a dataframe
            df = pd.DataFrame([data])
        
        if export_format == 'csv':
            file_path = f"reports/{filename}.csv"
            df.to_csv(file_path, index=False)
            return file_path
        else:  # excel
            file_path = f"reports/{filename}.xlsx"
            df.to_excel(file_path, index=False)
            return file_path
    
    else:
        raise ValueError(f"Unsupported export format: {export_format}")


def get_dashboard_data(dashboard_id=None, widget_id=None, tenant_id=None):
    """
    Get data for a dashboard or a specific widget
    
    Args:
        dashboard_id: Dashboard ID (optional if widget_id is provided)
        widget_id: Widget ID (optional if dashboard_id is provided)
        tenant_id: Tenant ID for filtering
        
    Returns:
        dict: Dashboard data with widget data or single widget data
    """
    try:
        # If widget_id is provided, get data for a single widget
        if widget_id:
            widget = DashboardWidget.objects.get(id=widget_id)
            
            if tenant_id and widget.tenant_id != tenant_id:
                raise ValueError("Widget does not belong to the specified tenant")
            
            widget_data = {
                'id': str(widget.id),
                'title': widget.title,
                'type': widget.widget_type,
                'config': widget.config,
                'position': widget.position,
            }
            
            # Get data for widget based on associated report if any
            if widget.report:
                try:
                    # Execute report with widget-specific parameters
                    params = widget.config.get('parameters', {})
                    _, report_data = execute_report(
                        widget.report.id, 
                        parameters=params,
                        tenant_id=tenant_id or widget.tenant_id
                    )
                    
                    # Return just the report data
                    return report_data
                except Exception as e:
                    logger.exception(f"Error generating widget data: {e}")
                    widget_data['data'] = None
                    widget_data['error'] = str(e)
                    return widget_data
            else:
                # Widget doesn't have a report
                return {}
                
        # If dashboard_id is provided, get data for all widgets
        elif dashboard_id:
            dashboard = Dashboard.objects.get(id=dashboard_id)
            
            if tenant_id and dashboard.tenant_id != tenant_id:
                raise ValueError("Dashboard does not belong to the specified tenant")
            
            widgets_data = []
            
            for widget in dashboard.widgets.all().order_by('position'):
                widget_data = {
                    'id': str(widget.id),
                    'title': widget.title,
                    'type': widget.widget_type,
                    'config': widget.config,
                    'position': widget.position,
                }
                
                # Get data for widget based on associated report if any
                if widget.report:
                    try:
                        # Execute report with widget-specific parameters
                        params = widget.config.get('parameters', {})
                        _, report_data = execute_report(
                            widget.report.id, 
                            parameters=params,
                            tenant_id=tenant_id or dashboard.tenant_id
                        )
                        
                        # Add report data to widget
                        widget_data['data'] = report_data
                        widget_data['error'] = None
                    except Exception as e:
                        logger.exception(f"Error generating widget data: {e}")
                        widget_data['data'] = None
                        widget_data['error'] = str(e)
                
                widgets_data.append(widget_data)
            
            return {
                'id': str(dashboard.id),
                'name': dashboard.name,
                'description': dashboard.description,
                'layout': dashboard.layout,
                'widgets': widgets_data
            }
        else:
            raise ValueError("Either dashboard_id or widget_id must be provided")
    
    except Dashboard.DoesNotExist:
        logger.error(f"Dashboard with ID {dashboard_id} not found")
        raise ValueError(f"Dashboard with ID {dashboard_id} not found")
    except DashboardWidget.DoesNotExist:
        logger.error(f"Widget with ID {widget_id} not found")
        raise ValueError(f"Widget with ID {widget_id} not found") 