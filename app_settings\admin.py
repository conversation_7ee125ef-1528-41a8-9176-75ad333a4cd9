from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from app_settings.models import TenantSetting, SystemSetting, TenantProfile


@admin.register(TenantSetting)
class TenantSettingAdmin(admin.ModelAdmin):
    list_display = ('name', 'tenant_id', 'description', 'created_at')
    list_filter = ('name',)
    search_fields = ('name', 'description', 'tenant_id')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        (None, {
            'fields': ('tenant_id', 'name', 'description')
        }),
        (_('Value'), {
            'fields': ('value',),
            'classes': ('wide',)
        }),
        (_('Metadata'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(SystemSetting)
class SystemSettingAdmin(admin.ModelAdmin):
    list_display = ('name', 'description', 'created_at')
    search_fields = ('name', 'description')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        (None, {
            'fields': ('name', 'description')
        }),
        (_('Value'), {
            'fields': ('value',),
            'classes': ('wide',)
        }),
        (_('Metadata'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(TenantProfile)
class TenantProfileAdmin(admin.ModelAdmin):
    list_display = ('name', 'tenant_id', 'subscription_tier', 'contact_email')
    list_filter = ('subscription_tier',)
    search_fields = ('name', 'contact_email', 'contact_phone', 'address', 'website')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        (None, {
            'fields': ('tenant_id', 'name', 'logo', 'subscription_tier')
        }),
        (_('Contact Information'), {
            'fields': ('contact_email', 'contact_phone', 'address', 'website')
        }),
        (_('Metadata'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
