from django.urls import path
from . import views

app_name = 'reports'
 
urlpatterns = [
    # Basic reports (available without advanced reporting)
    path('', views.ReportListView.as_view(), name='report_list'),
    path('basic/inventory/', views.BasicInventoryReportView.as_view(), name='basic_inventory'),
    path('basic/sales/', views.BasicSalesReportView.as_view(), name='basic_sales'),
    path('basic/', views.BasicInventoryReportView.as_view(), name='basic_reports'),
    
    # Report management
    path('reports/<uuid:pk>/', views.ReportDetailView.as_view(), name='report_detail'),
    path('reports/<uuid:pk>/execute/', views.ExecuteReportView.as_view(), name='execute_report'),
    path('executions/<uuid:pk>/', views.ReportExecutionDetailView.as_view(), name='report_execution'),
    
    # Dashboards (requires advanced reporting feature)
    path('dashboards/', views.DashboardListView.as_view(), name='dashboard_list'),
    path('dashboards/<uuid:pk>/', views.DashboardDetailView.as_view(), name='dashboard_detail'),
    
    # API
    path('api/widgets/<uuid:pk>/data/', views.DashboardWidgetDataView.as_view(), name='widget_data'),
] 