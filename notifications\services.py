from django.utils.translation import gettext_lazy as _
from notifications.models import Notification
import json
import hmac
import hashlib
import requests
from datetime import datetime
import logging
from django.conf import settings
from .models import WebhookEndpoint, WebhookDelivery

logger = logging.getLogger(__name__)

def create_notification(tenant_id, title, message, level='info', object_type=None, object_id=None):
    """
    Create a new notification
    
    Args:
        tenant_id (UUID): The tenant ID
        title (str): The notification title
        message (str): The notification message
        level (str, optional): The notification level (info, success, warning, error)
        object_type (str, optional): The type of object this notification relates to
        object_id (str, optional): The ID of the object this notification relates to
        
    Returns:
        Notification: The created notification
    """
    return Notification.objects.create(
        tenant_id=tenant_id,
        title=title,
        message=message,
        level=level,
        object_type=object_type or '',
        object_id=str(object_id) if object_id else ''
    )


def get_unread_notifications(tenant_id, limit=None):
    """
    Get unread notifications for a tenant
    
    Args:
        tenant_id (UUID): The tenant ID
        limit (int, optional): Limit the number of notifications returned
        
    Returns:
        QuerySet: Unread notifications
    """
    notifications = Notification.objects.for_tenant(tenant_id).filter(is_read=False).order_by('-created_at')
    
    if limit:
        notifications = notifications[:limit]
        
    return notifications


def get_notification_count(tenant_id):
    """
    Get the count of unread notifications for a tenant
    
    Args:
        tenant_id (UUID): The tenant ID
        
    Returns:
        int: Count of unread notifications
    """
    return Notification.objects.for_tenant(tenant_id).filter(is_read=False).count()


def mark_all_as_read(tenant_id):
    """
    Mark all notifications as read for a tenant
    
    Args:
        tenant_id (UUID): The tenant ID
        
    Returns:
        int: Number of notifications marked as read
    """
    from django.utils import timezone
    
    return Notification.objects.for_tenant(tenant_id).filter(is_read=False).update(
        is_read=True,
        read_at=timezone.now()
    )


def generate_signature(payload, secret):
    """Generate HMAC signature for webhook payload"""
    if not secret:
        return None
    
    payload_str = json.dumps(payload)
    signature = hmac.new(
        key=secret.encode('utf-8'),
        msg=payload_str.encode('utf-8'),
        digestmod=hashlib.sha256
    ).hexdigest()
    
    return signature


def send_webhook(event_type, payload, tenant_id=None):
    """
    Send a webhook for the given event type and payload to all active endpoints
    that are subscribed to this event type.
    """
    # Get all active endpoints for this tenant that are subscribed to this event
    subscription_field = f'subscribe_{event_type}'
    
    filter_params = {'is_active': True}
    if tenant_id:
        filter_params['tenant_id'] = tenant_id
    
    # Get endpoints that are subscribed to this event type
    # This handles the case where the subscription field might not exist on the model
    endpoints = WebhookEndpoint.objects.filter(**filter_params)
    endpoints = [endpoint for endpoint in endpoints 
                if hasattr(endpoint, subscription_field) and getattr(endpoint, subscription_field)]
    
    if not endpoints:
        logger.info(f"No active endpoints found for event type: {event_type}")
        return []
    
    # Add metadata to payload
    enriched_payload = {
        "event_type": event_type,
        "timestamp": datetime.now().isoformat(),
        "data": payload
    }
    
    delivery_records = []
    
    # Send webhook to each endpoint
    for endpoint in endpoints:
        try:
            # Generate signature if secret is provided
            signature = generate_signature(enriched_payload, endpoint.secret_key)
            
            # Prepare headers
            headers = {
                'Content-Type': 'application/json',
            }
            if signature:
                headers['X-Webhook-Signature'] = signature
            
            # Send request
            response = requests.post(
                endpoint.url,
                json=enriched_payload,
                headers=headers,
                timeout=5  # 5 second timeout
            )
            
            # Record the delivery
            delivery = WebhookDelivery.objects.create(
                endpoint=endpoint,
                event_type=event_type,
                payload=enriched_payload,
                response_status=response.status_code,
                response_body=response.text[:1000],  # Limit response size
                is_success=response.ok,
                attempts=1,
                tenant_id=tenant_id or endpoint.tenant_id
            )
            
            delivery_records.append(delivery)
            
            if not response.ok:
                logger.warning(f"Webhook delivery failed: {endpoint.url}, status: {response.status_code}")
            
        except Exception as e:
            # Record failed delivery
            delivery = WebhookDelivery.objects.create(
                endpoint=endpoint,
                event_type=event_type,
                payload=enriched_payload,
                response_status=None,
                response_body=str(e)[:1000],
                is_success=False,
                attempts=1,
                tenant_id=tenant_id or endpoint.tenant_id
            )
            
            delivery_records.append(delivery)
            logger.exception(f"Error sending webhook to {endpoint.url}: {e}")
    
    return delivery_records


def retry_failed_webhooks(max_attempts=3):
    """
    Retry sending failed webhooks with exponential backoff
    """
    # Get failed deliveries that haven't reached max attempts
    failed_deliveries = WebhookDelivery.objects.filter(
        is_success=False,
        attempts__lt=max_attempts
    )
    
    if not failed_deliveries:
        return 0
    
    retry_count = 0
    
    for delivery in failed_deliveries:
        try:
            # Generate signature if needed
            signature = None
            if delivery.endpoint.secret_key:
                signature = generate_signature(delivery.payload, delivery.endpoint.secret_key)
            
            # Prepare headers
            headers = {
                'Content-Type': 'application/json',
            }
            if signature:
                headers['X-Webhook-Signature'] = signature
            
            # Send request
            response = requests.post(
                delivery.endpoint.url,
                json=delivery.payload,
                headers=headers,
                timeout=5  # 5 second timeout
            )
            
            # Update delivery record
            delivery.attempts += 1
            delivery.response_status = response.status_code
            delivery.response_body = response.text[:1000]
            delivery.is_success = response.ok
            delivery.save()
            
            if response.ok:
                retry_count += 1
            
        except Exception as e:
            # Update delivery record with error
            delivery.attempts += 1
            delivery.response_body = str(e)[:1000]
            delivery.save()
            
            logger.exception(f"Error retrying webhook {delivery.id}: {e}")
    
    return retry_count 