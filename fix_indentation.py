with open('work_orders/views.py', 'r') as f:
    content = f.read()

# Fix the first indentation issue with print statement
fixed_content = content.replace('                \n                        print(f"Found {len(filtered_schedules)} maintenance schedules")', '                \n            print(f"Found {len(filtered_schedules)} maintenance schedules")')

# Fix the second indentation issue with break statement
fixed_content = fixed_content.replace('                \n                                                        # We found a recommended schedule', '                \n                            # We found a recommended schedule')

with open('work_orders/views.py', 'w') as f:
    f.write(fixed_content)

print('File has been fixed.')