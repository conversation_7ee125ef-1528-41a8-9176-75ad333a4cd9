{% extends 'core/templates/dashboard_base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "لوحة تحكم المستودع" %}{% endblock %}

{% block content %}
<div class="container mx-auto">
    <!-- Page Header -->
    <div class="mb-6">
        <h1 class="text-2xl font-bold text-gray-800">{% trans "لوحة تحكم المستودع" %}</h1>
        <p class="text-gray-600">{% trans "إدارة المستودعات والمواقع والمخزون" %}</p>
    </div>

    <!-- Quick Stats Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <!-- Locations Card -->
        <div class="bg-white rounded-lg shadow p-4">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100 text-blue-500">
                    <i class="fas fa-warehouse text-xl"></i>
                </div>
                <div class="{% if LANGUAGE_CODE == 'ar' %}mr-4{% else %}ml-4{% endif %}">
                    <p class="text-sm text-gray-500">{% trans "المستودعات" %}</p>
                    <p class="text-lg font-semibold">{{ locations_count|default:"0" }}</p>
                </div>
            </div>
        </div>

        <!-- Bin Locations Card -->
        <div class="bg-white rounded-lg shadow p-4">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100 text-green-500">
                    <i class="fas fa-boxes text-xl"></i>
                </div>
                <div class="{% if LANGUAGE_CODE == 'ar' %}mr-4{% else %}ml-4{% endif %}">
                    <p class="text-sm text-gray-500">{% trans "المواقع" %}</p>
                    <p class="text-lg font-semibold">{{ bin_locations_count|default:"0" }}</p>
                </div>
            </div>
        </div>

        <!-- Transfers Card -->
        <div class="bg-white rounded-lg shadow p-4">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-amber-100 text-amber-500">
                    <i class="fas fa-exchange-alt text-xl"></i>
                </div>
                <div class="{% if LANGUAGE_CODE == 'ar' %}mr-4{% else %}ml-4{% endif %}">
                    <p class="text-sm text-gray-500">{% trans "التحويلات" %}</p>
                    <p class="text-lg font-semibold">{{ transfers_count|default:"0" }}</p>
                </div>
            </div>
        </div>

        <!-- Storage Utilization Card -->
        <div class="bg-white rounded-lg shadow p-4">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-purple-100 text-purple-500">
                    <i class="fas fa-chart-pie text-xl"></i>
                </div>
                <div class="{% if LANGUAGE_CODE == 'ar' %}mr-4{% else %}ml-4{% endif %}">
                    <p class="text-sm text-gray-500">{% trans "نسبة الإشغال" %}</p>
                    <p class="text-lg font-semibold">{{ storage_utilization|default:"0" }}%</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Sections -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Locations Section -->
        <div class="bg-white rounded-lg shadow overflow-hidden lg:col-span-2">
            <div class="p-4 bg-gray-50 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h2 class="text-lg font-semibold text-gray-700">{% trans "المستودعات والمواقع" %}</h2>
                    <a href="#" class="text-blue-500 hover:text-blue-700 text-sm font-medium">
                        {% trans "عرض الكل" %} <i class="fas fa-arrow-circle-right {% if LANGUAGE_CODE == 'ar' %}mr-1{% else %}ml-1{% endif %}"></i>
                    </a>
                </div>
            </div>
            <div class="p-4">
                {% if locations %}
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">{% trans "الاسم" %}</th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">{% trans "النوع" %}</th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">{% trans "المدينة" %}</th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">{% trans "الحالة" %}</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for location in locations %}
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="font-medium text-gray-900">{{ location.name }}</div>
                                    <div class="text-sm text-gray-500">{{ location.code }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    {% if location.location_type %}
                                    <span class="text-sm text-gray-900">{{ location.location_type.name }}</span>
                                    {% else %}
                                    <span class="text-sm text-gray-400">{% trans "غير محدد" %}</span>
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ location.city|default:"-" }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    {% if location.is_active %}
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">{% trans "نشط" %}</span>
                                    {% else %}
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">{% trans "غير نشط" %}</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-8">
                    <i class="fas fa-warehouse text-gray-300 text-5xl mb-4"></i>
                    <p class="text-gray-500">{% trans "لم يتم العثور على مستودعات" %}</p>
                    <a href="#" class="mt-2 inline-flex items-center text-sm text-blue-600 hover:text-blue-800">
                        <i class="fas fa-plus-circle {% if LANGUAGE_CODE == 'ar' %}ml-1{% else %}mr-1{% endif %}"></i> {% trans "إضافة مستودع جديد" %}
                    </a>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Transfers Section -->
        <div class="bg-white rounded-lg shadow overflow-hidden">
            <div class="p-4 bg-gray-50 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h2 class="text-lg font-semibold text-gray-700">{% trans "أحدث التحويلات" %}</h2>
                    <a href="#" class="text-blue-500 hover:text-blue-700 text-sm font-medium">
                        {% trans "عرض الكل" %} <i class="fas fa-arrow-circle-right {% if LANGUAGE_CODE == 'ar' %}mr-1{% else %}ml-1{% endif %}"></i>
                    </a>
                </div>
            </div>
            <div class="p-4">
                {% if transfers %}
                <div class="space-y-4">
                    {% for transfer in transfers %}
                    <div class="border border-gray-200 rounded-lg p-3">
                        <div class="flex justify-between items-start mb-2">
                            <div class="text-sm font-medium text-gray-900">{{ transfer.reference|default:transfer.id }}</div>
                            <div class="text-xs text-gray-500">{{ transfer.created_at|date:"d/m/Y" }}</div>
                        </div>
                        <div class="flex items-center text-sm text-gray-700 mb-2">
                            <div>{{ transfer.source_location.name }}</div>
                            <i class="fas fa-arrow-right mx-2 text-blue-500"></i>
                            <div>{{ transfer.destination_location.name }}</div>
                        </div>
                        <div class="text-xs text-gray-500">
                            <span class="font-medium">{% trans "الأصناف:" %}</span> {{ transfer.items_count }}
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-8">
                    <i class="fas fa-exchange-alt text-gray-300 text-5xl mb-4"></i>
                    <p class="text-gray-500">{% trans "لا توجد تحويلات حديثة" %}</p>
                    <a href="#" class="mt-2 inline-flex items-center text-sm text-blue-600 hover:text-blue-800">
                        <i class="fas fa-plus-circle {% if LANGUAGE_CODE == 'ar' %}ml-1{% else %}mr-1{% endif %}"></i> {% trans "إنشاء تحويل جديد" %}
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Additional Sections -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
        <!-- Low Stock Items -->
        <div class="bg-white rounded-lg shadow overflow-hidden">
            <div class="p-4 bg-gray-50 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h2 class="text-lg font-semibold text-gray-700">{% trans "الأصناف منخفضة المخزون" %}</h2>
                    <a href="#" class="text-blue-500 hover:text-blue-700 text-sm font-medium">
                        {% trans "عرض الكل" %} <i class="fas fa-arrow-circle-right {% if LANGUAGE_CODE == 'ar' %}mr-1{% else %}ml-1{% endif %}"></i>
                    </a>
                </div>
            </div>
            <div class="p-4">
                {% if low_stock_items %}
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">{% trans "الصنف" %}</th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">{% trans "الموقع" %}</th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">{% trans "الكمية" %}</th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">{% trans "الحد الأدنى" %}</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for item_location in low_stock_items %}
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="font-medium text-gray-900">{{ item_location.item.name }}</div>
                                    <div class="text-sm text-gray-500">{{ item_location.item.sku }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ item_location.location.name }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-red-600">
                                    {{ item_location.quantity }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ item_location.min_stock }}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-8">
                    <i class="fas fa-check-circle text-green-500 text-5xl mb-4"></i>
                    <p class="text-gray-500">{% trans "لا توجد أصناف منخفضة المخزون" %}</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Recent Activities -->
        <div class="bg-white rounded-lg shadow overflow-hidden">
            <div class="p-4 bg-gray-50 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-700">{% trans "النشاطات الأخيرة" %}</h2>
            </div>
            <div class="p-4">
                {% if warehouse_activities %}
                <div class="space-y-4">
                    {% for activity in warehouse_activities %}
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center">
                                <i class="fas fa-box text-blue-500"></i>
                            </div>
                        </div>
                        <div class="{% if LANGUAGE_CODE == 'ar' %}mr-3{% else %}ml-3{% endif %} flex-1">
                            <p class="text-sm text-gray-800">{{ activity.description }}</p>
                            <p class="text-xs text-gray-500">{{ activity.timestamp|date:"d/m/Y H:i" }}</p>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-8">
                    <i class="fas fa-history text-gray-300 text-5xl mb-4"></i>
                    <p class="text-gray-500">{% trans "لا توجد نشاطات حديثة" %}</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %} 