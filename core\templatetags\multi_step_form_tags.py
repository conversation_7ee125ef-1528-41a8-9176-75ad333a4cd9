"""
Template tags for multi-step form navigation.
"""
from django import template
from django.utils.translation import gettext_lazy as _

register = template.Library()

@register.inclusion_tag('components/multi_step_nav.html')
def render_multi_step_nav(steps, current_step, step_param=None, base_url=None):
    """
    Render the multi-step navigation component.
    
    Args:
        steps (list): List of step dictionaries with keys: name, label, icon (optional)
        current_step (str): The name of the current active step
        step_param (str, optional): The URL parameter name for the step
        base_url (str, optional): The base URL for step links
        
    Returns:
        dict: Context for the template
    """
    # Find the index of the current step
    current_step_index = 0
    for i, step in enumerate(steps):
        if step['name'] == current_step:
            current_step_index = i
            break
    
    return {
        'steps': steps,
        'current_step': current_step,
        'current_step_index': current_step_index,
        'step_param': step_param,
        'base_url': base_url
    }

@register.simple_tag
def work_order_steps(current_step=None):
    """
    Get the standard work order steps.
    
    Args:
        current_step (str, optional): The current active step
        
    Returns:
        dict: Steps context for multi_step_nav
    """
    steps = [
        {
            'name': 'customer',
            'label': _('Customer'),
            'icon': 'fas fa-user'
        },
        {
            'name': 'vehicle',
            'label': _('Vehicle'),
            'icon': 'fas fa-car'
        },
        {
            'name': 'details',
            'label': _('Work Order Details'),
            'icon': 'fas fa-clipboard-list'
        },
        {
            'name': 'operations',
            'label': _('Operations'),
            'icon': 'fas fa-tools'
        },
        {
            'name': 'parts',
            'label': _('Parts'),
            'icon': 'fas fa-cogs'
        },
        {
            'name': 'summary',
            'label': _('Summary'),
            'icon': 'fas fa-check-circle'
        }
    ]
    
    return {
        'steps': steps,
        'current_step': current_step
    } 