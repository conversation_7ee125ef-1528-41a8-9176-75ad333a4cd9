from django.urls import path
from . import views

app_name = 'sales'
 
urlpatterns = [
    # Dashboard
    path('', views.SalesDashboardView.as_view(), name='dashboard'),
    
    # Sales Orders
    path('orders/', views.SalesOrderListView.as_view(), name='sales_order_list'),
    path('orders/<uuid:pk>/', views.SalesOrderDetailView.as_view(), name='sales_order_detail'),
    path('orders/create/', views.SalesOrderCreateView.as_view(), name='sales_order_create'),
    path('orders/<uuid:pk>/update/', views.SalesOrderUpdateView.as_view(), name='sales_order_update'),
    
    # Customers
    path('customers/', views.CustomerListView.as_view(), name='customer_list'),
    path('customers/<uuid:pk>/', views.CustomerDetailView.as_view(), name='customer_detail'),
    path('customers/create/', views.CustomerCreateView.as_view(), name='customer_create'),
    path('customers/<uuid:pk>/update/', views.CustomerUpdateView.as_view(), name='customer_update'),
] 