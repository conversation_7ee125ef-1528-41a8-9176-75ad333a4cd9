from django.shortcuts import render
from django.urls import reverse
from django.views.generic import TemplateView
from django.utils.translation import gettext_lazy as _
from django.contrib.auth.mixins import LoginRequiredMixin
from django.db.models import F, Q
from feature_flags.middleware import requires_module

from inventory.models import Item, Movement, UnitOfMeasurement, OperationCompatibility

class CustomDashboardView(LoginRequiredMixin, TemplateView):
    """
    A custom dashboard view that doesn't rely on includes
    """
    template_name = 'inventory/custom_dashboard.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        tenant_id = getattr(self.request, 'tenant_id', None)
        queryset = Item.objects.all()
        
        if tenant_id:
            queryset = queryset.filter(tenant_id=tenant_id)
        
        # Get basic stats
        context['total_items'] = queryset.count()
        try:
            context['low_stock_count'] = queryset.filter(
                quantity__lt=F('min_stock_level')
            ).count()
        except:
            context['low_stock_count'] = 0
        
        # Recent items
        context['recent_items'] = queryset.order_by('-created_at')[:5]
        
        # Add low stock items for alerts section
        try:
            context['low_stock_items'] = queryset.filter(
                quantity__lt=F('min_stock_level')
            ).order_by('quantity')[:5]
        except:
            context['low_stock_items'] = []
        
        # Operation compatibilities for common operations
        compat_queryset = OperationCompatibility.objects.all()
        if tenant_id:
            compat_queryset = compat_queryset.filter(tenant_id=tenant_id)
        
        # Filter to show common operations first, then required ones
        try:
            context['operation_compatibilities'] = compat_queryset.filter(
                Q(is_common=True) | Q(is_required=True)
            ).select_related('item', 'operation_type', 'item__unit_of_measurement').order_by('-is_common', '-is_required')[:10]
        except:
            context['operation_compatibilities'] = []
        
        # Movement stats if available
        movement_queryset = Movement.objects.all()
        if tenant_id:
            movement_queryset = movement_queryset.filter(tenant_id=tenant_id)
        
        context['total_movements'] = movement_queryset.count()
        context['recent_movements'] = movement_queryset.order_by('-created_at')[:5]
        
        # Units of measurement stats
        unit_queryset = UnitOfMeasurement.objects.all()
        if tenant_id:
            unit_queryset = unit_queryset.filter(tenant_id=tenant_id)
        
        context['total_units'] = unit_queryset.count()
        context['recent_units'] = unit_queryset.order_by('-created_at')[:5]
        
        # Add URLs for the template
        context['inventory_item_list_url'] = reverse('inventory:item_list')
        context['inventory_low_stock_url'] = reverse('inventory:item_list') + '?low_stock=1'
        context['inventory_movement_list_url'] = reverse('inventory:movement_list')
        context['inventory_item_create_url'] = reverse('inventory:item_create')
        context['inventory_movement_create_url'] = reverse('inventory:movement_create')
        context['inventory_scan_barcode_url'] = reverse('inventory:scan_barcode')
        
        context['title'] = _('Inventory Dashboard')
        return context 