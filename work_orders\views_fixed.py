from django.shortcuts import render, redirect, get_object_or_404
from django.urls import reverse_lazy, reverse
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView, FormView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib.auth.decorators import login_required
from django.utils.translation import gettext_lazy as _
from django.contrib import messages
from django.http import JsonResponse
from django.db.models import Q
from django.utils import timezone
from django.core.exceptions import ValidationError
import json
from django import forms
from django.utils.http import urlencode
from django.db import models
from django.db.models import Q, F, Count, Sum, Case, When, Value, IntegerField, CharField, OuterRef, Subquery, Prefetch
from datetime import datetime, timedelta
import uuid
import random
import string

from .models import (
    WorkOrder, WorkOrderType, BillOfMaterials, WorkOrderOperation, 
    WorkOrderMaterial, MaintenanceSchedule, ScheduleOperation
)
from inventory.models import Item
from setup.models import ServiceCenter, Vehicle, Customer, Franchise, Company, VehicleMake, VehicleModel

@login_required
def api_get_spare_parts(request):
    """API endpoint to fetch spare parts based on operation and vehicle model.
    Includes warehouse availability information, pricing information, and transfer options.
    
    GET parameters:
        operation_id: ID of the operation type
        vehicle_id: ID of the vehicle (optional)
        service_center_id: ID of the service center (optional)
        franchise_id: ID of the franchise (optional)
        company_id: ID of the company (optional)
    """
    tenant_id = getattr(request, 'tenant_id', None)
    
    try:
        # Get parameters
        operation_id = request.GET.get('operation_id')
        vehicle_id = request.GET.get('vehicle_id')
        service_center_id = request.GET.get('service_center_id')
        company_id = request.GET.get('company_id')
        franchise_id = request.GET.get('franchise_id')
        
        # Debug logging
        print(f"API: get_spare_parts called with: operation_id={operation_id}, vehicle_id={vehicle_id}, service_center_id={service_center_id}")
        
        if not operation_id:
            return JsonResponse({
                'success': False,
                'error': 'Operation ID is required'
            })
        
        # Import models
        from setup.models import Vehicle, VehicleMake, VehicleModel, Franchise, Company, ServiceCenter
        from inventory.models import OperationPricing, PartPricing, Item
        from warehouse.models import ItemLocation, Location
        
        # Get operation type - using safe UUID handling
        operation_type = None
        from work_orders.models import WorkOrderType, ScheduleOperation, WorkOrderOperation
        
        try:
            # Try to get as WorkOrderType first - using direct ORM lookup without UUID conversion
            try:
                operation_type = WorkOrderType.objects.get(pk=operation_id)
                print(f"Found operation type by ID: {operation_type.name}")
            except WorkOrderType.DoesNotExist:
                # Try as ScheduleOperation
                try:
                    schedule_operation = ScheduleOperation.objects.get(pk=operation_id)
                    operation_type = schedule_operation.operation_type
                    print(f"Found operation type from schedule operation: {operation_type.name}")
                except ScheduleOperation.DoesNotExist:
                    # Try as WorkOrderOperation
                    try:
                        work_order_operation = WorkOrderOperation.objects.get(pk=operation_id)
                        if hasattr(work_order_operation, 'work_order') and work_order_operation.work_order and work_order_operation.work_order.work_order_type:
                            operation_type = work_order_operation.work_order.work_order_type
                            print(f"Found operation type from work order operation: {operation_type.name}")
                    except WorkOrderOperation.DoesNotExist:
                        pass
            
            if not operation_type:
                return JsonResponse({
                    'success': False,
                    'error': 'Operation not found'
                })
                
        except Exception as e:
            print(f"Error getting operation type: {str(e)}")
            return JsonResponse({
                'success': False,
                'error': f'Error processing operation: {str(e)}'
            })
        
        # Get vehicle if specified - using safe UUID handling
        vehicle = None
        vehicle_make = None
        vehicle_model = None
        if vehicle_id:
            try:
                # Try to get the vehicle object directly
                try:
                    vehicle = Vehicle.objects.get(pk=vehicle_id)
                    vehicle_make = vehicle.make
                    vehicle_model = vehicle.model
                    print(f"Found vehicle: {vehicle_make} {vehicle_model}")
                except Vehicle.DoesNotExist:
                    print(f"Vehicle with ID {vehicle_id} not found")
            except Exception as e:
                print(f"Error getting vehicle: {str(e)}")
                # Continue with other operations
        
        # Get service center if specified
        service_center = None
        if service_center_id:
            try:
                service_center = ServiceCenter.objects.get(pk=service_center_id)
                print(f"Found service center: {service_center.name}")
                if not company_id and service_center.company:
                    company_id = service_center.company.id
                    if not franchise_id and service_center.company.franchise:
                        franchise_id = service_center.company.franchise.id
            except ServiceCenter.DoesNotExist:
                print(f"Service center with ID {service_center_id} not found")
        
        # Get company if specified
        company = None
        if company_id:
            try:
                company = Company.objects.get(pk=company_id)
                print(f"Found company: {company.name}")
                if not franchise_id and company.franchise:
                    franchise_id = company.franchise.id
            except Company.DoesNotExist:
                print(f"Company with ID {company_id} not found")
        
        # Get franchise if specified
        franchise = None
        if franchise_id:
            try:
                franchise = Franchise.objects.get(pk=franchise_id)
                print(f"Found franchise: {franchise.name}")
            except Franchise.DoesNotExist:
                print(f"Franchise with ID {franchise_id} not found")
        
        # Find compatible parts
        parts_data = []
        
        # Get IDs of compatible parts - using Django ORM instead of direct SQL
        compatible_parts = []
        try:
            from inventory.models import OperationCompatibility
            
            # Get operation compatibility entries for this operation type
            op_compatibilities = OperationCompatibility.objects.filter(
                operation_type=operation_type,
                tenant_id=tenant_id
            )
            
            for op_compat in op_compatibilities:
                if op_compat.item:
                    compatible_parts.append(op_compat.item)
        except Exception as e:
            print(f"Error getting compatible parts through ORM: {str(e)}")
        
        # Filter by vehicle make/model if available
        if vehicle_make or vehicle_model:
            filtered_parts = []
            for part in compatible_parts:
                # Check if this part has vehicle compatibility that matches
                is_compatible = False
                for compat in part.vehicle_compatibilities.all():
                    if (not vehicle_make or compat.make.lower() == vehicle_make.lower() or not compat.make) and \
                       (not vehicle_model or compat.model.lower() == vehicle_model.lower() or not compat.model):
                        is_compatible = True
                        break
                if is_compatible:
                    filtered_parts.append(part)
            compatible_parts = filtered_parts
        
        print(f"Found {len(compatible_parts)} compatible parts")
        
        # Return empty list if no compatible parts found
        if not compatible_parts:
            return JsonResponse({
                'success': True,
                'parts': []
            })
            
        # For each part, check availability in warehouses
        for part in compatible_parts:
            # Default values
            recommended_quantity = 1
            part_price = part.unit_price
            price_source = 'standard'
            is_special_price = False
            special_valid_until = None
            
            # Add part data with minimal info if needed
            parts_data.append({
                'id': str(part.id),
                'name': part.name,
                'sku': part.sku,
                'standard_price': float(part.unit_price),
                'price': float(part_price),
                'price_source': price_source,
                'is_special_price': is_special_price,
                'unit_of_measurement': part.unit_of_measurement.symbol if part.unit_of_measurement else '',
                'recommended_quantity': float(recommended_quantity),
                'total_available': 0,
                'warehouse_availability': [],
                'is_required': False,
                'is_low_stock': False,
                'needs_transfer': False
            })
        
        print(f"Returning {len(parts_data)} parts")
        
        # Return the results
        return JsonResponse({
            'success': True,
            'parts': parts_data
        })
        
    except Exception as e:
        # Log the error
        import traceback
        error_traceback = traceback.format_exc()
        print(f"ERROR in api_get_spare_parts: {str(e)}")
        print(f"Request parameters: operation_id={request.GET.get('operation_id')}, vehicle_id={request.GET.get('vehicle_id')}, service_center_id={request.GET.get('service_center_id')}")
        print(f"Traceback: {error_traceback}")
        
        return JsonResponse({
            'success': False,
            'error': str(e),
            'message': "An error occurred while getting spare parts. Please try again or contact support if the problem persists."
        }) 