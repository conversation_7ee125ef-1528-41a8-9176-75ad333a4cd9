{% load i18n %}

<div class="bg-blue-50 p-3 rounded-lg">
    <div class="flex justify-between items-start">
        <div>
            <h6 class="font-semibold text-blue-800">{{ vehicle.make }} {{ vehicle.model }} {% if vehicle.year %}({{ vehicle.year }}){% endif %}</h6>
            <div class="mt-1">
                {% if vehicle.license_plate %}
                    <div class="text-sm text-gray-700">
                        <i class="fas fa-id-card text-blue-600 mr-2"></i> {% trans "License" %}: {{ vehicle.license_plate }}
                    </div>
                {% endif %}
                
                {% if vehicle.vin %}
                    <div class="text-sm text-gray-700">
                        <i class="fas fa-barcode text-blue-600 mr-2"></i> {% trans "VIN" %}: {{ vehicle.vin }}
                    </div>
                {% endif %}
                
                {% if vehicle.color %}
                    <div class="text-sm text-gray-700">
                        <i class="fas fa-palette text-blue-600 mr-2"></i> {{ vehicle.color }}
                    </div>
                {% endif %}
                
                {% if vehicle.owner %}
                    <div class="text-sm text-gray-700">
                        <i class="fas fa-user text-blue-600 mr-2"></i> {% trans "Owner" %}: {{ vehicle.owner.get_full_name }}
                    </div>
                {% endif %}
            </div>
        </div>
        
        <div>
            <button type="button" class="text-red-600 hover:text-red-800 text-sm"
                    onclick="clearVehicleSelection()">
                <i class="fas fa-times"></i> {% trans "Clear" %}
            </button>
        </div>
    </div>
    
    <input type="hidden" name="vehicle" value="{{ vehicle.id }}">
</div> 