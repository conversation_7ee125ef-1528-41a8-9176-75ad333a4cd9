/**
 * Flowbite.js
 * Library for interactive components in Tailwind CSS like dropdowns, modals, navbars, etc.
 * Minified version for use in the After-Sales Franchise Management System
 * 
 * This file would normally be downloaded from the Flowbite website or npm package.
 */

/**
 * Simplified Flowbite implementation for Aftersails
 * This provides just enough to make the scripts.js work
 */

// Create a global Flowbite object
window.Flowbite = {
    // Simple tooltip implementation
    Tooltip: function(element) {
        this.element = element;
        return this;
    },
    
    // Simple dropdown implementation
    Dropdown: function(element) {
        this.element = element;
        return this;
    }
};

// Let the console know we've loaded our custom implementation
console.log("Flowbite custom implementation loaded");

// For development purposes, we're using a simplified version
// that loads the actual library from CDN
(function(){
  var script = document.createElement('script');
  script.src = 'https://cdnjs.cloudflare.com/ajax/libs/flowbite/1.8.1/flowbite.min.js';
  script.crossOrigin = 'anonymous';
  script.referrerPolicy = 'no-referrer';
  document.head.appendChild(script);
})(); 