# PostgreSQL Database Configuration for Migration
# Copy this file to .env and modify the values as needed

# Database Engine (don't change this)
SQL_ENGINE=django.db.backends.postgresql

# PostgreSQL Connection Details
SQL_DATABASE=postgres
SQL_USER=postgres
SQL_PASSWORD=postgrespw
SQL_HOST=**************
SQL_PORT=8136

# Django Settings
DEBUG=False
SECRET_KEY=your-secret-key-here

# Optional: Email configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password

# Optional: Other settings
LANGUAGE_CODE=ar
TIME_ZONE=Africa/Cairo 