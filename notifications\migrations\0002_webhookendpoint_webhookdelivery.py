# Generated by Django 4.2.20 on 2025-05-07 11:15

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('notifications', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='WebhookEndpoint',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('name', models.CharField(max_length=100, verbose_name='Name')),
                ('url', models.URLField(max_length=500, verbose_name='URL')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('is_active', models.BooleanField(default=True, verbose_name='Active')),
                ('secret_key', models.CharField(blank=True, help_text='Used for webhook signature validation', max_length=255, verbose_name='Secret Key')),
                ('subscribe_item_created', models.BooleanField(default=True, verbose_name='Item Created')),
                ('subscribe_item_updated', models.BooleanField(default=True, verbose_name='Item Updated')),
                ('subscribe_stock_low', models.BooleanField(default=True, verbose_name='Stock Low')),
                ('subscribe_movement_created', models.BooleanField(default=True, verbose_name='Movement Created')),
            ],
            options={
                'verbose_name': 'Webhook Endpoint',
                'verbose_name_plural': 'Webhook Endpoints',
            },
        ),
        migrations.CreateModel(
            name='WebhookDelivery',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated at')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('tenant_id', models.UUIDField(db_index=True, verbose_name='Tenant ID')),
                ('event_type', models.CharField(max_length=100, verbose_name='Event Type')),
                ('payload', models.JSONField(verbose_name='Payload')),
                ('response_status', models.IntegerField(blank=True, null=True, verbose_name='Response Status')),
                ('response_body', models.TextField(blank=True, verbose_name='Response Body')),
                ('is_success', models.BooleanField(default=False, verbose_name='Success')),
                ('attempts', models.IntegerField(default=0, verbose_name='Attempts')),
                ('endpoint', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='deliveries', to='notifications.webhookendpoint', verbose_name='Endpoint')),
            ],
            options={
                'verbose_name': 'Webhook Delivery',
                'verbose_name_plural': 'Webhook Deliveries',
                'ordering': ['-created_at'],
            },
        ),
    ]
