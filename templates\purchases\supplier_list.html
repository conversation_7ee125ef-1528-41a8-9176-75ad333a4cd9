{% extends "dashboard_base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "الموردين" %}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="mb-6 flex flex-wrap items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-800">{% trans "الموردين" %}</h1>
            <p class="text-gray-600">{% trans "إدارة وعرض بيانات الموردين" %}</p>
        </div>
        <div class="mt-4 md:mt-0">
            <a href="{% url 'purchases:supplier_create' %}" class="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg inline-flex items-center">
                <i class="fas fa-user-plus {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i>
                {% trans "مورد جديد" %}
            </a>
        </div>
    </div>
    
    {% if missing_tenant %}
        <div class="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-6" role="alert">
            <p class="font-bold">{% trans "Tenant ID Missing" %}</p>
            <p>{% trans "No tenant ID was found. Please make sure your X-Tenant-ID header is set correctly." %}</p>
        </div>
    {% endif %}
    
    <!-- Search Form -->
    <div class="bg-white shadow rounded-lg mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-800">{% trans "بحث" %}</h2>
        </div>
        <div class="p-6">
            <form method="get" class="flex flex-wrap gap-4">
                <div class="w-full md:w-3/4">
                    <label for="search" class="block text-sm font-medium text-gray-700 mb-1">{% trans "اسم المورد" %}</label>
                    <input type="text" name="search" id="search" value="{{ current_search }}" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md" placeholder="{% trans 'بحث باسم المورد' %}">
                </div>
                <div class="w-full md:w-1/4 flex items-end">
                    <button type="submit" class="w-full bg-blue-100 hover:bg-blue-200 text-blue-700 py-2 px-4 rounded-md flex justify-center items-center">
                        <i class="fas fa-search {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i>
                        {% trans "بحث" %}
                    </button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Suppliers Grid -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-800">{% trans "قائمة الموردين" %}</h2>
        </div>
        
        {% if suppliers %}
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6">
                {% for supplier in suppliers %}
                    <div class="bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow">
                        <div class="p-5 border-b border-gray-200">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 bg-blue-100 rounded-full p-3">
                                    <i class="fas fa-truck text-blue-600"></i>
                                </div>
                                <div class="{% if LANGUAGE_CODE == 'ar' %}mr-3{% else %}ml-3{% endif %}">
                                    <h3 class="text-lg leading-6 font-medium text-gray-900">{{ supplier.name }}</h3>
                                    {% if supplier.contact_person %}
                                        <p class="text-sm text-gray-500">{{ supplier.contact_person }}</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="px-5 py-3">
                            {% if supplier.phone %}
                                <div class="flex items-center text-sm mb-2">
                                    <i class="fas fa-phone text-gray-400 {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i>
                                    <span>{{ supplier.phone }}</span>
                                </div>
                            {% endif %}
                            {% if supplier.email %}
                                <div class="flex items-center text-sm mb-2">
                                    <i class="fas fa-envelope text-gray-400 {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %}"></i>
                                    <span>{{ supplier.email }}</span>
                                </div>
                            {% endif %}
                            {% if supplier.address %}
                                <div class="flex items-start text-sm">
                                    <i class="fas fa-map-marker-alt text-gray-400 {% if LANGUAGE_CODE == 'ar' %}ml-2{% else %}mr-2{% endif %} mt-1"></i>
                                    <span>{{ supplier.address }}</span>
                                </div>
                            {% endif %}
                        </div>
                        <div class="px-5 py-3 bg-gray-50 border-t border-gray-200 flex justify-between">
                            <a href="{% url 'purchases:supplier_detail' supplier.id %}" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                {% trans "عرض" %} <i class="fas fa-eye {% if LANGUAGE_CODE == 'ar' %}mr-1{% else %}ml-1{% endif %}"></i>
                            </a>
                            <a href="{% url 'purchases:supplier_update' supplier.id %}" class="text-amber-600 hover:text-amber-800 text-sm font-medium">
                                {% trans "تعديل" %} <i class="fas fa-edit {% if LANGUAGE_CODE == 'ar' %}mr-1{% else %}ml-1{% endif %}"></i>
                            </a>
                        </div>
                    </div>
                {% endfor %}
            </div>
            
            <!-- Pagination -->
            {% if is_paginated %}
                <div class="px-6 py-4 bg-gray-50 border-t border-gray-200">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-700">
                                {% trans "عرض" %}
                                <span class="font-medium">{{ page_obj.start_index }}</span>
                                {% trans "إلى" %}
                                <span class="font-medium">{{ page_obj.end_index }}</span>
                                {% trans "من" %}
                                <span class="font-medium">{{ paginator.count }}</span>
                                {% trans "نتيجة" %}
                            </p>
                        </div>
                        <div>
                            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                {% if page_obj.has_previous %}
                                    <a href="?page={{ page_obj.previous_page_number }}{% if current_search %}&search={{ current_search }}{% endif %}" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                        <span class="sr-only">{% trans "السابق" %}</span>
                                        <i class="fas fa-chevron-left"></i>
                                    </a>
                                {% endif %}
                                
                                {% for i in paginator.page_range %}
                                    {% if page_obj.number == i %}
                                        <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-700">
                                            {{ i }}
                                        </span>
                                    {% elif i > page_obj.number|add:"-3" and i < page_obj.number|add:"3" %}
                                        <a href="?page={{ i }}{% if current_search %}&search={{ current_search }}{% endif %}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                            {{ i }}
                                        </a>
                                    {% endif %}
                                {% endfor %}
                                
                                {% if page_obj.has_next %}
                                    <a href="?page={{ page_obj.next_page_number }}{% if current_search %}&search={{ current_search }}{% endif %}" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                        <span class="sr-only">{% trans "التالي" %}</span>
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                {% endif %}
                            </nav>
                        </div>
                    </div>
                </div>
            {% endif %}
        {% else %}
            <div class="p-6 text-center text-gray-500">
                {% trans "لا يوجد موردين" %}
                <p class="mt-2">
                    <a href="{% url 'purchases:supplier_create' %}" class="text-blue-600 hover:text-blue-800">
                        {% trans "إضافة مورد جديد" %}
                    </a>
                </p>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %} 