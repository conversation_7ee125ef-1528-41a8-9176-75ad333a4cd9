{% extends "base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    /* Floating notification styles */
    .notification-container {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        width: auto;
        max-width: 400px;
    }
    .notification {
        background-color: white;
        border-right: 4px solid;
        margin-bottom: 10px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        transform: translateX(120%);
        transition: transform 0.3s ease-out;
        overflow: hidden;
    }
    .notification.show {
        transform: translateX(0);
    }
    .notification-success {
        border-right-color: #3B82F6;
    }
    .notification-error {
        border-right-color: #EF4444;
    }
    .notification-warning {
        border-right-color: #F59E0B;
    }
    .notification-info {
        border-right-color: #3B82F6;
    }
</style>
{% endblock %}

{% block content %}
<!-- Floating notification container -->
<div class="notification-container" id="notificationContainer"></div>

<div class="container px-4 py-8 mx-auto fade-in-rtl rtl">
   
    
    <!-- Setup Status -->
    <div class="p-6 mb-8 bg-blue-50 rounded-lg shadow-md hover-scale">
        <h2 class="mb-6 text-xl font-semibold text-gray-800 flex items-center">
            <i class="fas fa-chart-line ml-2 mr-0 flex items-center justify-center"></i>
            <span class="inline-block">{% trans "Setup Status" %}</span>
        </h2>
        <div class="grid grid-cols-1 gap-6 md:grid-cols-3">
            <!-- Franchises -->
            <div class="p-5 bg-white rounded-lg shadow-sm hover-scale border-right-blue">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-500">{% trans "Franchises" %}</p>
                        <p class="text-3xl font-bold {% if franchise_count > 0 %}text-blue-600{% else %}text-red-600{% endif %}">
                            {{ franchise_count }}
                        </p>
                    </div>
                    <div class="flex items-center justify-center w-12 h-12 text-white {% if franchise_count > 0 %}bg-purple-500{% else %}bg-purple-500{% endif %} rounded-full">
                        <i class="fas fa-building text-2xl"></i>
                    </div>
                </div>
                <a href="{% url 'setup:franchise_list' %}" class="block mt-5 text-sm font-medium text-blue-600 hover:underline flex items-center">
                    {% if franchise_count > 0 %}
                        <i class="fas fa-cog mr-2 flex items-center justify-center"></i>
                        <span class="inline-block button-text">{% trans "Manage franchises" %}</span>
                    {% else %}
                        <i class="fas fa-plus-circle mr-2 flex items-center justify-center"></i>
                        <span class="inline-block button-text">{% trans "Create your first franchise" %}</span>
                    {% endif %}
                </a>
            </div>
            
            <!-- Companies -->
            <div class="p-5 bg-white rounded-lg shadow-sm hover-scale border-right-blue">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-500">{% trans "Companies" %}</p>
                        <p class="text-3xl font-bold {% if company_count > 0 %}text-blue-600{% else %}text-red-600{% endif %}">
                            {{ company_count }}
                        </p>
                    </div>
                    <div class="flex items-center justify-center w-12 h-12 text-white {% if company_count > 0 %}bg-blue-500{% else %}bg-blue-500{% endif %} rounded-full">
                        <i class="fas fa-briefcase text-2xl"></i>
                    </div>
                </div>
                <a href="{% url 'setup:company_list' %}" class="block mt-5 text-sm font-medium text-blue-600 hover:underline flex items-center">
                    {% if company_count > 0 %}
                        <i class="fas fa-cog mr-2"></i>
                        <span class="inline-block button-text">{% trans "Manage companies" %}</span>
                    {% else %}
                        <i class="fas fa-plus-circle mr-2"></i>
                        <span class="inline-block button-text">{% trans "Create your first company" %}</span>
                    {% endif %}
                </a>
            </div>
            
            <!-- Service Centers -->
            <div class="p-5 bg-white rounded-lg shadow-sm hover-scale border-right-blue">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-500">{% trans "Service Centers" %}</p>
                        <p class="text-3xl font-bold {% if service_center_count > 0 %}text-blue-600{% else %}text-red-600{% endif %}">
                            {{ service_center_count }}
                        </p>
                    </div>
                    <div class="flex items-center justify-center w-12 h-12 text-white {% if service_center_count > 0 %}bg-orange-500{% else %}bg-orange-500{% endif %} rounded-full">
                        <i class="fas fa-store text-2xl"></i>
                    </div>
                </div>
                <a href="{% url 'setup:service_center_list' %}" class="block mt-5 text-sm font-medium text-blue-600 hover:underline flex items-center">
                    {% if service_center_count > 0 %}
                        <i class="fas fa-cog mr-2"></i>
                        <span class="inline-block button-text">{% trans "Manage service centers" %}</span>
                    {% else %}
                        <i class="fas fa-plus-circle mr-2"></i>
                        <span class="inline-block button-text">{% trans "Create your first service center" %}</span>
                    {% endif %}
                </a>
            </div>
        </div>
        
        {% if franchise_count == 0 and company_count == 0 and service_center_count == 0 %}
        <div class="mt-6 p-4 bg-yellow-50 border-r-4 border-yellow-400 rounded text-right">
            <div class="flex flex-row-reverse items-center">
                <div class="flex-shrink-0 ml-3 flex items-center justify-center">
                    <i class="fas fa-exclamation-triangle text-yellow-400 text-lg"></i>
                </div>
                <div class="flex-grow">
                    <p class="text-sm text-yellow-700 w-full text-right">
                        {% trans "Your system is not set up yet. Please create at least one franchise, company, or service center to get started." %}
                    </p>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
    
    <!-- Setup Guide -->
    <div class="p-6 mb-8 bg-white rounded-lg shadow-md hover-scale">
        <h2 class="mb-6 text-xl font-semibold text-gray-800 flex items-center">
            <i class="fas fa-map-signs ml-2 mr-0 flex items-center justify-center"></i>
            <span class="inline-block">{% trans "Quick Setup Guide" %}</span>
        </h2>
        
        <div class="grid grid-cols-1 gap-6">
            <!-- Step 1: Create Franchise -->
            <div class="bg-gray-50 rounded-lg p-4 shadow-sm hover-scale">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <span class="flex items-center justify-center h-12 w-12 rounded-full bg-blue-500 text-white">
                            <i class="fas fa-building fa-lg"></i>
                        </span>
                    </div>
                    <div class="flex-1 mr-4 ml-6">
                        <h3 class="text-lg font-medium text-gray-900">
                            {% trans "Create a Franchise" %}
                        </h3>
                        <p class="text-sm text-gray-500 mt-2">
                            {% trans "Set up your main franchise with contact information and business details." %}
                        </p>
                    </div>
                    <div>
                        <a href="{% url 'setup:franchise_list' %}" class="inline-flex items-center justify-center px-6 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <i class="fas fa-play"></i>
                            
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Step 2: Create Companies -->
            <div class="bg-gray-50 rounded-lg p-4 shadow-sm hover-scale">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <span class="flex items-center justify-center h-12 w-12 rounded-full bg-blue-500 text-white">
                            <i class="fas fa-briefcase fa-lg"></i>
                        </span>
                    </div>
                    <div class="flex-1 mr-4 ml-6">
                        <h3 class="text-lg font-medium text-gray-900">
                            {% trans "Create Companies" %}
                        </h3>
                        <p class="text-sm text-gray-500 mt-2">
                            {% trans "Add companies that operate under your franchise with specific business units." %}
                        </p>
                    </div>
                    <div>
                        <a href="{% url 'setup:company_list' %}" class="inline-flex items-center justify-center px-6 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <i class="fas fa-play"></i>
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Step 3: Add Service Centers -->
            <div class="bg-gray-50 rounded-lg p-4 shadow-sm hover-scale">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <span class="flex items-center justify-center h-12 w-12 rounded-full bg-blue-500 text-white">
                            <i class="fas fa-store fa-lg"></i>
                        </span>
                    </div>
                    <div class="flex-1 mr-4 ml-6">
                        <h3 class="text-lg font-medium text-gray-900">
                            {% trans "Add Service Centers" %}
                        </h3>
                        <p class="text-sm text-gray-500 mt-2">
                            {% trans "Create service centers with locations, contact information and service capabilities." %}
                        </p>
                    </div>
                    <div>
                        <a href="{% url 'setup:service_center_list' %}" class="inline-flex items-center justify-center px-6 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <i class="fas fa-play"></i>
                            
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Step 4: Go to Inventory -->
            <div class="bg-gray-50 rounded-lg p-4 shadow-sm hover-scale">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <span class="flex items-center justify-center h-12 w-12 rounded-full bg-blue-500 text-white">
                            <i class="fas fa-boxes fa-lg"></i>
                        </span>
                    </div>
                    <div class="flex-1 mr-4 ml-6">
                        <h3 class="text-lg font-medium text-gray-900">
                            {% trans "Go to Inventory" %}
                        </h3>
                        <p class="text-sm text-gray-500 mt-2">
                            {% trans "Start managing your inventory items, stock levels, and movements." %}
                        </p>
                    </div>
                    <div>
                        <a href="{% url 'inventory:dashboard' %}" class="inline-flex items-center justify-center px-6 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <i class="fas fa-arrow-right"></i>
                            
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    {% if recent_franchises or recent_companies or recent_service_centers %}
    <!-- Recent Entries -->
    <div class="p-6 bg-white rounded-lg shadow-md hover-scale">
        <h2 class="mb-4 text-xl font-semibold text-gray-800 flex items-center">
            <i class="fas fa-history ml-2 mr-0"></i>
            <span class="inline-block">{% trans "Recent Entries" %}</span>
        </h2>
        
        {% if recent_franchises %}
        <div class="mb-6">
            <h3 class="mb-2 text-lg font-medium text-gray-700 flex items-center">
                <i class="fas fa-building ml-2 mr-0 flex items-center justify-center"></i>
                <span class="inline-block">{% trans "Franchises" %}</span>
            </h3>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "Name" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "Contact" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "Actions" %}
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for franchise in recent_franchises %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">{{ franchise.name }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">{{ franchise.contact_email }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <a href="{% url 'setup:franchise_detail' franchise.pk %}" class="text-blue-600 hover:text-blue-900 flex items-center justify-end">
                                    <span class="inline-block ml-2">{% trans "View" %}</span>
                                    <i class="fas fa-eye flex items-center justify-center"></i>
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        {% endif %}
        
        {% if recent_companies %}
        <div class="mb-6">
            <h3 class="mb-2 text-lg font-medium text-gray-700 flex items-center">
                <i class="fas fa-briefcase ml-2 mr-0 flex items-center justify-center"></i>
                <span class="inline-block">{% trans "Companies" %}</span>
            </h3>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "Name" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "Franchise" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "Actions" %}
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for company in recent_companies %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">{{ company.name }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">{{ company.franchise.name }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <a href="{% url 'setup:company_detail' company.pk %}" class="text-blue-600 hover:text-blue-900 flex items-center justify-end">
                                    <span class="inline-block ml-2">{% trans "View" %}</span>
                                    <i class="fas fa-eye flex items-center justify-center"></i>
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        {% endif %}
        
        {% if recent_service_centers %}
        <div>
            <h3 class="mb-2 text-lg font-medium text-gray-700 flex items-center">
                <i class="fas fa-store ml-2 mr-0 flex items-center justify-center"></i>
                <span class="inline-block">{% trans "Service Centers" %}</span>
            </h3>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "Name" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "Company" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "Location" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {% trans "Actions" %}
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for center in recent_service_centers %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">{{ center.name }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">{{ center.company.name }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">{{ center.location }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <a href="{% url 'setup:service_center_detail' center.pk %}" class="text-blue-600 hover:text-blue-900 flex items-center justify-end">
                                    <span class="inline-block ml-2">{% trans "View" %}</span>
                                    <i class="fas fa-eye flex items-center justify-center"></i>
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        {% endif %}
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Notification functions
    function showNotification(message, type = 'info', duration = 5000) {
        const container = document.getElementById('notificationContainer');
        
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type} p-4 flex items-center`;
        
        // Set icon based on type
        let iconHtml = '';
        switch (type) {
            case 'success':
                iconHtml = '<svg class="flex-shrink-0 w-5 h-5 text-green-600 ml-3" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg>';
                break;
            case 'error':
                iconHtml = '<svg class="flex-shrink-0 w-5 h-5 text-red-600 ml-3" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path></svg>';
                break;
            case 'warning':
                iconHtml = '<svg class="flex-shrink-0 w-5 h-5 text-yellow-600 ml-3" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>';
                break;
            default:
                iconHtml = '<svg class="flex-shrink-0 w-5 h-5 text-blue-600 ml-3" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg>';
        }
        
        // Create notification content
        notification.innerHTML = `
            <div class="flex-1 text-right">
                <p class="text-sm font-medium">${message}</p>
            </div>
            ${iconHtml}
            <button class="mr-3 text-gray-400 hover:text-gray-900" onclick="this.parentNode.remove()">
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
            </button>
        `;
        
        // Add to container
        container.appendChild(notification);
        
        // Trigger animation
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);
        
        // Auto remove after duration
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, duration);
    }

    // Process Django messages and display as floating notifications
    document.addEventListener('DOMContentLoaded', function() {
        // Django messages are stored in cookies or localStorage by some implementations
        // We'll provide two methods to receive notifications:
        
        // 1. Via URL parameters (for redirects)
        const urlParams = new URLSearchParams(window.location.search);
        const messageParam = urlParams.get('message');
        const messageType = urlParams.get('type') || 'info';
        
        if (messageParam) {
            showNotification(decodeURIComponent(messageParam), messageType);
            
            // Clean URL without reloading
            if (window.history && window.history.replaceState) {
                const newUrl = window.location.pathname + window.location.hash;
                window.history.replaceState({}, document.title, newUrl);
            }
        }
        
        // 2. Via custom events (for AJAX responses)
        window.addEventListener('showNotification', function(e) {
            const { message, type, duration } = e.detail;
            showNotification(message, type, duration);
        });
    });
    
    // Connect notification to dashboard actions
    document.addEventListener('DOMContentLoaded', function() {
        // Common dashboard actions that should show notifications
        const actionMap = {
            'create': 'Creating new item...',
            'update': 'Updating item...',
            'delete': 'Deleting item...',
            'manage': 'Loading items...',
            'view': 'Loading details...'
        };
        
        // Example: Trigger notification on button click
        document.querySelectorAll('.hover-scale a').forEach(link => {
            link.addEventListener('click', function(e) {
                // Don't show for navigation, but prepare event listeners
                // for buttons that should show notifications
                
                // In a real implementation, we would dispatch custom events
                // when AJAX calls complete, using:
                // window.dispatchEvent(new CustomEvent('showNotification', {
                //     detail: { message: 'Success message', type: 'success' }
                // }));
            });
        });
        
        // Utility function to trigger notifications from anywhere in your code
        window.notify = function(message, type = 'info', duration = 5000) {
            showNotification(message, type, duration);
        };
        
        // Test notification (uncomment to see it working)
        // setTimeout(() => {
        //    window.notify('Custom notification system is now active', 'success');
        // }, 1000);
    });
</script>
{% endblock %} 