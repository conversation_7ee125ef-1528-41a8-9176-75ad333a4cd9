{% extends "dashboard_base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Reports" %}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="mb-6">
        <h1 class="text-2xl font-bold text-gray-800">{% trans "Reports" %}</h1>
        <p class="text-gray-600">{% trans "View and manage reports" %}</p>
    </div>
    
    {% if missing_tenant %}
        <div class="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-6" role="alert">
            <p class="font-bold">{% trans "Tenant ID Missing" %}</p>
            <p>{% trans "No tenant ID was found. Please make sure your X-Tenant-ID header is set correctly." %}</p>
        </div>
    {% endif %}
    
    <div class="bg-white shadow overflow-hidden rounded-lg">
        <div class="px-4 py-5 border-b border-gray-200 sm:px-6 flex justify-between items-center">
            <h3 class="text-lg leading-6 font-medium text-gray-900">
                {% trans "Available Reports" %}
            </h3>
            
            {% if has_advanced_reporting %}
            <div>
                <a href="{% url 'reports:dashboard_list' %}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <i class="fas fa-tachometer-alt mr-2"></i>
                    {% trans "Dashboards" %}
                </a>
            </div>
            {% endif %}
        </div>
        
        {% if reports %}
            <ul class="divide-y divide-gray-200">
                {% for report in reports %}
                <li class="px-6 py-4 flex items-center">
                    <div class="min-w-0 flex-1">
                        <div class="flex items-center">
                            <div class="text-lg font-medium text-indigo-600 truncate">{{ report.name }}</div>
                            <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                {{ report.get_report_type_display }}
                            </span>
                        </div>
                        <div class="mt-1 text-sm text-gray-500">
                            {{ report.description }}
                        </div>
                    </div>
                    <div class="ml-4 flex-shrink-0 flex space-x-2">
                        <a href="{% url 'reports:report_detail' report.id %}" class="inline-flex items-center px-3 py-1 border border-transparent text-sm leading-5 font-medium rounded-md text-gray-700 bg-gray-100 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                            <i class="fas fa-eye mr-1"></i>
                            {% trans "View" %}
                        </a>
                        <a href="{% url 'reports:execute_report' report.id %}" class="inline-flex items-center px-3 py-1 border border-transparent text-sm leading-5 font-medium rounded-md text-green-700 bg-green-100 hover:bg-green-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                            <i class="fas fa-play mr-1"></i>
                            {% trans "Run" %}
                        </a>
                    </div>
                </li>
                {% endfor %}
            </ul>
        {% else %}
            <div class="px-6 py-12 text-center">
                {% if missing_tenant %}
                    <p class="text-gray-500 mb-4">{% trans "No reports available because tenant ID is missing." %}</p>
                    <p class="text-gray-500">{% trans "Please set the X-Tenant-ID header to access reports." %}</p>
                {% else %}
                    <p class="text-gray-500 mb-4">{% trans "No reports available." %}</p>
                    <p class="text-gray-500">{% trans "Reports will appear here once they are created." %}</p>
                {% endif %}
            </div>
        {% endif %}
    </div>
    
    <div class="mt-8 bg-white shadow overflow-hidden rounded-lg">
        <div class="px-4 py-5 border-b border-gray-200 sm:px-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900">
                {% trans "Basic Reports" %}
            </h3>
            <p class="mt-1 text-sm text-gray-500">
                {% trans "Quick access to common reports" %}
            </p>
        </div>
        <div class="bg-gray-50 px-4 py-5 sm:p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="bg-white overflow-hidden shadow rounded-lg border border-gray-200 hover:shadow-md transition-shadow">
                    <div class="px-4 py-5 sm:p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 bg-blue-100 rounded-md p-3">
                                <i class="fas fa-boxes text-blue-600"></i>
                            </div>
                            <div class="ml-5">
                                <h3 class="text-lg leading-6 font-medium text-gray-900">{% trans "Inventory Report" %}</h3>
                                <p class="mt-1 text-sm text-gray-500">{% trans "Basic inventory status and metrics" %}</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-50 px-4 py-4 sm:px-6">
                        <a href="{% url 'reports:basic_inventory' %}" class="text-sm font-medium text-blue-600 hover:text-blue-500">
                            {% trans "View Report" %} <i class="fas fa-arrow-right ml-1"></i>
                        </a>
                    </div>
                </div>
                
                <div class="bg-white overflow-hidden shadow rounded-lg border border-gray-200 hover:shadow-md transition-shadow">
                    <div class="px-4 py-5 sm:p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 bg-green-100 rounded-md p-3">
                                <i class="fas fa-chart-line text-green-600"></i>
                            </div>
                            <div class="ml-5">
                                <h3 class="text-lg leading-6 font-medium text-gray-900">{% trans "Sales Report" %}</h3>
                                <p class="mt-1 text-sm text-gray-500">{% trans "Basic sales metrics and trends" %}</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-50 px-4 py-4 sm:px-6">
                        <a href="{% url 'reports:basic_sales' %}" class="text-sm font-medium text-green-600 hover:text-green-500">
                            {% trans "View Report" %} <i class="fas fa-arrow-right ml-1"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 