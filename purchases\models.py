from django.db import models
from django.utils.translation import gettext_lazy as _
from core.models import TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel
from core.querysets import BaseQuerySet
from inventory.models import Item


class Supplier(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """
    Supplier model for purchases
    """
    name = models.CharField(_("Name"), max_length=255)
    email = models.EmailField(_("Email"), blank=True)
    phone = models.CharField(_("Phone"), max_length=50, blank=True)
    address = models.TextField(_("Address"), blank=True)
    is_active = models.BooleanField(_("Active"), default=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Supplier")
        verbose_name_plural = _("Suppliers")
        
    def __str__(self):
        return self.name


class PurchaseOrder(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """
    Purchase order model
    """
    STATUS_CHOICES = (
        ('draft', _('Draft')),
        ('sent', _('Sent')),
        ('confirmed', _('Confirmed')),
        ('received', _('Received')),
        ('cancelled', _('Cancelled')),
    )
    
    order_number = models.CharField(_("PO Number"), max_length=50, unique=True)
    supplier = models.ForeignKey(
        Supplier, 
        on_delete=models.PROTECT, 
        related_name='purchase_orders',
        verbose_name=_("Supplier")
    )
    order_date = models.DateField(_("Order Date"))
    expected_delivery_date = models.DateField(_("Expected Delivery Date"), null=True, blank=True)
    status = models.CharField(_("Status"), max_length=20, choices=STATUS_CHOICES, default='draft')
    notes = models.TextField(_("Notes"), blank=True)
    total_amount = models.DecimalField(_("Total Amount"), max_digits=10, decimal_places=2, default=0)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Purchase Order")
        verbose_name_plural = _("Purchase Orders")
        ordering = ['-order_date']
        
    def __str__(self):
        return f"{self.order_number} - {self.supplier.name}"
    
    def update_total_amount(self):
        """
        Update the total amount based on order items
        """
        self.total_amount = sum(item.total_price for item in self.items.all())
        self.save(update_fields=['total_amount', 'updated_at'])


class PurchaseOrderItem(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """
    Purchase order item model
    """
    purchase_order = models.ForeignKey(
        PurchaseOrder, 
        on_delete=models.CASCADE, 
        related_name='items',
        verbose_name=_("Purchase Order")
    )
    item = models.ForeignKey(
        Item, 
        on_delete=models.PROTECT, 
        related_name='purchase_items',
        verbose_name=_("Item")
    )
    quantity = models.DecimalField(_("Quantity"), max_digits=10, decimal_places=2)
    unit_price = models.DecimalField(_("Unit Price"), max_digits=10, decimal_places=2)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Purchase Order Item")
        verbose_name_plural = _("Purchase Order Items")
        unique_together = [['purchase_order', 'item']]
        
    def __str__(self):
        return f"{self.quantity} x {self.item.name} in {self.purchase_order.order_number}"
    
    @property
    def total_price(self):
        """
        Calculate total price for this line item
        """
        return self.quantity * self.unit_price
        
    def save(self, *args, **kwargs):
        """
        Override save method to update purchase order total
        """
        super().save(*args, **kwargs)
        self.purchase_order.update_total_amount()


class PurchaseReceipt(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """
    Purchase receipt model for receiving goods
    """
    receipt_number = models.CharField(_("Receipt Number"), max_length=50, unique=True)
    purchase_order = models.ForeignKey(
        PurchaseOrder, 
        on_delete=models.PROTECT, 
        related_name='receipts',
        verbose_name=_("Purchase Order")
    )
    receipt_date = models.DateField(_("Receipt Date"))
    notes = models.TextField(_("Notes"), blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Purchase Receipt")
        verbose_name_plural = _("Purchase Receipts")
        ordering = ['-receipt_date']
        
    def __str__(self):
        return f"{self.receipt_number} - {self.purchase_order.order_number}"


class PurchaseReceiptItem(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """
    Purchase receipt item model
    """
    purchase_receipt = models.ForeignKey(
        PurchaseReceipt, 
        on_delete=models.CASCADE, 
        related_name='items',
        verbose_name=_("Purchase Receipt")
    )
    purchase_order_item = models.ForeignKey(
        PurchaseOrderItem, 
        on_delete=models.PROTECT, 
        related_name='receipts',
        verbose_name=_("Purchase Order Item")
    )
    quantity = models.DecimalField(_("Quantity Received"), max_digits=10, decimal_places=2)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("Purchase Receipt Item")
        verbose_name_plural = _("Purchase Receipt Items")
        
    def __str__(self):
        return f"{self.quantity} x {self.purchase_order_item.item.name} in {self.purchase_receipt.receipt_number}"
