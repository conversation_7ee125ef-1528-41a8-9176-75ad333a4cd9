from django.db import models
from django.utils.translation import gettext_lazy as _
from core.models.common import TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel
from core.querysets import BaseQuerySet
from django.utils import timezone
from setup.models import Customer
from decimal import Decimal
import uuid


class CustomerClassification(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """
    Customer classification tiers (e.g., Regular, Silver, Gold, Platinum)
    """
    name = models.CharField(_('Classification Name'), max_length=100)
    description = models.TextField(_('Description'), blank=True)
    level = models.PositiveIntegerField(_('Level'), default=0, help_text=_('Higher numbers represent better classifications'))
    icon = models.CharField(_('Icon'), max_length=50, blank=True)
    color = models.CharField(_('Color'), max_length=20, blank=True)
    is_active = models.BooleanField(_('Is Active'), default=True)
    
    # Benefits
    discount_percentage = models.DecimalField(_('Default Discount %'), max_digits=5, decimal_places=2, default=0)
    service_priority = models.PositiveIntegerField(_('Service Priority'), default=0, help_text=_('Higher numbers get higher priority'))
    credit_limit_multiplier = models.DecimalField(_('Credit Limit Multiplier'), max_digits=5, decimal_places=2, default=1.0)
    extended_payment_days = models.PositiveIntegerField(_('Extended Payment Days'), default=0)
    
    # Other Settings
    notes = models.TextField(_('Notes'), blank=True)
    attributes = models.JSONField(_('Custom Attributes'), default=dict, blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _('Customer Classification')
        verbose_name_plural = _('Customer Classifications')
        ordering = ['level']
    
    def __str__(self):
        return self.name


class ClassificationCriteria(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """
    Criteria for automatic customer classification
    """
    CRITERIA_TYPE_CHOICES = [
        ('spend_amount', _('Total Spend Amount')),
        ('spend_frequency', _('Spend Frequency')),
        ('visit_count', _('Visit Count')),
        ('average_spend', _('Average Spend')),
        ('payment_history', _('Payment History')),
        ('vehicle_count', _('Vehicle Count')),
        ('vehicle_value', _('Vehicle Value')),
        ('service_type_usage', _('Service Type Usage')),
        ('referrals', _('Referrals Made')),
        ('days_since_first', _('Days Since First Visit')),
        ('loyalty_duration', _('Loyalty Duration')),
        ('custom', _('Custom Criteria')),
    ]
    
    OPERATOR_CHOICES = [
        ('gt', _('Greater Than')),
        ('gte', _('Greater Than or Equal')),
        ('lt', _('Less Than')),
        ('lte', _('Less Than or Equal')),
        ('eq', _('Equal To')),
        ('between', _('Between')),
    ]
    
    PERIOD_CHOICES = [
        ('all_time', _('All Time')),
        ('year', _('Last Year')),
        ('6_months', _('Last 6 Months')),
        ('3_months', _('Last 3 Months')),
        ('month', _('Last Month')),
    ]
    
    classification = models.ForeignKey(
        CustomerClassification,
        on_delete=models.CASCADE,
        related_name='criteria',
        verbose_name=_('Classification'),
        help_text=_('Classification this criteria applies to')
    )
    
    # Criteria Details
    criteria_type = models.CharField(_('Criteria Type'), max_length=50, choices=CRITERIA_TYPE_CHOICES)
    operator = models.CharField(_('Operator'), max_length=20, choices=OPERATOR_CHOICES)
    value = models.DecimalField(_('Value'), max_digits=15, decimal_places=2)
    value2 = models.DecimalField(_('Second Value'), max_digits=15, decimal_places=2, null=True, blank=True, 
                                help_text=_('For "between" operator'))
    period = models.CharField(_('Time Period'), max_length=20, choices=PERIOD_CHOICES, default='all_time')
    
    # Criteria weight/importance
    weight = models.DecimalField(_('Weight'), max_digits=5, decimal_places=2, default=1.0,
                               help_text=_('Weight factor for this criteria'))
    
    # For custom criteria
    custom_evaluation = models.TextField(_('Custom Evaluation'), blank=True,
                                       help_text=_('JSON or description of custom evaluation logic'))
    
    is_required = models.BooleanField(_('Is Required'), default=False,
                                    help_text=_('Customer must meet this criteria regardless of other criteria'))
    
    # Other Settings
    description = models.CharField(_('Description'), max_length=255, blank=True)
    notes = models.TextField(_('Notes'), blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _('Classification Criteria')
        verbose_name_plural = _('Classification Criteria')
        ordering = ['classification__level', 'criteria_type']
    
    def __str__(self):
        return f"{self.classification.name} - {self.get_criteria_type_display()}"


class CustomerClassificationHistory(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """
    History of customer classification changes
    """
    customer = models.ForeignKey(
        Customer,
        on_delete=models.CASCADE,
        related_name='classification_history',
        verbose_name=_('Customer')
    )
    
    previous_classification = models.ForeignKey(
        CustomerClassification,
        on_delete=models.SET_NULL,
        related_name='previous_customers',
        verbose_name=_('Previous Classification'),
        null=True, blank=True
    )
    
    new_classification = models.ForeignKey(
        CustomerClassification,
        on_delete=models.CASCADE,
        related_name='new_customers',
        verbose_name=_('New Classification')
    )
    
    change_date = models.DateTimeField(_('Change Date'), default=timezone.now)
    
    # Change reason
    automatic = models.BooleanField(_('Automatic Change'), default=True)
    changed_by = models.UUIDField(_('Changed By User ID'), null=True, blank=True)
    reason = models.TextField(_('Reason for Change'), blank=True)
    
    # Evaluation details
    evaluation_data = models.JSONField(_('Evaluation Data'), default=dict, blank=True,
                                     help_text=_('Details of the evaluation that led to this change'))
    
    # Notification status
    notification_sent = models.BooleanField(_('Notification Sent'), default=False)
    notification_date = models.DateTimeField(_('Notification Date'), null=True, blank=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _('Customer Classification History')
        verbose_name_plural = _('Customer Classification History')
        ordering = ['-change_date']
    
    def __str__(self):
        previous = self.previous_classification.name if self.previous_classification else 'None'
        return f"{self.customer.full_name}: {previous} → {self.new_classification.name}" 