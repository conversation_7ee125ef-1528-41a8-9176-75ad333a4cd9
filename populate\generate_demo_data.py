import os
import sys
import django
import random
from datetime import datetime, timedelta
from django.db import transaction
from faker import Faker
import uuid

# Add the parent directory to sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

# Import necessary models
from setup.models import ServiceCenter, ServiceLevel, ServiceCenterType
from setup.models import Franchise, Company, Customer, Vehicle
from inventory.models import Item, ItemClassification, UnitOfMeasurement
from django.utils import timezone
from django.contrib.auth.models import User

# Initialize Faker
fake = Faker('ar_EG')  # Using Egyptian Arabic locale for Egyptian market specifics

class DemoDataGenerator:
    """Comprehensive demo data generator for Aftersails system with Egyptian market specifics."""
    
    def __init__(self):
        # Generate a tenant ID (UUID)
        self.tenant_id = str(uuid.uuid4())
        print(f"Using tenant ID: {self.tenant_id}")
        
        # Check if we have a user, create one if not
        if not User.objects.filter(is_superuser=True).exists():
            User.objects.create_superuser('admin', '<EMAIL>', 'admin')
            print("Created superuser 'admin' with password 'admin'")
    
    @transaction.atomic
    def generate_inventory_data(self):
        """Generate inventory data with Egyptian market specifics."""
        print("Generating inventory data...")
        
        # Create Units of Measurement with Arabic names
        uom_data = [
            {'name': 'قطعة', 'symbol': 'PC', 'description': 'عدد القطع'},
            {'name': 'لتر', 'symbol': 'L', 'description': 'حجم بالليتر'},
            {'name': 'كيلوجرام', 'symbol': 'KG', 'description': 'وزن بالكيلوجرام'},
            {'name': 'متر', 'symbol': 'M', 'description': 'طول بالمتر'},
            {'name': 'مجموعة', 'symbol': 'SET', 'description': 'مجموعة كاملة'},
            {'name': 'زوج', 'symbol': 'PR', 'description': 'زوج من القطع'},
        ]
        
        units = []
        for data in uom_data:
            unit, created = UnitOfMeasurement.objects.get_or_create(
                tenant_id=self.tenant_id,
                symbol=data['symbol'],
                defaults={
                    'name': data['name'],
                    'description': data['description'],
                    'is_base_unit': True
                }
            )
            units.append(unit)
            if created:
                print(f"Created UOM: {unit.name}")
        
        # Create Item Classifications for vehicle parts
        classification_data = [
            {'name': 'قطع محرك', 'code': 'ENG', 'description': 'قطع غيار المحرك'},
            {'name': 'قطع كهربائية', 'code': 'ELEC', 'description': 'قطع غيار كهربائية'},
            {'name': 'قطع هيكل', 'code': 'BODY', 'description': 'قطع غيار الهيكل الخارجي'},
            {'name': 'قطع تعليق', 'code': 'SUSP', 'description': 'قطع غيار نظام التعليق'},
            {'name': 'قطع فرامل', 'code': 'BRAKE', 'description': 'قطع غيار نظام الفرامل'},
            {'name': 'زيوت وسوائل', 'code': 'FLUID', 'description': 'زيوت وسوائل للسيارة'},
            {'name': 'فلاتر', 'code': 'FILTER', 'description': 'فلاتر متنوعة'},
            {'name': 'إكسسوارات', 'code': 'ACCESS', 'description': 'إكسسوارات السيارة'},
        ]
        
        # Check if ItemClassification model has a 'code' field
        has_code_field = hasattr(ItemClassification, 'code')
        
        classifications = []
        for data in classification_data:
            # Create filter criteria based on available fields
            if has_code_field:
                filter_kwargs = {'tenant_id': self.tenant_id, 'code': data['code']}
            else:
                filter_kwargs = {'tenant_id': self.tenant_id, 'name': data['name']}
            
            # Create the classification
            classification, created = ItemClassification.objects.get_or_create(
                **filter_kwargs,
                defaults={
                    'name': data['name'],
                    'description': data['description']
                }
            )
            classifications.append(classification)
            if created:
                print(f"Created Classification: {classification.name}")
        
        # Create Inventory Items with Egyptian car makes
        item_count = 200  # Generate 200 inventory items
        
        # Egyptian popular car makes
        eg_car_makes = [
            'تويوتا', 'هيونداي', 'نيسان', 'شيفروليه', 'كيا', 'مرسيدس', 'بي إم دبليو', 
            'بيجو', 'رينو', 'فيات', 'ميتسوبيشي', 'سوزوكي', 'لادا', 'جيلي', 'بريليانس',
            'شيري', 'بي واي دي', 'ام جي', 'سكودا', 'فولفو'
        ]
        
        # Check Item model fields
        has_code_field = hasattr(Item, 'code')
        
        # Create items
        items_created = 0
        for i in range(item_count):
            # Select random classification and unit
            classification = random.choice(classifications)
            unit = random.choice(units)
            
            # Generate item details
            make = random.choice(eg_car_makes)
            
            # Create different types of parts based on classification
            part_names = {
                'ENG': ['فلتر زيت', 'طقم بساتم', 'طقم شنبر', 'وش سلندر', 'عمود كامات', 'سير كاتينة', 'بلوف', 'كاربراتير', 'دينامو', 'مارش', 'طرمبة بنزين', 'رداتير'],
                'ELEC': ['بطارية', 'دينامو', 'فيوز', 'لمبة أمامية', 'لمبة خلفية', 'موتور مساحات', 'حساس حرارة', 'طقم أسلاك شمعات', 'شمعات', 'كمبيوتر', 'ريلاي'],
                'BODY': ['كبوت', 'رفرف', 'باب أمامي', 'باب خلفي', 'شنطة', 'صدام أمامي', 'صدام خلفي', 'زجاج أمامي', 'مرايا جانبية', 'سقف'],
                'SUSP': ['مساعد أمامي', 'مساعد خلفي', 'مقص سفلي', 'عفشة كاملة', 'بلية عجل', 'طبة عفشة', 'ذراع توجيه'],
                'BRAKE': ['تيل فرامل أمامي', 'تيل فرامل خلفي', 'فحمات', 'هوبات', 'اسطوانة فرامل', 'ماستر فرامل', 'خراطيم فرامل'],
                'FLUID': ['زيت محرك', 'زيت فتيس', 'زيت باور', 'سائل تبريد', 'سائل فرامل', 'مياه زجاج'],
                'FILTER': ['فلتر هواء', 'فلتر بنزين', 'فلتر زيت', 'فلتر مكيف'],
                'ACCESS': ['حامل موبايل', 'فرش أرضية', 'غطاء سيارة', 'مخدات رأس', 'شاشة مولتيميديا', 'كاميرا خلفية']
            }
            
            # Get part names for this classification
            if hasattr(classification, 'code') and classification.code in part_names:
                parts = part_names[classification.code]
            else:
                parts = ['قطعة غيار', 'قطعة']
                
            part_name = random.choice(parts)
            
            # Create a name including make and part
            name = f"{part_name} {make}"
            
            # Generate a code or item_number based on what the model has
            item_code = f"ITM-{make[:2]}{random.randint(1000, 9999)}"
            
            # Base item data
            item_data = {
                'tenant_id': self.tenant_id,
                'name': name,
                'description': f"وصف {name}",
                'unit_of_measure': unit,
                'is_active': True,
                'barcode': f"ITM{random.randint(10000000, 99999999)}",
            }
            
            # Add fields conditionally based on model
            if hasattr(Item, 'classification'):
                item_data['classification'] = classification
                
            if hasattr(Item, 'code'):
                item_data['code'] = item_code
            elif hasattr(Item, 'item_number'):
                item_data['item_number'] = item_code
                
            if hasattr(Item, 'base_price'):
                item_data['base_price'] = random.randint(50, 5000)
            if hasattr(Item, 'selling_price'):
                item_data['selling_price'] = random.randint(60, 6000)
                
            if hasattr(Item, 'min_stock'):
                item_data['min_stock'] = random.randint(5, 20)
            if hasattr(Item, 'reorder_point'):
                item_data['reorder_point'] = random.randint(10, 30)
            if hasattr(Item, 'reorder_qty'):
                item_data['reorder_qty'] = random.randint(15, 50)
            if hasattr(Item, 'lead_time_days'):
                item_data['lead_time_days'] = random.randint(7, 30)
            
            # Create the item using flexible key-based lookup
            if has_code_field:
                item, created = Item.objects.get_or_create(
                    tenant_id=self.tenant_id,
                    code=item_code,
                    defaults=item_data
                )
            else:
                # If no code field, use name instead
                item, created = Item.objects.get_or_create(
                    tenant_id=self.tenant_id,
                    name=name,
                    defaults=item_data
                )
            
            if created:
                items_created += 1
                
                # Set initial stock if field exists
                if hasattr(item, 'on_hand_qty'):
                    item.on_hand_qty = random.randint(10, 100)
                    item.save(update_fields=['on_hand_qty'])
                
                if items_created % 20 == 0:
                    print(f"Created {items_created} items so far...")
        
        print(f"Created total of {items_created} inventory items with Egyptian market specifics")
    
    @transaction.atomic
    def generate_setup_data(self):
        """Generate setup data with Egyptian market specifics."""
        print("Generating setup data...")
        
        # Create Service Levels
        service_levels = [
            {'name': 'البلاتينية', 'code': 'PLAT', 'level': 1, 'description': 'خدمة بلاتينية ممتازة'},
            {'name': 'الذهبية', 'code': 'GOLD', 'level': 2, 'description': 'خدمة ذهبية'},
            {'name': 'الفضية', 'code': 'SILV', 'level': 3, 'description': 'خدمة فضية'},
            {'name': 'البرونزية', 'code': 'BRNZ', 'level': 4, 'description': 'خدمة برونزية أساسية'},
        ]
        
        for data in service_levels:
            if hasattr(ServiceLevel, 'code'):
                # If code field exists
                level, created = ServiceLevel.objects.get_or_create(
                    tenant_id=self.tenant_id,
                    code=data['code'],
                    defaults={
                        'name': data['name'],
                        'level': data['level'],
                        'description': data['description'],
                        'is_active': True
                    }
                )
            else:
                # Fallback to using name
                level, created = ServiceLevel.objects.get_or_create(
                    tenant_id=self.tenant_id,
                    name=data['name'],
                    defaults={
                        'level': data['level'],
                        'description': data['description'],
                        'is_active': True
                    }
                )
            
            if created:
                print(f"Created Service Level: {level.name}")
        
        # Create Service Center Types
        center_types = [
            {'name': 'مركز خدمة كامل', 'code': 'FULL', 'description': 'مركز خدمة كامل لجميع أنواع الخدمات'},
            {'name': 'مركز صيانة سريعة', 'code': 'QUICK', 'description': 'مركز للصيانة السريعة والخدمات البسيطة'},
            {'name': 'مركز إصلاح متخصص', 'code': 'SPEC', 'description': 'مركز إصلاح متخصص في نوع معين من الإصلاحات'},
            {'name': 'مركز قطع غيار', 'code': 'PARTS', 'description': 'مركز لبيع قطع الغيار فقط'},
        ]
        
        for data in center_types:
            if hasattr(ServiceCenterType, 'code'):
                c_type, created = ServiceCenterType.objects.get_or_create(
                    tenant_id=self.tenant_id,
                    code=data['code'],
                    defaults={
                        'name': data['name'],
                        'description': data['description'],
                        'is_active': True
                    }
                )
            else:
                c_type, created = ServiceCenterType.objects.get_or_create(
                    tenant_id=self.tenant_id,
                    name=data['name'],
                    defaults={
                        'description': data['description'],
                        'is_active': True
                    }
                )
            
            if created:
                print(f"Created Service Center Type: {c_type.name}")
        
        # Create Franchise with 3 companies in Egyptian cities
        franchise = self._create_franchise()
        
        # Create Companies under the franchise
        companies = self._create_companies(franchise)
        
        # Create Service Centers for each company
        service_centers = self._create_service_centers(companies)
        
        # Create Customers
        self._create_customers(46)  # Create 46 Egyptian customers
        
        # Create Vehicles (handled separately due to previous issues)
        self._create_vehicles()
        
        print("Setup data generation complete")
    
    def _create_franchise(self):
        """Create a franchise with Egyptian details."""
        
        # Check if Franchise has tenant_id field
        franchise_fields = {
            'name': 'أفترسيلز مصر',
            'registration_number': f"FR{random.randint(10000, 99999)}",
            'address': 'شارع الهرم، الجيزة',
            'city': 'القاهرة',
            'state': 'الجيزة',
            'country': 'مصر',
            'postal_code': f"{random.randint(10000, 99999)}",
            'phone': f"+20{random.randint(1000000000, 2099999999)}",
            'email': '<EMAIL>',
            'website': 'https://www.aftersails-egypt.com',
            'is_active': True
        }
        
        # Add tax field with the right name
        if hasattr(Franchise, 'tax_id'):
            franchise_fields['tax_id'] = f"{random.randint(100, 999)}-{random.randint(100, 999)}-{random.randint(100, 999)}"
        elif hasattr(Franchise, 'tax_number'):
            franchise_fields['tax_number'] = f"{random.randint(100, 999)}-{random.randint(100, 999)}-{random.randint(100, 999)}"
        
        # Add date field with the right name
        if hasattr(Franchise, 'founding_date'):
            franchise_fields['founding_date'] = datetime(2020, 1, 1)
        elif hasattr(Franchise, 'established_date'):
            franchise_fields['established_date'] = datetime(2020, 1, 1)
        
        # Add tenant_id if the model has it
        if hasattr(Franchise, 'tenant_id'):
            franchise_fields['tenant_id'] = self.tenant_id
        
        # Check if code field exists
        if hasattr(Franchise, 'code'):
            franchise, created = Franchise.objects.get_or_create(
                code='EGFRN',
                defaults=franchise_fields
            )
        else:
            # Use name as the unique identifier if no code field
            franchise, created = Franchise.objects.get_or_create(
                name='أفترسيلز مصر',
                defaults=franchise_fields
            )
        
        if created:
            print(f"Created Franchise: {franchise.name}")
        
        return franchise
    
    def _create_companies(self, franchise):
        """Create companies under the franchise in major Egyptian cities."""
        eg_cities = [
            {'name': 'القاهرة', 'state': 'القاهرة'},
            {'name': 'الإسكندرية', 'state': 'الإسكندرية'},
            {'name': 'المنصورة', 'state': 'الدقهلية'}
        ]
        
        companies = []
        for i, city_data in enumerate(eg_cities):
            # Base company fields
            company_data = {
                'name': f"أفترسيلز {city_data['name']}",
                'franchise': franchise,
                'registration_number': f"C{random.randint(10000, 99999)}",
                'address': f"شارع {fake.street_name()}",
                'city': city_data['name'],
                'state': city_data['state'],
                'country': 'مصر',
                'postal_code': f"{random.randint(10000, 99999)}",
                'phone': f"+20{random.randint(1000000000, 2099999999)}",
                'email': f"info@aftersails-{i+1}.com",
                'is_active': True
            }
            
            # Add tax field with the right name
            if hasattr(Company, 'tax_id'):
                company_data['tax_id'] = f"{random.randint(300, 999)}-{random.randint(300, 999)}-{random.randint(300, 999)}"
            elif hasattr(Company, 'tax_number'):
                company_data['tax_number'] = f"{random.randint(300, 999)}-{random.randint(300, 999)}-{random.randint(300, 999)}"
            
            # Add tenant_id if the model has it
            if hasattr(Company, 'tenant_id'):
                company_data['tenant_id'] = self.tenant_id
                
            # Create company based on available fields
            if hasattr(Company, 'code'):
                company, created = Company.objects.get_or_create(
                    code=f"EGC{i+1}",
                    defaults=company_data
                )
            else:
                # Use name as unique identifier
                company, created = Company.objects.get_or_create(
                    name=company_data['name'],
                    defaults=company_data
                )
            
            companies.append(company)
            
            if created:
                print(f"Created Company: {company.name}")
        
        return companies
    
    def _create_service_centers(self, companies):
        """Create service centers for each company."""
        center_locations = {
            'القاهرة': [
                {'name': 'مركز المعادي', 'area': 'المعادي'},
                {'name': 'مركز مدينة نصر', 'area': 'مدينة نصر'},
                {'name': 'مركز الدقي', 'area': 'الدقي'}
            ],
            'الإسكندرية': [
                {'name': 'مركز سموحة', 'area': 'سموحة'},
                {'name': 'مركز ميامي', 'area': 'ميامي'}
            ],
            'المنصورة': [
                {'name': 'مركز المنصورة الرئيسي', 'area': 'شارع الجمهورية'},
                {'name': 'مركز جمصة', 'area': 'جمصة'},
                {'name': 'مركز طلخا', 'area': 'طلخا'}
            ]
        }
        
        # Get service center types and service levels
        center_types = list(ServiceCenterType.objects.filter(tenant_id=self.tenant_id) if hasattr(ServiceCenterType, 'tenant_id') else ServiceCenterType.objects.all())
        service_levels = list(ServiceLevel.objects.filter(tenant_id=self.tenant_id) if hasattr(ServiceLevel, 'tenant_id') else ServiceLevel.objects.all())
        
        if not center_types or not service_levels:
            print("No service center types or service levels found, skipping service center creation")
            return []
        
        service_centers = []
        
        for company in companies:
            # Get locations for this company's city
            locations = center_locations.get(company.city, [])
            
            for i, loc in enumerate(locations):
                # Select random center type and service level
                center_type = random.choice(center_types)
                service_level = random.choice(service_levels)
                
                # Base service center data
                sc_data = {
                    'name': loc['name'],
                    'company': company,
                    'center_type': center_type,
                    'service_level': service_level,
                    'address': f"{loc['area']}، {company.city}",
                    'city': company.city,
                    'state': company.state,
                    'country': 'مصر',
                    'postal_code': company.postal_code,
                    'phone': f"+20{random.randint(1000000000, 2099999999)}",
                    'email': f"service.{company.city.lower()}{i+1}@aftersails.com",
                    'manager_name': fake.name(),
                    'working_hours': '9:00 AM - 6:00 PM',
                    'is_active': True
                }
                
                # Add tenant_id if the model has it
                if hasattr(ServiceCenter, 'tenant_id'):
                    sc_data['tenant_id'] = self.tenant_id
                
                # Create service center based on available fields
                if hasattr(ServiceCenter, 'code') and hasattr(company, 'code'):
                    service_center, created = ServiceCenter.objects.get_or_create(
                        code=f"{company.code}-SC{i+1}",
                        defaults=sc_data
                    )
                else:
                    # Use name as unique identifier
                    service_center, created = ServiceCenter.objects.get_or_create(
                        name=sc_data['name'],
                        company=company,
                        defaults=sc_data
                    )
                
                service_centers.append(service_center)
                
                if created:
                    print(f"Created Service Center: {service_center.name}")
        
        return service_centers
    
    def _create_customers(self, count=50):
        """Create customer records with Egyptian names and details."""
        print(f"Creating {count} customers with Egyptian profile data...")
        
        customers_created = 0
        
        for i in range(count):
            # Generate random customer details
            is_company = random.random() > 0.8  # 20% company customers
            
            if is_company:
                # Business customer
                company_types = ["شركة", "مؤسسة", "مصنع", "ورشة", "معرض"]
                business_name = fake.company()
                name = f"{random.choice(company_types)} {business_name}"
                contact_person = fake.name()
            else:
                # Individual customer
                name = fake.name()
                contact_person = ""
            
            # Base customer data
            customer_data = {
                'name': name,
                'phone': fake.phone_number(),
                'address': fake.address(),
                'city': random.choice(['القاهرة', 'الإسكندرية', 'المنصورة', 'أسيوط', 'طنطا', 'الإسماعيلية']),
                'country': 'مصر',
                'notes': fake.text(max_nb_chars=100) if random.random() > 0.7 else "",
                'is_active': True
            }
            
            # Add fields conditionally
            if hasattr(Customer, 'is_company'):
                customer_data['is_company'] = is_company
            if hasattr(Customer, 'contact_person'):
                customer_data['contact_person'] = contact_person
            if hasattr(Customer, 'tenant_id'):
                customer_data['tenant_id'] = self.tenant_id
            
            try:
                # Create customer
                customer, created = Customer.objects.get_or_create(
                    email=fake.email(),  # Use email as unique identifier
                    defaults=customer_data
                )
                
                if created:
                    # Set registration_date if field exists
                    if hasattr(customer, 'registration_date'):
                        customer.registration_date = timezone.now() - timedelta(days=random.randint(1, 365*2))
                        customer.save(update_fields=['registration_date'])
                    
                    customers_created += 1
                    
                    if customers_created % 10 == 0:
                        print(f"Created {customers_created} customers so far...")
            except Exception as e:
                print(f"Error creating customer: {str(e)}")
        
        print(f"Created total of {customers_created} customers")
        return customers_created
    
    def _create_vehicles(self):
        """Create vehicles with proper Egyptian specifics."""
        print("Creating vehicles...")
        
        # Get all customers
        if hasattr(Customer, 'tenant_id'):
            customers = list(Customer.objects.filter(tenant_id=self.tenant_id))
        else:
            customers = list(Customer.objects.all())
            
        if not customers:
            print("No customers found, skipping vehicle creation")
            return 0
        
        # Egyptian popular car makes and models
        eg_car_data = {
            'تويوتا': ['كورولا', 'يارس', 'لاند كروزر', 'فورتشنر', 'كامري'],
            'هيونداي': ['النترا', 'اكسنت', 'توسان', 'فيرنا', 'كريتا'],
            'نيسان': ['صني', 'سنترا', 'قشقاي', 'باترول'],
            'شيفروليه': ['أفيو', 'أوبترا', 'كروز', 'كابتيفا'],
            'كيا': ['سيراتو', 'سبورتاج', 'بيكانتو', 'سول'],
            'مرسيدس': ['C180', 'E200', 'GLC', 'A200'],
            'بي إم دبليو': ['الفئة الثالثة', 'الفئة الخامسة', 'X3', 'X5'],
            'بيجو': ['301', '508', '3008', '2008'],
            'رينو': ['لوجان', 'داستر', 'ميجان', 'كادجار'],
            'فيات': ['تيبو', '500', 'دوبلو'],
            'جيلي': ['إمجراند', 'باندينو'],
            'بي واي دي': ['F3', 'L3', 'S7'],
            'سكودا': ['أوكتافيا', 'كودياك', 'فابيا']
        }
        
        # License plate letters in Arabic
        arabic_letters = ['أ', 'ب', 'ج', 'د', 'هـ', 'و', 'ز', 'ح', 'ط', 'ي', 'ك', 'ل', 'م', 'ن', 'س', 'ع', 'ف', 'ص']
        
        # Egyptian governorate codes for license plates
        governorate_codes = ['القاهرة', 'الجيزة', 'الإسكندرية', 'الدقهلية', 'الشرقية', 'المنوفية', 'القليوبية', 'الغربية']
        
        vehicles_created = 0
        
        # Each customer gets 1-3 vehicles
        for customer in customers:
            vehicle_count = random.randint(1, 3)
            
            for i in range(vehicle_count):
                # Pick a random make and model
                make = random.choice(list(eg_car_data.keys()))
                model = random.choice(eg_car_data[make])
                
                # Generate a random year between 2005 and 2023
                year = random.randint(2005, 2023)
                
                # Generate Egyptian style license plate
                plate_letter = random.choice(arabic_letters)
                plate_numbers = random.randint(1000, 9999)
                governorate = random.choice(governorate_codes)
                license_plate = f"{plate_numbers} {plate_letter} {governorate}"
                
                # Generate random color (in Arabic)
                colors = ['أبيض', 'أسود', 'فضي', 'رمادي', 'أحمر', 'أزرق', 'أخضر', 'بني', 'بيج', 'برتقالي']
                color = random.choice(colors)
                
                # Create a random VIN
                vin = f"EGY{make[:1]}{random.randint(10000000, 99999999)}"
                
                try:
                    # Base vehicle data
                    vehicle_data = {
                        'make': make,
                        'model': model,
                        'year': year,
                        'license_plate': license_plate,
                        'color': color,
                        'vin': vin,
                        'engine_number': f"ENG{random.randint(100000, 999999)}",
                        'chassis_number': f"CHS{random.randint(100000, 999999)}",
                        'status': 'active',
                        'notes': fake.text(max_nb_chars=100) if random.random() > 0.8 else "",
                        'customer': customer  # Explicit assignment to customer
                    }
                    
                    # Add tenant_id if model has it
                    if hasattr(Vehicle, 'tenant_id'):
                        vehicle_data['tenant_id'] = self.tenant_id
                    
                    # Create vehicle object
                    vehicle = Vehicle(**vehicle_data)
                    
                    # Set registration date
                    vehicle.registration_date = timezone.now() - timedelta(days=random.randint(1, 365*5))
                    
                    # Only set these fields if they exist in the model
                    if hasattr(vehicle, 'last_service_date'):
                        vehicle.last_service_date = timezone.now() - timedelta(days=random.randint(1, 180))
                    if hasattr(vehicle, 'next_service_date'):
                        vehicle.next_service_date = timezone.now() + timedelta(days=random.randint(1, 180))
                    if hasattr(vehicle, 'current_odometer'):
                        vehicle.current_odometer = random.randint(1000, 150000)
                    
                    vehicle.save()
                    vehicles_created += 1
                    
                    if vehicles_created % 20 == 0:
                        print(f"Created {vehicles_created} vehicles so far...")
                
                except Exception as e:
                    print(f"Error creating vehicle: {str(e)}")
        
        print(f"Created total of {vehicles_created} vehicles")
        return vehicles_created
    
    def run(self):
        """Run all generators to create comprehensive demo data."""
        print("Starting Comprehensive Demo Data Generator...")
        
        # Generate inventory data first
        self.generate_inventory_data()
        
        # Generate setup data including customers and vehicles
        self.generate_setup_data()
        
        print("\n✅ Demo data generation complete!")
        print("Generated data includes:")
        print(f"- Inventory items: {Item.objects.filter(tenant_id=self.tenant_id).count()}")
        print(f"- Customers: {Customer.objects.filter(tenant_id=self.tenant_id).count() if hasattr(Customer, 'tenant_id') else Customer.objects.count()}")
        print(f"- Vehicles: {Vehicle.objects.filter(tenant_id=self.tenant_id).count() if hasattr(Vehicle, 'tenant_id') else Vehicle.objects.count()}")
        print(f"- Service Centers: {ServiceCenter.objects.filter(tenant_id=self.tenant_id).count() if hasattr(ServiceCenter, 'tenant_id') else ServiceCenter.objects.count()}")
        
        print("\nYou can now use the system with this realistic Egyptian market demo data.")


def main():
    """Execute the demo data generator."""
    generator = DemoDataGenerator()
    generator.run()


if __name__ == "__main__":
    main() 