from django.shortcuts import render, get_object_or_404
from django.views.generic import ListView, DetailView, TemplateView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.utils.translation import gettext_lazy as _
from django.db.models import Count, Sum, Q

from rest_framework import viewsets, permissions, status
from rest_framework.decorators import api_view, permission_classes, action
from rest_framework.response import Response
from rest_framework.views import APIView

from .models import (
    Franchise, Company, ServiceCenter, Vehicle, ServiceHistory, 
    ServiceLevel, ServiceCenterMakeModel, VehicleOwnershipTransfer,
    VehicleMake, VehicleModel
)
from .serializers import (
    ServiceCenterMakeModelSerializer, ServiceCenterMakeSerializer, 
    VehicleMakeModelSerializer, VehicleMakeSerializer, VehicleModelSerializer
)
from .forms import VehicleOwnershipTransferForm, BulkVehicleOwnershipTransferForm
from django.views.generic.edit import FormView, CreateView
from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
from django.contrib import messages
from django.urls import reverse, reverse_lazy
from django.shortcuts import redirect

class SetupDashboardView(LoginRequiredMixin, TemplateView):
    template_name = 'setup/dashboard.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = _('Organization Setup Dashboard')
        
        # Summary counts
        context['franchise_count'] = Franchise.objects.count()
        context['company_count'] = Company.objects.count()
        context['service_center_count'] = ServiceCenter.objects.count()
        context['vehicle_count'] = Vehicle.objects.count()
        context['service_history_count'] = ServiceHistory.objects.count()
        
        # Recent entries
        context['recent_franchises'] = Franchise.objects.all().order_by('-created_at')[:5]
        context['recent_companies'] = Company.objects.all().order_by('-created_at')[:5]
        context['recent_service_centers'] = ServiceCenter.objects.all().order_by('-created_at')[:5]
        context['recent_vehicles'] = Vehicle.objects.all().order_by('-created_at')[:5]
        
        return context

class FranchiseListView(LoginRequiredMixin, ListView):
    model = Franchise
    template_name = 'setup/franchise_list.html'
    context_object_name = 'franchises'
    paginate_by = 20
    
    def get_queryset(self):
        queryset = super().get_queryset()
        queryset = queryset.annotate(
            company_count=Count('companies'),
            service_center_count=Count('companies__service_centers')
        )
        return queryset
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = _('Franchises')
        return context

class FranchiseDetailView(LoginRequiredMixin, DetailView):
    model = Franchise
    template_name = 'setup/franchise_detail.html'
    context_object_name = 'franchise'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = self.object.name
        context['companies'] = self.object.companies.all()
        context['service_centers'] = ServiceCenter.objects.filter(company__franchise=self.object)
        return context

class CompanyListView(LoginRequiredMixin, ListView):
    model = Company
    template_name = 'setup/company_list.html'
    context_object_name = 'companies'
    paginate_by = 20
    
    def get_queryset(self):
        queryset = super().get_queryset()
        queryset = queryset.annotate(
            service_center_count=Count('service_centers')
        )
        return queryset
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = _('Companies')
        return context

class CompanyDetailView(LoginRequiredMixin, DetailView):
    model = Company
    template_name = 'setup/company_detail.html'
    context_object_name = 'company'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = self.object.name
        context['service_centers'] = self.object.service_centers.all()
        return context

class ServiceCenterListView(LoginRequiredMixin, ListView):
    model = ServiceCenter
    template_name = 'setup/service_center_list.html'
    context_object_name = 'service_centers'
    paginate_by = 20
    
    def get_queryset(self):
        queryset = super().get_queryset()
        queryset = queryset.annotate(
            vehicle_count=Count('vehicles')
        )
        return queryset
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = _('Service Centers')
        return context

class ServiceCenterDetailView(LoginRequiredMixin, DetailView):
    model = ServiceCenter
    template_name = 'setup/service_center_detail.html'
    context_object_name = 'service_center'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = self.object.name
        context['vehicles'] = self.object.vehicles.all()
        context['service_records'] = self.object.service_records.all().order_by('-service_date')[:10]
        return context

class VehicleListView(LoginRequiredMixin, ListView):
    model = Vehicle
    template_name = 'setup/vehicle_list.html'
    context_object_name = 'vehicles'
    paginate_by = 20
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Filter by service center if provided
        service_center_id = self.request.GET.get('service_center')
        if service_center_id:
            queryset = queryset.filter(service_center_id=service_center_id)
            
        queryset = queryset.annotate(
            service_count=Count('service_history')
        )
        return queryset
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = _('Vehicles')
        
        # Get service center filter if any
        service_center_id = self.request.GET.get('service_center')
        if service_center_id:
            context['service_center'] = get_object_or_404(ServiceCenter, pk=service_center_id)
            
        return context

class VehicleDetailView(LoginRequiredMixin, DetailView):
    model = Vehicle
    template_name = 'setup/vehicle_detail.html'
    context_object_name = 'vehicle'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = str(self.object)
        context['service_history'] = self.object.service_history.all().order_by('-service_date')
        return context

class VehicleServiceHistoryListView(LoginRequiredMixin, ListView):
    template_name = 'setup/vehicle_service_history.html'
    context_object_name = 'service_records'
    paginate_by = 20
    
    def get_queryset(self):
        self.vehicle = get_object_or_404(Vehicle, pk=self.kwargs['vehicle_pk'])
        return self.vehicle.service_history.all().order_by('-service_date')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['vehicle'] = self.vehicle
        context['title'] = _('Service History for {0}').format(str(self.vehicle))
        return context

# API Views
class ServiceCenterMakeModelViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing service center vehicle makes and models
    """
    queryset = ServiceCenterMakeModel.objects.all()
    serializer_class = ServiceCenterMakeModelSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Filter by service center
        service_center_id = self.request.query_params.get('service_center')
        if service_center_id:
            queryset = queryset.filter(service_center_id=service_center_id)
            
        # Filter by make
        make = self.request.query_params.get('make')
        if make:
            queryset = queryset.filter(make__iexact=make)
            
        # Only active records
        is_active = self.request.query_params.get('is_active')
        if is_active is not None:
            is_active_bool = is_active.lower() == 'true'
            queryset = queryset.filter(is_active=is_active_bool)
            
        return queryset


class VehicleMakeViewSet(viewsets.ModelViewSet):
    """
    API endpoint for vehicle makes/brands
    """
    queryset = VehicleMake.objects.all()
    serializer_class = VehicleMakeSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        queryset = super().get_queryset().annotate(
            models_count=Count('models')
        )
        
        # Filter by active status
        is_active = self.request.query_params.get('is_active')
        if is_active is not None:
            is_active_bool = is_active.lower() == 'true'
            queryset = queryset.filter(is_active=is_active_bool)
            
        # Filter by country
        country = self.request.query_params.get('country')
        if country:
            queryset = queryset.filter(country_of_origin__iexact=country)
            
        # Filter by name
        name = self.request.query_params.get('name')
        if name:
            queryset = queryset.filter(name__icontains=name)
            
        return queryset


class VehicleModelViewSet(viewsets.ModelViewSet):
    """
    API endpoint for vehicle models
    """
    queryset = VehicleModel.objects.all()
    serializer_class = VehicleModelSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Filter by make
        make_id = self.request.query_params.get('make')
        if make_id:
            queryset = queryset.filter(make_id=make_id)
            
        # Filter by vehicle class
        vehicle_class = self.request.query_params.get('vehicle_class')
        if vehicle_class:
            queryset = queryset.filter(vehicle_class__iexact=vehicle_class)
            
        # Filter by active status
        is_active = self.request.query_params.get('is_active')
        if is_active is not None:
            is_active_bool = is_active.lower() == 'true'
            queryset = queryset.filter(is_active=is_active_bool)
            
        # Filter by year range
        year = self.request.query_params.get('year')
        if year and year.isdigit():
            year_int = int(year)
            queryset = queryset.filter(
                Q(year_introduced__isnull=True) | Q(year_introduced__lte=year_int),
                Q(year_discontinued__isnull=True) | Q(year_discontinued__gte=year_int)
            )
            
        return queryset


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def vehicle_models_by_make(request, make_id):
    """
    Get list of vehicle models for a specific make
    """
    try:
        make = VehicleMake.objects.get(pk=make_id)
    except VehicleMake.DoesNotExist:
        return Response(
            {"error": "Vehicle make not found"}, 
            status=status.HTTP_404_NOT_FOUND
        )
    
    # Get the models for this make, ordered by name
    models = make.models.filter(is_active=True).order_by('name')
    
    # Apply filtering by year if provided
    year = request.query_params.get('year')
    if year and year.isdigit():
        year_int = int(year)
        models = models.filter(
            Q(year_introduced__isnull=True) | Q(year_introduced__lte=year_int),
            Q(year_discontinued__isnull=True) | Q(year_discontinued__gte=year_int)
        )
    
    # Apply filtering by vehicle class if provided
    vehicle_class = request.query_params.get('vehicle_class')
    if vehicle_class:
        models = models.filter(vehicle_class__iexact=vehicle_class)
    
    serializer = VehicleModelSerializer(models, many=True)
    return Response(serializer.data)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def service_center_supported_makes(request, pk):
    """
    Get list of makes supported by a service center,
    with their models grouped
    """
    service_center = get_object_or_404(ServiceCenter, pk=pk)
    
    # If center supports all makes, return a message
    if service_center.serves_all_vehicle_makes:
        return Response({
            "all_makes_supported": True,
            "message": "This service center supports all vehicle makes and models"
        })
        
    # Get all active makes with models for this service center
    make_models = ServiceCenterMakeModel.objects.filter(
        service_center=service_center,
        is_active=True
    ).order_by('make', 'model')
    
    # Group by make
    makes_dict = {}
    for item in make_models:
        if item.make not in makes_dict:
            makes_dict[item.make] = []
            
        if item.model:  # If a specific model is defined
            makes_dict[item.make].append(item.model)
    
    # Format the response
    response_data = [
        {"make": make, "models": models}
        for make, models in makes_dict.items()
    ]
    
    return Response(response_data)


@api_view(['GET'])
def vehicle_makes_models(request):
    """
    Get a list of all vehicle makes and models in the system
    for dropdowns/autocomplete
    """
    # Get unique makes from the vehicles table
    makes = Vehicle.objects.values_list('make', flat=True).distinct().order_by('make')
    
    # For each make, get all its models
    makes_models = []
    for make in makes:
        models = Vehicle.objects.filter(make=make).values_list('model', flat=True).distinct().order_by('model')
        makes_models.append({
            "make": make,
            "models": list(models)
        })
    
    serializer = VehicleMakeModelSerializer(makes_models, many=True)
    return Response(serializer.data)


@api_view(['GET'])
def check_service_center_supports_vehicle(request):
    """
    Check if a service center supports a specific vehicle make/model
    """
    service_center_id = request.query_params.get('service_center')
    make = request.query_params.get('make')
    model = request.query_params.get('model')
    
    if not service_center_id or not make:
        return Response(
            {"error": "service_center and make parameters are required"}, 
            status=status.HTTP_400_BAD_REQUEST
        )
    
    try:
        service_center = ServiceCenter.objects.get(pk=service_center_id)
    except ServiceCenter.DoesNotExist:
        return Response(
            {"error": "Service center not found"}, 
            status=status.HTTP_404_NOT_FOUND
        )
    
    is_supported = service_center.supports_vehicle_make_model(make, model)
    
    return Response({
        "is_supported": is_supported,
        "service_center": service_center.name,
        "make": make,
        "model": model if model else "all models"
    })

class InitiateVehicleTransferView(LoginRequiredMixin, FormView):
    """View for initiating vehicle ownership transfers"""
    template_name = 'setup/initiate_vehicle_transfer.html'
    form_class = BulkVehicleOwnershipTransferForm
    success_url = reverse_lazy('admin:setup_vehicleownershiptransfer_changelist')
    
    def get_form(self, form_class=None):
        form = super().get_form(form_class)
        return form
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get vehicle IDs from session or query params
        vehicle_ids = self.request.session.get('selected_vehicle_ids', [])
        if not vehicle_ids and 'vehicle_id' in self.request.GET:
            vehicle_ids = [self.request.GET.get('vehicle_id')]
            
        # Clear the session
        if 'selected_vehicle_ids' in self.request.session:
            del self.request.session['selected_vehicle_ids']
            
        # Get the vehicle objects
        vehicles = Vehicle.objects.filter(pk__in=vehicle_ids).select_related('owner')
        context['vehicles'] = vehicles
        context['title'] = _('Initiate Vehicle Ownership Transfer')
        
        return context
    
    def form_valid(self, form):
        vehicles = self.get_context_data()['vehicles']
        
        if not vehicles:
            messages.error(self.request, _("No vehicles selected for transfer."))
            return redirect('admin:setup_vehicle_changelist')
        
        # Create a transfer for each vehicle
        transfer_count = 0
        error_count = 0
        
        for vehicle in vehicles:
            try:
                # Skip vehicles without owners
                if not vehicle.owner:
                    messages.warning(
                        self.request, 
                        _("Vehicle {0} has no current owner and was skipped.").format(vehicle)
                    )
                    continue
                    
                # Skip if new owner is the same as current
                if vehicle.owner == form.cleaned_data['new_owner']:
                    messages.warning(
                        self.request, 
                        _("Vehicle {0} is already owned by {1} and was skipped.").format(
                            vehicle, form.cleaned_data['new_owner']
                        )
                    )
                    continue
                
                # Check for existing pending transfers
                if VehicleOwnershipTransfer.objects.filter(vehicle=vehicle, status='pending').exists():
                    messages.warning(
                        self.request, 
                        _("Vehicle {0} already has a pending transfer and was skipped.").format(vehicle)
                    )
                    continue
                
                # Create the transfer
                transfer = VehicleOwnershipTransfer(
                    tenant_id=vehicle.tenant_id,
                    vehicle=vehicle,
                    previous_owner=vehicle.owner,
                    new_owner=form.cleaned_data['new_owner'],
                    transfer_date=form.cleaned_data['transfer_date'],
                    sale_price=form.cleaned_data['sale_price'],
                    odometer_reading=form.cleaned_data['odometer_reading'],
                    notes=form.cleaned_data['notes'],
                    status='pending'
                )
                transfer.save()
                transfer_count += 1
            
            except Exception as e:
                messages.error(
                    self.request, 
                    _("Error creating transfer for {0}: {1}").format(vehicle, str(e))
                )
                error_count += 1
        
        if transfer_count > 0:
            messages.success(
                self.request, 
                _("{0} ownership transfers initiated successfully.").format(transfer_count)
            )
            
        if error_count > 0:
            messages.error(
                self.request, 
                _("{0} transfers failed. Please check the errors and try again.").format(error_count)
            )
            
        return super().form_valid(form)

class VehicleTransferDetailView(LoginRequiredMixin, DetailView):
    """View for viewing details of a vehicle ownership transfer"""
    model = VehicleOwnershipTransfer
    template_name = 'setup/vehicle_transfer_detail.html'
    context_object_name = 'transfer'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = _('Vehicle Ownership Transfer Details')
        return context
    
    def post(self, request, *args, **kwargs):
        """Handle POST requests with action commands"""
        transfer = self.get_object()
        action = request.POST.get('action')
        
        try:
            if action == 'approve':
                transfer.approve(request.user)
                messages.success(request, _("Transfer approved successfully."))
            elif action == 'complete':
                transfer.complete()
                messages.success(request, _("Transfer completed successfully."))
            elif action == 'reject':
                reason = request.POST.get('reason', '')
                transfer.reject(reason)
                messages.success(request, _("Transfer rejected."))
            elif action == 'cancel':
                transfer.cancel()
                messages.success(request, _("Transfer cancelled."))
            else:
                messages.error(request, _("Invalid action."))
        except Exception as e:
            messages.error(request, str(e))
            
        return redirect('setup:vehicle_transfer_detail', pk=transfer.pk)
