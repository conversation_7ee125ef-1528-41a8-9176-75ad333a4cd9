#!/usr/bin/env python3
"""
Database Dependencies Analyzer
=============================

This script analyzes the dependencies between Django models to help understand
the correct migration order and identify potential circular dependencies.

Usage:
    python analyze_dependencies.py
"""

import os
import sys
import django
from pathlib import Path
from collections import defaultdict

# Add the project root to Python path
BASE_DIR = Path(__file__).resolve().parent
sys.path.insert(0, str(BASE_DIR))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

from django.apps import apps
from django.db.models import ForeignKey, OneToOneField


class DependencyAnalyzer:
    def __init__(self):
        self.exclude_apps = [
            'contenttypes', 'auth', 'sessions', 'admin', 'messages',
            'django_otp', 'waffle', 'drf_api_logger', 'authtoken'
        ]
        
    def analyze(self):
        """Analyze all model dependencies"""
        print("🔍 Analyzing Model Dependencies")
        print("=" * 50)
        
        # Get all models
        models = self._get_models()
        print(f"📊 Found {len(models)} models to analyze\n")
        
        # Build dependency graph
        dependencies = self._build_dependency_graph(models)
        
        # Analyze by app
        self._analyze_by_app(models, dependencies)
        
        # Find models with no dependencies (safe to migrate first)
        self._find_independent_models(models, dependencies)
        
        # Find potential circular dependencies
        self._find_circular_dependencies(models, dependencies)
        
        # Suggest migration order
        self._suggest_migration_order(models, dependencies)
        
    def _get_models(self):
        """Get all Django models to analyze"""
        models = []
        for app_config in apps.get_app_configs():
            if app_config.label not in self.exclude_apps:
                for model in app_config.get_models():
                    if not model._meta.abstract and not model._meta.proxy:
                        models.append(model)
        return models
    
    def _build_dependency_graph(self, models):
        """Build a graph of model dependencies"""
        dependencies = defaultdict(set)
        
        for model in models:
            model_key = f"{model._meta.app_label}.{model.__name__}"
            
            for field in model._meta.get_fields():
                if isinstance(field, (ForeignKey, OneToOneField)):
                    related_model = field.related_model
                    if related_model and related_model in models:
                        related_key = f"{related_model._meta.app_label}.{related_model.__name__}"
                        if related_key != model_key:  # Avoid self-references
                            dependencies[model_key].add(related_key)
        
        return dependencies
    
    def _analyze_by_app(self, models, dependencies):
        """Analyze dependencies by Django app"""
        print("📱 Dependencies by App:")
        print("-" * 30)
        
        apps_models = defaultdict(list)
        for model in models:
            apps_models[model._meta.app_label].append(model)
        
        for app_label, app_models in sorted(apps_models.items()):
            print(f"\n🔸 {app_label.upper()} ({len(app_models)} models):")
            
            for model in sorted(app_models, key=lambda x: x.__name__):
                model_key = f"{model._meta.app_label}.{model.__name__}"
                deps = dependencies[model_key]
                
                if deps:
                    dep_list = sorted(list(deps))
                    print(f"  {model.__name__} → depends on: {', '.join(dep_list)}")
                else:
                    print(f"  {model.__name__} → no dependencies ✅")
    
    def _find_independent_models(self, models, dependencies):
        """Find models with no dependencies"""
        print("\n\n🎯 Models with NO Dependencies (Safe to migrate first):")
        print("-" * 60)
        
        independent = []
        for model in models:
            model_key = f"{model._meta.app_label}.{model.__name__}"
            if not dependencies[model_key]:
                independent.append(model_key)
        
        if independent:
            for i, model_key in enumerate(sorted(independent), 1):
                print(f"{i:2d}. {model_key}")
        else:
            print("⚠️  All models have dependencies!")
    
    def _find_circular_dependencies(self, models, dependencies):
        """Find potential circular dependencies"""
        print("\n\n🔄 Checking for Circular Dependencies:")
        print("-" * 45)
        
        model_keys = {f"{model._meta.app_label}.{model.__name__}" for model in models}
        visited = set()
        path = set()
        circular_deps = []
        
        def has_cycle(node):
            if node in path:
                return True
            if node in visited:
                return False
                
            visited.add(node)
            path.add(node)
            
            for neighbor in dependencies[node]:
                if neighbor in model_keys and has_cycle(neighbor):
                    circular_deps.append((node, neighbor))
                    return True
            
            path.remove(node)
            return False
        
        for model_key in model_keys:
            if model_key not in visited:
                has_cycle(model_key)
        
        if circular_deps:
            print("⚠️  Potential circular dependencies found:")
            for i, (from_model, to_model) in enumerate(circular_deps, 1):
                print(f"{i}. {from_model} ↔ {to_model}")
        else:
            print("✅ No circular dependencies detected!")
    
    def _suggest_migration_order(self, models, dependencies):
        """Suggest an optimal migration order"""
        print("\n\n📋 Suggested Migration Order:")
        print("-" * 35)
        
        # Simple topological sort
        remaining = {f"{model._meta.app_label}.{model.__name__}": model for model in models}
        ordered = []
        iteration = 0
        max_iterations = len(models) * 2
        
        while remaining and iteration < max_iterations:
            iteration += 1
            added_this_round = []
            
            for model_key, model in list(remaining.items()):
                # Check if all dependencies are already ordered
                deps = dependencies[model_key]
                unresolved_deps = deps & set(remaining.keys())
                
                if not unresolved_deps:
                    added_this_round.append((model_key, model))
            
            if added_this_round:
                for model_key, model in added_this_round:
                    ordered.append((model_key, model))
                    del remaining[model_key]
            elif remaining:
                # Break ties by choosing model with fewest dependencies
                min_deps_key = min(remaining.keys(), 
                                 key=lambda x: len(dependencies[x] & set(remaining.keys())))
                ordered.append((min_deps_key, remaining[min_deps_key]))
                del remaining[min_deps_key]
                print(f"⚠️  Breaking circular dependency at: {min_deps_key}")
        
        # Print the suggested order
        for i, (model_key, model) in enumerate(ordered, 1):
            table_name = model._meta.db_table
            deps = dependencies[model_key]
            dep_count = len(deps)
            
            status = "🟢" if dep_count == 0 else f"🟡({dep_count})"
            print(f"{i:3d}. {status} {model_key:<35} (table: {table_name})")
        
        print(f"\n📊 Total models to migrate: {len(ordered)}")


def main():
    analyzer = DependencyAnalyzer()
    analyzer.analyze()
    
    print("\n" + "=" * 70)
    print("💡 Tips for successful migration:")
    print("   1. Models with 🟢 have no dependencies - migrate these first")
    print("   2. Models with 🟡(n) have n dependencies - migrate after their dependencies")
    print("   3. If circular dependencies exist, you may need to disable foreign key checks")
    print("   4. Use the improved migration script: migrate_data_fixed.py")
    print("=" * 70)


if __name__ == '__main__':
    main() 