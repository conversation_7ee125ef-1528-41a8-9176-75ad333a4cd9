from django.db import models
from django.contrib.auth.models import User, Group, Permission
from django.utils.translation import gettext_lazy as _
from core.models.common import TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel
from core.querysets import BaseQuerySet
from setup.models import ServiceCenter, Company, Franchise

class Role(TimeStampedModel, UUIDPrimaryKeyModel):
    """
    System roles that define permissions across the application
    """
    ROLE_TYPES = [
        ('system_admin', _('System Administrator')),
        ('franchise_admin', _('Franchise Administrator')),
        ('company_admin', _('Company Administrator')),
        ('service_center_manager', _('Service Center Manager')),
        ('service_advisor', _('Service Advisor')),
        ('technician', _('Technician')),
        ('inventory_manager', _('Inventory Manager')),
        ('parts_clerk', _('Parts Clerk')),
        ('accountant', _('Accountant')),
        ('receptionist', _('Receptionist')),
        ('customer_service', _('Customer Service')),
        ('readonly', _('Read Only User')),
        ('warranty_admin', _('Warranty Administrator')),
        ('finance_manager', _('Finance Manager')),
        ('marketing_manager', _('Marketing Manager')),
        ('quality_inspector', _('Quality Control Inspector')),
        ('fleet_manager', _('Fleet Account Manager')),
        ('regional_manager', _('Regional Manager')),
        ('cashier', _('Cashier/Billing Specialist')),
        # Small center specific roles
        ('small_center_manager', _('Small Center Manager')),
        ('small_center_advisor', _('Small Center Advisor')),
        ('small_center_technician', _('Small Center Technician')),
        # Medium center specific roles
        ('medium_center_manager', _('Medium Center Manager')),
        ('medium_center_advisor', _('Medium Center Advisor')),
        ('medium_center_parts', _('Medium Center Parts Specialist')),
        ('medium_center_cashier', _('Medium Center Cashier')),
        # Large center specific roles
        ('large_center_service_manager', _('Large Center Service Manager')),
        ('large_center_parts_manager', _('Large Center Parts Manager')),
        ('large_center_customer_manager', _('Large Center Customer Service Manager')),
    ]
    
    name = models.CharField(_("Role Name"), max_length=100)
    code = models.CharField(_("Role Code"), max_length=50, unique=True)
    role_type = models.CharField(_("Role Type"), max_length=50, choices=ROLE_TYPES)
    description = models.TextField(_("Description"), blank=True)
    is_active = models.BooleanField(_("Is Active"), default=True)
    
    # Link to Django Group for permissions
    group = models.OneToOneField(
        Group,
        on_delete=models.CASCADE,
        related_name="role",
        verbose_name=_("Permission Group")
    )
    
    # Module access flags
    can_access_setup = models.BooleanField(_("Can Access Setup"), default=False)
    can_access_work_orders = models.BooleanField(_("Can Access Work Orders"), default=False)
    can_access_inventory = models.BooleanField(_("Can Access Inventory"), default=False)
    can_access_warehouse = models.BooleanField(_("Can Access Warehouse"), default=False)
    can_access_sales = models.BooleanField(_("Can Access Sales"), default=False)
    can_access_purchases = models.BooleanField(_("Can Access Purchases"), default=False)
    can_access_reports = models.BooleanField(_("Can Access Reports"), default=False)
    can_access_settings = models.BooleanField(_("Can Access Settings"), default=False)
    
    @staticmethod
    def get_suggested_roles_by_center_size(size):
        """
        Get suggested roles based on service center size
        
        Args:
            size: string ('small', 'medium', or 'large')
            
        Returns:
            dict: Dictionary mapping job functions to role codes
        """
        if size == 'small':
            return {
                'manager': 'small_center_manager',
                'service_advisor': 'small_center_advisor',
                'technician': 'small_center_technician',
            }
        elif size == 'medium':
            return {
                'manager': 'medium_center_manager',
                'service_advisor': 'medium_center_advisor',
                'parts': 'medium_center_parts',
                'cashier': 'medium_center_cashier',
                'technician': 'technician',
            }
        elif size == 'large':
            return {
                'service_manager': 'large_center_service_manager',
                'parts_manager': 'large_center_parts_manager',
                'customer_service_manager': 'large_center_customer_manager',
                'service_advisor': 'service_advisor',
                'parts_clerk': 'parts_clerk',
                'receptionist': 'receptionist',
                'cashier': 'cashier',
                'technician': 'technician',
                'quality_inspector': 'quality_inspector',
                'warranty_admin': 'warranty_admin',
            }
        else:
            # Default to medium if size is not recognized
            return Role.get_suggested_roles_by_center_size('medium')
    
    @classmethod
    def create_default_roles(cls):
        """
        Create default roles with appropriate permissions
        Used during system setup or when adding new role types
        """
        # Define default roles with their access permissions
        default_roles = [
            # System-wide roles
            {
                'name': 'System Administrator',
                'code': 'system_admin',
                'role_type': 'system_admin',
                'description': 'Full access to all system features and settings',
                'can_access_setup': True,
                'can_access_work_orders': True,
                'can_access_inventory': True,
                'can_access_warehouse': True,
                'can_access_sales': True,
                'can_access_purchases': True,
                'can_access_reports': True,
                'can_access_settings': True,
            },
            {
                'name': 'Franchise Administrator',
                'code': 'franchise_admin',
                'role_type': 'franchise_admin',
                'description': 'Manages all aspects of a franchise',
                'can_access_setup': True,
                'can_access_work_orders': True,
                'can_access_inventory': True,
                'can_access_warehouse': True,
                'can_access_sales': True,
                'can_access_purchases': True,
                'can_access_reports': True,
                'can_access_settings': True,
            },
            {
                'name': 'Company Administrator',
                'code': 'company_admin',
                'role_type': 'company_admin',
                'description': 'Manages all aspects of a company within a franchise',
                'can_access_setup': True,
                'can_access_work_orders': True,
                'can_access_inventory': True,
                'can_access_warehouse': True,
                'can_access_sales': True,
                'can_access_purchases': True,
                'can_access_reports': True,
                'can_access_settings': True,
            },
            # Standard service center roles
            {
                'name': 'Service Center Manager',
                'code': 'service_center_manager',
                'role_type': 'service_center_manager',
                'description': 'Oversees all operations of a service center',
                'can_access_setup': True,
                'can_access_work_orders': True,
                'can_access_inventory': True,
                'can_access_warehouse': True,
                'can_access_sales': True,
                'can_access_purchases': True,
                'can_access_reports': True,
                'can_access_settings': False,
            },
            {
                'name': 'Service Advisor',
                'code': 'service_advisor',
                'role_type': 'service_advisor',
                'description': 'Customer-facing role that creates and manages work orders',
                'can_access_setup': False,
                'can_access_work_orders': True,
                'can_access_inventory': True,
                'can_access_warehouse': False,
                'can_access_sales': True,
                'can_access_purchases': False,
                'can_access_reports': True,
                'can_access_settings': False,
            },
            {
                'name': 'Technician',
                'code': 'technician',
                'role_type': 'technician',
                'description': 'Performs vehicle service and repairs',
                'can_access_setup': False,
                'can_access_work_orders': True,
                'can_access_inventory': True,
                'can_access_warehouse': False,
                'can_access_sales': False,
                'can_access_purchases': False,
                'can_access_reports': False,
                'can_access_settings': False,
            },
            {
                'name': 'Parts Clerk',
                'code': 'parts_clerk',
                'role_type': 'parts_clerk',
                'description': 'Manages parts inventory and assists with parts sales',
                'can_access_setup': False,
                'can_access_work_orders': True,
                'can_access_inventory': True,
                'can_access_warehouse': True,
                'can_access_sales': True,
                'can_access_purchases': True,
                'can_access_reports': True,
                'can_access_settings': False,
            },
            {
                'name': 'Cashier',
                'code': 'cashier',
                'role_type': 'cashier',
                'description': 'Handles billing and payment processing',
                'can_access_setup': False,
                'can_access_work_orders': True,
                'can_access_inventory': False,
                'can_access_warehouse': False,
                'can_access_sales': True,
                'can_access_purchases': False,
                'can_access_reports': True,
                'can_access_settings': False,
            },
            # Small center roles
            {
                'name': 'Small Center Manager',
                'code': 'small_center_manager',
                'role_type': 'small_center_manager',
                'description': 'Combined manager role for small service centers',
                'can_access_setup': True,
                'can_access_work_orders': True,
                'can_access_inventory': True,
                'can_access_warehouse': True,
                'can_access_sales': True,
                'can_access_purchases': True,
                'can_access_reports': True,
                'can_access_settings': False,
            },
            {
                'name': 'Small Center Advisor',
                'code': 'small_center_advisor',
                'role_type': 'small_center_advisor',
                'description': 'Combined service advisor, parts, and cashier role for small centers',
                'can_access_setup': False,
                'can_access_work_orders': True,
                'can_access_inventory': True,
                'can_access_warehouse': True,
                'can_access_sales': True,
                'can_access_purchases': True,
                'can_access_reports': True,
                'can_access_settings': False,
            },
            {
                'name': 'Small Center Technician',
                'code': 'small_center_technician',
                'role_type': 'small_center_technician',
                'description': 'Technician with additional responsibilities for small centers',
                'can_access_setup': False,
                'can_access_work_orders': True,
                'can_access_inventory': True,
                'can_access_warehouse': False,
                'can_access_sales': False,
                'can_access_purchases': False,
                'can_access_reports': False,
                'can_access_settings': False,
            },
            # Medium center roles
            {
                'name': 'Medium Center Manager',
                'code': 'medium_center_manager',
                'role_type': 'medium_center_manager',
                'description': 'Manager for medium-sized service centers',
                'can_access_setup': True,
                'can_access_work_orders': True,
                'can_access_inventory': True,
                'can_access_warehouse': True,
                'can_access_sales': True,
                'can_access_purchases': True,
                'can_access_reports': True,
                'can_access_settings': False,
            },
            {
                'name': 'Medium Center Parts Specialist',
                'code': 'medium_center_parts',
                'role_type': 'medium_center_parts',
                'description': 'Parts specialist for medium-sized service centers',
                'can_access_setup': False,
                'can_access_work_orders': True,
                'can_access_inventory': True,
                'can_access_warehouse': True,
                'can_access_sales': True,
                'can_access_purchases': True,
                'can_access_reports': True,
                'can_access_settings': False,
            },
            # Large center roles
            {
                'name': 'Large Center Service Manager',
                'code': 'large_center_service_manager',
                'role_type': 'large_center_service_manager',
                'description': 'Service manager for large service centers',
                'can_access_setup': True,
                'can_access_work_orders': True,
                'can_access_inventory': True,
                'can_access_warehouse': True,
                'can_access_sales': True,
                'can_access_purchases': False,
                'can_access_reports': True,
                'can_access_settings': False,
            },
            {
                'name': 'Large Center Parts Manager',
                'code': 'large_center_parts_manager',
                'role_type': 'large_center_parts_manager',
                'description': 'Parts manager for large service centers',
                'can_access_setup': False,
                'can_access_work_orders': True,
                'can_access_inventory': True,
                'can_access_warehouse': True,
                'can_access_sales': True,
                'can_access_purchases': True,
                'can_access_reports': True,
                'can_access_settings': False,
            },
        ]
        
        # Create each role if it doesn't exist
        created_roles = []
        for role_data in default_roles:
            role, created = cls.objects.get_or_create(
                code=role_data['code'],
                defaults={
                    'name': role_data['name'],
                    'role_type': role_data['role_type'],
                    'description': role_data['description'],
                    'can_access_setup': role_data['can_access_setup'],
                    'can_access_work_orders': role_data['can_access_work_orders'],
                    'can_access_inventory': role_data['can_access_inventory'],
                    'can_access_warehouse': role_data['can_access_warehouse'],
                    'can_access_sales': role_data['can_access_sales'],
                    'can_access_purchases': role_data['can_access_purchases'],
                    'can_access_reports': role_data['can_access_reports'],
                    'can_access_settings': role_data['can_access_settings'],
                }
            )
            if created:
                created_roles.append(role)
        
        return created_roles
    
    @classmethod
    def setup_service_center_roles(cls, service_center):
        """
        Set up suggested roles for a specific service center based on its size
        
        Args:
            service_center: ServiceCenter instance
            
        Returns:
            list: List of roles applicable to this service center
        """
        role_codes = list(cls.get_suggested_roles_by_center_size(service_center.size).values())
        return list(cls.objects.filter(code__in=role_codes, is_active=True))
    
    class Meta:
        verbose_name = _("Role")
        verbose_name_plural = _("Roles")
        ordering = ['name']
        
    def __str__(self):
        return self.name
    
    def save(self, *args, **kwargs):
        """Create or update Django Group when saving Role"""
        if not self.group_id:
            group = Group.objects.create(name=f"role_{self.code}")
            self.group = group
        else:
            self.group.name = f"role_{self.code}"
            self.group.save()
        
        super().save(*args, **kwargs)

class UserRole(TimeStampedModel, UUIDPrimaryKeyModel, TenantAwareModel):
    """
    Assignment of roles to users with scope (franchise, company, service center)
    """
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="user_roles",
        verbose_name=_("User")
    )
    role = models.ForeignKey(
        Role,
        on_delete=models.CASCADE,
        related_name="user_roles",
        verbose_name=_("Role")
    )
    
    # Scope of the role - one of these must be set based on role type
    franchise = models.ForeignKey(
        Franchise,
        on_delete=models.CASCADE,
        null=True, blank=True,
        related_name="user_roles",
        verbose_name=_("Franchise Scope")
    )
    company = models.ForeignKey(
        Company,
        on_delete=models.CASCADE,
        null=True, blank=True,
        related_name="user_roles",
        verbose_name=_("Company Scope")
    )
    service_center = models.ForeignKey(
        ServiceCenter,
        on_delete=models.CASCADE,
        null=True, blank=True,
        related_name="user_roles",
        verbose_name=_("Service Center Scope")
    )
    
    is_primary = models.BooleanField(_("Is Primary Role"), default=False)
    is_active = models.BooleanField(_("Is Active"), default=True)
    
    objects = BaseQuerySet.as_manager()
    
    class Meta:
        verbose_name = _("User Role")
        verbose_name_plural = _("User Roles")
        ordering = ['-is_primary', 'role__name']
        
    def __str__(self):
        scope = ""
        if self.franchise:
            scope = f" at {self.franchise.name}"
        elif self.company:
            scope = f" at {self.company.name}"
        elif self.service_center:
            scope = f" at {self.service_center.name}"
        
        return f"{self.user.username} as {self.role.name}{scope}"
    
    def clean(self):
        """Validate the role scope based on role type"""
        from django.core.exceptions import ValidationError
        
        if not self.role:
            return
            
        role_type = self.role.role_type
        
        # System admin should have no scope
        if role_type == 'system_admin':
            if self.franchise or self.company or self.service_center:
                raise ValidationError(_("System administrators should not have a scope."))
                
        # Franchise admin must have franchise scope only
        elif role_type == 'franchise_admin':
            if not self.franchise or self.company or self.service_center:
                raise ValidationError(_("Franchise administrators must have a franchise scope."))
                
        # Company admin must have company scope only
        elif role_type in ['company_admin', 'regional_manager']:
            if self.franchise or not self.company or self.service_center:
                raise ValidationError(_("Company-level administrators must have a company scope."))
                
        # Service center roles must have service center scope only
        elif role_type in [
            'service_center_manager',
            'service_advisor',
            'technician',
            'inventory_manager',
            'parts_clerk',
            'accountant',
            'receptionist',
            'customer_service',
            'readonly',
            'warranty_admin',
            'finance_manager',
            'marketing_manager',
            'quality_inspector',
            'fleet_manager',
            'cashier',
        ]:
            if self.franchise or self.company or not self.service_center:
                raise ValidationError(_("Service center roles must have a service center scope."))
    
    def save(self, *args, **kwargs):
        # Add user to Django group for this role
        if self.is_active and self.role and self.role.group:
            self.user.groups.add(self.role.group)
        
        # If this is the primary role, make sure other roles for this user are not primary
        if self.is_primary:
            UserRole.objects.filter(user=self.user).exclude(pk=self.pk).update(is_primary=False)
            
        super().save(*args, **kwargs)
        
    def delete(self, *args, **kwargs):
        # Remove user from Django group for this role
        if self.role and self.role.group:
            self.user.groups.remove(self.role.group)
        
        super().delete(*args, **kwargs)

class ModulePermission(TimeStampedModel, UUIDPrimaryKeyModel):
    """
    Fine-grained permissions for specific modules
    """
    MODULE_CHOICES = [
        ('setup', _('Setup')),
        ('work_orders', _('Work Orders')),
        ('inventory', _('Inventory')),
        ('warehouse', _('Warehouse')),
        ('sales', _('Sales')), 
        ('purchases', _('Purchases')),
        ('reports', _('Reports')),
        ('settings', _('Settings')),
    ]
    
    ACTION_CHOICES = [
        ('view', _('View')),
        ('add', _('Add')),
        ('change', _('Change')),
        ('delete', _('Delete')),
        ('approve', _('Approve')),
        ('report', _('Generate Reports')),
    ]
    
    role = models.ForeignKey(
        Role,
        on_delete=models.CASCADE,
        related_name="module_permissions",
        verbose_name=_("Role")
    )
    module = models.CharField(_("Module"), max_length=50, choices=MODULE_CHOICES)
    action = models.CharField(_("Action"), max_length=50, choices=ACTION_CHOICES)
    
    class Meta:
        verbose_name = _("Module Permission")
        verbose_name_plural = _("Module Permissions")
        unique_together = [['role', 'module', 'action']]
        
    def __str__(self):
        return f"{self.role.name} - {self.get_module_display()} - {self.get_action_display()}"
        
    def save(self, *args, **kwargs):
        # Set the corresponding module access flag to True
        if self.module == 'setup':
            self.role.can_access_setup = True
        elif self.module == 'work_orders':
            self.role.can_access_work_orders = True
        elif self.module == 'inventory':
            self.role.can_access_inventory = True
        elif self.module == 'warehouse':
            self.role.can_access_warehouse = True
        elif self.module == 'sales':
            self.role.can_access_sales = True
        elif self.module == 'purchases':
            self.role.can_access_purchases = True
        elif self.module == 'reports':
            self.role.can_access_reports = True
        elif self.module == 'settings':
            self.role.can_access_settings = True
        
        self.role.save()
        
        # Map to Django permission
        codename = f"{self.action}_{self.module}"
        try:
            permission = Permission.objects.get(codename=codename)
            self.role.group.permissions.add(permission)
        except Permission.DoesNotExist:
            pass
            
        super().save(*args, **kwargs)
