# Generated by Django 4.2.20 on 2025-05-26 09:31

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ("setup", "0012_populate_egyptian_vehicle_data"),
        ("work_orders", "0004_workorder_customer"),
        ("inventory", "0018_partpricing_alter_operationpricing_options_and_more"),
    ]

    operations = [
        # Define operation pricing fields
        migrations.AlterField(
            model_name="operationpricing",
            name="base_price",
            field=models.DecimalField(
                decimal_places=2,
                help_text="Base price for this operation",
                max_digits=15,
                verbose_name="Base Price",
            ),
        ),
        migrations.AlterField(
            model_name="operationpricing",
            name="labor_hours",
            field=models.DecimalField(
                decimal_places=2,
                default=0,
                help_text="Estimated labor hours required",
                max_digits=8,
                verbose_name="Labor Hours",
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="operationpricing",
            name="labor_rate",
            field=models.DecimalField(
                decimal_places=2,
                default=0,
                help_text="Hourly labor rate",
                max_digits=15,
                verbose_name="Labor Rate",
            ),
        ),
        migrations.AlterField(
            model_name="operationpricing",
            name="notes",
            field=models.TextField(blank=True, verbose_name="Notes"),
        ),
        # Update PartPricing model to match our definition
        migrations.AlterField(
            model_name="partpricing",
            name="price",
            field=models.DecimalField(
                decimal_places=2, max_digits=15, verbose_name="Price"
            ),
        ),
        migrations.AlterField(
            model_name="partpricing",
            name="is_special_pricing",
            field=models.BooleanField(
                default=False,
                help_text="Indicates if this is a special price (discount/promotion)",
                verbose_name="Special Pricing",
            ),
        ),
        migrations.AlterField(
            model_name="partpricing",
            name="valid_from",
            field=models.DateField(
                blank=True,
                help_text="Start date for this pricing",
                null=True,
                verbose_name="Valid From",
            ),
        ),
        migrations.AlterField(
            model_name="partpricing",
            name="valid_to",
            field=models.DateField(
                blank=True,
                help_text="End date for this pricing",
                null=True,
                verbose_name="Valid To",
            ),
        ),
        migrations.AlterField(
            model_name="partpricing",
            name="notes",
            field=models.TextField(blank=True, verbose_name="Notes"),
        ),
        # Add the unique_together constraint
        migrations.AlterUniqueTogether(
            name="operationpricing",
            unique_together={
                (
                    "tenant_id",
                    "operation_type",
                    "vehicle_make",
                    "vehicle_model",
                    "year_from",
                    "year_to",
                )
            },
        ),
    ]
