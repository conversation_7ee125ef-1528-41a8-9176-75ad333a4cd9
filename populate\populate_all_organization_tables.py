import os
import sys
import django
import random
import uuid
from datetime import datetime, timedelta

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

# Import models
from django.db import transaction
from django.utils import timezone
from django.contrib.auth.models import User, Group
from setup.models import (
    Franchise, Company, ServiceCenter, Vehicle, Customer,
    VehicleOwnershipTransfer, ServiceHistory
)

# Add owner_name property to Vehicle model to fix the signal issue
def add_owner_name_property():
    """Add owner_name property to Vehicle model to fix signal issue"""
    
    # Define a property that returns the owner's name
    def owner_name(self):
        if self.owner:
            return str(self.owner)
        return "Unknown"
    
    # Add the property to the Vehicle class
    Vehicle.owner_name = property(owner_name)
    
    print("Added owner_name property to Vehicle model")

# Egyptian vehicle data
EGYPTIAN_MAKES_MODELS = [
    {"make": "تويوتا", "models": ["كورولا", "لاند كروزر", "كامري", "هايلكس", "فورتشنر", "راف 4", "يارس"]},
    {"make": "هيونداي", "models": ["النترا", "اكسنت", "توسان", "كريتا", "سوناتا", "فيرنا"]},
    {"make": "نيسان", "models": ["صني", "سنترا", "قشقاي", "جوك", "اكس تريل", "باترول"]},
    {"make": "شيفروليه", "models": ["أفيو", "أوبترا", "لانوس", "كروز", "كابتيفا"]},
    {"make": "كيا", "models": ["سيراتو", "سبورتاج", "بيكانتو", "سورينتو", "ريو"]},
    {"make": "ميتسوبيشي", "models": ["لانسر", "أوتلاندر", "باجيرو", "إكليبس"]},
    {"make": "بي ام دبليو", "models": ["الفئة الثالثة", "الفئة الخامسة", "الفئة السابعة", "X5", "X3"]},
    {"make": "مرسيدس", "models": ["C200", "E180", "GLC", "A Class", "GLE", "S Class"]},
    {"make": "أودي", "models": ["A4", "A6", "Q5", "Q7", "A3"]},
    {"make": "فولكس فاجن", "models": ["باسات", "جيتا", "تيجوان", "جولف"]}
]

# Egyptian cities
EGYPTIAN_CITIES = [
    "القاهرة", "الإسكندرية", "الجيزة", "شرم الشيخ", "الغردقة", 
    "المنصورة", "طنطا", "أسيوط", "الإسماعيلية", "بورسعيد",
    "سوهاج", "المنيا", "دمياط", "الزقازيق", "كفر الشيخ",
    "بني سويف", "أسوان", "الأقصر", "مرسى مطروح", "الفيوم"
]

# Egyptian colors
COLORS = ["أبيض", "أسود", "فضي", "رمادي", "أحمر", "أزرق", "بني", "ذهبي", "أخضر", "برتقالي"]

class OrganizationSetupGenerator:
    def __init__(self):
        # Apply monkey patch
        add_owner_name_property()
        
        # Get or create a tenant id
        self.tenant_id = self._get_tenant_id()
        
        print(f"Organization Setup Generator initialized with tenant ID: {self.tenant_id}")
    
    def _get_tenant_id(self):
        """Get an existing tenant ID from the database or create a new one"""
        try:
            customer = Customer.objects.first()
            if customer:
                return customer.tenant_id
            
            # Try service center
            service_center = ServiceCenter.objects.first()
            if service_center:
                return service_center.tenant_id
        except:
            pass
            
        # Create a new one if none found
        return str(uuid.uuid4())
    
    def generate_franchises(self, count=3):
        """Generate franchise data"""
        print(f"\nGenerating {count} franchises...")
        
        franchises_created = 0
        franchise_names = [
            "أفتر سيلز للصيانة المتكاملة",
            "سيارات مصر للخدمات الفنية",
            "المجموعة الذهبية لصيانة السيارات",
            "النيل الأزرق لخدمات السيارات",
            "الشرق الأوسط لصيانة السيارات"
        ]
        
        # Get or create a base company
        companies = Company.objects.all()
        if companies.exists():
            company = companies.first()
            print(f"Using existing company: {company.name}")
        else:
            company = Company.objects.create(
                id=uuid.uuid4(),
                tenant_id=self.tenant_id,
                name="أفتر سيلز مصر",
                business_type="franchise",
                address="شارع التحرير، القاهرة",
                email="<EMAIL>",
                phone="+20 ************",
                website="www.aftersales-eg.com",
                is_active=True,
                attributes={}
            )
            print(f"Created company: {company.name}")
        
        for i in range(count):
            try:
                # Create franchise
                name = franchise_names[i % len(franchise_names)]
                city = random.choice(EGYPTIAN_CITIES)
                
                franchise = Franchise.objects.create(
                    id=uuid.uuid4(),
                    tenant_id=self.tenant_id,
                    name=f"{name} - {city}",
                    address=f"شارع {random.randint(1,30)}، {city}",
                    phone=f"+20 {random.randint(100, 999)} {random.randint(1000000, 9999999)}",
                    email=f"info-{city.lower()}@aftersales.com",
                    website="www.aftersales-eg.com",
                    company=company,
                    is_active=True,
                    attributes={}
                )
                
                franchises_created += 1
                print(f"Created franchise: {franchise.name}")
                
            except Exception as e:
                print(f"Error creating franchise {i+1}: {e}")
        
        print(f"Created {franchises_created} franchises")
        return franchises_created

    def generate_vehicles(self, count=50):
        """Generate vehicle data"""
        print(f"\nGenerating {count} vehicles...")
        
        # Apply monkey patch
        add_owner_name_property()
        
        # Check for customers and service centers
        customers = list(Customer.objects.all())
        if not customers:
            print("No customers found. Please generate customers first.")
            return 0
            
        service_centers = list(ServiceCenter.objects.all())
        if not service_centers:
            print("No service centers found. Please generate service centers first.")
            return 0
        
        vehicles_created = 0
        current_year = datetime.now().year
        
        for i in range(count):
            try:
                # Get random vehicle data
                make_model = random.choice(EGYPTIAN_MAKES_MODELS)
                make = make_model["make"]
                model = random.choice(make_model["models"])
                
                # Get random owner and service center
                owner = random.choice(customers)
                service_center = random.choice(service_centers)
                
                # Generate random vehicle details
                year = random.randint(current_year - 12, current_year)
                vin = f"EGT{random.randint(100000, 999999)}"
                license_plate = f"{random.randint(100, 999)} {random.choice('أبتثجحخدذرزسشصضطظعغفقكلمنهوي')}{random.choice('أبتثجحخدذرزسشصضطظعغفقكلمنهوي')}{random.choice('أبتثجحخدذرزسشصضطظعغفقكلمنهوي')}"
                color = random.choice(COLORS)
                
                # Generate dates
                purchase_date = datetime(year, random.randint(1, 12), random.randint(1, 28)).date()
                warranty_years = random.randint(3, 5)
                warranty_end_date = datetime(year + warranty_years, purchase_date.month, 
                                         min(purchase_date.day, 28)).date()
                
                # Create vehicle
                vehicle = Vehicle(
                    id=uuid.uuid4(),
                    tenant_id=self.tenant_id,
                    make=make,
                    model=model,
                    year=year,
                    vin=vin,
                    license_plate=license_plate,
                    color=color,
                    owner=owner,
                    service_center=service_center,
                    purchase_date=purchase_date,
                    warranty_end_date=warranty_end_date,
                    notes=f"سيارة {make} {model} موديل {year}",
                    attributes={}
                )
                
                vehicle.save()
                vehicles_created += 1
                
                if vehicles_created % 10 == 0:
                    print(f"Created {vehicles_created} vehicles")
                
            except Exception as e:
                print(f"Error creating vehicle {i+1}: {e}")
                
        print(f"Created {vehicles_created} vehicles")
        return vehicles_created
    
    def generate_vehicle_transfers(self, count=10):
        """Generate vehicle ownership transfers"""
        print(f"\nGenerating {count} vehicle ownership transfers...")
        
        # Check for vehicles and customers
        vehicles = list(Vehicle.objects.filter(owner__isnull=False))
        if not vehicles:
            print("No vehicles with owners found. Please generate vehicles first.")
            return 0
            
        customers = list(Customer.objects.all())
        if len(customers) < 2:
            print("Need at least 2 customers for transfers. Please generate more customers.")
            return 0
        
        transfers_created = 0
        
        for i in range(count):
            try:
                # Get a random vehicle
                vehicle = random.choice(vehicles)
                
                # Skip if no owner
                if not vehicle.owner:
                    continue
                    
                # Get previous owner
                previous_owner = vehicle.owner
                
                # Get a new owner different from previous owner
                new_owners = [c for c in customers if c != previous_owner]
                if not new_owners:
                    continue
                new_owner = random.choice(new_owners)
                
                # Create transfer date
                transfer_date = timezone.now().date() - timedelta(days=random.randint(1, 60))
                
                # Choose a status
                status = random.choice(['completed', 'approved', 'pending'])
                
                # Create the transfer
                transfer = VehicleOwnershipTransfer(
                    id=uuid.uuid4(),
                    tenant_id=self.tenant_id,
                    vehicle=vehicle,
                    previous_owner=previous_owner,
                    new_owner=new_owner,
                    transfer_date=transfer_date,
                    status=status,
                    sale_price=random.randint(50000, 500000),
                    odometer_reading=random.randint(10000, 100000),
                    notes=f"نقل ملكية من {previous_owner} إلى {new_owner}"
                )
                
                # If completed, update the vehicle owner
                if status == 'completed':
                    # Update vehicle owner directly
                    vehicle.owner = new_owner
                    vehicle.save()
                
                transfer.save()
                transfers_created += 1
                
                print(f"Created transfer: {vehicle.make} {vehicle.model} from {previous_owner} to {new_owner}")
                
            except Exception as e:
                print(f"Error creating transfer {i+1}: {e}")
                
        print(f"Created {transfers_created} vehicle transfers")
        return transfers_created
    
    def generate_service_history(self, count=30):
        """Generate service history for vehicles"""
        print(f"\nGenerating {count} service history records...")
        
        # Check for vehicles
        vehicles = list(Vehicle.objects.all())
        if not vehicles:
            print("No vehicles found. Please generate vehicles first.")
            return 0
        
        service_history_created = 0
        
        service_types = [
            "صيانة دورية", "تغيير زيت", "إصلاح محرك", "إصلاح فرامل", 
            "غسيل وتنظيف", "تغيير إطارات", "فحص دوري", "إصلاح كهرباء"
        ]
        
        for i in range(count):
            try:
                # Get a random vehicle
                vehicle = random.choice(vehicles)
                
                # Create service date
                service_date = timezone.now().date() - timedelta(days=random.randint(1, 365))
                
                # Random service details
                service_type = random.choice(service_types)
                cost = random.randint(200, 5000)
                odometer = random.randint(5000, 100000)
                
                # Create service history record
                service = ServiceHistory(
                    id=uuid.uuid4(),
                    tenant_id=self.tenant_id,
                    vehicle=vehicle,
                    service_date=service_date,
                    service_type=service_type,
                    service_center=vehicle.service_center,
                    odometer_reading=odometer,
                    cost=cost,
                    notes=f"تمت {service_type} للسيارة",
                    attributes={}
                )
                
                service.save()
                service_history_created += 1
                
                if service_history_created % 10 == 0:
                    print(f"Created {service_history_created} service history records")
                
            except Exception as e:
                print(f"Error creating service history {i+1}: {e}")
                
        print(f"Created {service_history_created} service history records")
        return service_history_created

    def run(self):
        """Run the complete organization setup generation"""
        print("Starting organization setup data generation...")
        
        # Generate main franchise and company structure
        self.generate_franchises(3)
        
        # Generate vehicles with monkey patch to fix owner_name issue
        self.generate_vehicles(50)
        
        # Generate vehicle ownership transfers
        self.generate_vehicle_transfers(15)
        
        # Generate service history
        self.generate_service_history(30)
        
        print("\nOrganization setup data generation complete!")

if __name__ == "__main__":
    generator = OrganizationSetupGenerator()
    generator.run() 