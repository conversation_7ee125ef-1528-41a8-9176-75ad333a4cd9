import os
import django
from django.utils import timezone

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

# Direct database access using Django's connection
from django.db import connection

def insert_franchise_data():
    with connection.cursor() as cursor:
        # First, get company IDs to use as foreign keys
        cursor.execute("SELECT id FROM website_company LIMIT 4")
        company_ids = [row[0] for row in cursor.fetchall()]
        
        if not company_ids:
            print("No companies found. Please ensure website_company table has data.")
            return
        
        # Insert franchise data
        franchise_data = [
            ('أفتر سيلز للصيانة', 'شارع التحرير، القاهرة', '+20 102 555 6666', '<EMAIL>', True, company_ids[0]),
            ('الدلتا للصيانة', 'شارع الجلاء، المنصورة', '+20 102 555 7777', '<EMAIL>', True, company_ids[min(1, len(company_ids)-1)]),
            ('الإسكندرية أوتو سيرفيس', 'طريق الكورنيش، الإسكندرية', '+20 ************', '<EMAIL>', True, company_ids[min(2, len(company_ids)-1)]),
            ('الصعيد للخدمات الفنية', 'شارع أسيوط الرئيسي، أسيوط', '+20 ************', '<EMAIL>', True, company_ids[min(3, len(company_ids)-1)])
        ]
        
        franchises_created = 0
        current_time = timezone.now()
        
        for name, address, phone, email, is_active, company_id in franchise_data:
            # Check if franchise already exists
            cursor.execute("SELECT id FROM website_franchise WHERE name = %s", [name])
            if cursor.fetchone():
                print(f"Franchise '{name}' already exists, skipping.")
                continue
                
            # Insert new franchise
            try:
                cursor.execute("""
                INSERT INTO website_franchise 
                (name, address, phone, email, is_active, company_id, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                """, [name, address, phone, email, is_active, company_id, current_time, current_time])
                franchises_created += 1
                print(f"Created franchise: {name}")
            except Exception as e:
                print(f"Error creating franchise '{name}': {e}")
        
        print(f"Created {franchises_created} franchises")

if __name__ == "__main__":
    print("Starting franchise data insertion...")
    insert_franchise_data()
    print("Franchise data insertion complete!") 