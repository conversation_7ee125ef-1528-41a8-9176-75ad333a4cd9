from django.urls import path
from . import views

app_name = 'franchise_setup'

urlpatterns = [
    # Dashboard
    path('', views.franchise_dashboard, name='dashboard'),
    
    # Agreement management
    path('agreements/', views.agreement_list, name='agreement_list'),
    path('agreements/<uuid:pk>/', views.agreement_detail, name='agreement_detail'),
    
    # Compliance management
    path('compliance/', views.compliance_list, name='compliance_list'),
    path('compliance/<uuid:pk>/update-status/', views.update_compliance_status, name='update_compliance_status'),
    
    # Revenue share management
    path('revenue/', views.revenue_share_list, name='revenue_share_list'),
    
    # Template management
    path('templates/', views.template_list, name='template_list'),
    path('templates/<uuid:pk>/', views.template_detail, name='template_detail'),
] 